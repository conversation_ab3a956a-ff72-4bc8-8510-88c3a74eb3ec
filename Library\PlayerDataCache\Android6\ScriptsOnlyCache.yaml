ScriptsOnlyBuild:
  usedScripts:
    Assembly-CSharp.dll:
    - AlignmentTest
    - AssemblyAnimationManager
    - AssemblyPart
    - Neo4jAssemblyController
    - Neo4jAssemblyUI
    - Neo4jConnector
    - PICOActionBasedInputHandler
    - PICOInputActionAdapter
    - SimpleInputTest
    - VRAssemblyDebugger
    - VRAssemblyManager
    - VRAssemblyPositioner
    - VRAssemblyPreview
    - VRAssemblyUIIntegrator
    - VRCameraDataRecorder
    - VRCanvasInteractionFix
    - VRSceneManager
    - VRSimulator
    - VRStartMenuInputHandler
    - VRStartMenuManager
    - VRSystemDebugger
    - VRUIInteractor
    - VRUIManager
    - VRUserGuidance
    PICO.Platform.dll:
    - Unity.XR.PXR.PXR_PlatformSetting
    Unity.InputSystem.dll:
    - UnityEngine.InputSystem.InputActionAsset
    - UnityEngine.InputSystem.InputActionReference
    - UnityEngine.InputSystem.InputSettings
    - UnityEngine.InputSystem.InputSystemObject
    - UnityEngine.InputSystem.RemoteInputPlayerConnection
    - UnityEngine.InputSystem.XR.TrackedPoseDriver
    Unity.Rider.Editor.dll:
    - Packages.Rider.Editor.UnitTesting.CallbackData
    Unity.TextMeshPro.dll:
    - TMPro.TMP_FontAsset
    - TMPro.TMP_InputField
    - TMPro.TMP_Settings
    - TMPro.TMP_SpriteAsset
    - TMPro.TMP_StyleSheet
    - TMPro.TextMeshProUGUI
    Unity.XR.CoreUtils.dll:
    - Unity.XR.CoreUtils.XROrigin
    Unity.XR.Interaction.Toolkit.Editor.dll:
    - UnityEditor.XR.Interaction.Toolkit.XRInteractionEditorSettings
    Unity.XR.Interaction.Toolkit.dll:
    - UnityEngine.XR.Interaction.Toolkit.ActionBasedController
    - UnityEngine.XR.Interaction.Toolkit.Inputs.InputActionManager
    - UnityEngine.XR.Interaction.Toolkit.Inputs.Simulation.XRDeviceSimulatorSettings
    - UnityEngine.XR.Interaction.Toolkit.InteractionLayerSettings
    - UnityEngine.XR.Interaction.Toolkit.UI.XRUIInputModule
    - UnityEngine.XR.Interaction.Toolkit.XRInteractionManager
    - UnityEngine.XR.Interaction.Toolkit.XRInteractorLineVisual
    - UnityEngine.XR.Interaction.Toolkit.XRRayInteractor
    - UnityEngine.XR.Interaction.Toolkit.XRSimpleInteractable
    Unity.XR.Management.Editor.dll:
    - UnityEditor.XR.Management.XRGeneralSettingsPerBuildTarget
    Unity.XR.Management.dll:
    - UnityEngine.XR.Management.XRGeneralSettings
    - UnityEngine.XR.Management.XRManagerSettings
    Unity.XR.PICO.dll:
    - PXR_ControllerPower
    - PXR_Hand
    - PXR_HandPosePreview
    - Unity.XR.PXR.PXR_ControllerAnimator
    - Unity.XR.PXR.PXR_ControllerG3Animator
    - Unity.XR.PXR.PXR_ControllerLoader
    - Unity.XR.PXR.PXR_ControllerWithHandAnimator
    - Unity.XR.PXR.PXR_HandPoseGenerator
    - Unity.XR.PXR.PXR_Loader
    - Unity.XR.PXR.PXR_Manager
    - Unity.XR.PXR.PXR_ProjectSetting
    - Unity.XR.PXR.PXR_Settings
    - Unity.XR.PXR.PXR_SpatialAnchor
    UnityEditor.TestRunner.dll:
    - UnityEditor.TestTools.TestRunner.Api.CallbacksHolder
    - UnityEditor.TestTools.TestRunner.TestListCacheData
    - UnityEditor.TestTools.TestRunner.TestRun.TestJobDataHolder
    UnityEngine.UI.dll:
    - UnityEngine.EventSystems.EventSystem
    - UnityEngine.EventSystems.EventTrigger
    - UnityEngine.EventSystems.StandaloneInputModule
    - UnityEngine.UI.Button
    - UnityEngine.UI.CanvasScaler
    - UnityEngine.UI.GraphicRaycaster
    - UnityEngine.UI.Image
    - UnityEngine.UI.Slider
    - UnityEngine.UI.Text
    - UnityEngine.UI.Toggle
  serializedClasses:
    Unity.InputSystem:
    - UnityEngine.InputSystem.InputAction
    - UnityEngine.InputSystem.InputActionMap
    - UnityEngine.InputSystem.InputActionProperty
    - UnityEngine.InputSystem.InputBinding
    - UnityEngine.InputSystem.InputControlScheme
    - UnityEngine.InputSystem.InputControlScheme/DeviceRequirement
    Unity.TextMeshPro:
    - TMPro.FaceInfo_Legacy
    - TMPro.FontAssetCreationSettings
    - TMPro.KerningTable
    - TMPro.TMP_Character
    - TMPro.TMP_FontFeatureTable
    - TMPro.TMP_FontWeightPair
    - TMPro.TMP_GlyphAdjustmentRecord
    - TMPro.TMP_GlyphPairAdjustmentRecord
    - TMPro.TMP_GlyphValueRecord
    - TMPro.TMP_InputField/OnChangeEvent
    - TMPro.TMP_InputField/SelectionEvent
    - TMPro.TMP_InputField/SubmitEvent
    - TMPro.TMP_InputField/TextSelectionEvent
    - TMPro.TMP_InputField/TouchScreenKeyboardEvent
    - TMPro.TMP_Sprite
    - TMPro.TMP_SpriteCharacter
    - TMPro.TMP_SpriteGlyph
    - TMPro.TMP_Style
    - TMPro.VertexGradient
    Unity.XR.Interaction.Toolkit:
    - UnityEngine.XR.Interaction.Toolkit.ActivateEvent
    - UnityEngine.XR.Interaction.Toolkit.DeactivateEvent
    - UnityEngine.XR.Interaction.Toolkit.FocusEnterEvent
    - UnityEngine.XR.Interaction.Toolkit.FocusExitEvent
    - UnityEngine.XR.Interaction.Toolkit.HoverEnterEvent
    - UnityEngine.XR.Interaction.Toolkit.HoverExitEvent
    - UnityEngine.XR.Interaction.Toolkit.InteractionLayerMask
    - UnityEngine.XR.Interaction.Toolkit.SelectEnterEvent
    - UnityEngine.XR.Interaction.Toolkit.SelectExitEvent
    - UnityEngine.XR.Interaction.Toolkit.UI.UIHoverEnterEvent
    - UnityEngine.XR.Interaction.Toolkit.UI.UIHoverExitEvent
    - UnityEngine.XR.Interaction.Toolkit.XRInteractableEvent
    - UnityEngine.XR.Interaction.Toolkit.XRInteractorEvent
    Unity.XR.PICO:
    - PXR_HandPosePreview/ModelFinger
    - Unity.XR.PXR.BonesRecognizer/BonesGroup
    - Unity.XR.PXR.ShapesRecognizer/Finger
    - Unity.XR.PXR.ShapesRecognizer/FingerConfigs
    - Unity.XR.PXR.ShapesRecognizer/RangeConfigs
    - Unity.XR.PXR.ShapesRecognizer/RangeConfigsAbduction
    UnityEngine.CoreModule:
    - UnityEngine.Events.ArgumentCache
    - UnityEngine.Events.PersistentCallGroup
    - UnityEngine.Events.PersistentListenerMode
    UnityEngine.TextCoreFontEngineModule:
    - UnityEngine.TextCore.FaceInfo
    - UnityEngine.TextCore.Glyph
    - UnityEngine.TextCore.GlyphMetrics
    - UnityEngine.TextCore.GlyphRect
    UnityEngine.UI:
    - UnityEngine.EventSystems.EventTrigger/Entry
    - UnityEngine.EventSystems.EventTrigger/TriggerEvent
    - UnityEngine.UI.AnimationTriggers
    - UnityEngine.UI.Button/ButtonClickedEvent
    - UnityEngine.UI.ColorBlock
    - UnityEngine.UI.FontData
    - UnityEngine.UI.MaskableGraphic/CullStateChangedEvent
    - UnityEngine.UI.Navigation
    - UnityEngine.UI.Slider/SliderEvent
    - UnityEngine.UI.SpriteState
    - UnityEngine.UI.Toggle/ToggleEvent
  methodsToPreserve:
  - assembly: Assembly-CSharp
    fullTypeName: VRSceneManager
    methodName: LoadScene
  sceneClasses:
    Assets/Scenes/StartMenu.unity:
    - Class: 1
      Script: {instanceID: 0}
    - Class: 4
      Script: {instanceID: 0}
    - Class: 20
      Script: {instanceID: 0}
    - Class: 21
      Script: {instanceID: 0}
    - Class: 23
      Script: {instanceID: 0}
    - Class: 28
      Script: {instanceID: 0}
    - Class: 33
      Script: {instanceID: 0}
    - Class: 43
      Script: {instanceID: 0}
    - Class: 48
      Script: {instanceID: 0}
    - Class: 49
      Script: {instanceID: 0}
    - Class: 54
      Script: {instanceID: 0}
    - Class: 64
      Script: {instanceID: 0}
    - Class: 65
      Script: {instanceID: 0}
    - Class: 74
      Script: {instanceID: 0}
    - Class: 81
      Script: {instanceID: 0}
    - Class: 89
      Script: {instanceID: 0}
    - Class: 90
      Script: {instanceID: 0}
    - Class: 91
      Script: {instanceID: 0}
    - Class: 95
      Script: {instanceID: 0}
    - Class: 104
      Script: {instanceID: 0}
    - Class: 108
      Script: {instanceID: 0}
    - Class: 114
      Script: {instanceID: 16194}
    - Class: 114
      Script: {instanceID: 16270}
    - Class: 114
      Script: {instanceID: 16522}
    - Class: 114
      Script: {instanceID: 16600}
    - Class: 114
      Script: {instanceID: 16682}
    - Class: 114
      Script: {instanceID: 17138}
    - Class: 114
      Script: {instanceID: 17768}
    - Class: 114
      Script: {instanceID: 18056}
    - Class: 114
      Script: {instanceID: 18314}
    - Class: 114
      Script: {instanceID: 18494}
    - Class: 114
      Script: {instanceID: 19198}
    - Class: 114
      Script: {instanceID: 19424}
    - Class: 114
      Script: {instanceID: 19430}
    - Class: 114
      Script: {instanceID: 19516}
    - Class: 114
      Script: {instanceID: 19634}
    - Class: 114
      Script: {instanceID: 19888}
    - Class: 114
      Script: {instanceID: 19996}
    - Class: 114
      Script: {instanceID: 20372}
    - Class: 114
      Script: {instanceID: 20958}
    - Class: 114
      Script: {instanceID: 21146}
    - Class: 114
      Script: {instanceID: 21232}
    - Class: 114
      Script: {instanceID: 21430}
    - Class: 114
      Script: {instanceID: 22094}
    - Class: 114
      Script: {instanceID: 22108}
    - Class: 114
      Script: {instanceID: 22184}
    - Class: 114
      Script: {instanceID: 22500}
    - Class: 114
      Script: {instanceID: 22682}
    - Class: 114
      Script: {instanceID: 22892}
    - Class: 114
      Script: {instanceID: 22996}
    - Class: 114
      Script: {instanceID: 23508}
    - Class: 114
      Script: {instanceID: 23852}
    - Class: 114
      Script: {instanceID: 23938}
    - Class: 114
      Script: {instanceID: 24042}
    - Class: 114
      Script: {instanceID: 24586}
    - Class: 114
      Script: {instanceID: 24896}
    - Class: 114
      Script: {instanceID: 25020}
    - Class: 114
      Script: {instanceID: 25318}
    - Class: 114
      Script: {instanceID: 25560}
    - Class: 114
      Script: {instanceID: 31326}
    - Class: 114
      Script: {instanceID: 34060}
    - Class: 114
      Script: {instanceID: 34072}
    - Class: 114
      Script: {instanceID: 34076}
    - Class: 114
      Script: {instanceID: 34086}
    - Class: 114
      Script: {instanceID: 34136}
    - Class: 114
      Script: {instanceID: 34140}
    - Class: 114
      Script: {instanceID: 34150}
    - Class: 114
      Script: {instanceID: 34158}
    - Class: 114
      Script: {instanceID: 34162}
    - Class: 114
      Script: {instanceID: 34170}
    - Class: 114
      Script: {instanceID: 34180}
    - Class: 114
      Script: {instanceID: 34190}
    - Class: 114
      Script: {instanceID: 34198}
    - Class: 114
      Script: {instanceID: 38104}
    - Class: 114
      Script: {instanceID: 38106}
    - Class: 114
      Script: {instanceID: 38108}
    - Class: 114
      Script: {instanceID: 43724}
    - Class: 115
      Script: {instanceID: 0}
    - Class: 120
      Script: {instanceID: 0}
    - Class: 128
      Script: {instanceID: 0}
    - Class: 135
      Script: {instanceID: 0}
    - Class: 136
      Script: {instanceID: 0}
    - Class: 137
      Script: {instanceID: 0}
    - Class: 157
      Script: {instanceID: 0}
    - Class: 196
      Script: {instanceID: 0}
    - Class: 210
      Script: {instanceID: 0}
    - Class: 213
      Script: {instanceID: 0}
    - Class: 221
      Script: {instanceID: 0}
    - Class: 222
      Script: {instanceID: 0}
    - Class: 223
      Script: {instanceID: 0}
    - Class: 224
      Script: {instanceID: 0}
    - Class: 319
      Script: {instanceID: 0}
    "Assets/Scenes/\u5C0FU\u548C\u7535\u673A.unity":
    - Class: 1
      Script: {instanceID: 0}
    - Class: 4
      Script: {instanceID: 0}
    - Class: 20
      Script: {instanceID: 0}
    - Class: 21
      Script: {instanceID: 0}
    - Class: 23
      Script: {instanceID: 0}
    - Class: 28
      Script: {instanceID: 0}
    - Class: 33
      Script: {instanceID: 0}
    - Class: 43
      Script: {instanceID: 0}
    - Class: 48
      Script: {instanceID: 0}
    - Class: 64
      Script: {instanceID: 0}
    - Class: 74
      Script: {instanceID: 0}
    - Class: 81
      Script: {instanceID: 0}
    - Class: 89
      Script: {instanceID: 0}
    - Class: 91
      Script: {instanceID: 0}
    - Class: 95
      Script: {instanceID: 0}
    - Class: 104
      Script: {instanceID: 0}
    - Class: 108
      Script: {instanceID: 0}
    - Class: 114
      Script: {instanceID: 16194}
    - Class: 114
      Script: {instanceID: 16198}
    - Class: 114
      Script: {instanceID: 16204}
    - Class: 114
      Script: {instanceID: 16208}
    - Class: 114
      Script: {instanceID: 16270}
    - Class: 114
      Script: {instanceID: 16600}
    - Class: 114
      Script: {instanceID: 16644}
    - Class: 114
      Script: {instanceID: 16682}
    - Class: 114
      Script: {instanceID: 17768}
    - Class: 114
      Script: {instanceID: 17814}
    - Class: 114
      Script: {instanceID: 18056}
    - Class: 114
      Script: {instanceID: 18314}
    - Class: 114
      Script: {instanceID: 18400}
    - Class: 114
      Script: {instanceID: 18494}
    - Class: 114
      Script: {instanceID: 18956}
    - Class: 114
      Script: {instanceID: 19058}
    - Class: 114
      Script: {instanceID: 19178}
    - Class: 114
      Script: {instanceID: 19198}
    - Class: 114
      Script: {instanceID: 19430}
    - Class: 114
      Script: {instanceID: 19516}
    - Class: 114
      Script: {instanceID: 19634}
    - Class: 114
      Script: {instanceID: 19832}
    - Class: 114
      Script: {instanceID: 20542}
    - Class: 114
      Script: {instanceID: 21334}
    - Class: 114
      Script: {instanceID: 21394}
    - Class: 114
      Script: {instanceID: 21836}
    - Class: 114
      Script: {instanceID: 22002}
    - Class: 114
      Script: {instanceID: 22108}
    - Class: 114
      Script: {instanceID: 22184}
    - Class: 114
      Script: {instanceID: 22332}
    - Class: 114
      Script: {instanceID: 22446}
    - Class: 114
      Script: {instanceID: 22892}
    - Class: 114
      Script: {instanceID: 22982}
    - Class: 114
      Script: {instanceID: 23192}
    - Class: 114
      Script: {instanceID: 23508}
    - Class: 114
      Script: {instanceID: 23778}
    - Class: 114
      Script: {instanceID: 23804}
    - Class: 114
      Script: {instanceID: 24774}
    - Class: 114
      Script: {instanceID: 24896}
    - Class: 114
      Script: {instanceID: 25020}
    - Class: 114
      Script: {instanceID: 25330}
    - Class: 115
      Script: {instanceID: 0}
    - Class: 120
      Script: {instanceID: 0}
    - Class: 128
      Script: {instanceID: 0}
    - Class: 137
      Script: {instanceID: 0}
    - Class: 157
      Script: {instanceID: 0}
    - Class: 196
      Script: {instanceID: 0}
    - Class: 210
      Script: {instanceID: 0}
    - Class: 213
      Script: {instanceID: 0}
    - Class: 221
      Script: {instanceID: 0}
    - Class: 222
      Script: {instanceID: 0}
    - Class: 223
      Script: {instanceID: 0}
    - Class: 224
      Script: {instanceID: 0}
  scriptHashData:
  - hash:
      serializedVersion: 2
      Hash: 8b0fb436d504b931ba160651af617241
    assemblyName: UnityEngine.SpatialTracking.dll
    namespaceName: UnityEngine.SpatialTracking
    className: TrackedPoseDriver
  - hash:
      serializedVersion: 2
      Hash: 7bcb35908a60cac26edee0ff51816ada
    assemblyName: UnityEngine.UI.dll
    namespaceName: UnityEngine.UI
    className: CanvasScaler
  - hash:
      serializedVersion: 2
      Hash: da05abcc878171591b25804eef377360
    assemblyName: UnityEngine.UI.dll
    namespaceName: UnityEngine.UI
    className: LayoutElement
  - hash:
      serializedVersion: 2
      Hash: 10b44983f0a7578909bb4d68fa771902
    assemblyName: UnityEngine.UI.dll
    namespaceName: UnityEngine.UI
    className: Toggle
  - hash:
      serializedVersion: 2
      Hash: 2bd783b5b85429caff317fd8ece19300
    assemblyName: UnityEngine.UI.dll
    namespaceName: UnityEngine.UI
    className: Mask
  - hash:
      serializedVersion: 2
      Hash: 74101b1189e7f911aef3b5ca95b2db19
    assemblyName: UnityEngine.UI.dll
    namespaceName: UnityEngine.UI
    className: Dropdown
  - hash:
      serializedVersion: 2
      Hash: 8e0c8b499f64b686c0ec50f43e94d3af
    assemblyName: UnityEngine.UI.dll
    namespaceName: UnityEngine.UI
    className: Text
  - hash:
      serializedVersion: 2
      Hash: a1926869a1b71ceb204692733af622c6
    assemblyName: UnityEngine.UI.dll
    namespaceName: UnityEngine.UI
    className: ScrollRect
  - hash:
      serializedVersion: 2
      Hash: 4a084a380bc93f7fe467af245aac44cd
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: Neo4jConnector
  - hash:
      serializedVersion: 2
      Hash: c1058004017c4d95e55a1c77d955e000
    assemblyName: UnityEngine.UI.dll
    namespaceName: UnityEngine.UIElements
    className: PanelRaycaster
  - hash:
      serializedVersion: 2
      Hash: d6df62c5d240b6fee51975d5a42994dc
    assemblyName: Unity.XR.Interaction.Toolkit.dll
    namespaceName: UnityEngine.XR.Interaction.Toolkit
    className: TwoHandedGrabMoveProvider
  - hash:
      serializedVersion: 2
      Hash: e0c91eab1ed78e90554d0a44ec6d0fb4
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: SimpleExternalDataReceiver
  - hash:
      serializedVersion: 2
      Hash: a9d9d285d996b592f1ccf674fa017099
    assemblyName: UnityEngine.UI.dll
    namespaceName: UnityEngine.UI
    className: Image
  - hash:
      serializedVersion: 2
      Hash: 25fcf8376601657d17838a1b724fb8e2
    assemblyName: Unity.InputSystem.dll
    namespaceName: UnityEngine.InputSystem
    className: PlayerInput
  - hash:
      serializedVersion: 2
      Hash: 4b6df39eff434478d54da440811b39b6
    assemblyName: UnityEngine.XR.LegacyInputHelpers.dll
    namespaceName: UnityEngine.XR.LegacyInputHelpers
    className: ArmModel
  - hash:
      serializedVersion: 2
      Hash: 6d4b11bb7fb4425099fd0d1d2775efdf
    assemblyName: Unity.XR.Interaction.Toolkit.dll
    namespaceName: UnityEngine.XR.Interaction.Toolkit
    className: XRControllerRecorder
  - hash:
      serializedVersion: 2
      Hash: 6053d60f258883950e7e2cf83f24c3bc
    assemblyName: Unity.XR.PICO.dll
    namespaceName: 
    className: PXR_VstModelPosCheck
  - hash:
      serializedVersion: 2
      Hash: f1486b521d5e0947c7cc1cb73a789d6f
    assemblyName: Unity.XR.Interaction.Toolkit.dll
    namespaceName: UnityEngine.XR.Interaction.Toolkit.Transformers
    className: XRGeneralGrabTransformer
  - hash:
      serializedVersion: 2
      Hash: a506b72c53f139c380ebe7f3581a434b
    assemblyName: Unity.Timeline.dll
    namespaceName: UnityEngine.Timeline
    className: SignalAsset
  - hash:
      serializedVersion: 2
      Hash: 719a1390f9eb856966a1aaab72041631
    assemblyName: Unity.VisualScripting.Core.dll
    namespaceName: Unity.VisualScripting
    className: UnityOnScrollbarValueChangedMessageListener
  - hash:
      serializedVersion: 2
      Hash: f810935f76d28048a0a4be4479a0b8c9
    assemblyName: Cinemachine.dll
    namespaceName: Cinemachine
    className: CinemachineExternalCamera
  - hash:
      serializedVersion: 2
      Hash: bc5845ae80d1350ad1e97120e11c9ef7
    assemblyName: Unity.XR.Interaction.Toolkit.dll
    namespaceName: UnityEngine.XR.Interaction.Toolkit.Filtering
    className: XRPokeFilter
  - hash:
      serializedVersion: 2
      Hash: d31a15e5914874b76ab807188a41c969
    assemblyName: Unity.XR.PICO.dll
    namespaceName: Unity.XR.PXR.SecureMR
    className: PXR_SecureMRResult
  - hash:
      serializedVersion: 2
      Hash: d5dbba1e095217e88ae7faf82e4e2dbe
    assemblyName: UnityEngine.UI.dll
    namespaceName: UnityEngine.UI
    className: Selectable
  - hash:
      serializedVersion: 2
      Hash: ac51533c95676e7af6ba886a0ed52d0d
    assemblyName: Cinemachine.dll
    namespaceName: Cinemachine
    className: CinemachineFreeLook
  - hash:
      serializedVersion: 2
      Hash: 7a0b07b1892f2a5c214ff6d300f598c7
    assemblyName: Cinemachine.dll
    namespaceName: Cinemachine
    className: CinemachineTriggerAction
  - hash:
      serializedVersion: 2
      Hash: 650e41ede85c7130bc76d8e85aabd798
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: VRUIManager
  - hash:
      serializedVersion: 2
      Hash: bdddf1488100f2b05e78d1640001a071
    assemblyName: Unity.InputSystem.dll
    namespaceName: UnityEngine.InputSystem.HID.Editor
    className: HIDDescriptorWindow
  - hash:
      serializedVersion: 2
      Hash: 89fdfe9459ae7f55578ea8284fefe4ed
    assemblyName: Unity.InputSystem.dll
    namespaceName: UnityEngine.InputSystem.UI
    className: VirtualMouseInput
  - hash:
      serializedVersion: 2
      Hash: b6919327bcca55e4e56b20b361fa8ee4
    assemblyName: UnityEngine.UI.dll
    namespaceName: UnityEngine.UI
    className: Shadow
  - hash:
      serializedVersion: 2
      Hash: 40f1d61053fd46ebc6a06083fc0eafc0
    assemblyName: Unity.VisualScripting.Core.dll
    namespaceName: Unity.VisualScripting
    className: UnityOnCollisionStayMessageListener
  - hash:
      serializedVersion: 2
      Hash: 9037c280241a02c426d2c0fb97ad1af7
    assemblyName: Unity.XR.Interaction.Toolkit.dll
    namespaceName: UnityEngine.XR.Interaction.Toolkit
    className: ActionBasedController
  - hash:
      serializedVersion: 2
      Hash: 2ce604b10310758077019420042bcae6
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: VRAssemblyStepManager
  - hash:
      serializedVersion: 2
      Hash: d83bd8a6e9dc229920695c17cc16dc49
    assemblyName: Unity.XR.Interaction.Toolkit.dll
    namespaceName: UnityEngine.XR.Interaction.Toolkit.Locomotion.Teleportation
    className: FurthestTeleportationAnchorFilter
  - hash:
      serializedVersion: 2
      Hash: 52a1e0ebb97756d1ce55faa686f603ae
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: PICOActionBasedInputHandler
  - hash:
      serializedVersion: 2
      Hash: 8cad4214905686f750d512f476f4edf5
    assemblyName: Unity.XR.Interaction.Toolkit.dll
    namespaceName: UnityEngine.XR.Interaction.Toolkit.Transformers
    className: XRLegacyGrabTransformer
  - hash:
      serializedVersion: 2
      Hash: 5609918a60ea832f95015e31aebd9763
    assemblyName: Unity.VisualScripting.Core.dll
    namespaceName: Unity.VisualScripting
    className: UnityOnMouseExitMessageListener
  - hash:
      serializedVersion: 2
      Hash: 7063377460984e779b2f2b61d4a269d9
    assemblyName: Unity.XR.Interaction.Toolkit.dll
    namespaceName: UnityEngine.XR.Interaction.Toolkit
    className: XRInteractorLineVisual
  - hash:
      serializedVersion: 2
      Hash: bded33e439631f087d3612601816819f
    assemblyName: Cinemachine.dll
    namespaceName: 
    className: CinemachineCameraOffset
  - hash:
      serializedVersion: 2
      Hash: 34cc8064588d5255aa2fd540eb0453a8
    assemblyName: Pico.Spatializer.dll
    namespaceName: 
    className: PXR_Audio_Spatializer_MRSceneGeometryManager
  - hash:
      serializedVersion: 2
      Hash: e3fda0cfa6a434f0ecfa58c0991289be
    assemblyName: Unity.XR.Interaction.Toolkit.dll
    namespaceName: UnityEngine.XR.Interaction.Toolkit.UI
    className: LazyFollow
  - hash:
      serializedVersion: 2
      Hash: 02661e1bd0d013e91bac23202ba41249
    assemblyName: Unity.XR.PICO.dll
    namespaceName: 
    className: PXR_BodyTrackingDebugBlock
  - hash:
      serializedVersion: 2
      Hash: 6d8891a3aba9815d38620f0b85b12de4
    assemblyName: Unity.XR.Interaction.Toolkit.dll
    namespaceName: UnityEngine.XR.Interaction.Toolkit.AffordanceSystem.Theme.Primitives
    className: Vector2AffordanceThemeDatum
  - hash:
      serializedVersion: 2
      Hash: 862a6ec44e0093d7f53233bfab0819ca
    assemblyName: Unity.XR.PICO.dll
    namespaceName: Unity.XR.PXR
    className: PXR_SpatialMeshManager
  - hash:
      serializedVersion: 2
      Hash: cdc28f2d467859932ebec1f572f62175
    assemblyName: Unity.XR.Interaction.Toolkit.dll
    namespaceName: UnityEngine.XR.Interaction.Toolkit
    className: TeleportationArea
  - hash:
      serializedVersion: 2
      Hash: 192bcb00de0b6dc327b080f43a033895
    assemblyName: Cinemachine.dll
    namespaceName: Cinemachine
    className: CinemachineCollider
  - hash:
      serializedVersion: 2
      Hash: c8456ef0a330842582c175abd53e584e
    assemblyName: Unity.XR.PICO.dll
    namespaceName: Unity.XR.PXR.SecureMR
    className: PXR_SecureMRArithmeticComposeOperatorConfig
  - hash:
      serializedVersion: 2
      Hash: 4d957dc59211657e9748dd52e8578a50
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: VRUILayoutOptimizer
  - hash:
      serializedVersion: 2
      Hash: 38e7a5b163c4394727bc53d6d2090f88
    assemblyName: Cinemachine.dll
    namespaceName: Cinemachine
    className: CinemachineBlenderSettings
  - hash:
      serializedVersion: 2
      Hash: fea16a5216853087ca51be273ef852b4
    assemblyName: Unity.XR.Interaction.Toolkit.dll
    namespaceName: UnityEngine.XR.Interaction.Toolkit
    className: XRGazeAssistance
  - hash:
      serializedVersion: 2
      Hash: 94bb1bb4c329770d0e1c3ab7d75fcce6
    assemblyName: Unity.XR.Interaction.Toolkit.dll
    namespaceName: UnityEngine.XR.Interaction.Toolkit.AffordanceSystem.Theme.Primitives
    className: ColorAffordanceThemeDatum
  - hash:
      serializedVersion: 2
      Hash: 4763159aa2d72ed796c2398564a4d18d
    assemblyName: Unity.VisualScripting.Core.dll
    namespaceName: Unity.VisualScripting
    className: UnityOnTriggerEnterMessageListener
  - hash:
      serializedVersion: 2
      Hash: 9605a0475669ece0189f675c4d408e5f
    assemblyName: Unity.TextMeshPro.dll
    namespaceName: TMPro
    className: TMP_StyleSheet
  - hash:
      serializedVersion: 2
      Hash: 53094487dc1668e88601b408ef99eb93
    assemblyName: Unity.VisualScripting.Core.dll
    namespaceName: Unity.VisualScripting
    className: UnityOnDropMessageListener
  - hash:
      serializedVersion: 2
      Hash: ********************************
    assemblyName: Unity.VisualScripting.Core.dll
    namespaceName: Unity.VisualScripting
    className: UnityOnPointerUpMessageListener
  - hash:
      serializedVersion: 2
      Hash: ce7d8e656ace251f308b0d622ae254b1
    assemblyName: Unity.InputSystem.dll
    namespaceName: UnityEngine.InputSystem.Editor
    className: AdvancedDropdownWindow
  - hash:
      serializedVersion: 2
      Hash: baf218a9799098962f3ddb5d79364282
    assemblyName: UnityEngine.TestRunner.dll
    namespaceName: UnityEngine.TestTools.TestRunner
    className: PlaymodeTestsController
  - hash:
      serializedVersion: 2
      Hash: 7e1ca2b5e4b4a944c49675ecc44d136e
    assemblyName: Unity.VisualScripting.Core.dll
    namespaceName: Unity.VisualScripting
    className: UnityOnJointBreakMessageListener
  - hash:
      serializedVersion: 2
      Hash: 8d9b8b35cd143c74c6834eff21a0290d
    assemblyName: Unity.XR.Interaction.Toolkit.dll
    namespaceName: UnityEngine.XR.Interaction.Toolkit
    className: TeleportationAnchor
  - hash:
      serializedVersion: 2
      Hash: b8c62e6fbbd49b0adf47ca1dfd4b7754
    assemblyName: UnityEngine.UI.dll
    namespaceName: UnityEngine.UI
    className: RawImage
  - hash:
      serializedVersion: 2
      Hash: 9c9e79f36cc0f8752c6628c1b9e3300e
    assemblyName: Unity.XR.Interaction.Toolkit.dll
    namespaceName: UnityEngine.XR.Interaction.Toolkit.AffordanceSystem.Receiver.Primitives
    className: Vector4AffordanceReceiver
  - hash:
      serializedVersion: 2
      Hash: 827f00566266cb473f2004c02c387cc6
    assemblyName: Unity.XR.Interaction.Toolkit.dll
    namespaceName: UnityEngine.XR.Interaction.Toolkit.AffordanceSystem.Theme.Audio
    className: AudioAffordanceThemeDatum
  - hash:
      serializedVersion: 2
      Hash: e2aba63479fd5f4f6e3399342b961790
    assemblyName: PICO.TobSupport.dll
    namespaceName: 
    className: EnterpriseAPI
  - hash:
      serializedVersion: 2
      Hash: 818b1bab1829ebfe1869af48831f3c19
    assemblyName: Unity.XR.Interaction.Toolkit.dll
    namespaceName: UnityEngine.XR.Interaction.Toolkit
    className: ClimbSettingsDatum
  - hash:
      serializedVersion: 2
      Hash: 28794a0f734c8d37edb21133a9230f31
    assemblyName: Unity.XR.Interaction.Toolkit.dll
    namespaceName: UnityEngine.XR.Interaction.Toolkit.AffordanceSystem.Receiver.UI
    className: ImageColorAffordanceReceiver
  - hash:
      serializedVersion: 2
      Hash: 880566a7a430c83f4c04f216ed47d427
    assemblyName: Unity.XR.Interaction.Toolkit.dll
    namespaceName: UnityEngine.XR.Interaction.Toolkit
    className: XRTintInteractableVisual
  - hash:
      serializedVersion: 2
      Hash: 09ff78e01cc5b3dae99a7ab8c5582f50
    assemblyName: Unity.XR.PICO.dll
    namespaceName: Unity.XR.PXR.SecureMR
    className: PXR_SecureMRPipeline
  - hash:
      serializedVersion: 2
      Hash: d6eb7a6b5afb9d565f4167189acbe377
    assemblyName: Unity.XR.PICO.dll
    namespaceName: Unity.XR.PXR.SecureMR
    className: PXR_SecureMRByteTensorData
  - hash:
      serializedVersion: 2
      Hash: a7ba16712113de6fafba15234af7cfa0
    assemblyName: Unity.XR.Interaction.Toolkit.dll
    namespaceName: UnityEngine.XR.Interaction.Toolkit
    className: TunnelingVignetteController
  - hash:
      serializedVersion: 2
      Hash: d00f0bf296e194c544caad694f0c48bf
    assemblyName: Unity.XR.Interaction.Toolkit.dll
    namespaceName: UnityEngine.XR.Interaction.Toolkit
    className: XRRig
  - hash:
      serializedVersion: 2
      Hash: eec43af1c1f674f737c03b0b09f4d0f1
    assemblyName: Unity.XR.PICO.dll
    namespaceName: Unity.XR.PXR
    className: PXR_OverLay
  - hash:
      serializedVersion: 2
      Hash: 3a74ff97f36cb7570e57460d53dcf9c2
    assemblyName: Unity.VisualScripting.Core.dll
    namespaceName: Unity.VisualScripting
    className: UnityOnBecameVisibleMessageListener
  - hash:
      serializedVersion: 2
      Hash: 0b34785db0e5aefabfbd4d21baf9a953
    assemblyName: Unity.XR.PICO.dll
    namespaceName: Unity.XR.PXR.SecureMR
    className: PXR_SecureMRUpdateGltfOperatorConfig
  - hash:
      serializedVersion: 2
      Hash: fff6dfc5ee5cb748508178769455fe0b
    assemblyName: Unity.XR.Interaction.Toolkit.dll
    namespaceName: UnityEngine.XR.Interaction.Toolkit.AffordanceSystem.Receiver.Rendering
    className: FloatMaterialPropertyAffordanceReceiver
  - hash:
      serializedVersion: 2
      Hash: 885d0aeccbdf07c0572dd81626935d8b
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: PICOInputDebugger
  - hash:
      serializedVersion: 2
      Hash: d7acbd6ad1b391d94a914128f69d9325
    assemblyName: Cinemachine.dll
    namespaceName: Cinemachine
    className: CinemachineVirtualCamera
  - hash:
      serializedVersion: 2
      Hash: 0e8fac5f1526295ab99dabde6d312af5
    assemblyName: Unity.XR.Interaction.Toolkit.Samples.StarterAssets.dll
    namespaceName: UnityEngine.XR.Interaction.Toolkit.Samples.StarterAssets
    className: DynamicMoveProvider
  - hash:
      serializedVersion: 2
      Hash: 66721fd1cb2e57fd1d41cb00d8703c17
    assemblyName: Unity.XR.PICO.dll
    namespaceName: Unity.XR.PXR
    className: PXR_LateLatching
  - hash:
      serializedVersion: 2
      Hash: ef2a760dc8c0bb333363120518871762
    assemblyName: UnityEngine.UI.dll
    namespaceName: UnityEngine.EventSystems
    className: EventSystem
  - hash:
      serializedVersion: 2
      Hash: 46f8815aaefa696d509049c348ad6805
    assemblyName: Unity.TextMeshPro.dll
    namespaceName: TMPro
    className: TMP_Dropdown
  - hash:
      serializedVersion: 2
      Hash: c3220f7afd647b11dae09800190b3898
    assemblyName: Unity.Timeline.dll
    namespaceName: UnityEngine.Timeline
    className: AudioTrack
  - hash:
      serializedVersion: 2
      Hash: 563a5b2063761ebcd84c81cc7071eec7
    assemblyName: Unity.XR.Interaction.Toolkit.Samples.StarterAssets.dll
    namespaceName: UnityEngine.XR.Interaction.Toolkit.Samples.StarterAssets
    className: ObjectSpawner
  - hash:
      serializedVersion: 2
      Hash: 3339bfdb463eedceb9e4bfd5fbdd7af6
    assemblyName: Unity.XR.PICO.dll
    namespaceName: Unity.XR.PXR.SecureMR
    className: PXR_SecureMRLocalTensorReference
  - hash:
      serializedVersion: 2
      Hash: ef2fb3d6aadceb1e1a2e82f9954ae812
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: VRSimulator
  - hash:
      serializedVersion: 2
      Hash: 201b3e41302e8bd8463c6af1573b2adb
    assemblyName: Unity.VisualScripting.Core.dll
    namespaceName: Unity.VisualScripting
    className: UnityOnCollisionEnterMessageListener
  - hash:
      serializedVersion: 2
      Hash: a152f4bbfcc1c203222264e9469e6cd6
    assemblyName: Unity.Timeline.dll
    namespaceName: UnityEngine.Timeline
    className: TimelineAsset
  - hash:
      serializedVersion: 2
      Hash: 6554088abf80322f19a20856543c0003
    assemblyName: Unity.VisualScripting.Core.dll
    namespaceName: Unity.VisualScripting
    className: UnityMessageListener
  - hash:
      serializedVersion: 2
      Hash: 3d02ff497fa903158a32fa89adf3810b
    assemblyName: Cinemachine.dll
    namespaceName: Cinemachine
    className: CinemachinePipeline
  - hash:
      serializedVersion: 2
      Hash: fd5618094d1ec353bc50abe24baaba92
    assemblyName: Cinemachine.dll
    namespaceName: Cinemachine
    className: CinemachineHardLookAt
  - hash:
      serializedVersion: 2
      Hash: c3632937d58e47b256c6c90c1fb55119
    assemblyName: Unity.VisualScripting.Core.dll
    namespaceName: Unity.VisualScripting
    className: CoroutineRunner
  - hash:
      serializedVersion: 2
      Hash: 0927d4da9cf0b74300ac1ccf7f0265a4
    assemblyName: Unity.XR.Interaction.Toolkit.dll
    namespaceName: UnityEngine.XR.Interaction.Toolkit.Inputs.Simulation.Hands
    className: HandExpressionCapture
  - hash:
      serializedVersion: 2
      Hash: 84c295144a2fce36bf4afcb7fb81265f
    assemblyName: UnityEngine.TestRunner.dll
    namespaceName: UnityEngine.TestRunner.Utils
    className: TestRunCallbackListener
  - hash:
      serializedVersion: 2
      Hash: 3f2b0cb7bd125083fbda1c408209cc7c
    assemblyName: Unity.XR.Interaction.Toolkit.Samples.StarterAssets.dll
    namespaceName: UnityEngine.XR.Interaction.Toolkit.Samples.StarterAssets
    className: IncrementUIText
  - hash:
      serializedVersion: 2
      Hash: df6eab954ec864d23f72bf3587a64b76
    assemblyName: UnityEngine.UI.dll
    namespaceName: UnityEngine.UI
    className: GraphicRaycaster
  - hash:
      serializedVersion: 2
      Hash: 497452340d27fb2867912262d0f77de8
    assemblyName: Unity.XR.Interaction.Toolkit.dll
    namespaceName: UnityEngine.XR.Interaction.Toolkit
    className: ClimbProvider
  - hash:
      serializedVersion: 2
      Hash: daae5c475612329bf4998ee97a403991
    assemblyName: Unity.XR.Interaction.Toolkit.dll
    namespaceName: UnityEngine.XR.Interaction.Toolkit.UI.BodyUI
    className: HandMenu
  - hash:
      serializedVersion: 2
      Hash: 9e8898d1705d409ec4c988108072c8af
    assemblyName: Unity.TextMeshPro.dll
    namespaceName: TMPro
    className: TMP_SelectionCaret
  - hash:
      serializedVersion: 2
      Hash: c0d3fa4f13a5beb4477fcb6229facd7d
    assemblyName: Unity.XR.CoreUtils.dll
    namespaceName: Unity.XR.CoreUtils
    className: OnDestroyNotifier
  - hash:
      serializedVersion: 2
      Hash: be4876097885d91b8fc3deaa8b15b573
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: PICODeploymentManager
  - hash:
      serializedVersion: 2
      Hash: 68d5dec1c60c68c2dfc1f02723e0b735
    assemblyName: UnityEngine.UI.dll
    namespaceName: UnityEngine.UI
    className: InputField
  - hash:
      serializedVersion: 2
      Hash: bcb9b7a93c09c207eb313c02affe0d60
    assemblyName: Unity.TextMeshPro.dll
    namespaceName: TMPro
    className: TMP_SpriteAnimator
  - hash:
      serializedVersion: 2
      Hash: 642a4fb672e989357aca2d30d533daa7
    assemblyName: Unity.XR.PICO.dll
    namespaceName: Unity.XR.PXR.SecureMR
    className: PXR_SecureMRGltfMetadata
  - hash:
      serializedVersion: 2
      Hash: a401d62e0a7b4e119d20fb7f6723eba8
    assemblyName: Unity.VisualScripting.Core.dll
    namespaceName: Unity.VisualScripting
    className: UnityOnButtonClickMessageListener
  - hash:
      serializedVersion: 2
      Hash: 8c5ab079dbc06dd660a4f3f0f25c099e
    assemblyName: UnityEngine.TestRunner.dll
    namespaceName: UnityEngine.TestTools.TestRunner.Callbacks
    className: TestResultRendererCallback
  - hash:
      serializedVersion: 2
      Hash: 803e1991f819d19d6fcc5728dedcf475
    assemblyName: Cinemachine.dll
    namespaceName: Cinemachine
    className: CinemachineInputProvider
  - hash:
      serializedVersion: 2
      Hash: 96bab05c5a260c4416a3bb0fdd19fc3d
    assemblyName: Unity.XR.Interaction.Toolkit.dll
    namespaceName: UnityEngine.XR.Interaction.Toolkit.AffordanceSystem.Theme.Primitives
    className: Vector4AffordanceThemeDatum
  - hash:
      serializedVersion: 2
      Hash: ac0995427965ff4d5e37afde8449a2d7
    assemblyName: Unity.VisualScripting.Core.dll
    namespaceName: Unity.VisualScripting
    className: UnityOnInputFieldEndEditMessageListener
  - hash:
      serializedVersion: 2
      Hash: d2ab0b299147f65834573ffd942ba546
    assemblyName: Cinemachine.dll
    namespaceName: 
    className: CinemachineTrack
  - hash:
      serializedVersion: 2
      Hash: 18489aba93650f0e6326bf36f0c1b922
    assemblyName: Cinemachine.dll
    namespaceName: Cinemachine
    className: Cinemachine3rdPersonFollow
  - hash:
      serializedVersion: 2
      Hash: afae65d184ddbfcd51cef5a1d2e688dd
    assemblyName: Unity.XR.Interaction.Toolkit.dll
    namespaceName: UnityEngine.XR.Interaction.Toolkit.AffordanceSystem.Receiver.Primitives
    className: QuaternionAffordanceReceiver
  - hash:
      serializedVersion: 2
      Hash: d724b6e49da746e4fd6dfdcd6b0798ec
    assemblyName: Unity.VisualScripting.Core.dll
    namespaceName: Unity.VisualScripting
    className: UnityOnDropdownValueChangedMessageListener
  - hash:
      serializedVersion: 2
      Hash: 8454adf60332e0241844a9b48fe31f5b
    assemblyName: Unity.XR.PICO.dll
    namespaceName: Unity.XR.PXR
    className: PXR_ControllerAnimator
  - hash:
      serializedVersion: 2
      Hash: 9af49bcd875819e7ad6b8b8aa52801fd
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: PICOInputActionAdapter
  - hash:
      serializedVersion: 2
      Hash: cb9a815224f0f2005346ceaa3ad15318
    assemblyName: Unity.InputSystem.dll
    namespaceName: UnityEngine.InputSystem.OnScreen
    className: OnScreenButton
  - hash:
      serializedVersion: 2
      Hash: 8e198f3d60c90ecfd7f1670d6475a59d
    assemblyName: Unity.XR.Interaction.Toolkit.dll
    namespaceName: UnityEngine.XR.Interaction.Toolkit
    className: ActionBasedSnapTurnProvider
  - hash:
      serializedVersion: 2
      Hash: 1d5564e8e1874768c1eafe50e32d4962
    assemblyName: Unity.XR.Interaction.Toolkit.dll
    namespaceName: UnityEngine.XR.Interaction.Toolkit.UI
    className: CanvasOptimizer
  - hash:
      serializedVersion: 2
      Hash: 367774d3c69690fa4fb79fb1d07b0b48
    assemblyName: Unity.XR.PICO.dll
    namespaceName: Unity.XR.PXR
    className: PXR_ObjImporter
  - hash:
      serializedVersion: 2
      Hash: d99fe83053fe8d076746d187230eb2f3
    assemblyName: Pico.Spatializer.Example.dll
    namespaceName: 
    className: plastic_fps_controller
  - hash:
      serializedVersion: 2
      Hash: 4a3351e209ecf0eff6ab268e49d7f341
    assemblyName: UnityEngine.UI.dll
    namespaceName: UnityEngine.UI
    className: ToggleGroup
  - hash:
      serializedVersion: 2
      Hash: d3b117887d664b796e08789f8a61806f
    assemblyName: Cinemachine.dll
    namespaceName: Cinemachine
    className: CinemachineMixingCamera
  - hash:
      serializedVersion: 2
      Hash: 8165edc0ea71078aadd02b6c3ce945a1
    assemblyName: UnityEngine.UI.dll
    namespaceName: UnityEngine.UI
    className: Button
  - hash:
      serializedVersion: 2
      Hash: 1f1c70ad24ff984f856e15f0842e6d15
    assemblyName: Unity.XR.Interaction.Toolkit.dll
    namespaceName: UnityEngine.XR.Interaction.Toolkit.Filtering
    className: PokeThresholdDatum
  - hash:
      serializedVersion: 2
      Hash: 3fcd305d0f6ffcaa8f02a337ae18f9a6
    assemblyName: Unity.XR.PICO.dll
    namespaceName: Unity.XR.PXR
    className: PXR_HandPoseConfig
  - hash:
      serializedVersion: 2
      Hash: 6548e95c54760e43f43d68d5e9dc458b
    assemblyName: Unity.XR.Interaction.Toolkit.Samples.DeviceSimulator.dll
    namespaceName: UnityEngine.XR.Interaction.Toolkit.Samples.DeviceSimulator
    className: XRDeviceSimulatorControllerUI
  - hash:
      serializedVersion: 2
      Hash: 7424a7435a90d2df848d902cdaa1f86f
    assemblyName: Unity.XR.Interaction.Toolkit.dll
    namespaceName: UnityEngine.XR.Interaction.Toolkit.Locomotion.Teleportation
    className: GazeTeleportationAnchorFilter
  - hash:
      serializedVersion: 2
      Hash: e767c4aa5ddc7228b6ca83da9aa0fba2
    assemblyName: Unity.VisualScripting.Core.dll
    namespaceName: Unity.VisualScripting
    className: UnityOnInputFieldValueChangedMessageListener
  - hash:
      serializedVersion: 2
      Hash: 81bb2f606fceb858467b5a01d57faa6e
    assemblyName: Unity.VisualScripting.Core.dll
    namespaceName: Unity.VisualScripting
    className: UnityOnMouseDownMessageListener
  - hash:
      serializedVersion: 2
      Hash: 411a04d1ee8748824e224a553948367e
    assemblyName: Unity.VisualScripting.Core.dll
    namespaceName: Unity.VisualScripting
    className: UnityOnMouseDragMessageListener
  - hash:
      serializedVersion: 2
      Hash: 8f26d0b40f2450da2c8a82571d794c80
    assemblyName: Unity.XR.Interaction.Toolkit.dll
    namespaceName: UnityEngine.XR.Interaction.Toolkit.Inputs
    className: XRTransformStabilizer
  - hash:
      serializedVersion: 2
      Hash: f8b223f640cb8dc146d2777cc21877e2
    assemblyName: Unity.VisualScripting.Core.dll
    namespaceName: Unity.VisualScripting
    className: UnityOnScrollMessageListener
  - hash:
      serializedVersion: 2
      Hash: 68587ea39bb503ea8d65053e93c31d06
    assemblyName: UnityEngine.UI.dll
    namespaceName: UnityEngine.UI
    className: ContentSizeFitter
  - hash:
      serializedVersion: 2
      Hash: dbf1dfa253e20667829f0817c5b6d684
    assemblyName: Cinemachine.dll
    namespaceName: Cinemachine
    className: CinemachineSmoothPath
  - hash:
      serializedVersion: 2
      Hash: 94f6799173dc80f24f5b18ae8e50d71a
    assemblyName: Unity.XR.Interaction.Toolkit.dll
    namespaceName: UnityEngine.XR.Interaction.Toolkit.AffordanceSystem.Receiver.Rendering
    className: ColorGradientLineRendererAffordanceReceiver
  - hash:
      serializedVersion: 2
      Hash: c6d7eebeace00b1615341882742e98af
    assemblyName: Unity.InputSystem.dll
    namespaceName: UnityEngine.InputSystem.Editor
    className: InputActionImporterEditor
  - hash:
      serializedVersion: 2
      Hash: 1d9e07bb8536f382c86a0ef0c58f5e7f
    assemblyName: Unity.VisualScripting.Core.dll
    namespaceName: Unity.VisualScripting
    className: VariablesAsset
  - hash:
      serializedVersion: 2
      Hash: 041d76d2f91c7367898c2a5484cf6342
    assemblyName: Unity.XR.Interaction.Toolkit.Samples.DeviceSimulator.dll
    namespaceName: UnityEngine.XR.Interaction.Toolkit.Samples.DeviceSimulator
    className: XRDeviceSimulatorUI
  - hash:
      serializedVersion: 2
      Hash: ebd0409eb7f02c30d237d55591f7b5a2
    assemblyName: Cinemachine.dll
    namespaceName: Cinemachine
    className: CinemachinePixelPerfect
  - hash:
      serializedVersion: 2
      Hash: 0531d5fbddefc8de09b7b2699d523ee8
    assemblyName: Unity.VisualScripting.Flow.dll
    namespaceName: Unity.VisualScripting
    className: ScriptMachine
  - hash:
      serializedVersion: 2
      Hash: 59a3597e8b443837c9d7e56154cecead
    assemblyName: Unity.VisualScripting.Core.dll
    namespaceName: Unity.VisualScripting
    className: UnityOnSelectMessageListener
  - hash:
      serializedVersion: 2
      Hash: 365efbd55f5b8292511632406ff34fe5
    assemblyName: Unity.XR.Interaction.Toolkit.dll
    namespaceName: UnityEngine.XR.Interaction.Toolkit.AffordanceSystem.Rendering
    className: MaterialInstanceHelper
  - hash:
      serializedVersion: 2
      Hash: 01699fce5405367d8def9d5611d9763e
    assemblyName: Unity.XR.Interaction.Toolkit.dll
    namespaceName: UnityEngine.XR.Interaction.Toolkit
    className: XRInteractionGroup
  - hash:
      serializedVersion: 2
      Hash: e3a28a530f5c48f5ea254c16ab5d494b
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: VRUserGuidance
  - hash:
      serializedVersion: 2
      Hash: bd4016a2045f75e945497f07c5b80503
    assemblyName: UnityEngine.TestRunner.dll
    namespaceName: UnityEngine.TestTools.TestRunner.Callbacks
    className: PlayerQuitHandler
  - hash:
      serializedVersion: 2
      Hash: 1ac168f22bc4974ce29d75eafeb7a7ad
    assemblyName: Unity.XR.Interaction.Toolkit.dll
    namespaceName: UnityEngine.XR.Interaction.Toolkit
    className: ClimbInteractable
  - hash:
      serializedVersion: 2
      Hash: f87a82bdc1582c5f70b70ca85233d7f2
    assemblyName: UnityEngine.UI.dll
    namespaceName: UnityEngine.EventSystems
    className: BaseInput
  - hash:
      serializedVersion: 2
      Hash: 886a856cbed85afa3bafe7459143ec29
    assemblyName: Unity.XR.Interaction.Toolkit.dll
    namespaceName: UnityEngine.XR.Interaction.Toolkit
    className: XRGrabInteractable
  - hash:
      serializedVersion: 2
      Hash: ff68aa0368d26794ea38616a2495e288
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: PICOVRInputAdapter
  - hash:
      serializedVersion: 2
      Hash: b1b01fc5da4724193c1231ec20c5fbae
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: VRCameraDataRecorder
  - hash:
      serializedVersion: 2
      Hash: f1de51a4093c4ec2cec60bd8454cec4e
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: AssemblyAnimation
  - hash:
      serializedVersion: 2
      Hash: 65784ab5ae832e00230735e19000957b
    assemblyName: Unity.VisualScripting.Core.dll
    namespaceName: Unity.VisualScripting
    className: UnityOnControllerColliderHitMessageListener
  - hash:
      serializedVersion: 2
      Hash: 4fe30b079bc8419c58089caa901d9184
    assemblyName: Cinemachine.dll
    namespaceName: Cinemachine
    className: CinemachineFramingTransposer
  - hash:
      serializedVersion: 2
      Hash: c42e96e7879bb9ab4ed900bdbe25d0db
    assemblyName: Unity.XR.Interaction.Toolkit.dll
    namespaceName: UnityEngine.XR.Interaction.Toolkit.Filtering
    className: XRTargetFilter
  - hash:
      serializedVersion: 2
      Hash: e81f4e25ee0d18ab190dad466bd493ec
    assemblyName: Unity.XR.Management.dll
    namespaceName: UnityEngine.XR.Management
    className: XRGeneralSettings
  - hash:
      serializedVersion: 2
      Hash: b309f0b8b517f50a2601fbb7a2f9f705
    assemblyName: Unity.TextMeshPro.dll
    namespaceName: TMPro
    className: TextMeshPro
  - hash:
      serializedVersion: 2
      Hash: 769abea9aa79f49a2ee1a4facd0a61d5
    assemblyName: Unity.Timeline.dll
    namespaceName: UnityEngine.Timeline
    className: AnimationTrack
  - hash:
      serializedVersion: 2
      Hash: 4c4f99c547c151401cfb8934b52ababe
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: VRAssemblyManager
  - hash:
      serializedVersion: 2
      Hash: d7bd913ee74a4cad73a7c048d9783cd3
    assemblyName: Unity.VisualScripting.Core.dll
    namespaceName: Unity.VisualScripting
    className: UnityOnCollisionExit2DMessageListener
  - hash:
      serializedVersion: 2
      Hash: 1c96433c26d92368dad4ad7bc21f4c01
    assemblyName: Unity.XR.PICO.dll
    namespaceName: 
    className: PXR_ControllerPower
  - hash:
      serializedVersion: 2
      Hash: d153969766bde3274b187dfbe35e5cff
    assemblyName: Unity.XR.PICO.dll
    namespaceName: Unity.XR.PXR
    className: PXR_HandPose
  - hash:
      serializedVersion: 2
      Hash: be89a499e95fa05b44ba72ccc40b2b4a
    assemblyName: Unity.XR.Management.dll
    namespaceName: UnityEngine.XR.Management
    className: XRManagerSettings
  - hash:
      serializedVersion: 2
      Hash: 661e49e812fc8ba3a1de201748babe7a
    assemblyName: Unity.XR.Interaction.Toolkit.dll
    namespaceName: UnityEngine.XR.Interaction.Toolkit
    className: XRGazeInteractor
  - hash:
      serializedVersion: 2
      Hash: 6e0d742dbcf9dbf7ecc6a828d57d34f2
    assemblyName: Unity.XR.PICO.dll
    namespaceName: 
    className: PXR_BodyTrackingBlock
  - hash:
      serializedVersion: 2
      Hash: 6eaf6b878ffd372071083a531fee0b01
    assemblyName: Pico.Spatializer.dll
    namespaceName: 
    className: PXR_Audio_Spatializer_Context
  - hash:
      serializedVersion: 2
      Hash: 5d1b5a5aafd0c5cb3dc14985dc978d55
    assemblyName: Unity.VisualScripting.Flow.dll
    namespaceName: Unity.VisualScripting
    className: ScriptGraphAsset
  - hash:
      serializedVersion: 2
      Hash: 4ab5a4254613800a9af0da273d7f2656
    assemblyName: Unity.XR.CoreUtils.dll
    namespaceName: Unity.XR.CoreUtils.Datums
    className: StringDatum
  - hash:
      serializedVersion: 2
      Hash: 0cc751b147f1b65913842c59ecb3cc76
    assemblyName: Unity.XR.Interaction.Toolkit.dll
    namespaceName: UnityEngine.XR.Interaction.Toolkit
    className: InteractionLayerSettings
  - hash:
      serializedVersion: 2
      Hash: 2c7335239058ee7106af5c639248d4a2
    assemblyName: Unity.InputSystem.dll
    namespaceName: UnityEngine.InputSystem
    className: InputActionAsset
  - hash:
      serializedVersion: 2
      Hash: 8b7f2717b72ff842b337444e7eded496
    assemblyName: Unity.XR.PICO.dll
    namespaceName: Unity.XR.PXR.SecureMR
    className: PXR_SecureMRRenderTextOperatorConfig
  - hash:
      serializedVersion: 2
      Hash: 3c3e5980c88a14a86741564a7fd08aaa
    assemblyName: Unity.Timeline.dll
    namespaceName: UnityEngine.Timeline
    className: ActivationTrack
  - hash:
      serializedVersion: 2
      Hash: 614c1a43e0f3bbf140358b35cf9aa13a
    assemblyName: Cinemachine.dll
    namespaceName: Cinemachine
    className: CinemachineIndependentImpulseListener
  - hash:
      serializedVersion: 2
      Hash: d28b52a863ba5e1504776a993e061dd2
    assemblyName: Unity.VisualScripting.Core.dll
    namespaceName: Unity.VisualScripting
    className: UnityOnPointerExitMessageListener
  - hash:
      serializedVersion: 2
      Hash: 8ba697dafe4b5218a72b203422196057
    assemblyName: Unity.XR.CoreUtils.dll
    namespaceName: Unity.XR.CoreUtils
    className: XROrigin
  - hash:
      serializedVersion: 2
      Hash: 5f98caf0c7f63be67cb8d30140a9efef
    assemblyName: Unity.XR.PICO.dll
    namespaceName: 
    className: PXR_PermissionRequest
  - hash:
      serializedVersion: 2
      Hash: 45f7d659b892252ea52babfa320f537a
    assemblyName: Unity.XR.Interaction.Toolkit.Samples.StarterAssets.dll
    namespaceName: UnityEngine.XR.Interaction.Toolkit.Samples.StarterAssets
    className: DestroySelf
  - hash:
      serializedVersion: 2
      Hash: 073e3477c869fcfbff34ca4fa9237a7c
    assemblyName: Unity.XR.Interaction.Toolkit.dll
    namespaceName: UnityEngine.XR.Interaction.Toolkit
    className: TeleportationProvider
  - hash:
      serializedVersion: 2
      Hash: 4b9ef41da59c739e39a0e1cd34f1ddd8
    assemblyName: Unity.InputSystem.dll
    namespaceName: UnityEngine.InputSystem.Editor
    className: InputActionDebuggerWindow
  - hash:
      serializedVersion: 2
      Hash: a9fa5dd85789f559c89090b05e2577c3
    assemblyName: Unity.XR.Interaction.Toolkit.dll
    namespaceName: UnityEngine.XR.Interaction.Toolkit
    className: XRInteractionManager
  - hash:
      serializedVersion: 2
      Hash: 882a849f2f5653efce2b3972bf716e54
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: VRAssemblyInputManager
  - hash:
      serializedVersion: 2
      Hash: f27dbaaa7f025997ec9fca2ef101f9cc
    assemblyName: Unity.InputSystem.dll
    namespaceName: UnityEngine.InputSystem.Editor
    className: InputDebuggerWindow
  - hash:
      serializedVersion: 2
      Hash: 81edb5e973a40cf9ff08f4c26f68a9a0
    assemblyName: Cinemachine.dll
    namespaceName: Cinemachine
    className: CinemachineGroupComposer
  - hash:
      serializedVersion: 2
      Hash: 6f4c38ee1b06ec5021a35b43d6edd1fd
    assemblyName: Unity.InputSystem.dll
    namespaceName: UnityEngine.InputSystem.Editor
    className: InputStateWindow
  - hash:
      serializedVersion: 2
      Hash: 5db90ba1538b8e285d9a4cf769c2f07e
    assemblyName: Unity.InputSystem.dll
    namespaceName: UnityEngine.InputSystem.UI
    className: TrackedDeviceRaycaster
  - hash:
      serializedVersion: 2
      Hash: 93ab30db31a73803d338db45913c7bd1
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: VRAssemblyPositioner
  - hash:
      serializedVersion: 2
      Hash: 1824a9130d49df16abbf95d88546f25c
    assemblyName: Unity.VisualScripting.Core.dll
    namespaceName: Unity.VisualScripting
    className: UnityOnCollisionStay2DMessageListener
  - hash:
      serializedVersion: 2
      Hash: 21f4f1fec12583058b4c5843abeefdbb
    assemblyName: Unity.TextMeshPro.dll
    namespaceName: TMPro
    className: TMP_ColorGradient
  - hash:
      serializedVersion: 2
      Hash: 6a9516770b070951680a95390b9098a5
    assemblyName: Unity.Timeline.dll
    namespaceName: UnityEngine.Timeline
    className: SignalReceiver
  - hash:
      serializedVersion: 2
      Hash: 2547c13d8b5014ad6916e38132de0f5e
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: VRUIInteractor
  - hash:
      serializedVersion: 2
      Hash: 89003befdf0a1cfbd8100c7759ff3201
    assemblyName: Unity.VisualScripting.Core.dll
    namespaceName: Unity.VisualScripting
    className: VariablesSaver
  - hash:
      serializedVersion: 2
      Hash: 6fea02e0a8b3cb3b15440b3ff3fce29b
    assemblyName: UnityEngine.UI.dll
    namespaceName: UnityEngine.EventSystems
    className: PhysicsRaycaster
  - hash:
      serializedVersion: 2
      Hash: 8088a6e6266ae3e4922a5d6c505f6a7e
    assemblyName: Cinemachine.dll
    namespaceName: 
    className: CinemachineTouchInputMapper
  - hash:
      serializedVersion: 2
      Hash: 621258cbf131e999213abbde65c03985
    assemblyName: Unity.Timeline.dll
    namespaceName: UnityEngine.Timeline
    className: ControlPlayableAsset
  - hash:
      serializedVersion: 2
      Hash: dc3d5271f3271de34751f74fb838e919
    assemblyName: Cinemachine.dll
    namespaceName: Cinemachine
    className: Cinemachine3rdPersonAim
  - hash:
      serializedVersion: 2
      Hash: 03116c959005bf565efe4a31f8be265e
    assemblyName: UnityEngine.UI.dll
    namespaceName: UnityEngine.UI
    className: RectMask2D
  - hash:
      serializedVersion: 2
      Hash: 9b20bb37fe547a38e67e865c7074ed8f
    assemblyName: Unity.TextMeshPro.dll
    namespaceName: TMPro
    className: TMP_Settings
  - hash:
      serializedVersion: 2
      Hash: cb0af9d821296cd6914b3b57ceb35dac
    assemblyName: Unity.VisualScripting.Core.dll
    namespaceName: Unity.VisualScripting
    className: UnityOnJointBreak2DMessageListener
  - hash:
      serializedVersion: 2
      Hash: cc436a5c51759d2dc0114bc2ab5a257e
    assemblyName: Unity.XR.Interaction.Toolkit.dll
    namespaceName: UnityEngine.XR.Interaction.Toolkit
    className: LocomotionSystem
  - hash:
      serializedVersion: 2
      Hash: b187c202ddcafd4c52781696e213aad2
    assemblyName: Cinemachine.dll
    namespaceName: Cinemachine
    className: CinemachineHardLockToTarget
  - hash:
      serializedVersion: 2
      Hash: 76b17bf564fa9f8bd6ab3d1e431f9667
    assemblyName: Unity.VisualScripting.Core.dll
    namespaceName: Unity.VisualScripting
    className: DictionaryAsset
  - hash:
      serializedVersion: 2
      Hash: 5d77ddbaa8a9ea3eb8abf85398c27d56
    assemblyName: Cinemachine.dll
    namespaceName: Cinemachine
    className: GroupWeightManipulator
  - hash:
      serializedVersion: 2
      Hash: b149d2e53fef802559ade40668c4758b
    assemblyName: Unity.XR.Interaction.Toolkit.dll
    namespaceName: UnityEngine.XR.Interaction.Toolkit
    className: DeviceBasedContinuousMoveProvider
  - hash:
      serializedVersion: 2
      Hash: 1808bedf05234f59aad67504dc589413
    assemblyName: Cinemachine.dll
    namespaceName: Cinemachine
    className: CinemachineTargetGroup
  - hash:
      serializedVersion: 2
      Hash: 9ed9e421d17369f1952e18cc1edadbcb
    assemblyName: Unity.TextMeshPro.dll
    namespaceName: TMPro
    className: TMP_SubMeshUI
  - hash:
      serializedVersion: 2
      Hash: db74a8f2c513ab19c37eb215666ccc0c
    assemblyName: Unity.XR.PICO.dll
    namespaceName: Unity.XR.PXR.SecureMR
    className: PXR_SecureMRLocalTensor
  - hash:
      serializedVersion: 2
      Hash: 9cef675c00efb98bcf527a1d4ff9fed8
    assemblyName: Unity.InputSystem.dll
    namespaceName: UnityEngine.InputSystem.OnScreen
    className: OnScreenStick
  - hash:
      serializedVersion: 2
      Hash: 1d10a1cd5e26a7e91ede38b475389b08
    assemblyName: Pico.Spatializer.dll
    namespaceName: 
    className: PXR_Audio_Spatializer_SceneMaterial
  - hash:
      serializedVersion: 2
      Hash: 7219ebb0b4a64fd19236e423b34046f6
    assemblyName: Unity.TextMeshPro.dll
    namespaceName: TMPro
    className: TMP_SpriteAsset
  - hash:
      serializedVersion: 2
      Hash: 73e414d9bade6fd6f045aa054ca8a856
    assemblyName: Unity.VisualScripting.Core.dll
    namespaceName: Unity.VisualScripting
    className: UnityOnCancelMessageListener
  - hash:
      serializedVersion: 2
      Hash: 3cb3da584ffd21f9391d89417b79589c
    assemblyName: Unity.XR.PICO.dll
    namespaceName: Unity.XR.PXR.SecureMR
    className: PXR_SecureMRNmsOperatorConfig
  - hash:
      serializedVersion: 2
      Hash: f3ff4e45c46aad521b7dc4103ebc12ab
    assemblyName: Unity.XR.PICO.dll
    namespaceName: Unity.XR.PXR.SecureMR
    className: PXR_SecureMROperator
  - hash:
      serializedVersion: 2
      Hash: b2a268a0db44bf8f0d7a7b05f8de14ad
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: VRCameraDataRecorderExample
  - hash:
      serializedVersion: 2
      Hash: cb6e436fad8ad205794db196b22d97a2
    assemblyName: Cinemachine.dll
    namespaceName: Cinemachine
    className: CinemachineCollisionImpulseSource
  - hash:
      serializedVersion: 2
      Hash: 200450cfbb09fac16d2b70e6729db383
    assemblyName: Unity.XR.PICO.dll
    namespaceName: 
    className: PXR_ARCameraEffectManager
  - hash:
      serializedVersion: 2
      Hash: 0ebeebf908abd619a55b252a731f59d2
    assemblyName: Pico.Spatializer.dll
    namespaceName: 
    className: PXR_Audio_Spatializer_SceneGeometry
  - hash:
      serializedVersion: 2
      Hash: 8e35fe2655309f23b1e0b22231b6ab57
    assemblyName: Cinemachine.dll
    namespaceName: Cinemachine
    className: CinemachinePOV
  - hash:
      serializedVersion: 2
      Hash: 3d787c02bba725b66fa6b8aa39379b77
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: AssemblyPart
  - hash:
      serializedVersion: 2
      Hash: 59e5743e7ec6da179942737a5217fb2c
    assemblyName: Unity.InputSystem.dll
    namespaceName: UnityEngine.InputSystem.Editor
    className: InputDeviceDebuggerWindow
  - hash:
      serializedVersion: 2
      Hash: 9cd0152ee05f44c7a0421afc21da4418
    assemblyName: Unity.InputSystem.dll
    namespaceName: UnityEngine.InputSystem
    className: InputSystemObject
  - hash:
      serializedVersion: 2
      Hash: 6d1ca98bad1cb30a274f79776b279843
    assemblyName: Unity.VisualScripting.Core.dll
    namespaceName: Unity.VisualScripting
    className: UnityOnPointerClickMessageListener
  - hash:
      serializedVersion: 2
      Hash: 20ba1a8c3a23f7f7f597ae318e1bc829
    assemblyName: Unity.XR.Interaction.Toolkit.dll
    namespaceName: UnityEngine.XR.Interaction.Toolkit.AffordanceSystem.Receiver.Primitives
    className: ColorAffordanceReceiver
  - hash:
      serializedVersion: 2
      Hash: 931479a1d8270201cc51d706e998b932
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: VRUISetupHelper
  - hash:
      serializedVersion: 2
      Hash: 950f54278b72ed64c587658690831f77
    assemblyName: Unity.XR.PICO.dll
    namespaceName: Unity.XR.PXR.SecureMR
    className: PXR_SecureMRIntTensorData
  - hash:
      serializedVersion: 2
      Hash: 5afcb339ae446c9f47cd5b1193162bee
    assemblyName: Unity.XR.Interaction.Toolkit.Samples.StarterAssets.dll
    namespaceName: UnityEngine.XR.Interaction.Toolkit.Samples.StarterAssets
    className: ActionBasedControllerManager
  - hash:
      serializedVersion: 2
      Hash: e39ea0c775d26a15cac4eab690a2a9d0
    assemblyName: Unity.XR.Interaction.Toolkit.dll
    namespaceName: UnityEngine.XR.Interaction.Toolkit.AffordanceSystem.Theme.Primitives
    className: FloatAffordanceThemeDatum
  - hash:
      serializedVersion: 2
      Hash: 999c449ad95117c75b6263e062d2e58e
    assemblyName: Unity.Timeline.dll
    namespaceName: UnityEngine.Timeline
    className: ActivationPlayableAsset
  - hash:
      serializedVersion: 2
      Hash: b9484dc0a83a5260f8b27b6c3efe7f6b
    assemblyName: Cinemachine.dll
    namespaceName: Cinemachine
    className: CinemachineComposer
  - hash:
      serializedVersion: 2
      Hash: 00772c2e501491e3584a0eea65958c5a
    assemblyName: Unity.VisualScripting.Core.dll
    namespaceName: Unity.VisualScripting
    className: SceneVariables
  - hash:
      serializedVersion: 2
      Hash: 2d03b8ce630f57cc7f5f2c763d4ed1a1
    assemblyName: Unity.VisualScripting.Core.dll
    namespaceName: Unity.VisualScripting
    className: MacroScriptableObject
  - hash:
      serializedVersion: 2
      Hash: 78fa8c70bef8c6d96ce76e6d539a1987
    assemblyName: UnityEngine.UI.dll
    namespaceName: UnityEngine.UI
    className: GridLayoutGroup
  - hash:
      serializedVersion: 2
      Hash: 1bf7a0a2c93bd37dcb73a76d31519170
    assemblyName: Unity.InputSystem.dll
    namespaceName: UnityEngine.InputSystem
    className: RemoteInputPlayerConnection
  - hash:
      serializedVersion: 2
      Hash: 83e40a6bcfb77900485c54beb6eb0333
    assemblyName: Unity.XR.Interaction.Toolkit.dll
    namespaceName: UnityEngine.XR.Interaction.Toolkit.AffordanceSystem.Receiver.Rendering
    className: Vector3MaterialPropertyAffordanceReceiver
  - hash:
      serializedVersion: 2
      Hash: 056e06b22d42feb19ff3807a35d6efa1
    assemblyName: Cinemachine.dll
    namespaceName: Cinemachine
    className: CinemachineBlendListCamera
  - hash:
      serializedVersion: 2
      Hash: 945653e8ef6d43e530611c78897df6b3
    assemblyName: Unity.XR.Interaction.Toolkit.dll
    namespaceName: UnityEngine.XR.Interaction.Toolkit.UI
    className: XRUIInputModule
  - hash:
      serializedVersion: 2
      Hash: 01043222074ee56204fdf387360f22e3
    assemblyName: Unity.XR.Interaction.Toolkit.dll
    namespaceName: UnityEngine.XR.Interaction.Toolkit.AffordanceSystem.Theme.Primitives
    className: Vector3AffordanceThemeDatum
  - hash:
      serializedVersion: 2
      Hash: eedb3d25c110bf877f5d341cd2bf662a
    assemblyName: Unity.XR.PICO.dll
    namespaceName: Unity.XR.PXR
    className: PXR_SceneCaptureManager
  - hash:
      serializedVersion: 2
      Hash: 3d860abc344bfbd5a594aa55314fb635
    assemblyName: Unity.VisualScripting.Core.dll
    namespaceName: Unity.VisualScripting
    className: UnityOnEndDragMessageListener
  - hash:
      serializedVersion: 2
      Hash: dd186e52bba51b28bd6a08f77a3a7124
    assemblyName: UnityEngine.XR.LegacyInputHelpers.dll
    namespaceName: UnityEditor.XR.LegacyInputHelpers
    className: CameraOffset
  - hash:
      serializedVersion: 2
      Hash: f3af0e0f637016253c40351a71eac6ba
    assemblyName: Unity.VisualScripting.Core.dll
    namespaceName: Unity.VisualScripting
    className: UnityOnBeginDragMessageListener
  - hash:
      serializedVersion: 2
      Hash: 939bf23fee05aec56e493f95237a0c59
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: VRInputManager
  - hash:
      serializedVersion: 2
      Hash: 480e2a56c81cbe122ffc7c3fe7336a07
    assemblyName: Unity.Timeline.dll
    namespaceName: UnityEngine.Timeline
    className: AnimationPlayableAsset
  - hash:
      serializedVersion: 2
      Hash: b3b53420f0d10576e33674d9cccd900c
    assemblyName: Unity.InputSystem.dll
    namespaceName: UnityEngine.InputSystem.UI
    className: BaseInputOverride
  - hash:
      serializedVersion: 2
      Hash: e4f8e992bd8551aef082a903aad96f64
    assemblyName: Cinemachine.dll
    namespaceName: Cinemachine
    className: CinemachinePath
  - hash:
      serializedVersion: 2
      Hash: 89b081f67b76cf30c49d88a2ca6967a7
    assemblyName: Cinemachine.dll
    namespaceName: Cinemachine.PostFX
    className: CinemachineVolumeSettings
  - hash:
      serializedVersion: 2
      Hash: e688ed407bbfb11c21058187316ed44e
    assemblyName: Unity.TextMeshPro.dll
    namespaceName: TMPro
    className: TMP_InputField
  - hash:
      serializedVersion: 2
      Hash: 30805af31611bc7832cc4b6e47c0ee67
    assemblyName: Unity.XR.PICO.dll
    namespaceName: Unity.XR.PXR.SecureMR
    className: PXR_SecureMRFloatTensorData
  - hash:
      serializedVersion: 2
      Hash: 5bc8b03ef4c96d75a42e5e7b1b577e66
    assemblyName: UnityEngine.XR.LegacyInputHelpers.dll
    namespaceName: UnityEngine.XR.LegacyInputHelpers
    className: TransitionArmModel
  - hash:
      serializedVersion: 2
      Hash: 692d1b62ec44959d5ff4ca786bd45ee0
    assemblyName: Unity.XR.PICO.dll
    namespaceName: 
    className: PXR_ObjectTrackingBlock
  - hash:
      serializedVersion: 2
      Hash: 6278b4fd27c81c4376ed254299206902
    assemblyName: Unity.XR.PICO.dll
    namespaceName: Unity.XR.PXR
    className: PXR_ScreenFade
  - hash:
      serializedVersion: 2
      Hash: e8a39f74495905fc9188ce7b4c9436b0
    assemblyName: Pico.Spatializer.dll
    namespaceName: 
    className: PXR_Audio_Spatializer_AmbisonicSource
  - hash:
      serializedVersion: 2
      Hash: 49fa03f76b8d50dbcbf80c7161ca8b7f
    assemblyName: Unity.XR.PICO.dll
    namespaceName: 
    className: PXR_Hand
  - hash:
      serializedVersion: 2
      Hash: 79563ad2f50ae3008337214aea75b3aa
    assemblyName: PICO.TobSupport.dll
    namespaceName: 
    className: VirtualDisplayDemo
  - hash:
      serializedVersion: 2
      Hash: 9efc8c65b86b5c1022ed3efd7ac004a8
    assemblyName: Unity.VisualScripting.Core.dll
    namespaceName: Unity.VisualScripting
    className: GlobalMessageListener
  - hash:
      serializedVersion: 2
      Hash: 2380288dfe403a7f4b2182b575687cb8
    assemblyName: UnityEngine.UI.dll
    namespaceName: UnityEngine.UI
    className: HorizontalLayoutGroup
  - hash:
      serializedVersion: 2
      Hash: b46a27dc8fe542927657974694fc29aa
    assemblyName: UnityEngine.UI.dll
    namespaceName: UnityEngine.UI
    className: Slider
  - hash:
      serializedVersion: 2
      Hash: e62655405b42b0ff69c1edb8665a5b4d
    assemblyName: Unity.XR.Interaction.Toolkit.Samples.StarterAssets.dll
    namespaceName: UnityEngine.XR.Interaction.Toolkit.Samples.StarterAssets
    className: GazeInputManager
  - hash:
      serializedVersion: 2
      Hash: f52dd475db2cfd08cf8a297e2bbff1a8
    assemblyName: Unity.XR.PICO.dll
    namespaceName: Unity.XR.PXR
    className: PXR_Loader
  - hash:
      serializedVersion: 2
      Hash: 7283d7517057dea3e1257e97fd0e6f36
    assemblyName: Unity.VisualScripting.Core.dll
    namespaceName: Unity.VisualScripting
    className: UnityOnCollisionEnter2DMessageListener
  - hash:
      serializedVersion: 2
      Hash: a8d97edda379858fbc341d3045184b15
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: VRSystemDebugger
  - hash:
      serializedVersion: 2
      Hash: e78f6985cfa9077afa7bb6c3ee81529f
    assemblyName: Unity.TextMeshPro.dll
    namespaceName: TMPro
    className: TMP_FontAsset
  - hash:
      serializedVersion: 2
      Hash: 916a1d8536b7d2d9fbfb6a327164b5ac
    assemblyName: Unity.XR.PICO.dll
    namespaceName: Unity.XR.PXR.SecureMR
    className: PXR_SecureMRFileTensorData
  - hash:
      serializedVersion: 2
      Hash: 564dcfd3c966bc45c3965d107621e55c
    assemblyName: Unity.XR.Interaction.Toolkit.dll
    namespaceName: UnityEngine.XR.Interaction.Toolkit.AffordanceSystem.Receiver.Rendering
    className: Vector2MaterialPropertyAffordanceReceiver
  - hash:
      serializedVersion: 2
      Hash: bbbf677f7849193049266ec85b22b97d
    assemblyName: Unity.XR.PICO.dll
    namespaceName: Unity.XR.PXR.SecureMR
    className: PXR_SecureMRTensorMetadata
  - hash:
      serializedVersion: 2
      Hash: d32893222bf5b4f5e6414d5d7868ff68
    assemblyName: Unity.XR.PICO.dll
    namespaceName: 
    className: PXR_SecureMRTextureTensorData
  - hash:
      serializedVersion: 2
      Hash: 979b9adf15c6b9dffd0b4f337a430be9
    assemblyName: Unity.XR.Interaction.Toolkit.dll
    namespaceName: UnityEngine.XR.Interaction.Toolkit.Locomotion.Climbing
    className: ClimbTeleportInteractor
  - hash:
      serializedVersion: 2
      Hash: 98b77d46546e268044fb9f864ea6a45c
    assemblyName: Unity.XR.PICO.dll
    namespaceName: Unity.XR.PXR.SecureMR
    className: PXR_SecureMRPipelineRunner
  - hash:
      serializedVersion: 2
      Hash: 86d0d82bb27c9c434d7748ae612bdc88
    assemblyName: Unity.XR.Interaction.Toolkit.dll
    namespaceName: UnityEngine.XR.Interaction.Toolkit
    className: ActionBasedContinuousTurnProvider
  - hash:
      serializedVersion: 2
      Hash: 2d957d33afa5072080a40c9c783eb96c
    assemblyName: Unity.VisualScripting.Core.dll
    namespaceName: Unity.VisualScripting
    className: UnityOnBecameInvisibleMessageListener
  - hash:
      serializedVersion: 2
      Hash: 5d2eca6f6cfbab49b6fa5ee0d302f2f0
    assemblyName: UnityEngine.UI.dll
    namespaceName: UnityEngine.UIElements
    className: PanelEventHandler
  - hash:
      serializedVersion: 2
      Hash: 2be854dee95084c1537e4d6bb482ca5d
    assemblyName: Cinemachine.dll
    namespaceName: Cinemachine
    className: CinemachineDollyCart
  - hash:
      serializedVersion: 2
      Hash: ce29c624b4e215f90c5fc5df651c5363
    assemblyName: Unity.Timeline.dll
    namespaceName: UnityEngine.Timeline
    className: GroupTrack
  - hash:
      serializedVersion: 2
      Hash: 9e29667a89e02241a5acac926156e2f2
    assemblyName: Unity.VisualScripting.Core.dll
    namespaceName: Unity.VisualScripting
    className: UnityOnDeselectMessageListener
  - hash:
      serializedVersion: 2
      Hash: 0509e5ea3e9d9bffc131745a974d0480
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: VRAssemblyDebugger
  - hash:
      serializedVersion: 2
      Hash: aa4ce790932e1d26ce7a875d8c375c3b
    assemblyName: Unity.XR.Interaction.Toolkit.dll
    namespaceName: UnityEngine.XR.Interaction.Toolkit.UI
    className: TrackedDevicePhysicsRaycaster
  - hash:
      serializedVersion: 2
      Hash: 12729f5ef7a25d97a7ce2e04a09dda43
    assemblyName: Unity.XR.Interaction.Toolkit.dll
    namespaceName: UnityEngine.XR.Interaction.Toolkit.AffordanceSystem.Receiver.Audio
    className: AudioAffordanceReceiver
  - hash:
      serializedVersion: 2
      Hash: 8b2134b03e578f0b958ab5764b707bb1
    assemblyName: Unity.XR.Interaction.Toolkit.dll
    namespaceName: UnityEngine.XR.Interaction.Toolkit
    className: XRController
  - hash:
      serializedVersion: 2
      Hash: fba43b9e9825104cfccd68f26f96b745
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: AssemblyAnimationManager
  - hash:
      serializedVersion: 2
      Hash: 39230f691ce4f712b5eb4e70922e506a
    assemblyName: Unity.InputSystem.dll
    namespaceName: UnityEngine.InputSystem.Editor
    className: InputActionEditorWindow
  - hash:
      serializedVersion: 2
      Hash: 8de12aca8b8cb30fa29dfedb5a701e0e
    assemblyName: Unity.VisualScripting.State.dll
    namespaceName: Unity.VisualScripting
    className: StateGraphAsset
  - hash:
      serializedVersion: 2
      Hash: 006e9a6d9526e39e04ab3b71ae3ac7b8
    assemblyName: Cinemachine.dll
    namespaceName: Cinemachine.PostFX
    className: CinemachinePostProcessing
  - hash:
      serializedVersion: 2
      Hash: f0089fe7211502a04bee2a61606b37ad
    assemblyName: Unity.InputSystem.dll
    namespaceName: UnityEngine.InputSystem.Editor
    className: PlayerInputManagerEditor
  - hash:
      serializedVersion: 2
      Hash: 4629d478c5c0fec7fadeff182097485a
    assemblyName: Unity.XR.Interaction.Toolkit.dll
    namespaceName: UnityEngine.XR.Interaction.Toolkit
    className: XRScreenSpaceController
  - hash:
      serializedVersion: 2
      Hash: 1544cedbd70586f52825fae58f995a6d
    assemblyName: Cinemachine.dll
    namespaceName: Cinemachine
    className: CinemachineOrbitalTransposer
  - hash:
      serializedVersion: 2
      Hash: 41d56e2eb0448dfe0d2a9a9db8489ef3
    assemblyName: Unity.XR.PICO.dll
    namespaceName: 
    className: PXR_HandPosePreview
  - hash:
      serializedVersion: 2
      Hash: ee31d2c9c0739b774db54706ba1b2e68
    assemblyName: Unity.XR.Interaction.Toolkit.dll
    namespaceName: UnityEngine.XR.Interaction.Toolkit.Inputs
    className: InputActionManager
  - hash:
      serializedVersion: 2
      Hash: 443a8cf1e3dfd7bc3bfd2c5508f66600
    assemblyName: Unity.XR.PICO.dll
    namespaceName: Unity.XR.PXR
    className: PXR_SpatialMeshColorSetting
  - hash:
      serializedVersion: 2
      Hash: 2d68f5bdca155c29da4af5d006fab352
    assemblyName: Unity.XR.PICO.dll
    namespaceName: Unity.XR.PXR
    className: PXR_ControllerG3Animator
  - hash:
      serializedVersion: 2
      Hash: d78158674167b3da1fad56b0208ae288
    assemblyName: Cinemachine.dll
    namespaceName: Cinemachine
    className: CinemachineBrain
  - hash:
      serializedVersion: 2
      Hash: 80a1694e231aa04e1f6d4a1fd8392de4
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: Test
  - hash:
      serializedVersion: 2
      Hash: 15e2174438103ba23087ee5cc6c0e351
    assemblyName: Cinemachine.dll
    namespaceName: Cinemachine
    className: CinemachineConfiner
  - hash:
      serializedVersion: 2
      Hash: 0e321e09912fc665b76788dc31b0abfe
    assemblyName: Cinemachine.dll
    namespaceName: Cinemachine
    className: CinemachineSameAsFollowTarget
  - hash:
      serializedVersion: 2
      Hash: 835af30414a804f0b2946b4d19475a1a
    assemblyName: Unity.XR.PICO.dll
    namespaceName: Unity.XR.PXR
    className: PXR_Manager
  - hash:
      serializedVersion: 2
      Hash: 5aa4fc9c586bfc1f7d56918fab9e667e
    assemblyName: Cinemachine.dll
    namespaceName: Cinemachine
    className: CinemachineConfiner2D
  - hash:
      serializedVersion: 2
      Hash: 1cba768751bd6cd8bfc8adc5691dc0e6
    assemblyName: Unity.XR.Interaction.Toolkit.Samples.StarterAssets.dll
    namespaceName: UnityEngine.XR.Interaction.Toolkit.Samples.StarterAssets
    className: XRPokeFollowAffordance
  - hash:
      serializedVersion: 2
      Hash: 206a5c9d773ef22bc5ed2f451cd7ffbf
    assemblyName: Unity.VisualScripting.Core.dll
    namespaceName: Unity.VisualScripting
    className: AnimatorMessageListener
  - hash:
      serializedVersion: 2
      Hash: d13609b6d89635cbc29df61da760371b
    assemblyName: Cinemachine.dll
    namespaceName: Cinemachine
    className: CinemachineClearShot
  - hash:
      serializedVersion: 2
      Hash: 0ab78f031b53dd5d55f964f3cd1c223e
    assemblyName: UnityEngine.UI.dll
    namespaceName: UnityEngine.UI
    className: AspectRatioFitter
  - hash:
      serializedVersion: 2
      Hash: 281acd05524daf116460f458f4151aba
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: VRAssemblyPreview
  - hash:
      serializedVersion: 2
      Hash: e118dba0671fdfa6c252789661c08288
    assemblyName: Unity.XR.Interaction.Toolkit.dll
    namespaceName: UnityEngine.XR.Interaction.Toolkit.AffordanceSystem.State
    className: XRInteractableAffordanceStateProvider
  - hash:
      serializedVersion: 2
      Hash: ed034cff83e1d608e66691a37102630c
    assemblyName: Unity.TextMeshPro.dll
    namespaceName: TMPro
    className: TextMeshProUGUI
  - hash:
      serializedVersion: 2
      Hash: eaf4d82d1701b548e4c815285e9d8b1d
    assemblyName: Unity.XR.Interaction.Toolkit.dll
    namespaceName: UnityEngine.XR.Interaction.Toolkit
    className: XRPokeInteractor
  - hash:
      serializedVersion: 2
      Hash: b7d2fd308aabdc2ba4ca86534cd114db
    assemblyName: Unity.XR.Interaction.Toolkit.dll
    namespaceName: UnityEngine.XR.Interaction.Toolkit.AffordanceSystem.State
    className: XRInteractorAffordanceStateProvider
  - hash:
      serializedVersion: 2
      Hash: 5b5d5c5277b2ef662492673cfddb326e
    assemblyName: Unity.XR.Interaction.Toolkit.dll
    namespaceName: UnityEngine.XR.Interaction.Toolkit.AffordanceSystem.Receiver.Primitives
    className: Vector2AffordanceReceiver
  - hash:
      serializedVersion: 2
      Hash: e3f78750bc0c621e36807f6704f1387e
    assemblyName: Unity.XR.Interaction.Toolkit.dll
    namespaceName: UnityEngine.XR.Interaction.Toolkit.Inputs
    className: XRInputModalityManager
  - hash:
      serializedVersion: 2
      Hash: 33025c52ec11e3efd1699e11760a0597
    assemblyName: Unity.XR.PICO.dll
    namespaceName: Unity.XR.PXR
    className: PXR_Settings
  - hash:
      serializedVersion: 2
      Hash: 25509450b95d68e0a6989a1274690991
    assemblyName: Unity.XR.Interaction.Toolkit.dll
    namespaceName: UnityEngine.XR.Interaction.Toolkit
    className: CharacterControllerDriver
  - hash:
      serializedVersion: 2
      Hash: 875bd710dc77eb56444938cd23e97672
    assemblyName: Unity.XR.PICO.dll
    namespaceName: Unity.XR.PXR
    className: PXR_ControllerWithHandAnimator
  - hash:
      serializedVersion: 2
      Hash: fbb936660d403f6bbf16d6cfab4a93d4
    assemblyName: Unity.XR.Interaction.Toolkit.dll
    namespaceName: UnityEngine.XR.Interaction.Toolkit
    className: XRSocketInteractor
  - hash:
      serializedVersion: 2
      Hash: a7d2a447835cadfeecfba6de1cd8e68e
    assemblyName: Unity.XR.Interaction.Toolkit.dll
    namespaceName: UnityEngine.XR.Interaction.Toolkit.AffordanceSystem.Receiver.Primitives
    className: Vector3AffordanceReceiver
  - hash:
      serializedVersion: 2
      Hash: ab83fa9209b73d94dbec301001db7690
    assemblyName: Unity.VisualScripting.Core.dll
    namespaceName: Unity.VisualScripting
    className: UnityOnMouseUpAsButtonMessageListener
  - hash:
      serializedVersion: 2
      Hash: 14e6ae967299ff092bbe5aad43e9521b
    assemblyName: Unity.XR.CoreUtils.dll
    namespaceName: Unity.XR.CoreUtils.Datums
    className: IntDatum
  - hash:
      serializedVersion: 2
      Hash: ea6522a3a635f81f06052cace138f5be
    assemblyName: Unity.XR.Interaction.Toolkit.dll
    namespaceName: UnityEngine.XR.Interaction.Toolkit.UI
    className: CanvasTracker
  - hash:
      serializedVersion: 2
      Hash: 22c82d58ce34c12d8d4dd836a2d0476b
    assemblyName: Unity.XR.Interaction.Toolkit.dll
    namespaceName: UnityEngine.XR.Interaction.Toolkit
    className: XRRayInteractor
  - hash:
      serializedVersion: 2
      Hash: 9690f760294698c29b1bee7d9b7bd452
    assemblyName: UnityEngine.UI.dll
    namespaceName: UnityEngine.EventSystems
    className: StandaloneInputModule
  - hash:
      serializedVersion: 2
      Hash: ad9e1819a7a717899366794192eda299
    assemblyName: PICO.Platform.dll
    namespaceName: Unity.XR.PXR
    className: PXR_PlatformSetting
  - hash:
      serializedVersion: 2
      Hash: a273edb1f10a7eafb870721bc4bddaca
    assemblyName: UnityEngine.UI.dll
    namespaceName: UnityEngine.UI
    className: Scrollbar
  - hash:
      serializedVersion: 2
      Hash: 0ffacc03a651624ad5bb72bc787275f7
    assemblyName: Unity.TextMeshPro.dll
    namespaceName: TMPro
    className: TMP_SubMesh
  - hash:
      serializedVersion: 2
      Hash: ceb4961d39f347996fda1404ac174d05
    assemblyName: Unity.Timeline.dll
    namespaceName: UnityEngine.Timeline
    className: MarkerTrack
  - hash:
      serializedVersion: 2
      Hash: cde977fb11d1f6ae6783d1b69745d58a
    assemblyName: Cinemachine.dll
    namespaceName: Cinemachine
    className: CinemachineImpulseSource
  - hash:
      serializedVersion: 2
      Hash: e0def3efb589c6863fb13b32c42e0c6f
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: AssemblyAnimationService
  - hash:
      serializedVersion: 2
      Hash: 6691dd18200d133e8dbae24b6281d1cf
    assemblyName: UnityEngine.TestRunner.dll
    namespaceName: UnityEngine.TestTools
    className: BeforeAfterTestCommandState
  - hash:
      serializedVersion: 2
      Hash: c1933688180ee845ee0ae3bc5e3971e6
    assemblyName: Cinemachine.dll
    namespaceName: Cinemachine
    className: CinemachineImpulseListener
  - hash:
      serializedVersion: 2
      Hash: 0616b13f6c62e299153c6a68aaaec148
    assemblyName: Unity.InputSystem.dll
    namespaceName: UnityEngine.InputSystem.EnhancedTouch
    className: TouchSimulation
  - hash:
      serializedVersion: 2
      Hash: 5718db4fbfa13593401516b78a98b6e2
    assemblyName: Unity.InputSystem.dll
    namespaceName: UnityEngine.InputSystem.Editor
    className: PlayerInputEditor
  - hash:
      serializedVersion: 2
      Hash: 4548d5c4e1f28668d0f0f42130677cda
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: Neo4jAssemblyUI
  - hash:
      serializedVersion: 2
      Hash: 752f100eaca2a00eb5e13b1e2745ba1d
    assemblyName: Unity.InputSystem.dll
    namespaceName: UnityEngine.InputSystem.UI.Editor
    className: InputSystemUIInputModuleEditor
  - hash:
      serializedVersion: 2
      Hash: 33351225ad07d388c6f33d76ccfe9ba6
    assemblyName: Unity.TextMeshPro.dll
    namespaceName: TMPro
    className: TextContainer
  - hash:
      serializedVersion: 2
      Hash: de6911de94590817a2c9b1375e4e041e
    assemblyName: Unity.InputSystem.dll
    namespaceName: UnityEngine.InputSystem.Editor
    className: InputActionAssetEditor
  - hash:
      serializedVersion: 2
      Hash: a5746bb1464fb33c108c346c0a8aec46
    assemblyName: Unity.XR.Interaction.Toolkit.dll
    namespaceName: UnityEngine.XR.Interaction.Toolkit
    className: DeviceBasedContinuousTurnProvider
  - hash:
      serializedVersion: 2
      Hash: 9da94efd287aeaf788ae705bf90386e8
    assemblyName: Pico.Spatializer.dll
    namespaceName: 
    className: PXR_Audio_Spatializer_AudioListener
  - hash:
      serializedVersion: 2
      Hash: b906acd878baea9f22b8e04ea1ee81a1
    assemblyName: Unity.InputSystem.dll
    namespaceName: 
    className: DownloadableSample
  - hash:
      serializedVersion: 2
      Hash: 3a9f5b58909408ab2632c4081e2167f0
    assemblyName: Unity.XR.Interaction.Toolkit.dll
    namespaceName: UnityEngine.XR.Interaction.Toolkit
    className: XRInteractorReticleVisual
  - hash:
      serializedVersion: 2
      Hash: f1ba3939edcf4406dbb90ccb6e292259
    assemblyName: Unity.VisualScripting.Core.dll
    namespaceName: Unity.VisualScripting
    className: UnityOnDragMessageListener
  - hash:
      serializedVersion: 2
      Hash: c775193bec472a91767f76dccfe603e7
    assemblyName: UnityEngine.UI.dll
    namespaceName: UnityEngine.UI
    className: Outline
  - hash:
      serializedVersion: 2
      Hash: fd005ae1f5a717637a43c3076bad5085
    assemblyName: Unity.InputSystem.dll
    namespaceName: UnityEngine.InputSystem
    className: InputActionReference
  - hash:
      serializedVersion: 2
      Hash: 7bc7d09d56c3110f883116a9c4bacd93
    assemblyName: Unity.XR.Interaction.Toolkit.dll
    namespaceName: UnityEngine.XR.Interaction.Toolkit.Inputs
    className: XRHandSkeletonPokeDisplacer
  - hash:
      serializedVersion: 2
      Hash: 2838f174d85b11fa0aa4c86f42ec405c
    assemblyName: PICO.TobSupport.dll
    namespaceName: 
    className: VirtualDisplayEvent
  - hash:
      serializedVersion: 2
      Hash: 7f5d04a23d7ad272dc7905d8fa0d3ae5
    assemblyName: Unity.VisualScripting.Core.dll
    namespaceName: Unity.VisualScripting
    className: UnityOnPointerEnterMessageListener
  - hash:
      serializedVersion: 2
      Hash: 678e1b1d2ead89a22d454421d06258e4
    assemblyName: Unity.Timeline.dll
    namespaceName: UnityEngine.Timeline
    className: PlayableTrack
  - hash:
      serializedVersion: 2
      Hash: 06fb2a7c659cadf784d67ef764db13c3
    assemblyName: Unity.XR.PICO.dll
    namespaceName: Unity.XR.PXR.SecureMR
    className: PXR_SecureMRPipelineExecute
  - hash:
      serializedVersion: 2
      Hash: 22dc69f3fda1491db799a1d2955e3bfd
    assemblyName: Unity.XR.Interaction.Toolkit.dll
    namespaceName: UnityEngine.XR.Interaction.Toolkit.AffordanceSystem.Receiver.Rendering
    className: ColorMaterialPropertyAffordanceReceiver
  - hash:
      serializedVersion: 2
      Hash: 60848472e362199ae5eac55c8892dcbe
    assemblyName: UnityEngine.UI.dll
    namespaceName: UnityEngine.UI
    className: VerticalLayoutGroup
  - hash:
      serializedVersion: 2
      Hash: 09d8a38faf8c3784976079fb6b89acd1
    assemblyName: Unity.XR.Interaction.Toolkit.dll
    namespaceName: UnityEngine.XR.Interaction.Toolkit.Transformers
    className: XRSingleGrabFreeTransformer
  - hash:
      serializedVersion: 2
      Hash: c6e8b7c0ea40cc067ac8a0b506ce2aaf
    assemblyName: Unity.XR.PICO.dll
    namespaceName: Unity.XR.PXR.SecureMR
    className: PXR_SecureMROperand
  - hash:
      serializedVersion: 2
      Hash: 3812ed3f09ee72fc06a5c9d8ceb14f95
    assemblyName: Unity.XR.PICO.dll
    namespaceName: 
    className: SliderText
  - hash:
      serializedVersion: 2
      Hash: e5eef4adaf97423992ea995b0416be4d
    assemblyName: Unity.XR.PICO.dll
    namespaceName: Unity.XR.PXR.SecureMR
    className: PXR_SecureMRSortMatrixOperatorConfig
  - hash:
      serializedVersion: 2
      Hash: 3e2daf98382ba59dd320a76be6466888
    assemblyName: Unity.XR.PICO.dll
    namespaceName: 
    className: PXR_CameraEffectBlock
  - hash:
      serializedVersion: 2
      Hash: cb55a874cd9323dc6c605763ca21a72b
    assemblyName: Unity.XR.PICO.dll
    namespaceName: Unity.XR.PXR.SecureMR
    className: PXR_SecureMRComparisonOperatorConfig
  - hash:
      serializedVersion: 2
      Hash: 5427ec75cdce1bf2109d88421ae3597c
    assemblyName: Unity.XR.Interaction.Toolkit.dll
    namespaceName: UnityEngine.XR.Interaction.Toolkit
    className: XRControllerRecording
  - hash:
      serializedVersion: 2
      Hash: 3d852ed0e8c9b38616d90b775f92a20e
    assemblyName: Cinemachine.dll
    namespaceName: Cinemachine
    className: CinemachineFixedSignal
  - hash:
      serializedVersion: 2
      Hash: 2dbc527f7fa55bb12b72c4175bcee464
    assemblyName: Pico.Spatializer.dll
    namespaceName: 
    className: PXR_Audio_Spatializer_AudioSource
  - hash:
      serializedVersion: 2
      Hash: 098e616fddf2c530f28257c2c39f270b
    assemblyName: Unity.VisualScripting.Core.dll
    namespaceName: Unity.VisualScripting
    className: UnityOnCollisionExitMessageListener
  - hash:
      serializedVersion: 2
      Hash: 6e90988cab0beacf5b5c3a0cb6f33404
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: Neo4jAssemblyController
  - hash:
      serializedVersion: 2
      Hash: 111d589e9cb42750fdacf7d6d13c61a5
    assemblyName: Cinemachine.dll
    namespaceName: Cinemachine
    className: NoiseSettings
  - hash:
      serializedVersion: 2
      Hash: 466329aa1430edd5d9cc35262ee17096
    assemblyName: UnityEngine.UI.dll
    namespaceName: UnityEngine.EventSystems
    className: Physics2DRaycaster
  - hash:
      serializedVersion: 2
      Hash: 9c1c086addbec1dff8ea680fb18f7bdb
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: SimpleInputTest
  - hash:
      serializedVersion: 2
      Hash: f9786a3dca6228a6818ec4a0134d9212
    assemblyName: UnityEngine.TestRunner.dll
    namespaceName: UnityEngine.TestTools.TestRunner.Callbacks
    className: PlayModeRunnerCallback
  - hash:
      serializedVersion: 2
      Hash: 805c62fb3df38a31dfa854c4f1d54dfa
    assemblyName: UnityEngine.UI.dll
    namespaceName: UnityEngine.UI
    className: PositionAsUV1
  - hash:
      serializedVersion: 2
      Hash: 7042d10cf02e9e7999d50e2e2d4eae54
    assemblyName: Unity.XR.Interaction.Toolkit.dll
    namespaceName: UnityEngine.XR.Interaction.Toolkit.Inputs.Simulation
    className: XRDeviceSimulatorSettings
  - hash:
      serializedVersion: 2
      Hash: c467cc72cfd6c56b944b4455c5c97b1c
    assemblyName: Unity.XR.Interaction.Toolkit.dll
    namespaceName: UnityEngine.XR.Interaction.Toolkit
    className: XRDirectInteractor
  - hash:
      serializedVersion: 2
      Hash: 059275294748a4e56288432187916564
    assemblyName: Unity.XR.Interaction.Toolkit.dll
    namespaceName: UnityEngine.XR.Interaction.Toolkit.AffordanceSystem.Receiver.Rendering
    className: BlendShapeAffordanceReceiver
  - hash:
      serializedVersion: 2
      Hash: c05e73c4535140d8b52eabb410ae089c
    assemblyName: Unity.Timeline.dll
    namespaceName: UnityEngine.Timeline
    className: AudioPlayableAsset
  - hash:
      serializedVersion: 2
      Hash: fe080edb42253ff47e3e338b21fe9f9e
    assemblyName: Unity.VisualScripting.Core.dll
    namespaceName: Unity.VisualScripting
    className: UnityOnPointerDownMessageListener
  - hash:
      serializedVersion: 2
      Hash: 0724ea5abac6fc6264dc5cb4afc1bd64
    assemblyName: Unity.XR.Interaction.Toolkit.dll
    namespaceName: UnityEngine.XR.Interaction.Toolkit
    className: XRSimpleInteractable
  - hash:
      serializedVersion: 2
      Hash: 459334ca57e17e388588a5fb8a5d4c44
    assemblyName: Cinemachine.dll
    namespaceName: Cinemachine
    className: CinemachineTransposer
  - hash:
      serializedVersion: 2
      Hash: dc15cdcfcce97f93a72c051990a267d3
    assemblyName: Unity.VisualScripting.Core.dll
    namespaceName: Unity.VisualScripting
    className: UnityOnScrollRectValueChangedMessageListener
  - hash:
      serializedVersion: 2
      Hash: 03537dd42b6fc73a601799c77ceb0b9c
    assemblyName: UnityEngine.TestRunner.dll
    namespaceName: UnityEngine.TestTools.TestRunner.Callbacks
    className: RemoteTestResultSender
  - hash:
      serializedVersion: 2
      Hash: 453c286dc9c9c693626ccfeab876ea44
    assemblyName: UnityEngine.UI.dll
    namespaceName: UnityEngine.EventSystems
    className: EventTrigger
  - hash:
      serializedVersion: 2
      Hash: 99646bf392a020dc5b0597b2cf689126
    assemblyName: Unity.XR.Interaction.Toolkit.dll
    namespaceName: UnityEngine.XR.Interaction.Toolkit
    className: DeviceBasedSnapTurnProvider
  - hash:
      serializedVersion: 2
      Hash: c8073be35b87697b9b1ad85218e25434
    assemblyName: Unity.XR.Interaction.Toolkit.Samples.DeviceSimulator.dll
    namespaceName: UnityEngine.XR.Interaction.Toolkit.Samples.DeviceSimulator
    className: XRDeviceSimulatorHandsUI
  - hash:
      serializedVersion: 2
      Hash: 0bd403cafe5bfb0c5bbda285dc190598
    assemblyName: Cinemachine.dll
    namespaceName: 
    className: CinemachineShot
  - hash:
      serializedVersion: 2
      Hash: b08d1d91c083f76044c6b090597bf7e5
    assemblyName: Unity.InputSystem.dll
    namespaceName: UnityEngine.InputSystem
    className: InputSettings
  - hash:
      serializedVersion: 2
      Hash: 644007494bc8f11f96cb3d9910dc3bce
    assemblyName: Unity.VisualScripting.Core.dll
    namespaceName: Unity.VisualScripting
    className: UnityOnSubmitMessageListener
  - hash:
      serializedVersion: 2
      Hash: 64a060ec588b5d514f488fc696587fc4
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: PICODirectInputHandler
  - hash:
      serializedVersion: 2
      Hash: 55ac714ecd14f17465c0e21a680e5b69
    assemblyName: Unity.Timeline.dll
    namespaceName: UnityEngine.Timeline
    className: SignalTrack
  - hash:
      serializedVersion: 2
      Hash: d3669877414759386a730a648c50f255
    assemblyName: Cinemachine.dll
    namespaceName: Cinemachine
    className: CinemachineTrackedDolly
  - hash:
      serializedVersion: 2
      Hash: f677f9f9540afe6dd74c4150e2b6081f
    assemblyName: Unity.XR.Interaction.Toolkit.dll
    namespaceName: UnityEngine.XR.Interaction.Toolkit.AffordanceSystem.Receiver.Primitives
    className: QuaternionEulerAffordanceReceiver
  - hash:
      serializedVersion: 2
      Hash: e14dcf7af3981900fd7ccc998d6e0e85
    assemblyName: Unity.VisualScripting.Core.dll
    namespaceName: Unity.VisualScripting
    className: UnityOnMoveMessageListener
  - hash:
      serializedVersion: 2
      Hash: 62410d1ecc90a9dac13d1f096c1c7b04
    assemblyName: PICO.TobSupport.dll
    namespaceName: Unity.XR.PICO.TOBSupport
    className: PXR_EnterpriseTools
  - hash:
      serializedVersion: 2
      Hash: 7a271bf6ffe8af44373884203c1d7595
    assemblyName: Unity.XR.CoreUtils.dll
    namespaceName: Unity.XR.CoreUtils.Datums
    className: AnimationCurveDatum
  - hash:
      serializedVersion: 2
      Hash: f26051b54cf5cd4140c3ca6ce4184d61
    assemblyName: Unity.XR.Interaction.Toolkit.dll
    namespaceName: UnityEngine.XR.Interaction.Toolkit.Locomotion.Teleportation
    className: TeleportVolumeDestinationSettingsDatum
  - hash:
      serializedVersion: 2
      Hash: 42fdae9faf7c0ffa4ea74d43d40f29a1
    assemblyName: Unity.XR.Interaction.Toolkit.dll
    namespaceName: UnityEngine.XR.Interaction.Toolkit
    className: ActionBasedContinuousMoveProvider
  - hash:
      serializedVersion: 2
      Hash: 1ff76b7a82b2b0fd6d84eed67370a649
    assemblyName: Unity.Timeline.dll
    namespaceName: UnityEngine.Timeline
    className: ControlTrack
  - hash:
      serializedVersion: 2
      Hash: b54f4203bc5cb280d0758c27343b40a8
    assemblyName: Unity.InputSystem.dll
    namespaceName: UnityEngine.InputSystem.UI
    className: InputSystemUIInputModule
  - hash:
      serializedVersion: 2
      Hash: f1e8260b8c1f5b7787838735270bd875
    assemblyName: Unity.XR.Interaction.Toolkit.dll
    namespaceName: UnityEngine.XR.Interaction.Toolkit.AffordanceSystem.Receiver.Primitives
    className: FloatAffordanceReceiver
  - hash:
      serializedVersion: 2
      Hash: 82aed179d3bd44012e37e858664b8557
    assemblyName: Unity.XR.PICO.dll
    namespaceName: Unity.XR.PXR
    className: PXR_OverlayManager
  - hash:
      serializedVersion: 2
      Hash: 1bc2361d1b7f716347461ee447c65270
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: PICOVRInputHandler
  - hash:
      serializedVersion: 2
      Hash: 7151a7b2868fb02f8ac084398a4d750b
    assemblyName: Pico.Spatializer.Example.dll
    namespaceName: 
    className: GeomNumPrinter
  - hash:
      serializedVersion: 2
      Hash: 8545076e39e05e5f0a066bf663e653c4
    assemblyName: Unity.XR.PICO.dll
    namespaceName: Unity.XR.PXR
    className: PXR_HandPoseGenerator
  - hash:
      serializedVersion: 2
      Hash: 7f6ecb58026fbc2d40754c2cb25f6ede
    assemblyName: Unity.XR.Interaction.Toolkit.dll
    namespaceName: UnityEngine.XR.Interaction.Toolkit.AffordanceSystem.Receiver.Transformation
    className: UniformTransformScaleAffordanceReceiver
  - hash:
      serializedVersion: 2
      Hash: 74d10f1ce9336a3e675e041537da79e0
    assemblyName: Cinemachine.dll
    namespaceName: Cinemachine
    className: CinemachineStoryboard
  - hash:
      serializedVersion: 2
      Hash: 0bffe509a3ed7a5955fb10105f2fe587
    assemblyName: Unity.XR.PICO.dll
    namespaceName: Unity.XR.PXR.SecureMR
    className: PXR_SecureMRModelOperatorConfiguration
  - hash:
      serializedVersion: 2
      Hash: 4ea7de30cb668cc5a4a70a803205db1d
    assemblyName: Cinemachine.dll
    namespaceName: Cinemachine
    className: CinemachineStateDrivenCamera
  - hash:
      serializedVersion: 2
      Hash: 05066678c8fbd1ba3a3cd3ec9cf6f52c
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: AlignmentTest
  - hash:
      serializedVersion: 2
      Hash: 09411a3e730912e4e1ec6a12535e1386
    assemblyName: UnityEngine.XR.LegacyInputHelpers.dll
    namespaceName: UnityEngine.XR.LegacyInputHelpers
    className: SwingArmModel
  - hash:
      serializedVersion: 2
      Hash: 037370a41216c92cfd46716c22a85823
    assemblyName: Unity.XR.Interaction.Toolkit.dll
    namespaceName: UnityEngine.XR.Interaction.Toolkit.Transformers
    className: XRDualGrabFreeTransformer
  - hash:
      serializedVersion: 2
      Hash: 7d058425dfd4d06ddec4be7a4bb72067
    assemblyName: Unity.VisualScripting.Core.dll
    namespaceName: Unity.VisualScripting
    className: UnityOnParticleCollisionMessageListener
  - hash:
      serializedVersion: 2
      Hash: c795458f5fdb20c84a5f4224b195692a
    assemblyName: Unity.InputSystem.dll
    namespaceName: UnityEngine.InputSystem.UI
    className: MultiplayerEventSystem
  - hash:
      serializedVersion: 2
      Hash: 1221243affeb7b51593399d574153062
    assemblyName: Unity.InputSystem.dll
    namespaceName: UnityEngine.InputSystem.XR
    className: TrackedPoseDriver
  - hash:
      serializedVersion: 2
      Hash: 9a892a8a59574894e4dfcc95e537835f
    assemblyName: Unity.VisualScripting.Core.dll
    namespaceName: Unity.VisualScripting
    className: UnityOnMouseOverMessageListener
  - hash:
      serializedVersion: 2
      Hash: 859333885587342035ff75ae28f9d734
    assemblyName: Unity.VisualScripting.Core.dll
    namespaceName: Unity.VisualScripting
    className: Variables
  - hash:
      serializedVersion: 2
      Hash: 925cacfe5562091dc0a0277a0a94c50b
    assemblyName: PICO.Platform.dll
    namespaceName: Pico.Platform.Samples
    className: SimpleDemo
  - hash:
      serializedVersion: 2
      Hash: 227479bd62346e4ec94436066e4f660a
    assemblyName: Unity.VisualScripting.State.dll
    namespaceName: Unity.VisualScripting
    className: StateMachine
  - hash:
      serializedVersion: 2
      Hash: 535008cffceccb804cc6eba2a56b89c1
    assemblyName: Unity.XR.Interaction.Toolkit.dll
    namespaceName: UnityEngine.XR.Interaction.Toolkit.Inputs.Simulation
    className: XRDeviceSimulator
  - hash:
      serializedVersion: 2
      Hash: 85f59136a167991dc5e653e06a9494b3
    assemblyName: Unity.XR.PICO.dll
    namespaceName: Unity.XR.PXR
    className: PXR_ControllerLoader
  - hash:
      serializedVersion: 2
      Hash: b45f8e892748078fe81da8c1f600721b
    assemblyName: Unity.XR.PICO.dll
    namespaceName: Unity.XR.PXR.SecureMR
    className: PXR_SecureMRColorConvertOperatorConfig
  - hash:
      serializedVersion: 2
      Hash: 21afac633a879373a0532e1c59235b15
    assemblyName: Unity.XR.PICO.dll
    namespaceName: Unity.XR.PXR.SecureMR
    className: PXR_SecureMRTensorMapping
  - hash:
      serializedVersion: 2
      Hash: 87decd324e8a1a6df5c2eb0a7a7f1d21
    assemblyName: Unity.XR.Interaction.Toolkit.dll
    namespaceName: UnityEngine.XR.Interaction.Toolkit.UI.BodyUI
    className: FollowPresetDatum
  - hash:
      serializedVersion: 2
      Hash: 9141da52667d5b03d4f12036ebce4d0f
    assemblyName: Unity.VisualScripting.Core.dll
    namespaceName: Unity.VisualScripting
    className: UnityOnMouseUpMessageListener
  - hash:
      serializedVersion: 2
      Hash: cd5e4b671a6f3e47fe319d5913a5513e
    assemblyName: Unity.TextMeshPro.dll
    namespaceName: TMPro
    className: TMP_ScrollbarEventHandler
  - hash:
      serializedVersion: 2
      Hash: 144b90d8ac7f95ef0e61cb1c7f588783
    assemblyName: Unity.XR.PICO.dll
    namespaceName: Unity.XR.PXR.SecureMR
    className: PXR_SecureMRGlobalTensor
  - hash:
      serializedVersion: 2
      Hash: 48761c954745f3c75baf0da2ea021499
    assemblyName: Unity.XR.Interaction.Toolkit.dll
    namespaceName: UnityEngine.XR.Interaction.Toolkit
    className: XRInteractableSnapVolume
  - hash:
      serializedVersion: 2
      Hash: ded31f7c76891a352a8b17f067844a46
    assemblyName: Unity.XR.PICO.dll
    namespaceName: Unity.XR.PXR.SecureMR
    className: PXR_SecureMRProvider
  - hash:
      serializedVersion: 2
      Hash: fc28e0ec65d5882349f56e71bb6bfddd
    assemblyName: Unity.XR.Interaction.Toolkit.dll
    namespaceName: UnityEngine.XR.Interaction.Toolkit.UI
    className: TrackedDeviceGraphicRaycaster
  - hash:
      serializedVersion: 2
      Hash: a49f31917d44a804be83a7e958ff7709
    assemblyName: Cinemachine.dll
    namespaceName: 
    className: CinemachineRecomposer
  - hash:
      serializedVersion: 2
      Hash: 26d33857e2297a6bee8359f1e5888aab
    assemblyName: Unity.XR.Interaction.Toolkit.dll
    namespaceName: UnityEngine.XR.Interaction.Toolkit.Utilities
    className: DisposableManagerSingleton
  - hash:
      serializedVersion: 2
      Hash: 4aa0caef6997843541fcbb952ef3b89a
    assemblyName: Unity.XR.CoreUtils.dll
    namespaceName: Unity.XR.CoreUtils.Datums
    className: FloatDatum
  - hash:
      serializedVersion: 2
      Hash: 31d0d66962989f5f311b2a1d2ed736c4
    assemblyName: Cinemachine.dll
    namespaceName: Cinemachine
    className: CinemachineBasicMultiChannelPerlin
  - hash:
      serializedVersion: 2
      Hash: dc031b0a141ed84467e6f8d330e34aa6
    assemblyName: Unity.XR.Interaction.Toolkit.dll
    namespaceName: UnityEngine.XR.Interaction.Toolkit.AffordanceSystem.Rendering
    className: MaterialPropertyBlockHelper
  - hash:
      serializedVersion: 2
      Hash: 4c21ac31067b872730027b92ce7449c6
    assemblyName: Unity.XR.PICO.dll
    namespaceName: Unity.XR.PXR
    className: PXR_ProjectSetting
  - hash:
      serializedVersion: 2
      Hash: d42fc2b901d44d204ae99ca075c11c98
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: VRAssemblyUIIntegrator
  - hash:
      serializedVersion: 2
      Hash: 97732f4d87fb41c98310c74117c27cfb
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: PICOControllerSetup
  - hash:
      serializedVersion: 2
      Hash: 459d31b13454a7bb8b2768ffea2f637f
    assemblyName: Unity.XR.Interaction.Toolkit.dll
    namespaceName: UnityEngine.XR.Interaction.Toolkit
    className: GrabMoveProvider
  - hash:
      serializedVersion: 2
      Hash: af3c7bb5f1a72685c8bff2aeb7ca8d1b
    assemblyName: Unity.Timeline.dll
    namespaceName: UnityEngine.Timeline
    className: SignalEmitter
  - hash:
      serializedVersion: 2
      Hash: c35526d4c9f5c2ba055cc5ec800b3bff
    assemblyName: Unity.XR.PICO.dll
    namespaceName: Unity.XR.PXR.SecureMR
    className: PXR_SecureMRNormalizeOperatorConfig
  - hash:
      serializedVersion: 2
      Hash: 1e4094f9889dbb405020b5a600d7717f
    assemblyName: Unity.InputSystem.dll
    namespaceName: UnityEngine.InputSystem
    className: PlayerInputManager
  - hash:
      serializedVersion: 2
      Hash: 7c1d65f1c85f96657981a41d3e485486
    assemblyName: Unity.XR.Interaction.Toolkit.dll
    namespaceName: UnityEngine.XR.Interaction.Toolkit.AffordanceSystem.Receiver.Rendering
    className: Vector4MaterialPropertyAffordanceReceiver
  - hash:
      serializedVersion: 2
      Hash: 5c76b6826c5a42f322ba10c4da3fc50b
    assemblyName: Unity.VisualScripting.Core.dll
    namespaceName: Unity.VisualScripting
    className: UnityOnToggleValueChangedMessageListener
  - hash:
      serializedVersion: 2
      Hash: e4c7d57547c732696875431b7742c007
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: VRAssemblyOrientationHelper
  - hash:
      serializedVersion: 2
      Hash: 288d00eb82583e08d0f31804a32eaf4d
    assemblyName: Unity.XR.PICO.dll
    namespaceName: Unity.XR.PXR
    className: PXR_SpatialAnchor
  - hash:
      serializedVersion: 2
      Hash: 75f58323157f9b4e13d2528e58640a72
    assemblyName: Cinemachine.dll
    namespaceName: Cinemachine
    className: CinemachineFollowZoom
  - hash:
      serializedVersion: 2
      Hash: 2a1618af3cf6183b2385d416626c759a
    assemblyName: Unity.VisualScripting.Core.dll
    namespaceName: Unity.VisualScripting
    className: UnityOnMouseEnterMessageListener
  - hash:
      serializedVersion: 2
      Hash: 829a3f3825b2b9d72365ef92d8177711
    assemblyName: UnityEngine.UI.dll
    namespaceName: UnityEngine.EventSystems
    className: TouchInputModule
  - hash:
      serializedVersion: 2
      Hash: 519249e4c12fac3edb1ee8c2a70719b4
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: PICODirectInputAdapter
  - hash:
      serializedVersion: 2
      Hash: 33294b1f432ec75aca0623cbfe5850ad
    assemblyName: PICO.Platform.dll
    namespaceName: Pico.Platform.Framework
    className: Runner
  - hash:
      serializedVersion: 2
      Hash: bd801e6ac0cc826733286cca5b8025e6
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: SimpleAssemblyDataReceiver
  - hash:
      serializedVersion: 2
      Hash: d757ed4213cba93c8b85eae6f99b2650
    assemblyName: Unity.XR.Interaction.Toolkit.dll
    namespaceName: UnityEngine.XR.Interaction.Toolkit.Locomotion.Teleportation
    className: TeleportationMultiAnchorVolume
  - hash:
      serializedVersion: 2
      Hash: 75fc63450d14bdea70268db2c7811bd8
    assemblyName: Unity.XR.PICO.dll
    namespaceName: Unity.XR.PXR.SecureMR
    className: PXR_SecureMRUShortTensorData
  - hash:
      serializedVersion: 2
      Hash: c5495cef5472c394a7d823d1315ba01d
    assemblyName: Unity.VisualScripting.Core.dll
    namespaceName: Unity.VisualScripting
    className: UnityOnSliderValueChangedMessageListener
  - hash:
      serializedVersion: 2
      Hash: bd0cfed6f949170757aa7c09bd194e14
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: VRStartMenuSetupHelper
  - hash:
      serializedVersion: 2
      Hash: 3dd33e005100671703b23c7b3bc76bdc
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: VRStartMenuDemo
  - hash:
      serializedVersion: 2
      Hash: 244bfef8c222549e5bc0c66543109cae
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: VRSceneManager
  - hash:
      serializedVersion: 2
      Hash: 987aec3f130fa6e06c7f8087c1e90d87
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: VRStartMenuManager
  - hash:
      serializedVersion: 2
      Hash: 9f7954341180579deebfed190d7d8281
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: VRStartMenuInputHandler
  - hash:
      serializedVersion: 2
      Hash: 0908fa250b81d279412aee4f6b4bcfb8
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: PICOVRButtonFix
  - hash:
      serializedVersion: 2
      Hash: e36a086e3f822c23432fe2a3117d1a0a
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: VRCanvasInteractionFix
  platform: 13
  scenePathNames:
  - Assets/Scenes/StartMenu.unity
  - "Assets/Scenes/\u5C0FU\u548C\u7535\u673A.unity"
  playerPath: D:/nwu/Assembly/UnityProjects/VRAssembly/1.apk
