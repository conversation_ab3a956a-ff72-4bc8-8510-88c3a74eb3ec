using UnityEngine;
using UnityEngine.UI;
using UnityEngine.SceneManagement;
using TMPro;
using System.Collections;

/// <summary>
/// VR开始菜单管理器
/// 
/// 专门为PICO VR环境设计的开始界面管理系统
/// 提供用户名输入、场景切换和VR交互功能
/// </summary>
public class VRStartMenuManager : MonoBehaviour
{
    [Header("UI组件")]
    [SerializeField] private Canvas menuCanvas;
    [SerializeField] private TextMeshProUGUI titleText;
    [SerializeField] private TMP_InputField usernameInput;
    [SerializeField] private Button startButton;
    [SerializeField] private TextMeshProUGUI welcomeText;
    
    [Header("VR设置")]
    [SerializeField] private bool enableVRMode = true;
    [SerializeField] private Camera vrCamera;
    [SerializeField] private float uiDistance = 2.5f;
    [SerializeField] private float uiScale = 0.01f;
    [SerializeField] private Vector3 uiOffset = new Vector3(0, 0.2f, 0);
    
    [Header("场景设置")]
    [SerializeField] private string nextSceneName = "Animation";
    [SerializeField] private bool validateUsername = true;
    [SerializeField] private int minUsernameLength = 2;
    
    [Header("音效设置")]
    [SerializeField] private AudioSource audioSource;
    [SerializeField] private AudioClip buttonClickSound;
    [SerializeField] private AudioClip errorSound;
    
    [Header("VR UI组件")]
    private VRUIManager vrUIManager;
    private VRUIInteractor vrUIInteractor;
    private VRUILayoutOptimizer vrUIOptimizer;
    
    // 私有变量
    private bool isInitialized = false;
    private string currentUsername = "";
    private bool isTransitioning = false;
    
    // 事件
    public System.Action<string> OnUsernameChanged;
    public System.Action OnStartButtonClicked;
    public System.Action<string> OnSceneTransition;
    
    void Start()
    {
        StartCoroutine(InitializeStartMenu());
    }
    
    /// <summary>
    /// 初始化开始菜单
    /// </summary>
    private IEnumerator InitializeStartMenu()
    {
        Debug.Log("[VRStartMenuManager] 开始初始化VR开始菜单");
        
        // 等待一帧确保所有组件都已加载
        yield return null;
        
        // 查找或创建必要组件
        SetupComponents();
        
        // 配置VR UI系统
        if (enableVRMode)
        {
            SetupVRUI();
        }
        
        // 配置UI元素
        SetupUIElements();
        
        // 设置事件监听
        SetupEventListeners();
        
        // 优化VR布局
        if (enableVRMode && vrUIOptimizer != null)
        {
            vrUIOptimizer.OptimizeAllUIElements();
        }
        
        isInitialized = true;
        Debug.Log("[VRStartMenuManager] VR开始菜单初始化完成");
    }
    
    /// <summary>
    /// 设置组件
    /// </summary>
    private void SetupComponents()
    {
        // 查找摄像机
        if (vrCamera == null)
        {
            vrCamera = Camera.main;
            if (vrCamera == null)
            {
                vrCamera = FindObjectOfType<Camera>();
            }
        }
        
        // 查找Canvas
        if (menuCanvas == null)
        {
            menuCanvas = FindObjectOfType<Canvas>();
        }
        
        // 查找音频源
        if (audioSource == null)
        {
            audioSource = GetComponent<AudioSource>();
            if (audioSource == null)
            {
                audioSource = gameObject.AddComponent<AudioSource>();
            }
        }
    }
    
    /// <summary>
    /// 设置VR UI系统
    /// </summary>
    private void SetupVRUI()
    {
        if (menuCanvas == null)
        {
            Debug.LogError("[VRStartMenuManager] 找不到Canvas，无法设置VR UI");
            return;
        }
        
        // 配置Canvas为World Space
        menuCanvas.renderMode = RenderMode.WorldSpace;
        menuCanvas.worldCamera = vrCamera;
        
        // 添加VR UI管理器
        vrUIManager = menuCanvas.GetComponent<VRUIManager>();
        if (vrUIManager == null)
        {
            vrUIManager = menuCanvas.gameObject.AddComponent<VRUIManager>();
        }
        
        // 添加VR UI交互器
        vrUIInteractor = menuCanvas.GetComponent<VRUIInteractor>();
        if (vrUIInteractor == null)
        {
            vrUIInteractor = menuCanvas.gameObject.AddComponent<VRUIInteractor>();
        }
        
        // 添加VR UI布局优化器
        vrUIOptimizer = menuCanvas.GetComponent<VRUILayoutOptimizer>();
        if (vrUIOptimizer == null)
        {
            vrUIOptimizer = menuCanvas.gameObject.AddComponent<VRUILayoutOptimizer>();
        }
        
        // 添加TrackedDeviceGraphicRaycaster用于VR交互
        if (menuCanvas.GetComponent<UnityEngine.EventSystems.GraphicRaycaster>() == null)
        {
            menuCanvas.gameObject.AddComponent<UnityEngine.EventSystems.GraphicRaycaster>();
        }
        
        // 设置UI位置
        PositionUIForVR();
        
        Debug.Log("[VRStartMenuManager] VR UI系统设置完成");
    }
    
    /// <summary>
    /// 为VR定位UI
    /// </summary>
    private void PositionUIForVR()
    {
        if (menuCanvas == null || vrCamera == null) return;
        
        Vector3 cameraPosition = vrCamera.transform.position;
        Vector3 cameraForward = vrCamera.transform.forward;
        
        // 计算UI位置
        Vector3 targetPosition = cameraPosition + cameraForward * uiDistance + uiOffset;
        menuCanvas.transform.position = targetPosition;
        
        // 设置缩放
        menuCanvas.transform.localScale = Vector3.one * uiScale;
        
        // 面向用户
        menuCanvas.transform.LookAt(vrCamera.transform);
        menuCanvas.transform.Rotate(0, 180, 0); // 翻转让UI正面朝向用户
    }
    
    /// <summary>
    /// 设置UI元素
    /// </summary>
    private void SetupUIElements()
    {
        // 设置标题文本
        if (titleText != null)
        {
            titleText.text = "VR装配动画系统";
            titleText.fontSize = enableVRMode ? 48 : 36;
        }
        
        // 设置欢迎文本
        if (welcomeText != null)
        {
            welcomeText.text = "欢迎使用VR装配动画系统\n请输入您的用户名开始体验";
            welcomeText.fontSize = enableVRMode ? 24 : 18;
        }
        
        // 设置输入框
        if (usernameInput != null)
        {
            usernameInput.placeholder.GetComponent<TextMeshProUGUI>().text = "请输入用户名...";
            usernameInput.textComponent.fontSize = enableVRMode ? 20 : 16;
        }
        
        // 设置开始按钮
        if (startButton != null)
        {
            var buttonText = startButton.GetComponentInChildren<TextMeshProUGUI>();
            if (buttonText != null)
            {
                buttonText.text = "开始体验";
                buttonText.fontSize = enableVRMode ? 24 : 18;
            }
            
            // 初始状态禁用按钮
            startButton.interactable = false;
        }
    }
    
    /// <summary>
    /// 设置事件监听
    /// </summary>
    private void SetupEventListeners()
    {
        // 用户名输入事件
        if (usernameInput != null)
        {
            usernameInput.onValueChanged.AddListener(OnUsernameInputChanged);
        }
        
        // 开始按钮点击事件
        if (startButton != null)
        {
            startButton.onClick.AddListener(OnStartButtonPressed);
        }
    }
    
    /// <summary>
    /// 用户名输入改变事件
    /// </summary>
    private void OnUsernameInputChanged(string username)
    {
        currentUsername = username.Trim();
        
        // 验证用户名
        bool isValid = ValidateUsername(currentUsername);
        
        // 更新按钮状态
        if (startButton != null)
        {
            startButton.interactable = isValid;
        }
        
        // 触发事件
        OnUsernameChanged?.Invoke(currentUsername);
        
        Debug.Log($"[VRStartMenuManager] 用户名输入: {currentUsername}, 有效: {isValid}");
    }
    
    /// <summary>
    /// 验证用户名
    /// </summary>
    private bool ValidateUsername(string username)
    {
        if (!validateUsername) return true;
        
        return !string.IsNullOrEmpty(username) && username.Length >= minUsernameLength;
    }
    
    /// <summary>
    /// 开始按钮按下事件
    /// </summary>
    private void OnStartButtonPressed()
    {
        if (isTransitioning) return;
        
        Debug.Log($"[VRStartMenuManager] 开始按钮被点击，用户名: {currentUsername}");
        
        // 播放点击音效
        PlaySound(buttonClickSound);
        
        // 触发事件
        OnStartButtonClicked?.Invoke();
        
        // 开始场景切换
        StartCoroutine(TransitionToNextScene());
    }
    
    /// <summary>
    /// 切换到下一个场景
    /// </summary>
    private IEnumerator TransitionToNextScene()
    {
        isTransitioning = true;

        // 保存用户名到场景数据
        if (!string.IsNullOrEmpty(currentUsername))
        {
            VRSceneManager.Instance.SetSceneData("Username", currentUsername);
            VRSceneManager.Instance.SetSceneData("StartTime", System.DateTime.Now.ToString());
        }

        // 触发场景切换事件
        OnSceneTransition?.Invoke(nextSceneName);

        Debug.Log($"[VRStartMenuManager] 开始切换到场景: {nextSceneName}");

        // 可以在这里添加过渡动画
        yield return new WaitForSeconds(0.5f);

        // 使用VRSceneManager加载下一个场景
        VRSceneManager.Instance.LoadScene(nextSceneName);
    }
    
    /// <summary>
    /// 播放音效
    /// </summary>
    private void PlaySound(AudioClip clip)
    {
        if (audioSource != null && clip != null)
        {
            audioSource.PlayOneShot(clip);
        }
    }
    
    /// <summary>
    /// 获取当前用户名
    /// </summary>
    public string GetCurrentUsername()
    {
        return currentUsername;
    }
    
    /// <summary>
    /// 设置下一个场景名称
    /// </summary>
    public void SetNextSceneName(string sceneName)
    {
        nextSceneName = sceneName;
    }
    
    /// <summary>
    /// 重新定位UI到用户前方
    /// </summary>
    public void RepositionUI()
    {
        if (enableVRMode)
        {
            PositionUIForVR();
        }
    }
    
    void OnDestroy()
    {
        // 清理事件监听
        if (usernameInput != null)
        {
            usernameInput.onValueChanged.RemoveListener(OnUsernameInputChanged);
        }
        
        if (startButton != null)
        {
            startButton.onClick.RemoveListener(OnStartButtonPressed);
        }
    }
}
