using UnityEngine;
using UnityEngine.UI;
using UnityEngine.EventSystems;

#if UNITY_XR_INTERACTION_TOOLKIT
using UnityEngine.XR.Interaction.Toolkit;
using UnityEngine.XR.Interaction.Toolkit.UI;
#endif

/// <summary>
/// VR Canvas交互修复器
/// 
/// 解决VR环境中Canvas射线反馈和按钮点击问题
/// 确保只有按钮有射线反馈，Canvas背景没有反馈
/// </summary>
public class VRCanvasInteractionFix : MonoBehaviour
{
    [Header("调试设置")]
    [SerializeField] private bool enableDebugLogs = true;
    
    [Header("交互设置")]
    [SerializeField] private bool disableCanvasRaycastTarget = true;
    [SerializeField] private bool ensureButtonInteractable = true;
    [SerializeField] private bool addButtonEventTriggers = true;
    
    // 私有变量
    private Canvas targetCanvas;
    private Button[] allButtons;
    private Image[] allImages;
    
    void Start()
    {
        StartCoroutine(InitializeCanvasInteractionFix());
    }
    
    /// <summary>
    /// 初始化Canvas交互修复
    /// </summary>
    private System.Collections.IEnumerator InitializeCanvasInteractionFix()
    {
        // 等待一帧确保所有组件都已加载
        yield return null;
        
        if (enableDebugLogs)
            Debug.Log("[VRCanvasInteractionFix] 开始初始化Canvas交互修复");
        
        // 查找目标Canvas
        FindTargetCanvas();
        
        // 修复Canvas射线目标
        if (disableCanvasRaycastTarget)
        {
            FixCanvasRaycastTargets();
        }
        
        // 查找所有按钮
        FindAllButtons();
        
        // 确保按钮可交互
        if (ensureButtonInteractable)
        {
            EnsureButtonsInteractable();
        }
        
        // 添加按钮事件触发器
        if (addButtonEventTriggers)
        {
            AddButtonEventTriggers();
        }
        
        // 验证设置
        VerifyInteractionSetup();
        
        if (enableDebugLogs)
            Debug.Log("[VRCanvasInteractionFix] Canvas交互修复初始化完成");
    }
    
    /// <summary>
    /// 查找目标Canvas
    /// </summary>
    private void FindTargetCanvas()
    {
        // 首先尝试在当前GameObject上查找Canvas
        targetCanvas = GetComponent<Canvas>();
        
        // 如果没找到，查找场景中的任何Canvas
        if (targetCanvas == null)
        {
            targetCanvas = FindObjectOfType<Canvas>();
        }
        
        if (targetCanvas == null)
        {
            Debug.LogError("[VRCanvasInteractionFix] 场景中没有找到Canvas！");
            return;
        }
        
        if (enableDebugLogs)
            Debug.Log($"[VRCanvasInteractionFix] 找到目标Canvas: {targetCanvas.name}");
    }
    
    /// <summary>
    /// 修复Canvas射线目标
    /// </summary>
    private void FixCanvasRaycastTargets()
    {
        if (targetCanvas == null) return;
        
        // 查找所有Image组件
        allImages = targetCanvas.GetComponentsInChildren<Image>(true);
        
        foreach (var image in allImages)
        {
            if (image == null) continue;
            
            // 检查这个Image是否是按钮的一部分
            bool isButtonImage = image.GetComponent<Button>() != null;
            
            if (!isButtonImage)
            {
                // 如果不是按钮，禁用射线目标
                image.raycastTarget = false;
                
                if (enableDebugLogs)
                    Debug.Log($"[VRCanvasInteractionFix] 禁用非按钮Image的射线目标: {image.name}");
            }
            else
            {
                // 如果是按钮，确保射线目标启用
                image.raycastTarget = true;
                
                if (enableDebugLogs)
                    Debug.Log($"[VRCanvasInteractionFix] 确保按钮Image的射线目标启用: {image.name}");
            }
        }
        
        // 特别处理Canvas本身的Image组件（如果有的话）
        var canvasImage = targetCanvas.GetComponent<Image>();
        if (canvasImage != null)
        {
            canvasImage.raycastTarget = false;
            if (enableDebugLogs)
                Debug.Log("[VRCanvasInteractionFix] 禁用Canvas背景Image的射线目标");
        }
    }
    
    /// <summary>
    /// 查找所有按钮
    /// </summary>
    private void FindAllButtons()
    {
        if (targetCanvas == null) return;
        
        allButtons = targetCanvas.GetComponentsInChildren<Button>(true);
        
        if (enableDebugLogs)
            Debug.Log($"[VRCanvasInteractionFix] 找到 {allButtons.Length} 个按钮");
    }
    
    /// <summary>
    /// 确保按钮可交互
    /// </summary>
    private void EnsureButtonsInteractable()
    {
        if (allButtons == null) return;
        
        foreach (var button in allButtons)
        {
            if (button == null) continue;
            
            // 特殊处理：开始按钮可能需要在用户输入后才启用
            if (button.name.Contains("Start") || button.name.Contains("开始"))
            {
                if (enableDebugLogs)
                    Debug.Log($"[VRCanvasInteractionFix] 开始按钮保持当前状态: {button.name} (Interactable: {button.interactable})");
                continue;
            }
            
            // 其他按钮确保可交互
            if (!button.interactable)
            {
                button.interactable = true;
                if (enableDebugLogs)
                    Debug.Log($"[VRCanvasInteractionFix] 启用按钮交互: {button.name}");
            }
        }
    }
    
    /// <summary>
    /// 添加按钮事件触发器
    /// </summary>
    private void AddButtonEventTriggers()
    {
        if (allButtons == null) return;
        
        foreach (var button in allButtons)
        {
            if (button == null) continue;
            
            // 检查是否已经有EventTrigger
            var existingTrigger = button.GetComponent<EventTrigger>();
            if (existingTrigger != null)
            {
                if (enableDebugLogs)
                    Debug.Log($"[VRCanvasInteractionFix] 按钮已有EventTrigger: {button.name}");
                continue;
            }
            
            // 添加EventTrigger
            var eventTrigger = button.gameObject.AddComponent<EventTrigger>();
            
            // 添加指针进入事件（用于调试）
            var pointerEnter = new EventTrigger.Entry();
            pointerEnter.eventID = EventTriggerType.PointerEnter;
            pointerEnter.callback.AddListener((data) => {
                if (enableDebugLogs)
                    Debug.Log($"[VRCanvasInteractionFix] 射线进入按钮: {button.name}");
            });
            eventTrigger.triggers.Add(pointerEnter);
            
            // 添加指针退出事件（用于调试）
            var pointerExit = new EventTrigger.Entry();
            pointerExit.eventID = EventTriggerType.PointerExit;
            pointerExit.callback.AddListener((data) => {
                if (enableDebugLogs)
                    Debug.Log($"[VRCanvasInteractionFix] 射线离开按钮: {button.name}");
            });
            eventTrigger.triggers.Add(pointerExit);
            
            // 添加指针点击事件
            var pointerClick = new EventTrigger.Entry();
            pointerClick.eventID = EventTriggerType.PointerClick;
            pointerClick.callback.AddListener((data) => {
                if (enableDebugLogs)
                    Debug.Log($"[VRCanvasInteractionFix] 检测到按钮点击: {button.name}");
                
                // 确保按钮点击事件被触发
                if (button.interactable)
                {
                    button.onClick.Invoke();
                    if (enableDebugLogs)
                        Debug.Log($"[VRCanvasInteractionFix] 手动触发按钮点击事件: {button.name}");
                }
                else
                {
                    if (enableDebugLogs)
                        Debug.Log($"[VRCanvasInteractionFix] 按钮不可交互，跳过点击: {button.name}");
                }
            });
            eventTrigger.triggers.Add(pointerClick);
            
            if (enableDebugLogs)
                Debug.Log($"[VRCanvasInteractionFix] 为按钮添加EventTrigger: {button.name}");
        }
    }
    
    /// <summary>
    /// 验证交互设置
    /// </summary>
    private void VerifyInteractionSetup()
    {
        if (!enableDebugLogs) return;
        
        Debug.Log("=== VR Canvas交互修复验证 ===");
        
        // 检查Canvas
        if (targetCanvas != null)
        {
            Debug.Log($"Canvas: {targetCanvas.name}");
            
            // 检查Canvas背景Image
            var canvasImage = targetCanvas.GetComponent<Image>();
            if (canvasImage != null)
            {
                Debug.Log($"Canvas背景Image射线目标: {canvasImage.raycastTarget} (应该为false)");
            }
            
            // 检查Raycaster
            var raycaster = targetCanvas.GetComponent<GraphicRaycaster>();
            var trackedRaycaster = targetCanvas.GetComponent<TrackedDeviceGraphicRaycaster>();
            
            if (trackedRaycaster != null)
            {
                Debug.Log("射线投射器: TrackedDeviceGraphicRaycaster (适合VR)");
            }
            else if (raycaster != null)
            {
                Debug.Log("射线投射器: 标准GraphicRaycaster (可能在VR中效果不佳)");
            }
            else
            {
                Debug.LogWarning("射线投射器: 未找到！");
            }
        }
        
        // 检查按钮
        if (allButtons != null)
        {
            Debug.Log($"找到按钮数量: {allButtons.Length}");
            
            foreach (var button in allButtons)
            {
                if (button != null)
                {
                    var buttonImage = button.GetComponent<Image>();
                    var hasEventTrigger = button.GetComponent<EventTrigger>() != null;
                    
                    Debug.Log($"按钮 '{button.name}': 可交互={button.interactable}, 射线目标={buttonImage?.raycastTarget}, 有EventTrigger={hasEventTrigger}");
                }
            }
        }
        
        Debug.Log("=== 验证结束 ===");
    }
    
    /// <summary>
    /// 手动测试按钮点击（用于调试）
    /// </summary>
    [ContextMenu("测试按钮点击")]
    public void TestButtonClicks()
    {
        if (allButtons == null) return;
        
        foreach (var button in allButtons)
        {
            if (button != null && button.interactable)
            {
                Debug.Log($"[VRCanvasInteractionFix] 测试按钮: {button.name}");
                button.onClick.Invoke();
            }
        }
    }
    
    /// <summary>
    /// 重新应用修复（用于运行时调整）
    /// </summary>
    [ContextMenu("重新应用修复")]
    public void ReapplyFix()
    {
        StartCoroutine(InitializeCanvasInteractionFix());
    }
}
