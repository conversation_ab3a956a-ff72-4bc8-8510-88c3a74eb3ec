using UnityEngine;
using UnityEngine.UI;
using TMPro;
using System.Collections;

/// <summary>
/// VR开始菜单演示脚本
/// 
/// 用于在StartMenu场景中演示和测试VR开始界面功能
/// 提供完整的设置、测试和调试功能
/// </summary>
public class VRStartMenuDemo : MonoBehaviour
{
    [Header("演示设置")]
    [SerializeField] private bool autoSetup = true;
    [SerializeField] private bool enableDebugMode = true;
    [SerializeField] private bool showInstructions = true;
    
    [Header("测试配置")]
    [SerializeField] private bool simulateVRInput = false;
    [SerializeField] private string testUsername = "TestUser";
    [SerializeField] private float simulationDelay = 2f;
    
    [Header("UI引用")]
    [SerializeField] private Canvas instructionCanvas;
    [SerializeField] private TextMeshProUGUI instructionText;
    [SerializeField] private Button setupButton;
    [SerializeField] private Button testButton;
    
    // 私有变量
    private VRStartMenuSetupHelper setupHelper;
    private VRStartMenuManager menuManager;
    private VRStartMenuInputHandler inputHandler;
    private VRSceneManager sceneManager;
    private bool isSetupComplete = false;
    
    void Start()
    {
        StartCoroutine(InitializeDemo());
    }
    
    /// <summary>
    /// 初始化演示
    /// </summary>
    private IEnumerator InitializeDemo()
    {
        Debug.Log("[VRStartMenuDemo] 开始初始化演示");
        
        // 等待一帧确保所有组件都已加载
        yield return null;
        
        // 创建说明UI
        if (showInstructions)
        {
            CreateInstructionUI();
        }
        
        // 自动设置
        if (autoSetup)
        {
            yield return new WaitForSeconds(1f);
            SetupVRStartMenu();
        }
        
        // 设置事件监听
        SetupEventListeners();
        
        Debug.Log("[VRStartMenuDemo] 演示初始化完成");
    }
    
    /// <summary>
    /// 创建说明UI
    /// </summary>
    private void CreateInstructionUI()
    {
        // 创建说明Canvas
        GameObject canvasGO = new GameObject("Instruction Canvas");
        instructionCanvas = canvasGO.AddComponent<Canvas>();
        instructionCanvas.renderMode = RenderMode.ScreenSpaceOverlay;
        instructionCanvas.sortingOrder = 100;
        
        canvasGO.AddComponent<CanvasScaler>();
        canvasGO.AddComponent<GraphicRaycaster>();
        
        // 创建背景面板
        GameObject panelGO = new GameObject("Instruction Panel");
        panelGO.transform.SetParent(canvasGO.transform, false);
        
        Image panelImage = panelGO.AddComponent<Image>();
        panelImage.color = new Color(0, 0, 0, 0.7f);
        
        RectTransform panelRect = panelGO.GetComponent<RectTransform>();
        panelRect.anchorMin = Vector2.zero;
        panelRect.anchorMax = Vector2.one;
        panelRect.offsetMin = Vector2.zero;
        panelRect.offsetMax = Vector2.zero;
        
        // 创建说明文本
        GameObject textGO = new GameObject("Instruction Text");
        textGO.transform.SetParent(panelGO.transform, false);
        
        instructionText = textGO.AddComponent<TextMeshProUGUI>();
        instructionText.text = GetInstructionText();
        instructionText.fontSize = 18;
        instructionText.color = Color.white;
        instructionText.alignment = TextAlignmentOptions.Center;
        
        RectTransform textRect = textGO.GetComponent<RectTransform>();
        textRect.anchorMin = new Vector2(0.1f, 0.3f);
        textRect.anchorMax = new Vector2(0.9f, 0.8f);
        textRect.offsetMin = Vector2.zero;
        textRect.offsetMax = Vector2.zero;
        
        // 创建设置按钮
        CreateInstructionButton("设置VR菜单", new Vector2(0.3f, 0.2f), new Vector2(0.45f, 0.25f), SetupVRStartMenu);
        
        // 创建测试按钮
        CreateInstructionButton("测试功能", new Vector2(0.55f, 0.2f), new Vector2(0.7f, 0.25f), TestVRStartMenu);
        
        // 创建关闭按钮
        CreateInstructionButton("关闭说明", new Vector2(0.75f, 0.2f), new Vector2(0.9f, 0.25f), HideInstructions);
    }
    
    /// <summary>
    /// 创建说明按钮
    /// </summary>
    private void CreateInstructionButton(string text, Vector2 anchorMin, Vector2 anchorMax, System.Action action)
    {
        GameObject buttonGO = new GameObject($"Button_{text}");
        buttonGO.transform.SetParent(instructionCanvas.transform, false);
        
        Image buttonImage = buttonGO.AddComponent<Image>();
        buttonImage.color = new Color(0.2f, 0.6f, 1f, 1f);
        
        Button button = buttonGO.AddComponent<Button>();
        button.onClick.AddListener(() => action?.Invoke());
        
        RectTransform buttonRect = buttonGO.GetComponent<RectTransform>();
        buttonRect.anchorMin = anchorMin;
        buttonRect.anchorMax = anchorMax;
        buttonRect.offsetMin = Vector2.zero;
        buttonRect.offsetMax = Vector2.zero;
        
        // 添加按钮文本
        GameObject textGO = new GameObject("Text");
        textGO.transform.SetParent(buttonGO.transform, false);
        
        TextMeshProUGUI buttonText = textGO.AddComponent<TextMeshProUGUI>();
        buttonText.text = text;
        buttonText.fontSize = 14;
        buttonText.color = Color.white;
        buttonText.alignment = TextAlignmentOptions.Center;
        
        RectTransform textRect = textGO.GetComponent<RectTransform>();
        textRect.anchorMin = Vector2.zero;
        textRect.anchorMax = Vector2.one;
        textRect.offsetMin = Vector2.zero;
        textRect.offsetMax = Vector2.zero;
    }
    
    /// <summary>
    /// 获取说明文本
    /// </summary>
    private string GetInstructionText()
    {
        return @"VR开始菜单演示

这个演示展示了如何在PICO VR环境中创建和使用开始界面。

功能特性：
• VR兼容的UI布局和交互
• 控制器输入支持（触发器、按钮）
• 虚拟键盘用于文本输入
• 触觉反馈和音效
• 场景切换管理
• 用户数据保存

VR控制说明：
• 菜单按钮：显示/隐藏虚拟键盘
• 主按钮：确认输入
• 副按钮：删除字符
• 触发器：点击UI元素

使用步骤：
1. 点击'设置VR菜单'创建VR界面
2. 在VR中输入用户名
3. 点击开始按钮切换到主场景

注意：确保Animation场景已添加到Build Settings中";
    }
    
    /// <summary>
    /// 设置VR开始菜单
    /// </summary>
    public void SetupVRStartMenu()
    {
        Debug.Log("[VRStartMenuDemo] 开始设置VR开始菜单");
        
        // 创建设置助手
        if (setupHelper == null)
        {
            GameObject helperGO = new GameObject("VRStartMenuSetupHelper");
            setupHelper = helperGO.AddComponent<VRStartMenuSetupHelper>();
        }
        
        // 创建VR菜单
        setupHelper.CreateVRStartMenu();
        
        // 获取组件引用
        menuManager = FindObjectOfType<VRStartMenuManager>();
        inputHandler = FindObjectOfType<VRStartMenuInputHandler>();
        
        // 确保场景管理器存在
        if (sceneManager == null)
        {
            sceneManager = VRSceneManager.Instance;
        }
        
        // 设置事件监听
        SetupVRMenuEvents();
        
        isSetupComplete = true;
        
        // 更新说明文本
        if (instructionText != null)
        {
            instructionText.text = "VR开始菜单设置完成！\n\n现在您可以：\n• 使用VR控制器与界面交互\n• 输入用户名\n• 点击开始按钮切换场景\n\n或点击'测试功能'进行自动测试";
        }
        
        Debug.Log("[VRStartMenuDemo] VR开始菜单设置完成");
    }
    
    /// <summary>
    /// 设置VR菜单事件
    /// </summary>
    private void SetupVRMenuEvents()
    {
        if (menuManager != null)
        {
            menuManager.OnUsernameChanged += OnUsernameChanged;
            menuManager.OnStartButtonClicked += OnStartButtonClicked;
            menuManager.OnSceneTransition += OnSceneTransition;
        }
        
        if (inputHandler != null)
        {
            inputHandler.OnInputChanged += OnInputChanged;
            inputHandler.OnInputConfirmed += OnInputConfirmed;
        }
        
        if (sceneManager != null)
        {
            sceneManager.OnSceneLoadStarted += OnSceneLoadStarted;
            sceneManager.OnSceneLoadProgress += OnSceneLoadProgress;
            sceneManager.OnSceneLoadCompleted += OnSceneLoadCompleted;
            sceneManager.OnSceneLoadFailed += OnSceneLoadFailed;
        }
    }
    
    /// <summary>
    /// 测试VR开始菜单
    /// </summary>
    public void TestVRStartMenu()
    {
        if (!isSetupComplete)
        {
            SetupVRStartMenu();
        }
        
        if (simulateVRInput)
        {
            StartCoroutine(SimulateVRInput());
        }
        else
        {
            Debug.Log("[VRStartMenuDemo] 请在VR中手动测试功能");
        }
    }
    
    /// <summary>
    /// 模拟VR输入
    /// </summary>
    private IEnumerator SimulateVRInput()
    {
        Debug.Log("[VRStartMenuDemo] 开始模拟VR输入");
        
        yield return new WaitForSeconds(simulationDelay);
        
        // 模拟输入用户名
        if (menuManager != null)
        {
            var inputField = FindObjectOfType<TMP_InputField>();
            if (inputField != null)
            {
                inputField.text = testUsername;
                Debug.Log($"[VRStartMenuDemo] 模拟输入用户名: {testUsername}");
            }
        }
        
        yield return new WaitForSeconds(simulationDelay);
        
        // 模拟点击开始按钮
        var startButton = FindObjectOfType<Button>();
        if (startButton != null && startButton.interactable)
        {
            startButton.onClick.Invoke();
            Debug.Log("[VRStartMenuDemo] 模拟点击开始按钮");
        }
    }
    
    /// <summary>
    /// 隐藏说明
    /// </summary>
    public void HideInstructions()
    {
        if (instructionCanvas != null)
        {
            instructionCanvas.gameObject.SetActive(false);
        }
    }
    
    /// <summary>
    /// 显示说明
    /// </summary>
    public void ShowInstructions()
    {
        if (instructionCanvas != null)
        {
            instructionCanvas.gameObject.SetActive(true);
        }
    }
    
    /// <summary>
    /// 设置事件监听
    /// </summary>
    private void SetupEventListeners()
    {
        // 键盘快捷键
        if (enableDebugMode)
        {
            Debug.Log("[VRStartMenuDemo] 调试模式已启用");
            Debug.Log("快捷键: F1=显示说明, F2=设置菜单, F3=测试功能");
        }
    }
    
    void Update()
    {
        // 调试快捷键
        if (enableDebugMode)
        {
            if (Input.GetKeyDown(KeyCode.F1))
            {
                ShowInstructions();
            }
            else if (Input.GetKeyDown(KeyCode.F2))
            {
                SetupVRStartMenu();
            }
            else if (Input.GetKeyDown(KeyCode.F3))
            {
                TestVRStartMenu();
            }
        }
    }
    
    // 事件处理方法
    private void OnUsernameChanged(string username)
    {
        Debug.Log($"[VRStartMenuDemo] 用户名改变: {username}");
    }
    
    private void OnStartButtonClicked()
    {
        Debug.Log("[VRStartMenuDemo] 开始按钮被点击");
    }
    
    private void OnSceneTransition(string sceneName)
    {
        Debug.Log($"[VRStartMenuDemo] 场景切换: {sceneName}");
    }
    
    private void OnInputChanged(string input)
    {
        Debug.Log($"[VRStartMenuDemo] 输入改变: {input}");
    }
    
    private void OnInputConfirmed(string input)
    {
        Debug.Log($"[VRStartMenuDemo] 输入确认: {input}");
    }
    
    private void OnSceneLoadStarted(string sceneName)
    {
        Debug.Log($"[VRStartMenuDemo] 场景加载开始: {sceneName}");
    }
    
    private void OnSceneLoadProgress(string sceneName, float progress)
    {
        Debug.Log($"[VRStartMenuDemo] 场景加载进度: {sceneName} - {progress:P}");
    }
    
    private void OnSceneLoadCompleted(string sceneName)
    {
        Debug.Log($"[VRStartMenuDemo] 场景加载完成: {sceneName}");
    }
    
    private void OnSceneLoadFailed(string sceneName)
    {
        Debug.LogError($"[VRStartMenuDemo] 场景加载失败: {sceneName}");
    }
    
    void OnDestroy()
    {
        // 清理事件监听
        if (menuManager != null)
        {
            menuManager.OnUsernameChanged -= OnUsernameChanged;
            menuManager.OnStartButtonClicked -= OnStartButtonClicked;
            menuManager.OnSceneTransition -= OnSceneTransition;
        }
        
        if (inputHandler != null)
        {
            inputHandler.OnInputChanged -= OnInputChanged;
            inputHandler.OnInputConfirmed -= OnInputConfirmed;
        }
    }
}
