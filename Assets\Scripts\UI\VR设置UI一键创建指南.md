# VR设置UI一键创建指南

## 🚀 快速开始（推荐方式）

### 方法1：使用Unity菜单（最简单）

1. **打开Unity编辑器**
2. **点击顶部菜单**：`VR Assembly` → `Create VR Settings UI`
3. **等待创建完成**，会弹出确认对话框
4. **完成！** 设置UI已自动创建并配置

### 方法2：使用脚本组件

1. **在场景中创建空GameObject**
2. **添加组件**：`VRSettingsUISetupHelper`
3. **在Inspector中右键点击组件** → `创建VR设置UI`
4. **完成！**

## 🎨 UI风格特点

### 完全匹配开始场景风格
- ✅ **相同的颜色方案**：深蓝色主题，白色文字
- ✅ **相同的按钮样式**：圆角按钮，悬停效果
- ✅ **相同的字体大小**：标题36px，按钮24px，正文20px
- ✅ **相同的VR配置**：距离2.5米，缩放0.01，面向用户

### 自动创建的UI元素
- 🎯 **设置按钮**：固定在左上角，带齿轮图标
- 📱 **设置面板**：居中弹出，半透明背景
- ❌ **关闭按钮**：面板右上角的"×"按钮
- 🔧 **示例功能按钮**：可扩展的功能按钮

## 🔧 自定义配置

### 在VRSettingsUISetupHelper中可调整：

```csharp
[Header("UI风格配置")]
backgroundColor = (0.1, 0.1, 0.2, 0.9)     // 背景颜色
primaryColor = (0.2, 0.6, 1, 1)            // 主色调
textColor = 白色                            // 文字颜色
buttonNormalColor = (0.2, 0.4, 0.8, 1)     // 按钮正常颜色

[Header("VR配置")]
uiDistance = 2.5f                           // UI距离
uiScale = 0.01f                             // UI缩放
settingsButtonOffset = (-0.8, 0.4, 0)      // 设置按钮偏移

[Header("字体大小")]
titleFontSize = 36                          // 标题字体大小
buttonFontSize = 24                         // 按钮字体大小
normalTextFontSize = 20                     // 正文字体大小
```

### 添加齿轮图标：

1. **导入齿轮图片**到项目中
2. **设置图片类型**为`Sprite (2D and UI)`
3. **在VRSettingsUISetupHelper中**拖入`Gear Icon Sprite`字段
4. **重新创建UI**或手动替换图标

## 📋 创建后的结构

```
VRSettingsCanvas
├── SettingsButton (左上角齿轮按钮)
│   └── GearIcon (齿轮图标)
└── SettingsPanel (设置面板，初始隐藏)
    ├── TitleText ("设置"标题)
    ├── CloseButton (关闭按钮)
    └── ExampleButton (示例功能按钮)
```

## 🎮 使用方式

### 自动功能：
- ✅ **点击齿轮按钮** → 打开设置面板
- ✅ **点击关闭按钮** → 关闭设置面板
- ✅ **VR控制器交互** → 自动支持射线点击
- ✅ **跟随头部移动** → UI始终在合适位置

### 扩展功能：
```csharp
// 获取VRSettingsUI组件
VRSettingsUI settingsUI = FindObjectOfType<VRSettingsUI>();

// 程序化控制
settingsUI.OpenSettingsPanel();    // 打开面板
settingsUI.CloseSettingsPanel();   // 关闭面板
settingsUI.ToggleSettingsPanel();  // 切换面板状态

// 事件监听
settingsUI.OnSettingsOpened += () => Debug.Log("设置面板打开");
settingsUI.OnSettingsClosed += () => Debug.Log("设置面板关闭");
```

## 🔧 添加更多功能按钮

### 在设置面板中添加新按钮：

1. **找到SettingsPanel**
2. **复制ExampleButton**
3. **修改按钮文本和位置**
4. **在代码中添加点击事件**：

```csharp
// 在VRSettingsUI的SetupEventListeners方法中添加
Button newButton = settingsPanel.transform.Find("NewButton").GetComponent<Button>();
newButton.onClick.AddListener(() => {
    Debug.Log("新功能被点击");
    // 添加你的功能代码
});
```

## 🎯 与摄像机记录器集成

### 添加记录控制按钮：

```csharp
// 在设置面板中添加记录控制
Button startRecordButton = CreateButton("开始记录");
Button stopRecordButton = CreateButton("停止记录");
Button saveDataButton = CreateButton("保存数据");

// 绑定到VRCameraDataRecorder
VRCameraDataRecorder recorder = FindObjectOfType<VRCameraDataRecorder>();
startRecordButton.onClick.AddListener(() => recorder.StartRecording());
stopRecordButton.onClick.AddListener(() => recorder.StopRecording());
saveDataButton.onClick.AddListener(() => recorder.SaveDataToJson());
```

## ⚠️ 注意事项

1. **确保场景中有摄像机**：脚本会自动查找主摄像机
2. **EventSystem会自动创建**：如果场景中没有的话
3. **Canvas使用World Space**：适合VR环境
4. **UI层级设置为10**：确保在其他UI之上
5. **初始面板是隐藏的**：点击设置按钮才显示

## 🐛 常见问题

**Q: 创建后看不到设置按钮？**
A: 检查摄像机位置，UI可能在摄像机后面。调整uiDistance参数。

**Q: 点击没有反应？**
A: 确保场景中有EventSystem，脚本会自动创建。

**Q: 齿轮图标显示不正确？**
A: 检查图片导入设置，确保类型为Sprite (2D and UI)。

**Q: UI太大或太小？**
A: 调整uiScale参数，默认0.01适合大多数情况。

**Q: 想要修改颜色？**
A: 在VRSettingsUISetupHelper中修改颜色参数，然后重新创建UI。

## 🎉 完成！

现在您有了一个完全匹配开始场景风格的VR设置UI！

- 🎯 **左上角齿轮按钮**：始终可见，易于访问
- 📱 **弹出式设置面板**：美观的动画效果
- 🎨 **统一的视觉风格**：与开始场景完美匹配
- 🎮 **完整的VR支持**：控制器交互，头部跟随
- 🔧 **易于扩展**：添加更多功能按钮
