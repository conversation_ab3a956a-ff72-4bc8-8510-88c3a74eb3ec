using UnityEngine;
using SimpleJSON;
using System.IO;
using System.Collections;
using System.Collections.Generic;
using System;

/// <summary>
/// VR摄像机数据记录器
/// 用于获取PICO头显摄像机的旋转矩阵数据并输出为JSON格式
/// </summary>
public class VRCameraDataRecorder : MonoBehaviour
{
    [Header("记录设置")]
    [SerializeField] private bool enableRecording = false;
    [SerializeField] private float recordingInterval = 0.1f; // 记录间隔（秒）
    [SerializeField] private int maxRecords = 1000; // 最大记录数量
    [SerializeField] private bool autoSaveOnStop = true; // 停止时自动保存
    
    [Header("输出设置")]
    [SerializeField] private string outputFileName = "VRCameraData";
    [SerializeField] private bool includeTimestamp = true;
    [SerializeField] private bool prettyPrintJson = true;
    [SerializeField] private bool saveToStreamingAssets = false; // PICO设备建议使用PersistentDataPath
    [SerializeField] private bool enableExternalStorage = true; // 尝试保存到外部存储
    
    [Header("数据选项")]
    [SerializeField] private bool recordPosition = true;
    [SerializeField] private bool recordRotation = true;
    [SerializeField] private bool recordRotationMatrix = true;
    [SerializeField] private bool recordEulerAngles = true;
    [SerializeField] private bool recordQuaternion = true;
    [SerializeField] private bool recordForwardVector = true;
    [SerializeField] private bool recordUpVector = true;
    [SerializeField] private bool recordRightVector = true;
    
    [Header("快捷键设置")]
    [SerializeField] private KeyCode startRecordingKey = KeyCode.F2;
    [SerializeField] private KeyCode stopRecordingKey = KeyCode.F3;
    [SerializeField] private KeyCode saveDataKey = KeyCode.F4;
    [SerializeField] private KeyCode clearDataKey = KeyCode.F5;
    
    [Header("调试设置")]
    [SerializeField] private bool showDebugInfo = true;
    [SerializeField] private bool logToConsole = true;
    
    // 内部变量
    private Camera vrCamera;
    private VRSimulator vrSimulator;
    private List<CameraDataRecord> recordedData = new List<CameraDataRecord>();
    private Coroutine recordingCoroutine;
    private bool isRecording = false;
    private float recordingStartTime;
    private string currentUsername = ""; // 当前用户名
    
    // 数据记录结构
    [System.Serializable]
    public class CameraDataRecord
    {
        public float timestamp;
        public Vector3Data position;
        public Vector3Data eulerAngles;
        public QuaternionData rotation;
        public Matrix4x4Data rotationMatrix;
        public Vector3Data forwardVector;
        public Vector3Data upVector;
        public Vector3Data rightVector;
        public string deviceInfo;
        public string username; // 添加用户名字段
    }
    
    [System.Serializable]
    public class Vector3Data
    {
        public float x, y, z;
        
        public Vector3Data(Vector3 vector)
        {
            x = vector.x;
            y = vector.y;
            z = vector.z;
        }
    }
    
    [System.Serializable]
    public class QuaternionData
    {
        public float x, y, z, w;
        
        public QuaternionData(Quaternion quaternion)
        {
            x = quaternion.x;
            y = quaternion.y;
            z = quaternion.z;
            w = quaternion.w;
        }
    }
    
    [System.Serializable]
    public class Matrix4x4Data
    {
        public float[] matrix = new float[16];
        
        public Matrix4x4Data(Matrix4x4 matrix4x4)
        {
            for (int i = 0; i < 16; i++)
            {
                matrix[i] = matrix4x4[i];
            }
        }
    }
    
    void Start()
    {
        InitializeRecorder();
        
        if (showDebugInfo)
        {
            Debug.Log("[VRCameraDataRecorder] 摄像机数据记录器已初始化");
            LogControlInstructions();
        }
    }
    
    void Update()
    {
        HandleInput();
        
        if (showDebugInfo && isRecording)
        {
            UpdateDebugDisplay();
        }
    }
    
    /// <summary>
    /// 初始化记录器
    /// </summary>
    private void InitializeRecorder()
    {
        // 查找VR摄像机
        FindVRCamera();

        // 获取用户名信息
        GetUsernameFromSceneManager();

        // 确保输出目录存在
        EnsureOutputDirectory();

        if (vrCamera == null)
        {
            Debug.LogError("[VRCameraDataRecorder] ❌ 未找到VR摄像机，无法进行记录");
        }
        else
        {
            Debug.Log($"[VRCameraDataRecorder] ✅ 找到VR摄像机: {vrCamera.name}");
        }

        if (!string.IsNullOrEmpty(currentUsername))
        {
            Debug.Log($"[VRCameraDataRecorder] ✅ 获取到用户名: {currentUsername}");
        }
        else
        {
            Debug.LogWarning("[VRCameraDataRecorder] ⚠️ 未获取到用户名，将使用默认值");
        }
    }

    /// <summary>
    /// 从场景管理器获取用户名
    /// </summary>
    private void GetUsernameFromSceneManager()
    {
        try
        {
            // 尝试从VRSceneManager获取用户名
            if (VRSceneManager.Instance != null)
            {
                currentUsername = VRSceneManager.Instance.GetSceneData<string>("Username", "");

                if (!string.IsNullOrEmpty(currentUsername))
                {
                    Debug.Log($"[VRCameraDataRecorder] 从VRSceneManager获取用户名: {currentUsername}");
                    return;
                }
            }

            // 尝试从PlayerPrefs获取用户名（备用方案）
            currentUsername = PlayerPrefs.GetString("Username", "");

            if (!string.IsNullOrEmpty(currentUsername))
            {
                Debug.Log($"[VRCameraDataRecorder] 从PlayerPrefs获取用户名: {currentUsername}");
                return;
            }

            // 如果都没有，使用默认用户名
            currentUsername = "UnknownUser";
            Debug.LogWarning("[VRCameraDataRecorder] 未找到用户名，使用默认值: UnknownUser");
        }
        catch (System.Exception e)
        {
            Debug.LogError($"[VRCameraDataRecorder] 获取用户名时出错: {e.Message}");
            currentUsername = "ErrorUser";
        }
    }

    /// <summary>
    /// 手动设置用户名
    /// </summary>
    /// <param name="username">用户名</param>
    public void SetUsername(string username)
    {
        if (!string.IsNullOrEmpty(username))
        {
            currentUsername = username;
            Debug.Log($"[VRCameraDataRecorder] 手动设置用户名: {currentUsername}");
        }
        else
        {
            Debug.LogWarning("[VRCameraDataRecorder] 尝试设置空用户名，操作被忽略");
        }
    }

    /// <summary>
    /// 获取当前用户名
    /// </summary>
    /// <returns>当前用户名</returns>
    public string GetCurrentUsername()
    {
        return currentUsername;
    }
    
    /// <summary>
    /// 查找VR摄像机
    /// </summary>
    private void FindVRCamera()
    {
        // 优先查找VR模拟器
        vrSimulator = FindObjectOfType<VRSimulator>();
        if (vrSimulator != null && Application.isEditor)
        {
            var method = vrSimulator.GetType().GetMethod("GetVRCamera");
            if (method != null)
            {
                vrCamera = method.Invoke(vrSimulator, null) as Camera;
                if (vrCamera != null)
                {
                    Debug.Log("[VRCameraDataRecorder] 使用VR模拟器摄像机");
                    return;
                }
            }
        }
        
        // 查找主摄像机
        vrCamera = Camera.main;
        if (vrCamera != null)
        {
            Debug.Log("[VRCameraDataRecorder] 使用主摄像机");
        }
        
        // 如果还没找到，查找所有摄像机
        if (vrCamera == null)
        {
            Camera[] cameras = FindObjectsOfType<Camera>();
            if (cameras.Length > 0)
            {
                vrCamera = cameras[0];
                Debug.Log($"[VRCameraDataRecorder] 使用第一个找到的摄像机: {vrCamera.name}");
            }
        }
    }
    
    /// <summary>
    /// 确保输出目录存在
    /// </summary>
    private void EnsureOutputDirectory()
    {
        string outputPath = GetOutputPath();
        string directory = Path.GetDirectoryName(outputPath);
        
        if (!Directory.Exists(directory))
        {
            Directory.CreateDirectory(directory);
            Debug.Log($"[VRCameraDataRecorder] 创建输出目录: {directory}");
        }
    }
    
    /// <summary>
    /// 获取输出路径（PICO设备优化版本）
    /// </summary>
    private string GetOutputPath()
    {
        string fileName = outputFileName;

        if (includeTimestamp)
        {
            string timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");
            fileName += "_" + timestamp;
        }

        fileName += ".json";

        // PICO设备文件存储策略
        if (Application.platform == RuntimePlatform.Android)
        {
            return GetAndroidOptimizedPath(fileName);
        }
        else if (saveToStreamingAssets)
        {
            // 编辑器模式使用StreamingAssets
            string streamingAssetsPath = Path.Combine(Application.streamingAssetsPath, "VRCameraData");
            if (!Directory.Exists(streamingAssetsPath))
            {
                Directory.CreateDirectory(streamingAssetsPath);
            }
            return Path.Combine(streamingAssetsPath, fileName);
        }
        else
        {
            return Path.Combine(Application.persistentDataPath, fileName);
        }
    }

    /// <summary>
    /// 获取Android设备优化的文件路径
    /// </summary>
    private string GetAndroidOptimizedPath(string fileName)
    {
        string basePath;

        if (enableExternalStorage)
        {
            // 尝试使用外部存储（如果可用）
            try
            {
                // 获取外部存储路径
                string externalPath = GetExternalStoragePath();
                if (!string.IsNullOrEmpty(externalPath))
                {
                    basePath = Path.Combine(externalPath, "VRCameraData");
                    if (logToConsole)
                    {
                        Debug.Log($"[VRCameraDataRecorder] 使用外部存储路径: {basePath}");
                    }
                }
                else
                {
                    basePath = Path.Combine(Application.persistentDataPath, "VRCameraData");
                    if (logToConsole)
                    {
                        Debug.Log($"[VRCameraDataRecorder] 外部存储不可用，使用内部存储: {basePath}");
                    }
                }
            }
            catch (Exception e)
            {
                Debug.LogWarning($"[VRCameraDataRecorder] 外部存储访问失败: {e.Message}");
                basePath = Path.Combine(Application.persistentDataPath, "VRCameraData");
            }
        }
        else
        {
            // 使用应用内部存储
            basePath = Path.Combine(Application.persistentDataPath, "VRCameraData");
            if (logToConsole)
            {
                Debug.Log($"[VRCameraDataRecorder] 使用应用内部存储: {basePath}");
            }
        }

        // 确保目录存在
        if (!Directory.Exists(basePath))
        {
            Directory.CreateDirectory(basePath);
        }

        return Path.Combine(basePath, fileName);
    }

    /// <summary>
    /// 获取外部存储路径（Android专用）
    /// </summary>
    private string GetExternalStoragePath()
    {
        try
        {
            // 尝试获取Android外部存储路径
            using (AndroidJavaClass unityPlayer = new AndroidJavaClass("com.unity3d.player.UnityPlayer"))
            using (AndroidJavaObject currentActivity = unityPlayer.GetStatic<AndroidJavaObject>("currentActivity"))
            using (AndroidJavaObject externalFilesDir = currentActivity.Call<AndroidJavaObject>("getExternalFilesDir", (string)null))
            {
                if (externalFilesDir != null)
                {
                    return externalFilesDir.Call<string>("getAbsolutePath");
                }
            }
        }
        catch (Exception e)
        {
            Debug.LogWarning($"[VRCameraDataRecorder] 获取外部存储路径失败: {e.Message}");
        }

        return null;
    }
    
    /// <summary>
    /// 记录控制说明
    /// </summary>
    private void LogControlInstructions()
    {
        Debug.Log("=== VR摄像机数据记录器控制说明 ===");
        Debug.Log($"F2键: 开始记录");
        Debug.Log($"F3键: 停止记录");
        Debug.Log($"F4键: 保存数据到JSON文件");
        Debug.Log($"F5键: 清空已记录的数据");
        Debug.Log("=====================================");
    }
    
    /// <summary>
    /// 处理输入
    /// </summary>
    private void HandleInput()
    {
        if (Input.GetKeyDown(startRecordingKey))
        {
            StartRecording();
        }
        
        if (Input.GetKeyDown(stopRecordingKey))
        {
            StopRecording();
        }
        
        if (Input.GetKeyDown(saveDataKey))
        {
            SaveDataToJson();
        }
        
        if (Input.GetKeyDown(clearDataKey))
        {
            ClearRecordedData();
        }
    }
    
    /// <summary>
    /// 更新调试显示
    /// </summary>
    private void UpdateDebugDisplay()
    {
        // 每秒更新一次调试信息
        if (Time.frameCount % 60 == 0)
        {
            float recordingTime = Time.time - recordingStartTime;
            Debug.Log($"[VRCameraDataRecorder] 记录中... 时间: {recordingTime:F1}s, 记录数: {recordedData.Count}");
        }
    }

    /// <summary>
    /// 开始记录
    /// </summary>
    [ContextMenu("开始记录")]
    public void StartRecording()
    {
        if (isRecording)
        {
            Debug.LogWarning("[VRCameraDataRecorder] 已经在记录中");
            return;
        }

        if (vrCamera == null)
        {
            Debug.LogError("[VRCameraDataRecorder] ❌ VR摄像机未找到，无法开始记录");
            return;
        }

        isRecording = true;
        recordingStartTime = Time.time;
        enableRecording = true;

        recordingCoroutine = StartCoroutine(RecordingLoop());

        Debug.Log("[VRCameraDataRecorder] ✅ 开始记录VR摄像机数据");

        if (logToConsole)
        {
            Debug.Log($"记录间隔: {recordingInterval}秒");
            Debug.Log($"最大记录数: {maxRecords}");
            Debug.Log($"当前摄像机: {vrCamera.name}");
        }
    }

    /// <summary>
    /// 停止记录
    /// </summary>
    [ContextMenu("停止记录")]
    public void StopRecording()
    {
        if (!isRecording)
        {
            Debug.LogWarning("[VRCameraDataRecorder] 当前没有在记录");
            return;
        }

        isRecording = false;
        enableRecording = false;

        if (recordingCoroutine != null)
        {
            StopCoroutine(recordingCoroutine);
            recordingCoroutine = null;
        }

        float recordingTime = Time.time - recordingStartTime;
        Debug.Log($"[VRCameraDataRecorder] ⏹️ 停止记录，总时间: {recordingTime:F1}秒，记录数: {recordedData.Count}");

        if (autoSaveOnStop && recordedData.Count > 0)
        {
            SaveDataToJson();
        }
    }

    /// <summary>
    /// 记录循环协程
    /// </summary>
    private IEnumerator RecordingLoop()
    {
        while (isRecording && recordedData.Count < maxRecords)
        {
            RecordCurrentCameraData();
            yield return new WaitForSeconds(recordingInterval);
        }

        if (recordedData.Count >= maxRecords)
        {
            Debug.LogWarning($"[VRCameraDataRecorder] 达到最大记录数 {maxRecords}，自动停止记录");
            StopRecording();
        }
    }

    /// <summary>
    /// 记录当前摄像机数据
    /// </summary>
    private void RecordCurrentCameraData()
    {
        if (vrCamera == null) return;

        Transform cameraTransform = vrCamera.transform;

        var record = new CameraDataRecord
        {
            timestamp = Time.time - recordingStartTime,
            deviceInfo = GetDeviceInfo(),
            username = currentUsername // 添加用户名信息
        };

        // 记录位置
        if (recordPosition)
        {
            record.position = new Vector3Data(cameraTransform.position);
        }

        // 记录欧拉角
        if (recordEulerAngles)
        {
            record.eulerAngles = new Vector3Data(cameraTransform.eulerAngles);
        }

        // 记录四元数
        if (recordQuaternion)
        {
            record.rotation = new QuaternionData(cameraTransform.rotation);
        }

        // 记录旋转矩阵
        if (recordRotationMatrix)
        {
            Matrix4x4 rotationMatrix = Matrix4x4.Rotate(cameraTransform.rotation);
            record.rotationMatrix = new Matrix4x4Data(rotationMatrix);
        }

        // 记录方向向量
        if (recordForwardVector)
        {
            record.forwardVector = new Vector3Data(cameraTransform.forward);
        }

        if (recordUpVector)
        {
            record.upVector = new Vector3Data(cameraTransform.up);
        }

        if (recordRightVector)
        {
            record.rightVector = new Vector3Data(cameraTransform.right);
        }

        recordedData.Add(record);

        if (logToConsole && recordedData.Count % 10 == 0)
        {
            Debug.Log($"[VRCameraDataRecorder] 已记录 {recordedData.Count} 条数据");
        }
    }

    /// <summary>
    /// 获取设备信息
    /// </summary>
    private string GetDeviceInfo()
    {
        string deviceInfo = "Unknown";

        // 检查是否在PICO设备上运行
        if (Application.platform == RuntimePlatform.Android)
        {
            deviceInfo = "PICO_Android";
        }
        else if (Application.isEditor)
        {
            if (vrSimulator != null)
            {
                deviceInfo = "Unity_Editor_VRSimulator";
            }
            else
            {
                deviceInfo = "Unity_Editor";
            }
        }
        else
        {
            deviceInfo = Application.platform.ToString();
        }

        return deviceInfo;
    }

    /// <summary>
    /// 保存数据到JSON文件
    /// </summary>
    [ContextMenu("保存数据到JSON")]
    public void SaveDataToJson()
    {
        if (recordedData.Count == 0)
        {
            Debug.LogWarning("[VRCameraDataRecorder] 没有数据可保存");
            return;
        }

        try
        {
            string outputPath = GetOutputPath();

            // 创建JSON对象
            var jsonData = CreateJsonData();

            // 转换为JSON字符串
            string jsonString;
            if (prettyPrintJson)
            {
                jsonString = jsonData.ToString(4); // 4个空格缩进
            }
            else
            {
                jsonString = jsonData.ToString();
            }

            // 写入文件
            File.WriteAllText(outputPath, jsonString);

            Debug.Log($"[VRCameraDataRecorder] ✅ 数据已保存到: {outputPath}");
            Debug.Log($"记录数量: {recordedData.Count}");
            Debug.Log($"文件大小: {new FileInfo(outputPath).Length / 1024f:F1} KB");

            // 显示如何访问文件的说明
            LogFileAccessInstructions(outputPath);

            if (logToConsole)
            {
                LogDataSummary();
            }
        }
        catch (Exception e)
        {
            Debug.LogError($"[VRCameraDataRecorder] ❌ 保存失败: {e.Message}");
        }
    }

    /// <summary>
    /// 创建JSON数据
    /// </summary>
    private JSONNode CreateJsonData()
    {
        var jsonRoot = new JSONObject();

        // 添加元数据
        var metadata = new JSONObject();
        metadata["username"] = currentUsername; // 添加用户名到元数据
        metadata["recordingStartTime"] = recordingStartTime.ToString("yyyy-MM-dd HH:mm:ss");
        metadata["recordingDuration"] = (Time.time - recordingStartTime).ToString("F2");
        metadata["recordCount"] = recordedData.Count;
        metadata["recordingInterval"] = recordingInterval;
        metadata["deviceInfo"] = GetDeviceInfo();
        metadata["unityVersion"] = Application.unityVersion;
        metadata["platform"] = Application.platform.ToString();

        jsonRoot["metadata"] = metadata;

        // 添加记录数据
        var dataArray = new JSONArray();

        foreach (var record in recordedData)
        {
            var recordJson = new JSONObject();
            recordJson["timestamp"] = record.timestamp;
            recordJson["username"] = record.username; // 添加用户名到每条记录
            recordJson["deviceInfo"] = record.deviceInfo;

            if (recordPosition && record.position != null)
            {
                var posJson = new JSONObject();
                posJson["x"] = record.position.x;
                posJson["y"] = record.position.y;
                posJson["z"] = record.position.z;
                recordJson["position"] = posJson;
            }

            if (recordEulerAngles && record.eulerAngles != null)
            {
                var eulerJson = new JSONObject();
                eulerJson["x"] = record.eulerAngles.x;
                eulerJson["y"] = record.eulerAngles.y;
                eulerJson["z"] = record.eulerAngles.z;
                recordJson["eulerAngles"] = eulerJson;
            }

            if (recordQuaternion && record.rotation != null)
            {
                var quatJson = new JSONObject();
                quatJson["x"] = record.rotation.x;
                quatJson["y"] = record.rotation.y;
                quatJson["z"] = record.rotation.z;
                quatJson["w"] = record.rotation.w;
                recordJson["quaternion"] = quatJson;
            }

            if (recordRotationMatrix && record.rotationMatrix != null)
            {
                var matrixJson = new JSONArray();
                for (int i = 0; i < 16; i++)
                {
                    matrixJson[i] = record.rotationMatrix.matrix[i];
                }
                recordJson["rotationMatrix"] = matrixJson;
            }

            if (recordForwardVector && record.forwardVector != null)
            {
                var forwardJson = new JSONObject();
                forwardJson["x"] = record.forwardVector.x;
                forwardJson["y"] = record.forwardVector.y;
                forwardJson["z"] = record.forwardVector.z;
                recordJson["forward"] = forwardJson;
            }

            if (recordUpVector && record.upVector != null)
            {
                var upJson = new JSONObject();
                upJson["x"] = record.upVector.x;
                upJson["y"] = record.upVector.y;
                upJson["z"] = record.upVector.z;
                recordJson["up"] = upJson;
            }

            if (recordRightVector && record.rightVector != null)
            {
                var rightJson = new JSONObject();
                rightJson["x"] = record.rightVector.x;
                rightJson["y"] = record.rightVector.y;
                rightJson["z"] = record.rightVector.z;
                recordJson["right"] = rightJson;
            }

            dataArray.Add(recordJson);
        }

        jsonRoot["data"] = dataArray;

        return jsonRoot;
    }

    /// <summary>
    /// 记录数据摘要
    /// </summary>
    private void LogDataSummary()
    {
        if (recordedData.Count == 0) return;

        Debug.Log("=== VR摄像机数据记录摘要 ===");
        Debug.Log($"记录数量: {recordedData.Count}");
        Debug.Log($"记录时长: {(Time.time - recordingStartTime):F1}秒");
        Debug.Log($"记录间隔: {recordingInterval}秒");

        // 显示第一条和最后一条记录的信息
        var firstRecord = recordedData[0];
        var lastRecord = recordedData[recordedData.Count - 1];

        Debug.Log($"首次记录时间: {firstRecord.timestamp:F2}秒");
        Debug.Log($"最后记录时间: {lastRecord.timestamp:F2}秒");

        if (recordPosition)
        {
            Debug.Log($"起始位置: {firstRecord.position.x:F2}, {firstRecord.position.y:F2}, {firstRecord.position.z:F2}");
            Debug.Log($"结束位置: {lastRecord.position.x:F2}, {lastRecord.position.y:F2}, {lastRecord.position.z:F2}");
        }

        if (recordEulerAngles)
        {
            Debug.Log($"起始旋转: {firstRecord.eulerAngles.x:F1}°, {firstRecord.eulerAngles.y:F1}°, {firstRecord.eulerAngles.z:F1}°");
            Debug.Log($"结束旋转: {lastRecord.eulerAngles.x:F1}°, {lastRecord.eulerAngles.y:F1}°, {lastRecord.eulerAngles.z:F1}°");
        }

        Debug.Log("============================");
    }

    /// <summary>
    /// 记录文件访问说明
    /// </summary>
    private void LogFileAccessInstructions(string filePath)
    {
        Debug.Log("=== 文件访问说明 ===");

        if (Application.platform == RuntimePlatform.Android)
        {
            Debug.Log("📱 PICO设备文件访问方法:");
            Debug.Log("方法1: 使用ADB命令");
            Debug.Log($"  adb pull \"{filePath}\" ./");
            Debug.Log("");
            Debug.Log("方法2: 使用文件管理器");
            Debug.Log("  1. 在PICO设备上安装文件管理器应用");
            Debug.Log("  2. 导航到应用数据目录");
            Debug.Log($"  3. 查找文件: {Path.GetFileName(filePath)}");
            Debug.Log("");
            Debug.Log("方法3: 通过应用分享");
            Debug.Log("  - 可以在应用中添加分享功能");
            Debug.Log("  - 通过邮件或云存储分享文件");
            Debug.Log("");
            Debug.Log($"💾 完整路径: {filePath}");

            // 检查是否为外部存储
            if (filePath.Contains("Android/data"))
            {
                Debug.Log("📂 文件保存在外部存储，可通过USB连接访问");
            }
            else
            {
                Debug.Log("📂 文件保存在应用内部存储，需要root权限或ADB访问");
            }
        }
        else
        {
            Debug.Log($"💻 编辑器模式 - 文件位置: {filePath}");
            Debug.Log("可以直接在文件资源管理器中访问");
        }

        Debug.Log("==================");
    }

    /// <summary>
    /// 清空记录数据
    /// </summary>
    [ContextMenu("清空记录数据")]
    public void ClearRecordedData()
    {
        int count = recordedData.Count;
        recordedData.Clear();

        Debug.Log($"[VRCameraDataRecorder] 🗑️ 已清空 {count} 条记录数据");
    }

    /// <summary>
    /// 获取当前记录状态
    /// </summary>
    public bool IsRecording()
    {
        return isRecording;
    }

    /// <summary>
    /// 获取记录数据数量
    /// </summary>
    public int GetRecordCount()
    {
        return recordedData.Count;
    }

    /// <summary>
    /// 手动记录一次数据
    /// </summary>
    [ContextMenu("手动记录一次")]
    public void RecordOnce()
    {
        if (vrCamera == null)
        {
            Debug.LogError("[VRCameraDataRecorder] ❌ VR摄像机未找到");
            return;
        }

        RecordCurrentCameraData();
        Debug.Log($"[VRCameraDataRecorder] 📸 手动记录完成，当前记录数: {recordedData.Count}");
    }

    void OnGUI()
    {
        if (!showDebugInfo) return;

        // 在屏幕左上角显示记录状态
        GUI.Box(new Rect(10, 10, 300, 120), "VR摄像机数据记录器");

        GUI.Label(new Rect(20, 35, 280, 20), $"状态: {(isRecording ? "记录中" : "停止")}");
        GUI.Label(new Rect(20, 55, 280, 20), $"记录数: {recordedData.Count} / {maxRecords}");

        if (isRecording)
        {
            float recordingTime = Time.time - recordingStartTime;
            GUI.Label(new Rect(20, 75, 280, 20), $"记录时间: {recordingTime:F1}秒");
        }

        GUI.Label(new Rect(20, 95, 280, 20), $"摄像机: {(vrCamera != null ? vrCamera.name : "未找到")}");
        GUI.Label(new Rect(20, 115, 280, 20), "F2:开始 F3:停止 F4:保存 F5:清空");
    }
}
