Using pre-set license
Built from '2021.3/china_unity/release' branch; Version is '2021.3.44f1c1 (0c301d0bd4e3) revision 798749'; Using compiler version '192829333'; Build Type 'Release'
OS: 'Windows 11  (10.0.22631) 64bit CoreCountrySpecific' Language: 'zh' Physical Memory: 16091 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1

COMMAND LINE ARGUMENTS:
C:\Program Files\Unity\Hub\Editor\2021.3.44f1c1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker12
-projectPath
D:/nwu/Assembly/UnityProjects/VRAssembly
-logFile
Logs/AssetImportWorker12.log
-srvPort
4081
Successfully changed project path to: D:/nwu/Assembly/UnityProjects/VRAssembly
D:/nwu/Assembly/UnityProjects/VRAssembly
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [42564] Host "[IP] ********** [Port] 0 [Flags] 2 [Guid] 3083117081 [EditorId] 3083117081 [Version] 1048832 [Id] WindowsEditor(7,PC-SHENSHEN) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" joined multi-casting on [***********:54997]...

Player connection [42564] Host "[IP] ********** [Port] 0 [Flags] 2 [Guid] 3083117081 [EditorId] 3083117081 [Version] 1048832 [Id] WindowsEditor(7,PC-SHENSHEN) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" joined alternative multi-casting on [***********:34997]...

AS: AutoStreaming module initializing.
[Physics::Module] Initialized MultithreadedJobDispatcher with {0} workers.
Refreshing native plugins compatible for Editor in 54.07 ms, found 13 plugins.
Preloading 2 native plugins for Editor in 2.11 ms.
Initialize engine version: 2021.3.44f1c1 (0c301d0bd4e3)
[Subsystems] Discovering subsystems at path C:/Program Files/Unity/Hub/Editor/2021.3.44f1c1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path D:/nwu/Assembly/UnityProjects/VRAssembly/Assets
GfxDevice: creating device client; threaded=0; jobified=0
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce RTX 4070 Laptop GPU (ID=0x2860)
    Vendor:   NVIDIA
    VRAM:     7948 MB
    Driver:   32.0.15.6624
Initialize mono
Mono path[0] = 'C:/Program Files/Unity/Hub/Editor/2021.3.44f1c1/Editor/Data/Managed'
Mono path[1] = 'C:/Program Files/Unity/Hub/Editor/2021.3.44f1c1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'C:/Program Files/Unity/Hub/Editor/2021.3.44f1c1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56204
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: C:/Program Files/Unity/Hub/Editor/2021.3.44f1c1/Editor/Data/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll
Register platform support module: C:/Program Files/Unity/Hub/Editor/2021.3.44f1c1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.021901 seconds.
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Android Extension - Scanning For ADB Devices 1293 ms
Refreshing native plugins compatible for Editor in 40.14 ms, found 13 plugins.
Preloading 2 native plugins for Editor in 0.21 ms.
Mono: successfully reloaded assembly
- Completed reload, in  1.881 seconds
Domain Reload Profiling:
	ReloadAssembly (1881ms)
		BeginReloadAssembly (118ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (0ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (0ms)
		EndReloadAssembly (1616ms)
			LoadAssemblies (106ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (73ms)
			ReleaseScriptCaches (0ms)
			RebuildScriptCaches (16ms)
			SetupLoadedEditorAssemblies (1481ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (1353ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (40ms)
				BeforeProcessingInitializeOnLoad (1ms)
				ProcessInitializeOnLoadAttributes (58ms)
				ProcessInitializeOnLoadMethodAttributes (29ms)
				AfterProcessingInitializeOnLoad (0ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (0ms)
Platform modules already initialized, skipping
Registering precompiled user dll's ...
Registered in 0.012138 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 42.04 ms, found 13 plugins.
Preloading 2 native plugins for Editor in 0.21 ms.
PXRLog [Build Check]RegisterBuildPlayerHandler,Already Do not show: False
0x00007ff6c74fefad (Unity) StackWalker::GetCurrentCallstack
0x00007ff6c7505b39 (Unity) StackWalker::ShowCallstack
0x00007ff6c84a8df3 (Unity) GetStacktrace
0x00007ff6c8b7165d (Unity) DebugStringToFile
0x00007ff6c664ea32 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x000001a6ca4de1e3 (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
0x000001a6ca4ddebb (Mono JIT Code) UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
0x000001a6ca4ddc40 (Mono JIT Code) UnityEngine.Logger:Log (UnityEngine.LogType,object)
0x000001a6ca4ddb08 (Mono JIT Code) UnityEngine.Debug:Log (object)
0x000001a6ca4daf43 (Mono JIT Code) [PXR_SDKBuildCheck.cs:32] Unity.XR.PXR.Editor.PXR_SDKBuildCheck:.cctor () 
0x000001a6ca3ba9f5 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
0x00007ffb3c53697e (mono-2.0-bdwgc) [mini-runtime.c:3445] mono_jit_runtime_invoke 
0x00007ffb3c46cdd4 (mono-2.0-bdwgc) [object.c:3068] do_runtime_invoke 
0x00007ffb3c467b79 (mono-2.0-bdwgc) [object.c:563] mono_runtime_class_init_full 
0x00007ffb3c39ddd7 (mono-2.0-bdwgc) [icall.c:1292] ves_icall_System_Runtime_CompilerServices_RuntimeHelpers_RunClassConstructor 
0x00007ffb3c3cfebc (mono-2.0-bdwgc) [icall-def.h:756] ves_icall_System_Runtime_CompilerServices_RuntimeHelpers_RunClassConstructor_raw 
0x000001a6ca3ba09a (Mono JIT Code) (wrapper managed-to-native) System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (intptr)
0x000001a6ca3b9fab (Mono JIT Code) System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
0x000001a6ca3b96f3 (Mono JIT Code) UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])
0x000001a6c5576298 (Mono JIT Code) (wrapper runtime-invoke) <Module>:runtime_invoke_void_object (object,intptr,intptr,intptr)
0x00007ffb3c53697e (mono-2.0-bdwgc) [mini-runtime.c:3445] mono_jit_runtime_invoke 
0x00007ffb3c46cdd4 (mono-2.0-bdwgc) [object.c:3068] do_runtime_invoke 
0x00007ffb3c46cf4c (mono-2.0-bdwgc) [object.c:3115] mono_runtime_invoke 
0x00007ff6c7421934 (Unity) scripting_method_invoke
0x00007ff6c7400b84 (Unity) ScriptingInvocation::Invoke
0x00007ff6c73fb6e5 (Unity) ScriptingInvocation::Invoke<void>
0x00007ff6c753f4c3 (Unity) Scripting::UnityEditor::EditorAssembliesProxy::ProcessInitializeOnLoadAttributes
0x00007ff6c73f570b (Unity) MonoManager::SetupLoadedEditorAssemblies
0x00007ff6c73eb1ed (Unity) MonoManager::EndReloadAssembly
0x00007ff6c73f2d88 (Unity) MonoManager::ReloadAssembly
0x00007ff6c835fa12 (Unity) LoadDomainAndUserAssemblies
0x00007ff6c8360144 (Unity) LoadUserAssemblies
0x00007ff6c8060c43 (Unity) Application::InitializeProject
0x00007ff6c84b20a0 (Unity) WinMain
0x00007ff6c987fbae (Unity) __scrt_common_main_seh
0x00007ffbd486259d (KERNEL32) BaseThreadInitThunk
0x00007ffbd4e8af78 (ntdll) RtlUserThreadStart

Package Manager log level set to [2]
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.138 seconds
Domain Reload Profiling:
	ReloadAssembly (2139ms)
		BeginReloadAssembly (326ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (3ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (12ms)
		EndReloadAssembly (1611ms)
			LoadAssemblies (299ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (212ms)
			ReleaseScriptCaches (0ms)
			RebuildScriptCaches (46ms)
			SetupLoadedEditorAssemblies (1166ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (15ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (42ms)
				BeforeProcessingInitializeOnLoad (84ms)
				ProcessInitializeOnLoadAttributes (919ms)
				ProcessInitializeOnLoadMethodAttributes (23ms)
				AfterProcessingInitializeOnLoad (82ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (9ms)
Platform modules already initialized, skipping
========================================================================
Worker process is ready to serve import requests
Launched and connected shader compiler UnityShaderCompiler.exe after 0.18 seconds
Refreshing native plugins compatible for Editor in 0.88 ms, found 13 plugins.
Preloading 2 native plugins for Editor in 0.31 ms.
Unloading 4800 Unused Serialized files (Serialized files now loaded: 0)
Unloading 69 unused Assets / (122.7 KB). Loaded Objects now: 5254.
Memory consumption went from 196.2 MB to 196.1 MB.
Total: 2.521800 ms (FindLiveObjects: 0.221800 ms CreateObjectMapping: 0.068000 ms MarkObjects: 2.153000 ms  DeleteObjects: 0.077500 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.021050 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 0.64 ms, found 13 plugins.
Preloading 2 native plugins for Editor in 0.21 ms.
PXRLog [Build Check]RegisterBuildPlayerHandler,Already Do not show: False
0x00007ff6c74fefad (Unity) StackWalker::GetCurrentCallstack
0x00007ff6c7505b39 (Unity) StackWalker::ShowCallstack
0x00007ff6c84a8df3 (Unity) GetStacktrace
0x00007ff6c8b7165d (Unity) DebugStringToFile
0x00007ff6c664ea32 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x000001a6d389c083 (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
0x000001a6d389bd5b (Mono JIT Code) UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
0x000001a6d389bae0 (Mono JIT Code) UnityEngine.Logger:Log (UnityEngine.LogType,object)
0x000001a6d389b9a8 (Mono JIT Code) UnityEngine.Debug:Log (object)
0x000001a6d3898a53 (Mono JIT Code) [PXR_SDKBuildCheck.cs:32] Unity.XR.PXR.Editor.PXR_SDKBuildCheck:.cctor () 
0x000001a6d3a47575 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
0x00007ffb3c53697e (mono-2.0-bdwgc) [mini-runtime.c:3445] mono_jit_runtime_invoke 
0x00007ffb3c46cdd4 (mono-2.0-bdwgc) [object.c:3068] do_runtime_invoke 
0x00007ffb3c467b79 (mono-2.0-bdwgc) [object.c:563] mono_runtime_class_init_full 
0x00007ffb3c39ddd7 (mono-2.0-bdwgc) [icall.c:1292] ves_icall_System_Runtime_CompilerServices_RuntimeHelpers_RunClassConstructor 
0x00007ffb3c3cfebc (mono-2.0-bdwgc) [icall-def.h:756] ves_icall_System_Runtime_CompilerServices_RuntimeHelpers_RunClassConstructor_raw 
0x000001a6d3a46c1a (Mono JIT Code) (wrapper managed-to-native) System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (intptr)
0x000001a6d3a46b2b (Mono JIT Code) System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
0x000001a6d3a46273 (Mono JIT Code) UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])
0x000001a6d3156298 (Mono JIT Code) (wrapper runtime-invoke) <Module>:runtime_invoke_void_object (object,intptr,intptr,intptr)
0x00007ffb3c53697e (mono-2.0-bdwgc) [mini-runtime.c:3445] mono_jit_runtime_invoke 
0x00007ffb3c46cdd4 (mono-2.0-bdwgc) [object.c:3068] do_runtime_invoke 
0x00007ffb3c46cf4c (mono-2.0-bdwgc) [object.c:3115] mono_runtime_invoke 
0x00007ff6c7421934 (Unity) scripting_method_invoke
0x00007ff6c7400b84 (Unity) ScriptingInvocation::Invoke
0x00007ff6c73fb6e5 (Unity) ScriptingInvocation::Invoke<void>
0x00007ff6c753f4c3 (Unity) Scripting::UnityEditor::EditorAssembliesProxy::ProcessInitializeOnLoadAttributes
0x00007ff6c73f570b (Unity) MonoManager::SetupLoadedEditorAssemblies
0x00007ff6c73eb1ed (Unity) MonoManager::EndReloadAssembly
0x00007ff6c73f2d88 (Unity) MonoManager::ReloadAssembly
0x00007ff6c835fa12 (Unity) LoadDomainAndUserAssemblies
0x00007ff6c8360144 (Unity) LoadUserAssemblies
0x00007ff6c885c091 (Unity) <lambda_f8411f227bdd47da33e064bfa1662688>::operator()
0x00007ff6c889ce92 (Unity) asio::detail::completion_handler<core::mutable_function<void __cdecl(void)>,asio::io_context::basic_executor_type<std::allocator<void>,0> >::do_complete
0x00007ff6c8888d7e (Unity) asio::detail::win_iocp_io_context::do_one
0x00007ff6c888a9e4 (Unity) asio::detail::win_iocp_io_context::run
0x00007ff6c889b27e (Unity) IOService::Run
0x00007ff6c886dbcf (Unity) AssetImportWorkerClient::Run
0x00007ff6c8835d13 (Unity) RunAssetImportWorkerClientV2
0x00007ff6c8835d9b (Unity) RunAssetImporterV2
0x00007ff6c8060c48 (Unity) Application::InitializeProject
0x00007ff6c84b20a0 (Unity) WinMain
0x00007ff6c987fbae (Unity) __scrt_common_main_seh
0x00007ffbd486259d (KERNEL32) BaseThreadInitThunk
0x00007ffbd4e8af78 (ntdll) RtlUserThreadStart

[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
CreateSerializedAsset is not supported while importing out-of-process
0x00007ff6c74fefad (Unity) StackWalker::GetCurrentCallstack
0x00007ff6c7505b39 (Unity) StackWalker::ShowCallstack
0x00007ff6c84a8df3 (Unity) GetStacktrace
0x00007ff6c8b7165d (Unity) DebugStringToFile
0x00007ff6c887b618 (Unity) CreateSerializedAssetV2
0x00007ff6c8810f55 (Unity) CreateSerializedAssetV2
0x00007ff6c877f8ee (Unity) AssetDatabase::CreateSerializedAsset
0x00007ff6c6cea4ff (Unity) AssetDatabaseBindings::CreateAsset
0x00007ff6c6b0b819 (Unity) AssetDatabase_CUSTOM_CreateAsset
0x000001a6ca4dcf35 (Mono JIT Code) (wrapper managed-to-native) UnityEditor.AssetDatabase:CreateAsset (UnityEngine.Object,string)
0x000001a6ca4dc753 (Mono JIT Code) [PXR_ProjectSetting.cs:121] Unity.XR.PXR.PXR_ProjectSetting:GetProjectConfig () 
0x000001a6ca4dc383 (Mono JIT Code) [PXR_SDKSettingEditor.cs:111] Unity.XR.PXR.Editor.PXR_SDKSettingEditor:InitializeOnLoad () 
0x000001a6d3a475c3 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
0x00007ffb3c53697e (mono-2.0-bdwgc) [mini-runtime.c:3445] mono_jit_runtime_invoke 
0x00007ffb3c46cdd4 (mono-2.0-bdwgc) [object.c:3068] do_runtime_invoke 
0x00007ffb3c4746df (mono-2.0-bdwgc) [object.c:5770] mono_runtime_try_invoke_array 
0x00007ffb3c3a4eb2 (mono-2.0-bdwgc) [icall.c:4041] ves_icall_InternalInvoke 
0x00007ffb3c3cb091 (mono-2.0-bdwgc) [icall-def.h:692] ves_icall_InternalInvoke_raw 
0x000001a6d388a9d0 (Mono JIT Code) (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo:InternalInvoke (System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
0x000001a6d388a16b (Mono JIT Code) System.Reflection.RuntimeMethodInfo:Invoke (object,System.Reflection.BindingFlags,System.Reflection.Binder,object[],System.Globalization.CultureInfo)
0x000001a6d388a02e (Mono JIT Code) System.Reflection.MethodBase:Invoke (object,object[])
0x000001a6ca4b8c7b (Mono JIT Code) UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes ()
0x000001a6d3156b35 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
0x00007ffb3c53697e (mono-2.0-bdwgc) [mini-runtime.c:3445] mono_jit_runtime_invoke 
0x00007ffb3c46cdd4 (mono-2.0-bdwgc) [object.c:3068] do_runtime_invoke 
0x00007ffb3c46cf4c (mono-2.0-bdwgc) [object.c:3115] mono_runtime_invoke 
0x00007ff6c7421934 (Unity) scripting_method_invoke
0x00007ff6c7400b84 (Unity) ScriptingInvocation::Invoke
0x00007ff6c73fb6e5 (Unity) ScriptingInvocation::Invoke<void>
0x00007ff6c753f53b (Unity) Scripting::UnityEditor::EditorAssembliesProxy::ProcessInitializeOnLoadMethodAttributes
0x00007ff6c73f5733 (Unity) MonoManager::SetupLoadedEditorAssemblies
0x00007ff6c73eb1ed (Unity) MonoManager::EndReloadAssembly
0x00007ff6c73f2d88 (Unity) MonoManager::ReloadAssembly
0x00007ff6c835fa12 (Unity) LoadDomainAndUserAssemblies
0x00007ff6c8360144 (Unity) LoadUserAssemblies
0x00007ff6c885c091 (Unity) <lambda_f8411f227bdd47da33e064bfa1662688>::operator()
0x00007ff6c889ce92 (Unity) asio::detail::completion_handler<core::mutable_function<void __cdecl(void)>,asio::io_context::basic_executor_type<std::allocator<void>,0> >::do_complete
0x00007ff6c8888d7e (Unity) asio::detail::win_iocp_io_context::do_one
0x00007ff6c888a9e4 (Unity) asio::detail::win_iocp_io_context::run
0x00007ff6c889b27e (Unity) IOService::Run
0x00007ff6c886dbcf (Unity) AssetImportWorkerClient::Run
0x00007ff6c8835d13 (Unity) RunAssetImportWorkerClientV2
0x00007ff6c8835d9b (Unity) RunAssetImporterV2
0x00007ff6c8060c48 (Unity) Application::InitializeProject
0x00007ff6c84b20a0 (Unity) WinMain
0x00007ff6c987fbae (Unity) __scrt_common_main_seh
0x00007ffbd486259d (KERNEL32) BaseThreadInitThunk
0x00007ffbd4e8af78 (ntdll) RtlUserThreadStart

System.Reflection.TargetInvocationException: Exception has been thrown by the target of an invocation. ---> UnityEngine.UnityException: Creating asset at path Assets/Resources/PXR_ProjectSetting.asset failed.
  at (wrapper managed-to-native) UnityEditor.AssetDatabase.CreateAsset(UnityEngine.Object,string)
  at Unity.XR.PXR.PXR_ProjectSetting.GetProjectConfig () [0x0015a] in D:\PICO Unity Integration SDK-3.2.0-20250529\Runtime\Scripts\PXR_ProjectSetting.cs:117 
  at Unity.XR.PXR.Editor.PXR_SDKSettingEditor.InitializeOnLoad () [0x00000] in D:\PICO Unity Integration SDK-3.2.0-20250529\Editor\PXR_SDKSettingEditor.cs:111 
  at (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo.InternalInvoke(System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x0006a] in <688f272cc27c4df98c503f9d1f1267d0>:0 
   --- End of inner exception stack trace ---
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x00083] in <688f272cc27c4df98c503f9d1f1267d0>:0 
  at System.Reflection.MethodBase.Invoke (System.Object obj, System.Object[] parameters) [0x00000] in <688f272cc27c4df98c503f9d1f1267d0>:0 
  at UnityEditor.EditorAssemblies.ProcessInitializeOnLoadMethodAttributes () [0x000a5] in <1518627dacca44efa32edd45bc720c48>:0 
0x00007ff6c74fefad (Unity) StackWalker::GetCurrentCallstack
0x00007ff6c7505b39 (Unity) StackWalker::ShowCallstack
0x00007ff6c84a8df3 (Unity) GetStacktrace
0x00007ff6c8b7165d (Unity) DebugStringToFile
0x00007ff6c664ea32 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x000001a6d389c083 (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
0x000001a6d389bd5b (Mono JIT Code) UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
0x000001a6d389bae0 (Mono JIT Code) UnityEngine.Logger:Log (UnityEngine.LogType,object)
0x000001a6ca4dd1c5 (Mono JIT Code) UnityEngine.Debug:LogError (object)
0x000001a6ca4b8c9b (Mono JIT Code) UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes ()
0x000001a6d3156b35 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
0x00007ffb3c53697e (mono-2.0-bdwgc) [mini-runtime.c:3445] mono_jit_runtime_invoke 
0x00007ffb3c46cdd4 (mono-2.0-bdwgc) [object.c:3068] do_runtime_invoke 
0x00007ffb3c46cf4c (mono-2.0-bdwgc) [object.c:3115] mono_runtime_invoke 
0x00007ff6c7421934 (Unity) scripting_method_invoke
0x00007ff6c7400b84 (Unity) ScriptingInvocation::Invoke
0x00007ff6c73fb6e5 (Unity) ScriptingInvocation::Invoke<void>
0x00007ff6c753f53b (Unity) Scripting::UnityEditor::EditorAssembliesProxy::ProcessInitializeOnLoadMethodAttributes
0x00007ff6c73f5733 (Unity) MonoManager::SetupLoadedEditorAssemblies
0x00007ff6c73eb1ed (Unity) MonoManager::EndReloadAssembly
0x00007ff6c73f2d88 (Unity) MonoManager::ReloadAssembly
0x00007ff6c835fa12 (Unity) LoadDomainAndUserAssemblies
0x00007ff6c8360144 (Unity) LoadUserAssemblies
0x00007ff6c885c091 (Unity) <lambda_f8411f227bdd47da33e064bfa1662688>::operator()
0x00007ff6c889ce92 (Unity) asio::detail::completion_handler<core::mutable_function<void __cdecl(void)>,asio::io_context::basic_executor_type<std::allocator<void>,0> >::do_complete
0x00007ff6c8888d7e (Unity) asio::detail::win_iocp_io_context::do_one
0x00007ff6c888a9e4 (Unity) asio::detail::win_iocp_io_context::run
0x00007ff6c889b27e (Unity) IOService::Run
0x00007ff6c886dbcf (Unity) AssetImportWorkerClient::Run
0x00007ff6c8835d13 (Unity) RunAssetImportWorkerClientV2
0x00007ff6c8835d9b (Unity) RunAssetImporterV2
0x00007ff6c8060c48 (Unity) Application::InitializeProject
0x00007ff6c84b20a0 (Unity) WinMain
0x00007ff6c987fbae (Unity) __scrt_common_main_seh
0x00007ffbd486259d (KERNEL32) BaseThreadInitThunk
0x00007ffbd4e8af78 (ntdll) RtlUserThreadStart

Mono: successfully reloaded assembly
- Completed reload, in  1.986 seconds
Domain Reload Profiling:
	ReloadAssembly (1986ms)
		BeginReloadAssembly (467ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (2ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (32ms)
		EndReloadAssembly (1283ms)
			LoadAssemblies (405ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (229ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (33ms)
			SetupLoadedEditorAssemblies (787ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (20ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (92ms)
				ProcessInitializeOnLoadAttributes (598ms)
				ProcessInitializeOnLoadMethodAttributes (27ms)
				AfterProcessingInitializeOnLoad (48ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (7ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.64 ms, found 13 plugins.
Preloading 2 native plugins for Editor in 0.35 ms.
Unloading 4751 Unused Serialized files (Serialized files now loaded: 0)
Unloading 51 unused Assets / (95.7 KB). Loaded Objects now: 5270.
Memory consumption went from 192.1 MB to 192.0 MB.
Total: 2.496300 ms (FindLiveObjects: 0.200200 ms CreateObjectMapping: 0.066300 ms MarkObjects: 2.195200 ms  DeleteObjects: 0.033100 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
Editor requested this worker to shutdown with reason: Scaling down because of idle timeout
AssetImportWorker is now disconnected from the server
Process exiting
Exiting without the bug reporter. Application will terminate with return code 0