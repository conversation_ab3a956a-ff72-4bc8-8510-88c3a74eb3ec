using UnityEngine;

#if UNITY_XR_INTERACTION_TOOLKIT
using UnityEngine.XR.Interaction.Toolkit;
using UnityEngine.XR.Interaction.Toolkit.Inputs;
using UnityEngine.InputSystem;
#endif

/// <summary>
/// PICO Action-based输入处理器
/// 专门处理ActionBasedController的输入
/// </summary>
public class PICOActionBasedInputHandler : MonoBehaviour
{
    [Header("组件引用")]
    [SerializeField] private VRAssemblyDebugger debugger;

#if UNITY_XR_INTERACTION_TOOLKIT
    [Header("Action-based控制器")]
    [SerializeField] private ActionBasedController leftController;
    [SerializeField] private ActionBasedController rightController;
#endif

    [Header("调试设置")]
    [SerializeField] private bool enableDebugLogs = true;
    [SerializeField] private bool useKeyboardFallback = true;
    [SerializeField] private bool enableVerboseLogging = true;
    [SerializeField] private float logInterval = 1f; // 每秒记录一次状态

    // 输入状态跟踪
    private bool lastSelectPressed = false;
    private bool lastActivatePressed = false;
    private float lastLogTime = 0f;

    void Start()
    {
        InitializeComponents();
        LogInitializationStatus();
    }

    void Update()
    {
        HandleInput();

        // 定期记录状态信息
        if (enableVerboseLogging && Time.time - lastLogTime > logInterval)
        {
            LogCurrentStatus();
            lastLogTime = Time.time;
        }
    }

    /// <summary>
    /// 初始化组件
    /// </summary>
    private void InitializeComponents()
    {
        // 自动查找VRAssemblyDebugger
        if (debugger == null)
        {
            debugger = FindObjectOfType<VRAssemblyDebugger>();
        }

#if UNITY_XR_INTERACTION_TOOLKIT
        // 自动查找ActionBasedController
        if (leftController == null || rightController == null)
        {
            var controllers = FindObjectsOfType<ActionBasedController>();
            
            foreach (var controller in controllers)
            {
                if (controller.name.ToLower().Contains("left"))
                {
                    leftController = controller;
                }
                else if (controller.name.ToLower().Contains("right"))
                {
                    rightController = controller;
                }
            }
        }
#endif
    }

    /// <summary>
    /// 记录初始化状态
    /// </summary>
    private void LogInitializationStatus()
    {
        if (!enableDebugLogs) return;

        Debug.Log("=== PICO Action-based输入处理器初始化 ===");
        Debug.Log($"VRAssemblyDebugger: {(debugger != null ? "✅ 已找到" : "❌ 未找到")}");

#if UNITY_XR_INTERACTION_TOOLKIT
        Debug.Log($"左手控制器: {(leftController != null ? $"✅ {leftController.name}" : "❌ 未找到")}");
        Debug.Log($"右手控制器: {(rightController != null ? $"✅ {rightController.name}" : "❌ 未找到")}");

        // 检查Input Actions配置
        if (rightController != null)
        {
            Debug.Log("右手控制器Input Actions状态:");
            LogActionStatus("Select Action", rightController.selectAction);
            LogActionStatus("Activate Action", rightController.activateAction);
        }
#else
        Debug.Log("XR Interaction Toolkit: ❌ 不可用");
#endif

        Debug.Log("输入映射:");
        Debug.Log("  键盘T/右手扳机(Activate) → 装配区域定位");
        Debug.Log("  键盘P/右手握把(Select) → 装配面朝向");
        Debug.Log("  键盘G → 重置位置");
        Debug.Log("  键盘F1 → 调试切换");
        Debug.Log("=======================================");
    }

    /// <summary>
    /// 记录当前状态
    /// </summary>
    private void LogCurrentStatus()
    {
#if UNITY_XR_INTERACTION_TOOLKIT
        if (rightController != null)
        {
            bool selectPressed = IsActionPressed(rightController.selectAction);
            bool activatePressed = IsActionPressed(rightController.activateAction);

            if (selectPressed || activatePressed)
            {
                Debug.Log($"[PICO状态] 右手控制器 - 握把:{selectPressed} 扳机:{activatePressed}");
            }
        }
        else if (leftController != null)
        {
            bool selectPressed = IsActionPressed(leftController.selectAction);
            bool activatePressed = IsActionPressed(leftController.activateAction);

            if (selectPressed || activatePressed)
            {
                Debug.Log($"[PICO状态] 左手控制器 - 握把:{selectPressed} 扳机:{activatePressed}");
            }
        }
        else
        {
            Debug.Log("[PICO状态] 未找到可用的ActionBasedController");
        }
#else
        Debug.Log("[PICO状态] XR Interaction Toolkit不可用");
#endif
    }

#if UNITY_XR_INTERACTION_TOOLKIT
    /// <summary>
    /// 记录Action状态
    /// </summary>
    private void LogActionStatus(string name, InputActionProperty actionProperty)
    {
        if (actionProperty.action != null)
        {
            Debug.Log($"  {name}: ✅ 已配置 (enabled: {actionProperty.action.enabled})");
            if (actionProperty.action.bindings.Count > 0)
            {
                Debug.Log($"    绑定: {actionProperty.action.bindings[0].path}");
            }
        }
        else
        {
            Debug.Log($"  {name}: ❌ 未配置");
        }
    }
#endif

    /// <summary>
    /// 处理输入
    /// </summary>
    private void HandleInput()
    {
        // 键盘备用输入
        if (useKeyboardFallback)
        {
            if (Input.GetKeyDown(KeyCode.A)) OnSelectPressed("键盘A"); // A键 → 装配区域定位
            if (Input.GetKeyDown(KeyCode.B)) OnActivatePressed("键盘B"); // B键 → 装配面朝向
            if (Input.GetKeyDown(KeyCode.G)) OnResetPressed("键盘");
            if (Input.GetKeyDown(KeyCode.F1)) OnDebugTogglePressed("键盘");
        }

#if UNITY_XR_INTERACTION_TOOLKIT
        // 处理VR控制器输入
        if (rightController != null)
        {
            HandleControllerInput(rightController, "右手");
        }
        else if (leftController != null)
        {
            HandleControllerInput(leftController, "左手");
        }
#endif
    }

#if UNITY_XR_INTERACTION_TOOLKIT
    /// <summary>
    /// 处理控制器输入
    /// </summary>
    private void HandleControllerInput(ActionBasedController controller, string handName)
    {
        // 获取按钮状态
        bool selectPressed = IsActionPressed(controller.selectAction);      // 握把按钮
        bool activatePressed = IsActionPressed(controller.activateAction);  // 扳机按钮

        // 检测按钮按下事件（边缘检测）
        // 注意：扳机键(activate)现在只用于UI交互，不再控制装配区域

        if (selectPressed && !lastSelectPressed)
        {
            OnSelectPressed(handName);
        }

        // 更新状态
        lastActivatePressed = activatePressed;
        lastSelectPressed = selectPressed;

        // 显示输入状态
        if (enableDebugLogs && (activatePressed || selectPressed))
        {
            Debug.Log($"[PICO输入] {handName} - 扳机(Activate):{activatePressed} 握把(Select):{selectPressed}");
        }
    }

    /// <summary>
    /// 检查Action是否被按下
    /// </summary>
    private bool IsActionPressed(InputActionProperty actionProperty)
    {
        if (actionProperty.action != null && actionProperty.action.enabled)
        {
            try
            {
                // 尝试读取float值
                float value = actionProperty.action.ReadValue<float>();
                return value > 0.5f;
            }
            catch
            {
                try
                {
                    // 如果float失败，尝试读取bool值
                    return actionProperty.action.ReadValue<bool>();
                }
                catch
                {
                    return false;
                }
            }
        }
        return false;
    }
#endif

    /// <summary>
    /// B按钮按下 - 装配面朝向
    /// </summary>
    private void OnActivatePressed(string source)
    {
        if (debugger == null) return;

        if (enableDebugLogs)
        {
            Debug.Log($"[PICO输入] {source}B按钮(Activate) → 装配面朝向");
        }

        debugger.TestOrientation();
    }

    /// <summary>
    /// A按钮(握把)按下 - 装配区域定位
    /// </summary>
    private void OnSelectPressed(string source)
    {
        if (debugger == null) return;

        if (enableDebugLogs)
        {
            Debug.Log($"[PICO输入] {source}A按钮(Select) → 装配区域定位");
        }

        debugger.TestCameraBasedPositioning();
    }

    /// <summary>
    /// 重置按下
    /// </summary>
    private void OnResetPressed(string source)
    {
        if (debugger == null) return;

        if (enableDebugLogs)
        {
            Debug.Log($"[PICO输入] {source} → 重置位置");
        }
        
        debugger.ResetPosition();
    }

    /// <summary>
    /// 调试切换按下
    /// </summary>
    private void OnDebugTogglePressed(string source)
    {
        if (debugger == null) return;

        if (enableDebugLogs)
        {
            Debug.Log($"[PICO输入] {source} → 调试切换");
        }
        
        debugger.ToggleDebugMode();
    }

    /// <summary>
    /// 测试所有功能
    /// </summary>
    [ContextMenu("测试所有功能")]
    public void TestAllFunctions()
    {
        StartCoroutine(TestSequence());
    }

    /// <summary>
    /// 测试序列
    /// </summary>
    private System.Collections.IEnumerator TestSequence()
    {
        Debug.Log("开始测试所有功能...");
        
        OnActivatePressed("测试");
        yield return new WaitForSeconds(3f);
        
        OnSelectPressed("测试");
        yield return new WaitForSeconds(3f);
        
        OnResetPressed("测试");
        yield return new WaitForSeconds(2f);
        
        Debug.Log("功能测试完成！");
    }

    /// <summary>
    /// 显示使用说明
    /// </summary>
    [ContextMenu("显示使用说明")]
    public void ShowUsageInstructions()
    {
        string instructions = @"
🎮 PICO Action-based控制器输入映射：

基于XRI Default Input Actions的映射：
🔫 扳机按钮 (TriggerButton → Activate Action) → 装配区域定位
✊ 握把按钮 (Grip → Select Action) → 装配面朝向

键盘备用输入：
T键 → 装配区域定位
P键 → 装配面朝向
G键 → 重置位置
F1键 → 调试切换

💡 使用说明：
1. 确保场景中有ActionBasedController组件
2. 确保控制器引用了XRI Default Input Actions
3. 在Unity编辑器中可使用键盘测试
4. 观察Console了解按钮响应状态

🔧 故障排除：
- 如果按钮无响应，检查Input Actions是否正确配置
- 确认XR Origin使用的是XRI Default Input Actions
- 检查控制器的selectAction和activateAction是否已配置
";
        Debug.Log(instructions);
    }
}
