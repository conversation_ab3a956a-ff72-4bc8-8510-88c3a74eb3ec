using UnityEngine;
using UnityEngine.UI;
using TMPro;
using UnityEngine.EventSystems;

#if UNITY_EDITOR
using UnityEditor;
#endif

/// <summary>
/// VR Start Menu Setup Helper
/// 
/// Helper for quickly creating and configuring VR start menu UI in scene
/// </summary>
public class VRStartMenuSetupHelper_EN : MonoBehaviour
{
    [Header("UI Creation Settings")]
    [SerializeField] private bool createCanvas = true;
    [SerializeField] private bool createEventSystem = true;
    [SerializeField] private bool setupVRComponents = true;
    [SerializeField] private bool optimizeForVR = true;
    
    [Header("UI Style Settings")]
    [SerializeField] private Color backgroundColor = new Color(0.1f, 0.1f, 0.2f, 0.8f);
    [SerializeField] private Color primaryColor = new Color(0.2f, 0.6f, 1f, 1f);
    [SerializeField] private Color textColor = Color.white;
    [SerializeField] private Font defaultFont;
    
    [Header("VR Settings")]
    [SerializeField] private float uiDistance = 2.5f;
    [SerializeField] private float uiScale = 0.01f;
    [SerializeField] private Vector3 uiOffset = new Vector3(0, 0.2f, 0);
    
    /// <summary>
    /// Create VR start menu
    /// </summary>
    [ContextMenu("Create VR Start Menu")]
    public void CreateVRStartMenu()
    {
        Debug.Log("[VRStartMenuSetupHelper] Starting VR start menu creation");
        
        // 1. Create Canvas
        Canvas canvas = CreateMenuCanvas();
        
        // 2. Create event system
        if (createEventSystem)
        {
            CreateEventSystemIfNeeded();
        }
        
        // 3. Create UI elements
        CreateMenuUI(canvas);
        
        // 4. Add VR components
        if (setupVRComponents)
        {
            SetupVRComponents(canvas);
        }
        
        // 5. Add menu manager
        SetupMenuManager(canvas);
        
        // 6. Add VR input handler
        SetupVRInputHandler(canvas);
        
        Debug.Log("[VRStartMenuSetupHelper] VR start menu creation completed");
    }
    
    /// <summary>
    /// Create menu Canvas
    /// </summary>
    private Canvas CreateMenuCanvas()
    {
        Canvas existingCanvas = FindObjectOfType<Canvas>();
        if (existingCanvas != null && !createCanvas)
        {
            Debug.Log("[VRStartMenuSetupHelper] Using existing Canvas");
            return existingCanvas;
        }
        
        // Create Canvas GameObject
        GameObject canvasGO = new GameObject("VR Start Menu Canvas");
        Canvas canvas = canvasGO.AddComponent<Canvas>();
        CanvasScaler scaler = canvasGO.AddComponent<CanvasScaler>();
        GraphicRaycaster raycaster = canvasGO.AddComponent<GraphicRaycaster>();
        
        // Configure Canvas
        canvas.renderMode = RenderMode.WorldSpace;
        canvas.worldCamera = Camera.main;
        
        // Configure CanvasScaler
        scaler.uiScaleMode = CanvasScaler.ScaleMode.ScaleWithScreenSize;
        scaler.referenceResolution = new Vector2(1920, 1080);
        scaler.screenMatchMode = CanvasScaler.ScreenMatchMode.MatchWidthOrHeight;
        scaler.matchWidthOrHeight = 0.5f;
        
        // Set position and scale
        canvasGO.transform.localScale = Vector3.one * uiScale;
        PositionCanvas(canvasGO.transform);
        
        Debug.Log("[VRStartMenuSetupHelper] Canvas creation completed");
        return canvas;
    }
    
    /// <summary>
    /// Position Canvas
    /// </summary>
    private void PositionCanvas(Transform canvasTransform)
    {
        Camera mainCamera = Camera.main;
        if (mainCamera == null) return;
        
        Vector3 cameraPosition = mainCamera.transform.position;
        Vector3 cameraForward = mainCamera.transform.forward;
        
        Vector3 targetPosition = cameraPosition + cameraForward * uiDistance + uiOffset;
        canvasTransform.position = targetPosition;
        
        canvasTransform.LookAt(mainCamera.transform);
        canvasTransform.Rotate(0, 180, 0);
    }
    
    /// <summary>
    /// Create event system if needed
    /// </summary>
    private void CreateEventSystemIfNeeded()
    {
        EventSystem existingEventSystem = FindObjectOfType<EventSystem>();
        if (existingEventSystem != null)
        {
            Debug.Log("[VRStartMenuSetupHelper] Using existing EventSystem");
            return;
        }
        
        GameObject eventSystemGO = new GameObject("EventSystem");
        eventSystemGO.AddComponent<EventSystem>();
        eventSystemGO.AddComponent<StandaloneInputModule>();
        
        Debug.Log("[VRStartMenuSetupHelper] EventSystem creation completed");
    }
    
    /// <summary>
    /// Create menu UI
    /// </summary>
    private void CreateMenuUI(Canvas canvas)
    {
        // Create main panel
        GameObject mainPanel = CreateMainPanel(canvas.transform);
        
        // Create title
        CreateTitleText(mainPanel.transform);
        
        // Create welcome text
        CreateWelcomeText(mainPanel.transform);
        
        // Create input area
        CreateInputArea(mainPanel.transform);
        
        // Create start button
        CreateStartButton(mainPanel.transform);
    }
    
    /// <summary>
    /// Create main panel
    /// </summary>
    private GameObject CreateMainPanel(Transform parent)
    {
        GameObject panel = new GameObject("Main Panel");
        panel.transform.SetParent(parent, false);
        
        Image panelImage = panel.AddComponent<Image>();
        panelImage.color = backgroundColor;
        
        RectTransform rectTransform = panel.GetComponent<RectTransform>();
        rectTransform.anchorMin = Vector2.zero;
        rectTransform.anchorMax = Vector2.one;
        rectTransform.offsetMin = Vector2.zero;
        rectTransform.offsetMax = Vector2.zero;
        
        return panel;
    }
    
    /// <summary>
    /// Create title text
    /// </summary>
    private void CreateTitleText(Transform parent)
    {
        GameObject titleGO = new GameObject("Title Text");
        titleGO.transform.SetParent(parent, false);
        
        TextMeshProUGUI titleText = titleGO.AddComponent<TextMeshProUGUI>();
        titleText.text = "VR Assembly Animation System";
        titleText.fontSize = 48;
        titleText.color = textColor;
        titleText.alignment = TextAlignmentOptions.Center;
        titleText.fontStyle = FontStyles.Bold;
        
        RectTransform rectTransform = titleGO.GetComponent<RectTransform>();
        rectTransform.anchorMin = new Vector2(0, 0.8f);
        rectTransform.anchorMax = new Vector2(1, 1f);
        rectTransform.offsetMin = new Vector2(50, 0);
        rectTransform.offsetMax = new Vector2(-50, -20);
    }
    
    /// <summary>
    /// Create welcome text
    /// </summary>
    private void CreateWelcomeText(Transform parent)
    {
        GameObject welcomeGO = new GameObject("Welcome Text");
        welcomeGO.transform.SetParent(parent, false);
        
        TextMeshProUGUI welcomeText = welcomeGO.AddComponent<TextMeshProUGUI>();
        welcomeText.text = "Welcome to VR Assembly Animation System\nPlease enter your username to start";
        welcomeText.fontSize = 24;
        welcomeText.color = textColor;
        welcomeText.alignment = TextAlignmentOptions.Center;
        
        RectTransform rectTransform = welcomeGO.GetComponent<RectTransform>();
        rectTransform.anchorMin = new Vector2(0, 0.6f);
        rectTransform.anchorMax = new Vector2(1, 0.8f);
        rectTransform.offsetMin = new Vector2(50, 0);
        rectTransform.offsetMax = new Vector2(-50, 0);
    }
    
    /// <summary>
    /// Create input area
    /// </summary>
    private void CreateInputArea(Transform parent)
    {
        GameObject inputGO = new GameObject("Username Input");
        inputGO.transform.SetParent(parent, false);
        
        Image inputImage = inputGO.AddComponent<Image>();
        inputImage.color = Color.white;
        
        TMP_InputField inputField = inputGO.AddComponent<TMP_InputField>();
        
        // Create placeholder text
        GameObject placeholderGO = new GameObject("Placeholder");
        placeholderGO.transform.SetParent(inputGO.transform, false);
        TextMeshProUGUI placeholderText = placeholderGO.AddComponent<TextMeshProUGUI>();
        placeholderText.text = "Enter username...";
        placeholderText.fontSize = 20;
        placeholderText.color = new Color(0.5f, 0.5f, 0.5f, 1f);
        placeholderText.alignment = TextAlignmentOptions.Left;
        
        RectTransform placeholderRect = placeholderGO.GetComponent<RectTransform>();
        placeholderRect.anchorMin = Vector2.zero;
        placeholderRect.anchorMax = Vector2.one;
        placeholderRect.offsetMin = new Vector2(10, 0);
        placeholderRect.offsetMax = new Vector2(-10, 0);
        
        // Create input text
        GameObject textGO = new GameObject("Text");
        textGO.transform.SetParent(inputGO.transform, false);
        TextMeshProUGUI inputText = textGO.AddComponent<TextMeshProUGUI>();
        inputText.fontSize = 20;
        inputText.color = Color.black;
        inputText.alignment = TextAlignmentOptions.Left;
        
        RectTransform textRect = textGO.GetComponent<RectTransform>();
        textRect.anchorMin = Vector2.zero;
        textRect.anchorMax = Vector2.one;
        textRect.offsetMin = new Vector2(10, 0);
        textRect.offsetMax = new Vector2(-10, 0);
        
        // Configure input field
        inputField.textComponent = inputText;
        inputField.placeholder = placeholderText;
        inputField.characterLimit = 20;
        
        RectTransform inputRect = inputGO.GetComponent<RectTransform>();
        inputRect.anchorMin = new Vector2(0.2f, 0.4f);
        inputRect.anchorMax = new Vector2(0.8f, 0.5f);
        inputRect.offsetMin = Vector2.zero;
        inputRect.offsetMax = Vector2.zero;
    }
    
    /// <summary>
    /// Create start button
    /// </summary>
    private void CreateStartButton(Transform parent)
    {
        GameObject buttonGO = new GameObject("Start Button");
        buttonGO.transform.SetParent(parent, false);
        
        Image buttonImage = buttonGO.AddComponent<Image>();
        buttonImage.color = primaryColor;
        
        Button button = buttonGO.AddComponent<Button>();
        
        // Create button text
        GameObject textGO = new GameObject("Text");
        textGO.transform.SetParent(buttonGO.transform, false);
        TextMeshProUGUI buttonText = textGO.AddComponent<TextMeshProUGUI>();
        buttonText.text = "Start Experience";
        buttonText.fontSize = 24;
        buttonText.color = Color.white;
        buttonText.alignment = TextAlignmentOptions.Center;
        buttonText.fontStyle = FontStyles.Bold;
        
        RectTransform textRect = textGO.GetComponent<RectTransform>();
        textRect.anchorMin = Vector2.zero;
        textRect.anchorMax = Vector2.one;
        textRect.offsetMin = Vector2.zero;
        textRect.offsetMax = Vector2.zero;
        
        // Configure button colors
        ColorBlock colors = button.colors;
        colors.normalColor = primaryColor;
        colors.highlightedColor = Color.Lerp(primaryColor, Color.white, 0.2f);
        colors.pressedColor = Color.Lerp(primaryColor, Color.black, 0.2f);
        colors.disabledColor = Color.gray;
        button.colors = colors;
        
        RectTransform buttonRect = buttonGO.GetComponent<RectTransform>();
        buttonRect.anchorMin = new Vector2(0.3f, 0.2f);
        buttonRect.anchorMax = new Vector2(0.7f, 0.35f);
        buttonRect.offsetMin = Vector2.zero;
        buttonRect.offsetMax = Vector2.zero;
    }
    
    /// <summary>
    /// Setup VR components
    /// </summary>
    private void SetupVRComponents(Canvas canvas)
    {
        // Add VR UI Manager
        if (canvas.GetComponent<VRUIManager>() == null)
        {
            canvas.gameObject.AddComponent<VRUIManager>();
        }
        
        // Add VR UI Interactor
        if (canvas.GetComponent<VRUIInteractor>() == null)
        {
            canvas.gameObject.AddComponent<VRUIInteractor>();
        }
        
        // Add VR UI Layout Optimizer
        if (canvas.GetComponent<VRUILayoutOptimizer>() == null)
        {
            canvas.gameObject.AddComponent<VRUILayoutOptimizer>();
        }
        
        Debug.Log("[VRStartMenuSetupHelper] VR components setup completed");
    }
    
    /// <summary>
    /// Setup menu manager
    /// </summary>
    private void SetupMenuManager(Canvas canvas)
    {
        VRStartMenuManager_EN manager = canvas.GetComponent<VRStartMenuManager_EN>();
        if (manager == null)
        {
            manager = canvas.gameObject.AddComponent<VRStartMenuManager_EN>();
        }
        
        // Auto assign UI component references
        AssignUIReferences(manager, canvas);
        
        Debug.Log("[VRStartMenuSetupHelper] Menu manager setup completed");
    }
    
    /// <summary>
    /// Setup VR input handler
    /// </summary>
    private void SetupVRInputHandler(Canvas canvas)
    {
        VRStartMenuInputHandler inputHandler = canvas.GetComponent<VRStartMenuInputHandler>();
        if (inputHandler == null)
        {
            inputHandler = canvas.gameObject.AddComponent<VRStartMenuInputHandler>();
        }
        
        // Auto assign input component references
        AssignInputReferences(inputHandler, canvas);
        
        Debug.Log("[VRStartMenuSetupHelper] VR input handler setup completed");
    }
    
    /// <summary>
    /// Assign UI references
    /// </summary>
    private void AssignUIReferences(VRStartMenuManager_EN manager, Canvas canvas)
    {
        // Use reflection to set private fields (in editor)
#if UNITY_EDITOR
        var managerType = typeof(VRStartMenuManager_EN);
        
        // Set Canvas reference
        var canvasField = managerType.GetField("menuCanvas", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
        canvasField?.SetValue(manager, canvas);
        
        // Find and set other UI components
        var titleText = canvas.GetComponentInChildren<TextMeshProUGUI>();
        var inputField = canvas.GetComponentInChildren<TMP_InputField>();
        var button = canvas.GetComponentInChildren<Button>();
        
        var titleField = managerType.GetField("titleText", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
        titleField?.SetValue(manager, titleText);
        
        var inputField_field = managerType.GetField("usernameInput", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
        inputField_field?.SetValue(manager, inputField);
        
        var buttonField = managerType.GetField("startButton", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
        buttonField?.SetValue(manager, button);
        
        // Set camera reference
        var cameraField = managerType.GetField("vrCamera", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
        cameraField?.SetValue(manager, Camera.main);
#endif
    }
    
    /// <summary>
    /// Assign input references
    /// </summary>
    private void AssignInputReferences(VRStartMenuInputHandler inputHandler, Canvas canvas)
    {
        // Use reflection to set private fields (in editor)
#if UNITY_EDITOR
        var inputHandlerType = typeof(VRStartMenuInputHandler);
        
        // Find and set UI components
        var inputField = canvas.GetComponentInChildren<TMP_InputField>();
        var button = canvas.GetComponentInChildren<Button>();
        
        var inputFieldField = inputHandlerType.GetField("targetInputField", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
        inputFieldField?.SetValue(inputHandler, inputField);
        
        var buttonField = inputHandlerType.GetField("startButton", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
        buttonField?.SetValue(inputHandler, button);
#endif
    }
    
#if UNITY_EDITOR
    /// <summary>
    /// Editor menu: Create VR start menu
    /// </summary>
    [MenuItem("VR Assembly/Create VR Start Menu (English)")]
    public static void CreateVRStartMenuFromMenu()
    {
        VRStartMenuSetupHelper_EN helper = FindObjectOfType<VRStartMenuSetupHelper_EN>();
        if (helper == null)
        {
            GameObject helperGO = new GameObject("VRStartMenuSetupHelper_EN");
            helper = helperGO.AddComponent<VRStartMenuSetupHelper_EN>();
        }
        
        helper.CreateVRStartMenu();
        
        EditorUtility.DisplayDialog("VR Start Menu Created", "VR start menu creation completed!", "OK");
    }
#endif
}
