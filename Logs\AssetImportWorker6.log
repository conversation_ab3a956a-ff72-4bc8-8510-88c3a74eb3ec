Using pre-set license
Built from '2021.3/china_unity/release' branch; Version is '2021.3.44f1c1 (0c301d0bd4e3) revision 798749'; Using compiler version '192829333'; Build Type 'Release'
OS: 'Windows 11  (10.0.22631) 64bit CoreCountrySpecific' Language: 'zh' Physical Memory: 16091 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1

COMMAND LINE ARGUMENTS:
C:\Program Files\Unity\Hub\Editor\2021.3.44f1c1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker6
-projectPath
D:/nwu/Assembly/UnityProjects/VRAssembly
-logFile
Logs/AssetImportWorker6.log
-srvPort
4081
Successfully changed project path to: D:/nwu/Assembly/UnityProjects/VRAssembly
D:/nwu/Assembly/UnityProjects/VRAssembly
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [31580] Host "[IP] ********** [Port] 0 [Flags] 2 [Guid] 1548412121 [EditorId] 1548412121 [Version] 1048832 [Id] WindowsEditor(7,PC-SHENSHEN) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" joined multi-casting on [***********:54997]...

Player connection [31580] Host "[IP] ********** [Port] 0 [Flags] 2 [Guid] 1548412121 [EditorId] 1548412121 [Version] 1048832 [Id] WindowsEditor(7,PC-SHENSHEN) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" joined alternative multi-casting on [***********:34997]...

AS: AutoStreaming module initializing.
[Physics::Module] Initialized MultithreadedJobDispatcher with {0} workers.
Refreshing native plugins compatible for Editor in 56.34 ms, found 13 plugins.
Preloading 2 native plugins for Editor in 3.17 ms.
Initialize engine version: 2021.3.44f1c1 (0c301d0bd4e3)
[Subsystems] Discovering subsystems at path C:/Program Files/Unity/Hub/Editor/2021.3.44f1c1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path D:/nwu/Assembly/UnityProjects/VRAssembly/Assets
GfxDevice: creating device client; threaded=0; jobified=0
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce RTX 4070 Laptop GPU (ID=0x2860)
    Vendor:   NVIDIA
    VRAM:     7948 MB
    Driver:   32.0.15.6624
Initialize mono
Mono path[0] = 'C:/Program Files/Unity/Hub/Editor/2021.3.44f1c1/Editor/Data/Managed'
Mono path[1] = 'C:/Program Files/Unity/Hub/Editor/2021.3.44f1c1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'C:/Program Files/Unity/Hub/Editor/2021.3.44f1c1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56240
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: C:/Program Files/Unity/Hub/Editor/2021.3.44f1c1/Editor/Data/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll
Register platform support module: C:/Program Files/Unity/Hub/Editor/2021.3.44f1c1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.019435 seconds.
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Android Extension - Scanning For ADB Devices 625 ms
Refreshing native plugins compatible for Editor in 38.35 ms, found 13 plugins.
Preloading 2 native plugins for Editor in 0.25 ms.
Mono: successfully reloaded assembly
- Completed reload, in  1.179 seconds
Domain Reload Profiling:
	ReloadAssembly (1180ms)
		BeginReloadAssembly (120ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (0ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (0ms)
		EndReloadAssembly (939ms)
			LoadAssemblies (109ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (71ms)
			ReleaseScriptCaches (0ms)
			RebuildScriptCaches (15ms)
			SetupLoadedEditorAssemblies (803ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (678ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (39ms)
				BeforeProcessingInitializeOnLoad (1ms)
				ProcessInitializeOnLoadAttributes (59ms)
				ProcessInitializeOnLoadMethodAttributes (26ms)
				AfterProcessingInitializeOnLoad (0ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (0ms)
Platform modules already initialized, skipping
Registering precompiled user dll's ...
Registered in 0.015624 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 40.26 ms, found 13 plugins.
Preloading 2 native plugins for Editor in 0.29 ms.
PXRLog [Build Check]RegisterBuildPlayerHandler,Already Do not show: False
0x00007ff6c74fefad (Unity) StackWalker::GetCurrentCallstack
0x00007ff6c7505b39 (Unity) StackWalker::ShowCallstack
0x00007ff6c84a8df3 (Unity) GetStacktrace
0x00007ff6c8b7165d (Unity) DebugStringToFile
0x00007ff6c664ea32 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x000001a2ba75e1e3 (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
0x000001a2ba75debb (Mono JIT Code) UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
0x000001a2ba75dc40 (Mono JIT Code) UnityEngine.Logger:Log (UnityEngine.LogType,object)
0x000001a2ba75db08 (Mono JIT Code) UnityEngine.Debug:Log (object)
0x000001a2ba75af43 (Mono JIT Code) [PXR_SDKBuildCheck.cs:32] Unity.XR.PXR.Editor.PXR_SDKBuildCheck:.cctor () 
0x000001a2ba63a9f5 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
0x00007ffb3c53697e (mono-2.0-bdwgc) [mini-runtime.c:3445] mono_jit_runtime_invoke 
0x00007ffb3c46cdd4 (mono-2.0-bdwgc) [object.c:3068] do_runtime_invoke 
0x00007ffb3c467b79 (mono-2.0-bdwgc) [object.c:563] mono_runtime_class_init_full 
0x00007ffb3c39ddd7 (mono-2.0-bdwgc) [icall.c:1292] ves_icall_System_Runtime_CompilerServices_RuntimeHelpers_RunClassConstructor 
0x00007ffb3c3cfebc (mono-2.0-bdwgc) [icall-def.h:756] ves_icall_System_Runtime_CompilerServices_RuntimeHelpers_RunClassConstructor_raw 
0x000001a2ba63a09a (Mono JIT Code) (wrapper managed-to-native) System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (intptr)
0x000001a2ba639fab (Mono JIT Code) System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
0x000001a2ba6396f3 (Mono JIT Code) UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])
0x000001a2b7b86298 (Mono JIT Code) (wrapper runtime-invoke) <Module>:runtime_invoke_void_object (object,intptr,intptr,intptr)
0x00007ffb3c53697e (mono-2.0-bdwgc) [mini-runtime.c:3445] mono_jit_runtime_invoke 
0x00007ffb3c46cdd4 (mono-2.0-bdwgc) [object.c:3068] do_runtime_invoke 
0x00007ffb3c46cf4c (mono-2.0-bdwgc) [object.c:3115] mono_runtime_invoke 
0x00007ff6c7421934 (Unity) scripting_method_invoke
0x00007ff6c7400b84 (Unity) ScriptingInvocation::Invoke
0x00007ff6c73fb6e5 (Unity) ScriptingInvocation::Invoke<void>
0x00007ff6c753f4c3 (Unity) Scripting::UnityEditor::EditorAssembliesProxy::ProcessInitializeOnLoadAttributes
0x00007ff6c73f570b (Unity) MonoManager::SetupLoadedEditorAssemblies
0x00007ff6c73eb1ed (Unity) MonoManager::EndReloadAssembly
0x00007ff6c73f2d88 (Unity) MonoManager::ReloadAssembly
0x00007ff6c835fa12 (Unity) LoadDomainAndUserAssemblies
0x00007ff6c8360144 (Unity) LoadUserAssemblies
0x00007ff6c8060c43 (Unity) Application::InitializeProject
0x00007ff6c84b20a0 (Unity) WinMain
0x00007ff6c987fbae (Unity) __scrt_common_main_seh
0x00007ffbd486259d (KERNEL32) BaseThreadInitThunk
0x00007ffbd4e8af78 (ntdll) RtlUserThreadStart

Package Manager log level set to [2]
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  1.733 seconds
Domain Reload Profiling:
	ReloadAssembly (1734ms)
		BeginReloadAssembly (146ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (3ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (12ms)
		EndReloadAssembly (1476ms)
			LoadAssemblies (136ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (198ms)
			ReleaseScriptCaches (0ms)
			RebuildScriptCaches (44ms)
			SetupLoadedEditorAssemblies (1103ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (16ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (41ms)
				BeforeProcessingInitializeOnLoad (77ms)
				ProcessInitializeOnLoadAttributes (911ms)
				ProcessInitializeOnLoadMethodAttributes (23ms)
				AfterProcessingInitializeOnLoad (36ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (6ms)
Platform modules already initialized, skipping
========================================================================
Worker process is ready to serve import requests
Launched and connected shader compiler UnityShaderCompiler.exe after 0.10 seconds
Refreshing native plugins compatible for Editor in 0.55 ms, found 13 plugins.
Preloading 2 native plugins for Editor in 0.18 ms.
Unloading 4800 Unused Serialized files (Serialized files now loaded: 0)
Unloading 69 unused Assets / (122.6 KB). Loaded Objects now: 5254.
Memory consumption went from 196.2 MB to 196.1 MB.
Total: 2.300000 ms (FindLiveObjects: 0.274200 ms CreateObjectMapping: 0.095700 ms MarkObjects: 1.850300 ms  DeleteObjects: 0.078100 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.015582 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 0.65 ms, found 13 plugins.
Preloading 2 native plugins for Editor in 0.30 ms.
PXRLog [Build Check]RegisterBuildPlayerHandler,Already Do not show: False
0x00007ff6c74fefad (Unity) StackWalker::GetCurrentCallstack
0x00007ff6c7505b39 (Unity) StackWalker::ShowCallstack
0x00007ff6c84a8df3 (Unity) GetStacktrace
0x00007ff6c8b7165d (Unity) DebugStringToFile
0x00007ff6c664ea32 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x000001a2c355c083 (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
0x000001a2c355bd5b (Mono JIT Code) UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
0x000001a2c355bae0 (Mono JIT Code) UnityEngine.Logger:Log (UnityEngine.LogType,object)
0x000001a2c355b9a8 (Mono JIT Code) UnityEngine.Debug:Log (object)
0x000001a2c3558a53 (Mono JIT Code) [PXR_SDKBuildCheck.cs:32] Unity.XR.PXR.Editor.PXR_SDKBuildCheck:.cctor () 
0x000001a2c5af7575 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
0x00007ffb3c53697e (mono-2.0-bdwgc) [mini-runtime.c:3445] mono_jit_runtime_invoke 
0x00007ffb3c46cdd4 (mono-2.0-bdwgc) [object.c:3068] do_runtime_invoke 
0x00007ffb3c467b79 (mono-2.0-bdwgc) [object.c:563] mono_runtime_class_init_full 
0x00007ffb3c39ddd7 (mono-2.0-bdwgc) [icall.c:1292] ves_icall_System_Runtime_CompilerServices_RuntimeHelpers_RunClassConstructor 
0x00007ffb3c3cfebc (mono-2.0-bdwgc) [icall-def.h:756] ves_icall_System_Runtime_CompilerServices_RuntimeHelpers_RunClassConstructor_raw 
0x000001a2c5af6c1a (Mono JIT Code) (wrapper managed-to-native) System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (intptr)
0x000001a2c5af6b2b (Mono JIT Code) System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
0x000001a2c5af6273 (Mono JIT Code) UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])
0x000001a2bff76298 (Mono JIT Code) (wrapper runtime-invoke) <Module>:runtime_invoke_void_object (object,intptr,intptr,intptr)
0x00007ffb3c53697e (mono-2.0-bdwgc) [mini-runtime.c:3445] mono_jit_runtime_invoke 
0x00007ffb3c46cdd4 (mono-2.0-bdwgc) [object.c:3068] do_runtime_invoke 
0x00007ffb3c46cf4c (mono-2.0-bdwgc) [object.c:3115] mono_runtime_invoke 
0x00007ff6c7421934 (Unity) scripting_method_invoke
0x00007ff6c7400b84 (Unity) ScriptingInvocation::Invoke
0x00007ff6c73fb6e5 (Unity) ScriptingInvocation::Invoke<void>
0x00007ff6c753f4c3 (Unity) Scripting::UnityEditor::EditorAssembliesProxy::ProcessInitializeOnLoadAttributes
0x00007ff6c73f570b (Unity) MonoManager::SetupLoadedEditorAssemblies
0x00007ff6c73eb1ed (Unity) MonoManager::EndReloadAssembly
0x00007ff6c73f2d88 (Unity) MonoManager::ReloadAssembly
0x00007ff6c835fa12 (Unity) LoadDomainAndUserAssemblies
0x00007ff6c8360144 (Unity) LoadUserAssemblies
0x00007ff6c885c091 (Unity) <lambda_f8411f227bdd47da33e064bfa1662688>::operator()
0x00007ff6c889ce92 (Unity) asio::detail::completion_handler<core::mutable_function<void __cdecl(void)>,asio::io_context::basic_executor_type<std::allocator<void>,0> >::do_complete
0x00007ff6c8888d7e (Unity) asio::detail::win_iocp_io_context::do_one
0x00007ff6c888a9e4 (Unity) asio::detail::win_iocp_io_context::run
0x00007ff6c889b27e (Unity) IOService::Run
0x00007ff6c886dbcf (Unity) AssetImportWorkerClient::Run
0x00007ff6c8835d13 (Unity) RunAssetImportWorkerClientV2
0x00007ff6c8835d9b (Unity) RunAssetImporterV2
0x00007ff6c8060c48 (Unity) Application::InitializeProject
0x00007ff6c84b20a0 (Unity) WinMain
0x00007ff6c987fbae (Unity) __scrt_common_main_seh
0x00007ffbd486259d (KERNEL32) BaseThreadInitThunk
0x00007ffbd4e8af78 (ntdll) RtlUserThreadStart

[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
CreateSerializedAsset is not supported while importing out-of-process
0x00007ff6c74fefad (Unity) StackWalker::GetCurrentCallstack
0x00007ff6c7505b39 (Unity) StackWalker::ShowCallstack
0x00007ff6c84a8df3 (Unity) GetStacktrace
0x00007ff6c8b7165d (Unity) DebugStringToFile
0x00007ff6c887b618 (Unity) CreateSerializedAssetV2
0x00007ff6c8810f55 (Unity) CreateSerializedAssetV2
0x00007ff6c877f8ee (Unity) AssetDatabase::CreateSerializedAsset
0x00007ff6c6cea4ff (Unity) AssetDatabaseBindings::CreateAsset
0x00007ff6c6b0b819 (Unity) AssetDatabase_CUSTOM_CreateAsset
0x000001a2bcc4cf35 (Mono JIT Code) (wrapper managed-to-native) UnityEditor.AssetDatabase:CreateAsset (UnityEngine.Object,string)
0x000001a2bcc4c753 (Mono JIT Code) [PXR_ProjectSetting.cs:121] Unity.XR.PXR.PXR_ProjectSetting:GetProjectConfig () 
0x000001a2bcc4c383 (Mono JIT Code) [PXR_SDKSettingEditor.cs:111] Unity.XR.PXR.Editor.PXR_SDKSettingEditor:InitializeOnLoad () 
0x000001a2c5af75c3 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
0x00007ffb3c53697e (mono-2.0-bdwgc) [mini-runtime.c:3445] mono_jit_runtime_invoke 
0x00007ffb3c46cdd4 (mono-2.0-bdwgc) [object.c:3068] do_runtime_invoke 
0x00007ffb3c4746df (mono-2.0-bdwgc) [object.c:5770] mono_runtime_try_invoke_array 
0x00007ffb3c3a4eb2 (mono-2.0-bdwgc) [icall.c:4041] ves_icall_InternalInvoke 
0x00007ffb3c3cb091 (mono-2.0-bdwgc) [icall-def.h:692] ves_icall_InternalInvoke_raw 
0x000001a2c354a9d0 (Mono JIT Code) (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo:InternalInvoke (System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
0x000001a2c354a16b (Mono JIT Code) System.Reflection.RuntimeMethodInfo:Invoke (object,System.Reflection.BindingFlags,System.Reflection.Binder,object[],System.Globalization.CultureInfo)
0x000001a2c354a02e (Mono JIT Code) System.Reflection.MethodBase:Invoke (object,object[])
0x000001a2bcc38c7b (Mono JIT Code) UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes ()
0x000001a2bff76b35 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
0x00007ffb3c53697e (mono-2.0-bdwgc) [mini-runtime.c:3445] mono_jit_runtime_invoke 
0x00007ffb3c46cdd4 (mono-2.0-bdwgc) [object.c:3068] do_runtime_invoke 
0x00007ffb3c46cf4c (mono-2.0-bdwgc) [object.c:3115] mono_runtime_invoke 
0x00007ff6c7421934 (Unity) scripting_method_invoke
0x00007ff6c7400b84 (Unity) ScriptingInvocation::Invoke
0x00007ff6c73fb6e5 (Unity) ScriptingInvocation::Invoke<void>
0x00007ff6c753f53b (Unity) Scripting::UnityEditor::EditorAssembliesProxy::ProcessInitializeOnLoadMethodAttributes
0x00007ff6c73f5733 (Unity) MonoManager::SetupLoadedEditorAssemblies
0x00007ff6c73eb1ed (Unity) MonoManager::EndReloadAssembly
0x00007ff6c73f2d88 (Unity) MonoManager::ReloadAssembly
0x00007ff6c835fa12 (Unity) LoadDomainAndUserAssemblies
0x00007ff6c8360144 (Unity) LoadUserAssemblies
0x00007ff6c885c091 (Unity) <lambda_f8411f227bdd47da33e064bfa1662688>::operator()
0x00007ff6c889ce92 (Unity) asio::detail::completion_handler<core::mutable_function<void __cdecl(void)>,asio::io_context::basic_executor_type<std::allocator<void>,0> >::do_complete
0x00007ff6c8888d7e (Unity) asio::detail::win_iocp_io_context::do_one
0x00007ff6c888a9e4 (Unity) asio::detail::win_iocp_io_context::run
0x00007ff6c889b27e (Unity) IOService::Run
0x00007ff6c886dbcf (Unity) AssetImportWorkerClient::Run
0x00007ff6c8835d13 (Unity) RunAssetImportWorkerClientV2
0x00007ff6c8835d9b (Unity) RunAssetImporterV2
0x00007ff6c8060c48 (Unity) Application::InitializeProject
0x00007ff6c84b20a0 (Unity) WinMain
0x00007ff6c987fbae (Unity) __scrt_common_main_seh
0x00007ffbd486259d (KERNEL32) BaseThreadInitThunk
0x00007ffbd4e8af78 (ntdll) RtlUserThreadStart

System.Reflection.TargetInvocationException: Exception has been thrown by the target of an invocation. ---> UnityEngine.UnityException: Creating asset at path Assets/Resources/PXR_ProjectSetting.asset failed.
  at (wrapper managed-to-native) UnityEditor.AssetDatabase.CreateAsset(UnityEngine.Object,string)
  at Unity.XR.PXR.PXR_ProjectSetting.GetProjectConfig () [0x0015a] in D:\PICO Unity Integration SDK-3.2.0-20250529\Runtime\Scripts\PXR_ProjectSetting.cs:117 
  at Unity.XR.PXR.Editor.PXR_SDKSettingEditor.InitializeOnLoad () [0x00000] in D:\PICO Unity Integration SDK-3.2.0-20250529\Editor\PXR_SDKSettingEditor.cs:111 
  at (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo.InternalInvoke(System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x0006a] in <688f272cc27c4df98c503f9d1f1267d0>:0 
   --- End of inner exception stack trace ---
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x00083] in <688f272cc27c4df98c503f9d1f1267d0>:0 
  at System.Reflection.MethodBase.Invoke (System.Object obj, System.Object[] parameters) [0x00000] in <688f272cc27c4df98c503f9d1f1267d0>:0 
  at UnityEditor.EditorAssemblies.ProcessInitializeOnLoadMethodAttributes () [0x000a5] in <1518627dacca44efa32edd45bc720c48>:0 
0x00007ff6c74fefad (Unity) StackWalker::GetCurrentCallstack
0x00007ff6c7505b39 (Unity) StackWalker::ShowCallstack
0x00007ff6c84a8df3 (Unity) GetStacktrace
0x00007ff6c8b7165d (Unity) DebugStringToFile
0x00007ff6c664ea32 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x000001a2c355c083 (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
0x000001a2c355bd5b (Mono JIT Code) UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
0x000001a2c355bae0 (Mono JIT Code) UnityEngine.Logger:Log (UnityEngine.LogType,object)
0x000001a2bcc4d1c5 (Mono JIT Code) UnityEngine.Debug:LogError (object)
0x000001a2bcc38c9b (Mono JIT Code) UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes ()
0x000001a2bff76b35 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
0x00007ffb3c53697e (mono-2.0-bdwgc) [mini-runtime.c:3445] mono_jit_runtime_invoke 
0x00007ffb3c46cdd4 (mono-2.0-bdwgc) [object.c:3068] do_runtime_invoke 
0x00007ffb3c46cf4c (mono-2.0-bdwgc) [object.c:3115] mono_runtime_invoke 
0x00007ff6c7421934 (Unity) scripting_method_invoke
0x00007ff6c7400b84 (Unity) ScriptingInvocation::Invoke
0x00007ff6c73fb6e5 (Unity) ScriptingInvocation::Invoke<void>
0x00007ff6c753f53b (Unity) Scripting::UnityEditor::EditorAssembliesProxy::ProcessInitializeOnLoadMethodAttributes
0x00007ff6c73f5733 (Unity) MonoManager::SetupLoadedEditorAssemblies
0x00007ff6c73eb1ed (Unity) MonoManager::EndReloadAssembly
0x00007ff6c73f2d88 (Unity) MonoManager::ReloadAssembly
0x00007ff6c835fa12 (Unity) LoadDomainAndUserAssemblies
0x00007ff6c8360144 (Unity) LoadUserAssemblies
0x00007ff6c885c091 (Unity) <lambda_f8411f227bdd47da33e064bfa1662688>::operator()
0x00007ff6c889ce92 (Unity) asio::detail::completion_handler<core::mutable_function<void __cdecl(void)>,asio::io_context::basic_executor_type<std::allocator<void>,0> >::do_complete
0x00007ff6c8888d7e (Unity) asio::detail::win_iocp_io_context::do_one
0x00007ff6c888a9e4 (Unity) asio::detail::win_iocp_io_context::run
0x00007ff6c889b27e (Unity) IOService::Run
0x00007ff6c886dbcf (Unity) AssetImportWorkerClient::Run
0x00007ff6c8835d13 (Unity) RunAssetImportWorkerClientV2
0x00007ff6c8835d9b (Unity) RunAssetImporterV2
0x00007ff6c8060c48 (Unity) Application::InitializeProject
0x00007ff6c84b20a0 (Unity) WinMain
0x00007ff6c987fbae (Unity) __scrt_common_main_seh
0x00007ffbd486259d (KERNEL32) BaseThreadInitThunk
0x00007ffbd4e8af78 (ntdll) RtlUserThreadStart

Mono: successfully reloaded assembly
- Completed reload, in  1.447 seconds
Domain Reload Profiling:
	ReloadAssembly (1448ms)
		BeginReloadAssembly (176ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (2ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (26ms)
		EndReloadAssembly (1164ms)
			LoadAssemblies (142ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (209ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (30ms)
			SetupLoadedEditorAssemblies (780ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (19ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (71ms)
				ProcessInitializeOnLoadAttributes (613ms)
				ProcessInitializeOnLoadMethodAttributes (39ms)
				AfterProcessingInitializeOnLoad (38ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (5ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 0.66 ms, found 13 plugins.
Preloading 2 native plugins for Editor in 0.25 ms.
Unloading 4752 Unused Serialized files (Serialized files now loaded: 0)
Unloading 50 unused Assets / (95.7 KB). Loaded Objects now: 5271.
Memory consumption went from 192.1 MB to 192.0 MB.
Total: 2.234400 ms (FindLiveObjects: 0.207800 ms CreateObjectMapping: 0.062300 ms MarkObjects: 1.904600 ms  DeleteObjects: 0.058900 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.015713 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 0.68 ms, found 13 plugins.
Preloading 2 native plugins for Editor in 0.25 ms.
PXRLog [Build Check]RegisterBuildPlayerHandler,Already Do not show: False
0x00007ff6c74fefad (Unity) StackWalker::GetCurrentCallstack
0x00007ff6c7505b39 (Unity) StackWalker::ShowCallstack
0x00007ff6c84a8df3 (Unity) GetStacktrace
0x00007ff6c8b7165d (Unity) DebugStringToFile
0x00007ff6c664ea32 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x000001a2bb61c083 (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
0x000001a2bb61bd5b (Mono JIT Code) UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
0x000001a2bb61bae0 (Mono JIT Code) UnityEngine.Logger:Log (UnityEngine.LogType,object)
0x000001a2bb61b9a8 (Mono JIT Code) UnityEngine.Debug:Log (object)
0x000001a2bb618a53 (Mono JIT Code) [PXR_SDKBuildCheck.cs:32] Unity.XR.PXR.Editor.PXR_SDKBuildCheck:.cctor () 
0x000001a2bcc07575 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
0x00007ffb3c53697e (mono-2.0-bdwgc) [mini-runtime.c:3445] mono_jit_runtime_invoke 
0x00007ffb3c46cdd4 (mono-2.0-bdwgc) [object.c:3068] do_runtime_invoke 
0x00007ffb3c467b79 (mono-2.0-bdwgc) [object.c:563] mono_runtime_class_init_full 
0x00007ffb3c39ddd7 (mono-2.0-bdwgc) [icall.c:1292] ves_icall_System_Runtime_CompilerServices_RuntimeHelpers_RunClassConstructor 
0x00007ffb3c3cfebc (mono-2.0-bdwgc) [icall-def.h:756] ves_icall_System_Runtime_CompilerServices_RuntimeHelpers_RunClassConstructor_raw 
0x000001a2bcc06c1a (Mono JIT Code) (wrapper managed-to-native) System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (intptr)
0x000001a2bcc06b2b (Mono JIT Code) System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
0x000001a2bcc06273 (Mono JIT Code) UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])
0x000001a2c3546298 (Mono JIT Code) (wrapper runtime-invoke) <Module>:runtime_invoke_void_object (object,intptr,intptr,intptr)
0x00007ffb3c53697e (mono-2.0-bdwgc) [mini-runtime.c:3445] mono_jit_runtime_invoke 
0x00007ffb3c46cdd4 (mono-2.0-bdwgc) [object.c:3068] do_runtime_invoke 
0x00007ffb3c46cf4c (mono-2.0-bdwgc) [object.c:3115] mono_runtime_invoke 
0x00007ff6c7421934 (Unity) scripting_method_invoke
0x00007ff6c7400b84 (Unity) ScriptingInvocation::Invoke
0x00007ff6c73fb6e5 (Unity) ScriptingInvocation::Invoke<void>
0x00007ff6c753f4c3 (Unity) Scripting::UnityEditor::EditorAssembliesProxy::ProcessInitializeOnLoadAttributes
0x00007ff6c73f570b (Unity) MonoManager::SetupLoadedEditorAssemblies
0x00007ff6c73eb1ed (Unity) MonoManager::EndReloadAssembly
0x00007ff6c73f2d88 (Unity) MonoManager::ReloadAssembly
0x00007ff6c835fa12 (Unity) LoadDomainAndUserAssemblies
0x00007ff6c8360144 (Unity) LoadUserAssemblies
0x00007ff6c885c091 (Unity) <lambda_f8411f227bdd47da33e064bfa1662688>::operator()
0x00007ff6c889ce92 (Unity) asio::detail::completion_handler<core::mutable_function<void __cdecl(void)>,asio::io_context::basic_executor_type<std::allocator<void>,0> >::do_complete
0x00007ff6c8888d7e (Unity) asio::detail::win_iocp_io_context::do_one
0x00007ff6c888a9e4 (Unity) asio::detail::win_iocp_io_context::run
0x00007ff6c889b27e (Unity) IOService::Run
0x00007ff6c886dbcf (Unity) AssetImportWorkerClient::Run
0x00007ff6c8835d13 (Unity) RunAssetImportWorkerClientV2
0x00007ff6c8835d9b (Unity) RunAssetImporterV2
0x00007ff6c8060c48 (Unity) Application::InitializeProject
0x00007ff6c84b20a0 (Unity) WinMain
0x00007ff6c987fbae (Unity) __scrt_common_main_seh
0x00007ffbd486259d (KERNEL32) BaseThreadInitThunk
0x00007ffbd4e8af78 (ntdll) RtlUserThreadStart

[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
CreateSerializedAsset is not supported while importing out-of-process
0x00007ff6c74fefad (Unity) StackWalker::GetCurrentCallstack
0x00007ff6c7505b39 (Unity) StackWalker::ShowCallstack
0x00007ff6c84a8df3 (Unity) GetStacktrace
0x00007ff6c8b7165d (Unity) DebugStringToFile
0x00007ff6c887b618 (Unity) CreateSerializedAssetV2
0x00007ff6c8810f55 (Unity) CreateSerializedAssetV2
0x00007ff6c877f8ee (Unity) AssetDatabase::CreateSerializedAsset
0x00007ff6c6cea4ff (Unity) AssetDatabaseBindings::CreateAsset
0x00007ff6c6b0b819 (Unity) AssetDatabase_CUSTOM_CreateAsset
0x000001a2c4cecdb5 (Mono JIT Code) (wrapper managed-to-native) UnityEditor.AssetDatabase:CreateAsset (UnityEngine.Object,string)
0x000001a2c4cec753 (Mono JIT Code) [PXR_ProjectSetting.cs:121] Unity.XR.PXR.PXR_ProjectSetting:GetProjectConfig () 
0x000001a2c4cec383 (Mono JIT Code) [PXR_SDKSettingEditor.cs:111] Unity.XR.PXR.Editor.PXR_SDKSettingEditor:InitializeOnLoad () 
0x000001a2bcc075c3 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
0x00007ffb3c53697e (mono-2.0-bdwgc) [mini-runtime.c:3445] mono_jit_runtime_invoke 
0x00007ffb3c46cdd4 (mono-2.0-bdwgc) [object.c:3068] do_runtime_invoke 
0x00007ffb3c4746df (mono-2.0-bdwgc) [object.c:5770] mono_runtime_try_invoke_array 
0x00007ffb3c3a4eb2 (mono-2.0-bdwgc) [icall.c:4041] ves_icall_InternalInvoke 
0x00007ffb3c3cb091 (mono-2.0-bdwgc) [icall-def.h:692] ves_icall_InternalInvoke_raw 
0x000001a2bcc4ae20 (Mono JIT Code) (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo:InternalInvoke (System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
0x000001a2bcc4a5bb (Mono JIT Code) System.Reflection.RuntimeMethodInfo:Invoke (object,System.Reflection.BindingFlags,System.Reflection.Binder,object[],System.Globalization.CultureInfo)
0x000001a2bcc4a47e (Mono JIT Code) System.Reflection.MethodBase:Invoke (object,object[])
0x000001a2c4b28c7b (Mono JIT Code) UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes ()
0x000001a2c3546b35 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
0x00007ffb3c53697e (mono-2.0-bdwgc) [mini-runtime.c:3445] mono_jit_runtime_invoke 
0x00007ffb3c46cdd4 (mono-2.0-bdwgc) [object.c:3068] do_runtime_invoke 
0x00007ffb3c46cf4c (mono-2.0-bdwgc) [object.c:3115] mono_runtime_invoke 
0x00007ff6c7421934 (Unity) scripting_method_invoke
0x00007ff6c7400b84 (Unity) ScriptingInvocation::Invoke
0x00007ff6c73fb6e5 (Unity) ScriptingInvocation::Invoke<void>
0x00007ff6c753f53b (Unity) Scripting::UnityEditor::EditorAssembliesProxy::ProcessInitializeOnLoadMethodAttributes
0x00007ff6c73f5733 (Unity) MonoManager::SetupLoadedEditorAssemblies
0x00007ff6c73eb1ed (Unity) MonoManager::EndReloadAssembly
0x00007ff6c73f2d88 (Unity) MonoManager::ReloadAssembly
0x00007ff6c835fa12 (Unity) LoadDomainAndUserAssemblies
0x00007ff6c8360144 (Unity) LoadUserAssemblies
0x00007ff6c885c091 (Unity) <lambda_f8411f227bdd47da33e064bfa1662688>::operator()
0x00007ff6c889ce92 (Unity) asio::detail::completion_handler<core::mutable_function<void __cdecl(void)>,asio::io_context::basic_executor_type<std::allocator<void>,0> >::do_complete
0x00007ff6c8888d7e (Unity) asio::detail::win_iocp_io_context::do_one
0x00007ff6c888a9e4 (Unity) asio::detail::win_iocp_io_context::run
0x00007ff6c889b27e (Unity) IOService::Run
0x00007ff6c886dbcf (Unity) AssetImportWorkerClient::Run
0x00007ff6c8835d13 (Unity) RunAssetImportWorkerClientV2
0x00007ff6c8835d9b (Unity) RunAssetImporterV2
0x00007ff6c8060c48 (Unity) Application::InitializeProject
0x00007ff6c84b20a0 (Unity) WinMain
0x00007ff6c987fbae (Unity) __scrt_common_main_seh
0x00007ffbd486259d (KERNEL32) BaseThreadInitThunk
0x00007ffbd4e8af78 (ntdll) RtlUserThreadStart

System.Reflection.TargetInvocationException: Exception has been thrown by the target of an invocation. ---> UnityEngine.UnityException: Creating asset at path Assets/Resources/PXR_ProjectSetting.asset failed.
  at (wrapper managed-to-native) UnityEditor.AssetDatabase.CreateAsset(UnityEngine.Object,string)
  at Unity.XR.PXR.PXR_ProjectSetting.GetProjectConfig () [0x0015a] in D:\PICO Unity Integration SDK-3.2.0-20250529\Runtime\Scripts\PXR_ProjectSetting.cs:117 
  at Unity.XR.PXR.Editor.PXR_SDKSettingEditor.InitializeOnLoad () [0x00000] in D:\PICO Unity Integration SDK-3.2.0-20250529\Editor\PXR_SDKSettingEditor.cs:111 
  at (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo.InternalInvoke(System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x0006a] in <688f272cc27c4df98c503f9d1f1267d0>:0 
   --- End of inner exception stack trace ---
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x00083] in <688f272cc27c4df98c503f9d1f1267d0>:0 
  at System.Reflection.MethodBase.Invoke (System.Object obj, System.Object[] parameters) [0x00000] in <688f272cc27c4df98c503f9d1f1267d0>:0 
  at UnityEditor.EditorAssemblies.ProcessInitializeOnLoadMethodAttributes () [0x000a5] in <1518627dacca44efa32edd45bc720c48>:0 
0x00007ff6c74fefad (Unity) StackWalker::GetCurrentCallstack
0x00007ff6c7505b39 (Unity) StackWalker::ShowCallstack
0x00007ff6c84a8df3 (Unity) GetStacktrace
0x00007ff6c8b7165d (Unity) DebugStringToFile
0x00007ff6c664ea32 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x000001a2bb61c083 (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
0x000001a2bb61bd5b (Mono JIT Code) UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
0x000001a2bb61bae0 (Mono JIT Code) UnityEngine.Logger:Log (UnityEngine.LogType,object)
0x000001a2c4ced045 (Mono JIT Code) UnityEngine.Debug:LogError (object)
0x000001a2c4b28c9b (Mono JIT Code) UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes ()
0x000001a2c3546b35 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
0x00007ffb3c53697e (mono-2.0-bdwgc) [mini-runtime.c:3445] mono_jit_runtime_invoke 
0x00007ffb3c46cdd4 (mono-2.0-bdwgc) [object.c:3068] do_runtime_invoke 
0x00007ffb3c46cf4c (mono-2.0-bdwgc) [object.c:3115] mono_runtime_invoke 
0x00007ff6c7421934 (Unity) scripting_method_invoke
0x00007ff6c7400b84 (Unity) ScriptingInvocation::Invoke
0x00007ff6c73fb6e5 (Unity) ScriptingInvocation::Invoke<void>
0x00007ff6c753f53b (Unity) Scripting::UnityEditor::EditorAssembliesProxy::ProcessInitializeOnLoadMethodAttributes
0x00007ff6c73f5733 (Unity) MonoManager::SetupLoadedEditorAssemblies
0x00007ff6c73eb1ed (Unity) MonoManager::EndReloadAssembly
0x00007ff6c73f2d88 (Unity) MonoManager::ReloadAssembly
0x00007ff6c835fa12 (Unity) LoadDomainAndUserAssemblies
0x00007ff6c8360144 (Unity) LoadUserAssemblies
0x00007ff6c885c091 (Unity) <lambda_f8411f227bdd47da33e064bfa1662688>::operator()
0x00007ff6c889ce92 (Unity) asio::detail::completion_handler<core::mutable_function<void __cdecl(void)>,asio::io_context::basic_executor_type<std::allocator<void>,0> >::do_complete
0x00007ff6c8888d7e (Unity) asio::detail::win_iocp_io_context::do_one
0x00007ff6c888a9e4 (Unity) asio::detail::win_iocp_io_context::run
0x00007ff6c889b27e (Unity) IOService::Run
0x00007ff6c886dbcf (Unity) AssetImportWorkerClient::Run
0x00007ff6c8835d13 (Unity) RunAssetImportWorkerClientV2
0x00007ff6c8835d9b (Unity) RunAssetImporterV2
0x00007ff6c8060c48 (Unity) Application::InitializeProject
0x00007ff6c84b20a0 (Unity) WinMain
0x00007ff6c987fbae (Unity) __scrt_common_main_seh
0x00007ffbd486259d (KERNEL32) BaseThreadInitThunk
0x00007ffbd4e8af78 (ntdll) RtlUserThreadStart

Mono: successfully reloaded assembly
- Completed reload, in  1.425 seconds
Domain Reload Profiling:
	ReloadAssembly (1425ms)
		BeginReloadAssembly (169ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (2ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (28ms)
		EndReloadAssembly (1147ms)
			LoadAssemblies (137ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (205ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (30ms)
			SetupLoadedEditorAssemblies (768ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (18ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (72ms)
				ProcessInitializeOnLoadAttributes (609ms)
				ProcessInitializeOnLoadMethodAttributes (29ms)
				AfterProcessingInitializeOnLoad (39ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (8ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 0.67 ms, found 13 plugins.
Preloading 2 native plugins for Editor in 0.27 ms.
Unloading 4752 Unused Serialized files (Serialized files now loaded: 0)
Unloading 50 unused Assets / (95.7 KB). Loaded Objects now: 5288.
Memory consumption went from 192.2 MB to 192.1 MB.
Total: 2.316400 ms (FindLiveObjects: 0.205700 ms CreateObjectMapping: 0.066000 ms MarkObjects: 2.004000 ms  DeleteObjects: 0.040100 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 415390.216952 seconds.
  path: Assets/Scenes/StartMenu.unity
  artifactKey: Guid(47ce28e285e84c84d9d92ca2251b099b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Scenes/StartMenu.unity using Guid(47ce28e285e84c84d9d92ca2251b099b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '58bd331b0d74eea8e53559a86a3a391a') in 0.008982 seconds 
Number of asset objects unloaded after import = 0
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 16.63 ms, found 13 plugins.
Preloading 2 native plugins for Editor in 0.16 ms.
Unloading 45 Unused Serialized files (Serialized files now loaded: 0)
Unloading 45 unused Assets / (95.6 KB). Loaded Objects now: 5288.
Memory consumption went from 97.7 MB to 97.6 MB.
Total: 2.105100 ms (FindLiveObjects: 0.211000 ms CreateObjectMapping: 0.083100 ms MarkObjects: 1.783700 ms  DeleteObjects: 0.026100 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 137.331708 seconds.
  path: Assets/Scenes/小U和电机.unity
  artifactKey: Guid(9fc0d4010bbf28b4594072e72b8655ab) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Scenes/小U和电机.unity using Guid(9fc0d4010bbf28b4594072e72b8655ab) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '2d09df48df74a2070981c1ca4638d291') in 0.003345 seconds 
Number of asset objects unloaded after import = 0
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.018762 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.17 ms, found 13 plugins.
Preloading 2 native plugins for Editor in 0.26 ms.
PXRLog [Build Check]RegisterBuildPlayerHandler,Already Do not show: False
0x00007ff6c74fefad (Unity) StackWalker::GetCurrentCallstack
0x00007ff6c7505b39 (Unity) StackWalker::ShowCallstack
0x00007ff6c84a8df3 (Unity) GetStacktrace
0x00007ff6c8b7165d (Unity) DebugStringToFile
0x00007ff6c664ea32 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x000001a2bff7c083 (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
0x000001a2bff7bd5b (Mono JIT Code) UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
0x000001a2bff7bae0 (Mono JIT Code) UnityEngine.Logger:Log (UnityEngine.LogType,object)
0x000001a2bff7b9a8 (Mono JIT Code) UnityEngine.Debug:Log (object)
0x000001a2bff78a53 (Mono JIT Code) [PXR_SDKBuildCheck.cs:32] Unity.XR.PXR.Editor.PXR_SDKBuildCheck:.cctor () 
0x000001a2bff87575 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
0x00007ffb3c53697e (mono-2.0-bdwgc) [mini-runtime.c:3445] mono_jit_runtime_invoke 
0x00007ffb3c46cdd4 (mono-2.0-bdwgc) [object.c:3068] do_runtime_invoke 
0x00007ffb3c467b79 (mono-2.0-bdwgc) [object.c:563] mono_runtime_class_init_full 
0x00007ffb3c39ddd7 (mono-2.0-bdwgc) [icall.c:1292] ves_icall_System_Runtime_CompilerServices_RuntimeHelpers_RunClassConstructor 
0x00007ffb3c3cfebc (mono-2.0-bdwgc) [icall-def.h:756] ves_icall_System_Runtime_CompilerServices_RuntimeHelpers_RunClassConstructor_raw 
0x000001a2bff86c1a (Mono JIT Code) (wrapper managed-to-native) System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (intptr)
0x000001a2bff86b2b (Mono JIT Code) System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
0x000001a2bff86273 (Mono JIT Code) UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])
0x000001a2ba726298 (Mono JIT Code) (wrapper runtime-invoke) <Module>:runtime_invoke_void_object (object,intptr,intptr,intptr)
0x00007ffb3c53697e (mono-2.0-bdwgc) [mini-runtime.c:3445] mono_jit_runtime_invoke 
0x00007ffb3c46cdd4 (mono-2.0-bdwgc) [object.c:3068] do_runtime_invoke 
0x00007ffb3c46cf4c (mono-2.0-bdwgc) [object.c:3115] mono_runtime_invoke 
0x00007ff6c7421934 (Unity) scripting_method_invoke
0x00007ff6c7400b84 (Unity) ScriptingInvocation::Invoke
0x00007ff6c73fb6e5 (Unity) ScriptingInvocation::Invoke<void>
0x00007ff6c753f4c3 (Unity) Scripting::UnityEditor::EditorAssembliesProxy::ProcessInitializeOnLoadAttributes
0x00007ff6c73f570b (Unity) MonoManager::SetupLoadedEditorAssemblies
0x00007ff6c73eb1ed (Unity) MonoManager::EndReloadAssembly
0x00007ff6c73f2d88 (Unity) MonoManager::ReloadAssembly
0x00007ff6c835fa12 (Unity) LoadDomainAndUserAssemblies
0x00007ff6c8360144 (Unity) LoadUserAssemblies
0x00007ff6c885c091 (Unity) <lambda_f8411f227bdd47da33e064bfa1662688>::operator()
0x00007ff6c889ce92 (Unity) asio::detail::completion_handler<core::mutable_function<void __cdecl(void)>,asio::io_context::basic_executor_type<std::allocator<void>,0> >::do_complete
0x00007ff6c8888d7e (Unity) asio::detail::win_iocp_io_context::do_one
0x00007ff6c888a9e4 (Unity) asio::detail::win_iocp_io_context::run
0x00007ff6c889b27e (Unity) IOService::Run
0x00007ff6c886dbcf (Unity) AssetImportWorkerClient::Run
0x00007ff6c8835d13 (Unity) RunAssetImportWorkerClientV2
0x00007ff6c8835d9b (Unity) RunAssetImporterV2
0x00007ff6c8060c48 (Unity) Application::InitializeProject
0x00007ff6c84b20a0 (Unity) WinMain
0x00007ff6c987fbae (Unity) __scrt_common_main_seh
0x00007ffbd486259d (KERNEL32) BaseThreadInitThunk
0x00007ffbd4e8af78 (ntdll) RtlUserThreadStart

[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
CreateSerializedAsset is not supported while importing out-of-process
0x00007ff6c74fefad (Unity) StackWalker::GetCurrentCallstack
0x00007ff6c7505b39 (Unity) StackWalker::ShowCallstack
0x00007ff6c84a8df3 (Unity) GetStacktrace
0x00007ff6c8b7165d (Unity) DebugStringToFile
0x00007ff6c887b618 (Unity) CreateSerializedAssetV2
0x00007ff6c8810f55 (Unity) CreateSerializedAssetV2
0x00007ff6c877f8ee (Unity) AssetDatabase::CreateSerializedAsset
0x00007ff6c6cea4ff (Unity) AssetDatabaseBindings::CreateAsset
0x00007ff6c6b0b819 (Unity) AssetDatabase_CUSTOM_CreateAsset
0x000001a2c6fbcdb5 (Mono JIT Code) (wrapper managed-to-native) UnityEditor.AssetDatabase:CreateAsset (UnityEngine.Object,string)
0x000001a2c6fbc753 (Mono JIT Code) [PXR_ProjectSetting.cs:121] Unity.XR.PXR.PXR_ProjectSetting:GetProjectConfig () 
0x000001a2c6fbc383 (Mono JIT Code) [PXR_SDKSettingEditor.cs:111] Unity.XR.PXR.Editor.PXR_SDKSettingEditor:InitializeOnLoad () 
0x000001a2bff875c3 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
0x00007ffb3c53697e (mono-2.0-bdwgc) [mini-runtime.c:3445] mono_jit_runtime_invoke 
0x00007ffb3c46cdd4 (mono-2.0-bdwgc) [object.c:3068] do_runtime_invoke 
0x00007ffb3c4746df (mono-2.0-bdwgc) [object.c:5770] mono_runtime_try_invoke_array 
0x00007ffb3c3a4eb2 (mono-2.0-bdwgc) [icall.c:4041] ves_icall_InternalInvoke 
0x00007ffb3c3cb091 (mono-2.0-bdwgc) [icall-def.h:692] ves_icall_InternalInvoke_raw 
0x000001a2c4ceae20 (Mono JIT Code) (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo:InternalInvoke (System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
0x000001a2c4cea5bb (Mono JIT Code) System.Reflection.RuntimeMethodInfo:Invoke (object,System.Reflection.BindingFlags,System.Reflection.Binder,object[],System.Globalization.CultureInfo)
0x000001a2c4cea47e (Mono JIT Code) System.Reflection.MethodBase:Invoke (object,object[])
0x000001a2c5af8c7b (Mono JIT Code) UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes ()
0x000001a2ba726b35 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
0x00007ffb3c53697e (mono-2.0-bdwgc) [mini-runtime.c:3445] mono_jit_runtime_invoke 
0x00007ffb3c46cdd4 (mono-2.0-bdwgc) [object.c:3068] do_runtime_invoke 
0x00007ffb3c46cf4c (mono-2.0-bdwgc) [object.c:3115] mono_runtime_invoke 
0x00007ff6c7421934 (Unity) scripting_method_invoke
0x00007ff6c7400b84 (Unity) ScriptingInvocation::Invoke
0x00007ff6c73fb6e5 (Unity) ScriptingInvocation::Invoke<void>
0x00007ff6c753f53b (Unity) Scripting::UnityEditor::EditorAssembliesProxy::ProcessInitializeOnLoadMethodAttributes
0x00007ff6c73f5733 (Unity) MonoManager::SetupLoadedEditorAssemblies
0x00007ff6c73eb1ed (Unity) MonoManager::EndReloadAssembly
0x00007ff6c73f2d88 (Unity) MonoManager::ReloadAssembly
0x00007ff6c835fa12 (Unity) LoadDomainAndUserAssemblies
0x00007ff6c8360144 (Unity) LoadUserAssemblies
0x00007ff6c885c091 (Unity) <lambda_f8411f227bdd47da33e064bfa1662688>::operator()
0x00007ff6c889ce92 (Unity) asio::detail::completion_handler<core::mutable_function<void __cdecl(void)>,asio::io_context::basic_executor_type<std::allocator<void>,0> >::do_complete
0x00007ff6c8888d7e (Unity) asio::detail::win_iocp_io_context::do_one
0x00007ff6c888a9e4 (Unity) asio::detail::win_iocp_io_context::run
0x00007ff6c889b27e (Unity) IOService::Run
0x00007ff6c886dbcf (Unity) AssetImportWorkerClient::Run
0x00007ff6c8835d13 (Unity) RunAssetImportWorkerClientV2
0x00007ff6c8835d9b (Unity) RunAssetImporterV2
0x00007ff6c8060c48 (Unity) Application::InitializeProject
0x00007ff6c84b20a0 (Unity) WinMain
0x00007ff6c987fbae (Unity) __scrt_common_main_seh
0x00007ffbd486259d (KERNEL32) BaseThreadInitThunk
0x00007ffbd4e8af78 (ntdll) RtlUserThreadStart

System.Reflection.TargetInvocationException: Exception has been thrown by the target of an invocation. ---> UnityEngine.UnityException: Creating asset at path Assets/Resources/PXR_ProjectSetting.asset failed.
  at (wrapper managed-to-native) UnityEditor.AssetDatabase.CreateAsset(UnityEngine.Object,string)
  at Unity.XR.PXR.PXR_ProjectSetting.GetProjectConfig () [0x0015a] in D:\PICO Unity Integration SDK-3.2.0-20250529\Runtime\Scripts\PXR_ProjectSetting.cs:117 
  at Unity.XR.PXR.Editor.PXR_SDKSettingEditor.InitializeOnLoad () [0x00000] in D:\PICO Unity Integration SDK-3.2.0-20250529\Editor\PXR_SDKSettingEditor.cs:111 
  at (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo.InternalInvoke(System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x0006a] in <688f272cc27c4df98c503f9d1f1267d0>:0 
   --- End of inner exception stack trace ---
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x00083] in <688f272cc27c4df98c503f9d1f1267d0>:0 
  at System.Reflection.MethodBase.Invoke (System.Object obj, System.Object[] parameters) [0x00000] in <688f272cc27c4df98c503f9d1f1267d0>:0 
  at UnityEditor.EditorAssemblies.ProcessInitializeOnLoadMethodAttributes () [0x000a5] in <1518627dacca44efa32edd45bc720c48>:0 
0x00007ff6c74fefad (Unity) StackWalker::GetCurrentCallstack
0x00007ff6c7505b39 (Unity) StackWalker::ShowCallstack
0x00007ff6c84a8df3 (Unity) GetStacktrace
0x00007ff6c8b7165d (Unity) DebugStringToFile
0x00007ff6c664ea32 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x000001a2bff7c083 (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
0x000001a2bff7bd5b (Mono JIT Code) UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
0x000001a2bff7bae0 (Mono JIT Code) UnityEngine.Logger:Log (UnityEngine.LogType,object)
0x000001a2c6fbd045 (Mono JIT Code) UnityEngine.Debug:LogError (object)
0x000001a2c5af8c9b (Mono JIT Code) UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes ()
0x000001a2ba726b35 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
0x00007ffb3c53697e (mono-2.0-bdwgc) [mini-runtime.c:3445] mono_jit_runtime_invoke 
0x00007ffb3c46cdd4 (mono-2.0-bdwgc) [object.c:3068] do_runtime_invoke 
0x00007ffb3c46cf4c (mono-2.0-bdwgc) [object.c:3115] mono_runtime_invoke 
0x00007ff6c7421934 (Unity) scripting_method_invoke
0x00007ff6c7400b84 (Unity) ScriptingInvocation::Invoke
0x00007ff6c73fb6e5 (Unity) ScriptingInvocation::Invoke<void>
0x00007ff6c753f53b (Unity) Scripting::UnityEditor::EditorAssembliesProxy::ProcessInitializeOnLoadMethodAttributes
0x00007ff6c73f5733 (Unity) MonoManager::SetupLoadedEditorAssemblies
0x00007ff6c73eb1ed (Unity) MonoManager::EndReloadAssembly
0x00007ff6c73f2d88 (Unity) MonoManager::ReloadAssembly
0x00007ff6c835fa12 (Unity) LoadDomainAndUserAssemblies
0x00007ff6c8360144 (Unity) LoadUserAssemblies
0x00007ff6c885c091 (Unity) <lambda_f8411f227bdd47da33e064bfa1662688>::operator()
0x00007ff6c889ce92 (Unity) asio::detail::completion_handler<core::mutable_function<void __cdecl(void)>,asio::io_context::basic_executor_type<std::allocator<void>,0> >::do_complete
0x00007ff6c8888d7e (Unity) asio::detail::win_iocp_io_context::do_one
0x00007ff6c888a9e4 (Unity) asio::detail::win_iocp_io_context::run
0x00007ff6c889b27e (Unity) IOService::Run
0x00007ff6c886dbcf (Unity) AssetImportWorkerClient::Run
0x00007ff6c8835d13 (Unity) RunAssetImportWorkerClientV2
0x00007ff6c8835d9b (Unity) RunAssetImporterV2
0x00007ff6c8060c48 (Unity) Application::InitializeProject
0x00007ff6c84b20a0 (Unity) WinMain
0x00007ff6c987fbae (Unity) __scrt_common_main_seh
0x00007ffbd486259d (KERNEL32) BaseThreadInitThunk
0x00007ffbd4e8af78 (ntdll) RtlUserThreadStart

Mono: successfully reloaded assembly
- Completed reload, in  1.619 seconds
Domain Reload Profiling:
	ReloadAssembly (1619ms)
		BeginReloadAssembly (222ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (4ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (49ms)
		EndReloadAssembly (1267ms)
			LoadAssemblies (175ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (241ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (39ms)
			SetupLoadedEditorAssemblies (808ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (20ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (2ms)
				BeforeProcessingInitializeOnLoad (76ms)
				ProcessInitializeOnLoadAttributes (632ms)
				ProcessInitializeOnLoadMethodAttributes (35ms)
				AfterProcessingInitializeOnLoad (43ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (11ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 0.82 ms, found 13 plugins.
Preloading 2 native plugins for Editor in 0.20 ms.
Unloading 4752 Unused Serialized files (Serialized files now loaded: 0)
Unloading 50 unused Assets / (95.6 KB). Loaded Objects now: 5305.
Memory consumption went from 192.3 MB to 192.2 MB.
Total: 3.451900 ms (FindLiveObjects: 0.229700 ms CreateObjectMapping: 0.123100 ms MarkObjects: 3.041000 ms  DeleteObjects: 0.057100 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.015614 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 0.67 ms, found 13 plugins.
Preloading 2 native plugins for Editor in 0.29 ms.
PXRLog [Build Check]RegisterBuildPlayerHandler,Already Do not show: False
0x00007ff6c74fefad (Unity) StackWalker::GetCurrentCallstack
0x00007ff6c7505b39 (Unity) StackWalker::ShowCallstack
0x00007ff6c84a8df3 (Unity) GetStacktrace
0x00007ff6c8b7165d (Unity) DebugStringToFile
0x00007ff6c664ea32 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x000001a2c354c083 (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
0x000001a2c354bd5b (Mono JIT Code) UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
0x000001a2c354bae0 (Mono JIT Code) UnityEngine.Logger:Log (UnityEngine.LogType,object)
0x000001a2c354b9a8 (Mono JIT Code) UnityEngine.Debug:Log (object)
0x000001a2c3548a53 (Mono JIT Code) [PXR_SDKBuildCheck.cs:32] Unity.XR.PXR.Editor.PXR_SDKBuildCheck:.cctor () 
0x000001a2c4b17575 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
0x00007ffb3c53697e (mono-2.0-bdwgc) [mini-runtime.c:3445] mono_jit_runtime_invoke 
0x00007ffb3c46cdd4 (mono-2.0-bdwgc) [object.c:3068] do_runtime_invoke 
0x00007ffb3c467b79 (mono-2.0-bdwgc) [object.c:563] mono_runtime_class_init_full 
0x00007ffb3c39ddd7 (mono-2.0-bdwgc) [icall.c:1292] ves_icall_System_Runtime_CompilerServices_RuntimeHelpers_RunClassConstructor 
0x00007ffb3c3cfebc (mono-2.0-bdwgc) [icall-def.h:756] ves_icall_System_Runtime_CompilerServices_RuntimeHelpers_RunClassConstructor_raw 
0x000001a2c4b16c1a (Mono JIT Code) (wrapper managed-to-native) System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (intptr)
0x000001a2c4b16b2b (Mono JIT Code) System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
0x000001a2c4b16273 (Mono JIT Code) UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])
0x000001a2bcc26298 (Mono JIT Code) (wrapper runtime-invoke) <Module>:runtime_invoke_void_object (object,intptr,intptr,intptr)
0x00007ffb3c53697e (mono-2.0-bdwgc) [mini-runtime.c:3445] mono_jit_runtime_invoke 
0x00007ffb3c46cdd4 (mono-2.0-bdwgc) [object.c:3068] do_runtime_invoke 
0x00007ffb3c46cf4c (mono-2.0-bdwgc) [object.c:3115] mono_runtime_invoke 
0x00007ff6c7421934 (Unity) scripting_method_invoke
0x00007ff6c7400b84 (Unity) ScriptingInvocation::Invoke
0x00007ff6c73fb6e5 (Unity) ScriptingInvocation::Invoke<void>
0x00007ff6c753f4c3 (Unity) Scripting::UnityEditor::EditorAssembliesProxy::ProcessInitializeOnLoadAttributes
0x00007ff6c73f570b (Unity) MonoManager::SetupLoadedEditorAssemblies
0x00007ff6c73eb1ed (Unity) MonoManager::EndReloadAssembly
0x00007ff6c73f2d88 (Unity) MonoManager::ReloadAssembly
0x00007ff6c835fa12 (Unity) LoadDomainAndUserAssemblies
0x00007ff6c8360144 (Unity) LoadUserAssemblies
0x00007ff6c885c091 (Unity) <lambda_f8411f227bdd47da33e064bfa1662688>::operator()
0x00007ff6c889ce92 (Unity) asio::detail::completion_handler<core::mutable_function<void __cdecl(void)>,asio::io_context::basic_executor_type<std::allocator<void>,0> >::do_complete
0x00007ff6c8888d7e (Unity) asio::detail::win_iocp_io_context::do_one
0x00007ff6c888a9e4 (Unity) asio::detail::win_iocp_io_context::run
0x00007ff6c889b27e (Unity) IOService::Run
0x00007ff6c886dbcf (Unity) AssetImportWorkerClient::Run
0x00007ff6c8835d13 (Unity) RunAssetImportWorkerClientV2
0x00007ff6c8835d9b (Unity) RunAssetImporterV2
0x00007ff6c8060c48 (Unity) Application::InitializeProject
0x00007ff6c84b20a0 (Unity) WinMain
0x00007ff6c987fbae (Unity) __scrt_common_main_seh
0x00007ffbd486259d (KERNEL32) BaseThreadInitThunk
0x00007ffbd4e8af78 (ntdll) RtlUserThreadStart

[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
CreateSerializedAsset is not supported while importing out-of-process
0x00007ff6c74fefad (Unity) StackWalker::GetCurrentCallstack
0x00007ff6c7505b39 (Unity) StackWalker::ShowCallstack
0x00007ff6c84a8df3 (Unity) GetStacktrace
0x00007ff6c8b7165d (Unity) DebugStringToFile
0x00007ff6c887b618 (Unity) CreateSerializedAssetV2
0x00007ff6c8810f55 (Unity) CreateSerializedAssetV2
0x00007ff6c877f8ee (Unity) AssetDatabase::CreateSerializedAsset
0x00007ff6c6cea4ff (Unity) AssetDatabaseBindings::CreateAsset
0x00007ff6c6b0b819 (Unity) AssetDatabase_CUSTOM_CreateAsset
0x000001a2c355cdb5 (Mono JIT Code) (wrapper managed-to-native) UnityEditor.AssetDatabase:CreateAsset (UnityEngine.Object,string)
0x000001a2c355c753 (Mono JIT Code) [PXR_ProjectSetting.cs:121] Unity.XR.PXR.PXR_ProjectSetting:GetProjectConfig () 
0x000001a2c355c383 (Mono JIT Code) [PXR_SDKSettingEditor.cs:111] Unity.XR.PXR.Editor.PXR_SDKSettingEditor:InitializeOnLoad () 
0x000001a2c4b175c3 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
0x00007ffb3c53697e (mono-2.0-bdwgc) [mini-runtime.c:3445] mono_jit_runtime_invoke 
0x00007ffb3c46cdd4 (mono-2.0-bdwgc) [object.c:3068] do_runtime_invoke 
0x00007ffb3c4746df (mono-2.0-bdwgc) [object.c:5770] mono_runtime_try_invoke_array 
0x00007ffb3c3a4eb2 (mono-2.0-bdwgc) [icall.c:4041] ves_icall_InternalInvoke 
0x00007ffb3c3cb091 (mono-2.0-bdwgc) [icall-def.h:692] ves_icall_InternalInvoke_raw 
0x000001a2c6fbae20 (Mono JIT Code) (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo:InternalInvoke (System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
0x000001a2c6fba5bb (Mono JIT Code) System.Reflection.RuntimeMethodInfo:Invoke (object,System.Reflection.BindingFlags,System.Reflection.Binder,object[],System.Globalization.CultureInfo)
0x000001a2c6fba47e (Mono JIT Code) System.Reflection.MethodBase:Invoke (object,object[])
0x000001a2c340905b (Mono JIT Code) UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes ()
0x000001a2bcc26b35 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
0x00007ffb3c53697e (mono-2.0-bdwgc) [mini-runtime.c:3445] mono_jit_runtime_invoke 
0x00007ffb3c46cdd4 (mono-2.0-bdwgc) [object.c:3068] do_runtime_invoke 
0x00007ffb3c46cf4c (mono-2.0-bdwgc) [object.c:3115] mono_runtime_invoke 
0x00007ff6c7421934 (Unity) scripting_method_invoke
0x00007ff6c7400b84 (Unity) ScriptingInvocation::Invoke
0x00007ff6c73fb6e5 (Unity) ScriptingInvocation::Invoke<void>
0x00007ff6c753f53b (Unity) Scripting::UnityEditor::EditorAssembliesProxy::ProcessInitializeOnLoadMethodAttributes
0x00007ff6c73f5733 (Unity) MonoManager::SetupLoadedEditorAssemblies
0x00007ff6c73eb1ed (Unity) MonoManager::EndReloadAssembly
0x00007ff6c73f2d88 (Unity) MonoManager::ReloadAssembly
0x00007ff6c835fa12 (Unity) LoadDomainAndUserAssemblies
0x00007ff6c8360144 (Unity) LoadUserAssemblies
0x00007ff6c885c091 (Unity) <lambda_f8411f227bdd47da33e064bfa1662688>::operator()
0x00007ff6c889ce92 (Unity) asio::detail::completion_handler<core::mutable_function<void __cdecl(void)>,asio::io_context::basic_executor_type<std::allocator<void>,0> >::do_complete
0x00007ff6c8888d7e (Unity) asio::detail::win_iocp_io_context::do_one
0x00007ff6c888a9e4 (Unity) asio::detail::win_iocp_io_context::run
0x00007ff6c889b27e (Unity) IOService::Run
0x00007ff6c886dbcf (Unity) AssetImportWorkerClient::Run
0x00007ff6c8835d13 (Unity) RunAssetImportWorkerClientV2
0x00007ff6c8835d9b (Unity) RunAssetImporterV2
0x00007ff6c8060c48 (Unity) Application::InitializeProject
0x00007ff6c84b20a0 (Unity) WinMain
0x00007ff6c987fbae (Unity) __scrt_common_main_seh
0x00007ffbd486259d (KERNEL32) BaseThreadInitThunk
0x00007ffbd4e8af78 (ntdll) RtlUserThreadStart

System.Reflection.TargetInvocationException: Exception has been thrown by the target of an invocation. ---> UnityEngine.UnityException: Creating asset at path Assets/Resources/PXR_ProjectSetting.asset failed.
  at (wrapper managed-to-native) UnityEditor.AssetDatabase.CreateAsset(UnityEngine.Object,string)
  at Unity.XR.PXR.PXR_ProjectSetting.GetProjectConfig () [0x0015a] in D:\PICO Unity Integration SDK-3.2.0-20250529\Runtime\Scripts\PXR_ProjectSetting.cs:117 
  at Unity.XR.PXR.Editor.PXR_SDKSettingEditor.InitializeOnLoad () [0x00000] in D:\PICO Unity Integration SDK-3.2.0-20250529\Editor\PXR_SDKSettingEditor.cs:111 
  at (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo.InternalInvoke(System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x0006a] in <688f272cc27c4df98c503f9d1f1267d0>:0 
   --- End of inner exception stack trace ---
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x00083] in <688f272cc27c4df98c503f9d1f1267d0>:0 
  at System.Reflection.MethodBase.Invoke (System.Object obj, System.Object[] parameters) [0x00000] in <688f272cc27c4df98c503f9d1f1267d0>:0 
  at UnityEditor.EditorAssemblies.ProcessInitializeOnLoadMethodAttributes () [0x000a5] in <1518627dacca44efa32edd45bc720c48>:0 
0x00007ff6c74fefad (Unity) StackWalker::GetCurrentCallstack
0x00007ff6c7505b39 (Unity) StackWalker::ShowCallstack
0x00007ff6c84a8df3 (Unity) GetStacktrace
0x00007ff6c8b7165d (Unity) DebugStringToFile
0x00007ff6c664ea32 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x000001a2c354c083 (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
0x000001a2c354bd5b (Mono JIT Code) UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
0x000001a2c354bae0 (Mono JIT Code) UnityEngine.Logger:Log (UnityEngine.LogType,object)
0x000001a2c355d045 (Mono JIT Code) UnityEngine.Debug:LogError (object)
0x000001a2c340907b (Mono JIT Code) UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes ()
0x000001a2bcc26b35 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
0x00007ffb3c53697e (mono-2.0-bdwgc) [mini-runtime.c:3445] mono_jit_runtime_invoke 
0x00007ffb3c46cdd4 (mono-2.0-bdwgc) [object.c:3068] do_runtime_invoke 
0x00007ffb3c46cf4c (mono-2.0-bdwgc) [object.c:3115] mono_runtime_invoke 
0x00007ff6c7421934 (Unity) scripting_method_invoke
0x00007ff6c7400b84 (Unity) ScriptingInvocation::Invoke
0x00007ff6c73fb6e5 (Unity) ScriptingInvocation::Invoke<void>
0x00007ff6c753f53b (Unity) Scripting::UnityEditor::EditorAssembliesProxy::ProcessInitializeOnLoadMethodAttributes
0x00007ff6c73f5733 (Unity) MonoManager::SetupLoadedEditorAssemblies
0x00007ff6c73eb1ed (Unity) MonoManager::EndReloadAssembly
0x00007ff6c73f2d88 (Unity) MonoManager::ReloadAssembly
0x00007ff6c835fa12 (Unity) LoadDomainAndUserAssemblies
0x00007ff6c8360144 (Unity) LoadUserAssemblies
0x00007ff6c885c091 (Unity) <lambda_f8411f227bdd47da33e064bfa1662688>::operator()
0x00007ff6c889ce92 (Unity) asio::detail::completion_handler<core::mutable_function<void __cdecl(void)>,asio::io_context::basic_executor_type<std::allocator<void>,0> >::do_complete
0x00007ff6c8888d7e (Unity) asio::detail::win_iocp_io_context::do_one
0x00007ff6c888a9e4 (Unity) asio::detail::win_iocp_io_context::run
0x00007ff6c889b27e (Unity) IOService::Run
0x00007ff6c886dbcf (Unity) AssetImportWorkerClient::Run
0x00007ff6c8835d13 (Unity) RunAssetImportWorkerClientV2
0x00007ff6c8835d9b (Unity) RunAssetImporterV2
0x00007ff6c8060c48 (Unity) Application::InitializeProject
0x00007ff6c84b20a0 (Unity) WinMain
0x00007ff6c987fbae (Unity) __scrt_common_main_seh
0x00007ffbd486259d (KERNEL32) BaseThreadInitThunk
0x00007ffbd4e8af78 (ntdll) RtlUserThreadStart

Mono: successfully reloaded assembly
- Completed reload, in  1.392 seconds
Domain Reload Profiling:
	ReloadAssembly (1392ms)
		BeginReloadAssembly (175ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (2ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (29ms)
		EndReloadAssembly (1109ms)
			LoadAssemblies (138ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (205ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (29ms)
			SetupLoadedEditorAssemblies (731ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (18ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (69ms)
				ProcessInitializeOnLoadAttributes (581ms)
				ProcessInitializeOnLoadMethodAttributes (26ms)
				AfterProcessingInitializeOnLoad (36ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (6ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 0.80 ms, found 13 plugins.
Preloading 2 native plugins for Editor in 0.21 ms.
Unloading 4752 Unused Serialized files (Serialized files now loaded: 0)
Unloading 50 unused Assets / (95.7 KB). Loaded Objects now: 5322.
Memory consumption went from 192.3 MB to 192.3 MB.
Total: 2.144500 ms (FindLiveObjects: 0.186400 ms CreateObjectMapping: 0.062200 ms MarkObjects: 1.859100 ms  DeleteObjects: 0.036100 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 190.649153 seconds.
  path: Assets/Scenes/小U和电机.unity
  artifactKey: Guid(9fc0d4010bbf28b4594072e72b8655ab) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Scenes/小U和电机.unity using Guid(9fc0d4010bbf28b4594072e72b8655ab) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '033a99130bfe4ec179e4525df5d49afc') in 0.006203 seconds 
Number of asset objects unloaded after import = 0
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.027082 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 0.60 ms, found 13 plugins.
Preloading 2 native plugins for Editor in 0.20 ms.
PXRLog [Build Check]RegisterBuildPlayerHandler,Already Do not show: False
0x00007ff6c74fefad (Unity) StackWalker::GetCurrentCallstack
0x00007ff6c7505b39 (Unity) StackWalker::ShowCallstack
0x00007ff6c84a8df3 (Unity) GetStacktrace
0x00007ff6c8b7165d (Unity) DebugStringToFile
0x00007ff6c664ea32 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x000001a2bef9c083 (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
0x000001a2bef9bd5b (Mono JIT Code) UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
0x000001a2bef9bae0 (Mono JIT Code) UnityEngine.Logger:Log (UnityEngine.LogType,object)
0x000001a2bef9b9a8 (Mono JIT Code) UnityEngine.Debug:Log (object)
0x000001a2bef98a53 (Mono JIT Code) [PXR_SDKBuildCheck.cs:32] Unity.XR.PXR.Editor.PXR_SDKBuildCheck:.cctor () 
0x000001a2bff77575 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
0x00007ffb3c53697e (mono-2.0-bdwgc) [mini-runtime.c:3445] mono_jit_runtime_invoke 
0x00007ffb3c46cdd4 (mono-2.0-bdwgc) [object.c:3068] do_runtime_invoke 
0x00007ffb3c467b79 (mono-2.0-bdwgc) [object.c:563] mono_runtime_class_init_full 
0x00007ffb3c39ddd7 (mono-2.0-bdwgc) [icall.c:1292] ves_icall_System_Runtime_CompilerServices_RuntimeHelpers_RunClassConstructor 
0x00007ffb3c3cfebc (mono-2.0-bdwgc) [icall-def.h:756] ves_icall_System_Runtime_CompilerServices_RuntimeHelpers_RunClassConstructor_raw 
0x000001a2bff76c1a (Mono JIT Code) (wrapper managed-to-native) System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (intptr)
0x000001a2bff76b2b (Mono JIT Code) System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
0x000001a2bff76273 (Mono JIT Code) UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])
0x000001a2c5af6298 (Mono JIT Code) (wrapper runtime-invoke) <Module>:runtime_invoke_void_object (object,intptr,intptr,intptr)
0x00007ffb3c53697e (mono-2.0-bdwgc) [mini-runtime.c:3445] mono_jit_runtime_invoke 
0x00007ffb3c46cdd4 (mono-2.0-bdwgc) [object.c:3068] do_runtime_invoke 
0x00007ffb3c46cf4c (mono-2.0-bdwgc) [object.c:3115] mono_runtime_invoke 
0x00007ff6c7421934 (Unity) scripting_method_invoke
0x00007ff6c7400b84 (Unity) ScriptingInvocation::Invoke
0x00007ff6c73fb6e5 (Unity) ScriptingInvocation::Invoke<void>
0x00007ff6c753f4c3 (Unity) Scripting::UnityEditor::EditorAssembliesProxy::ProcessInitializeOnLoadAttributes
0x00007ff6c73f570b (Unity) MonoManager::SetupLoadedEditorAssemblies
0x00007ff6c73eb1ed (Unity) MonoManager::EndReloadAssembly
0x00007ff6c73f2d88 (Unity) MonoManager::ReloadAssembly
0x00007ff6c835fa12 (Unity) LoadDomainAndUserAssemblies
0x00007ff6c8360144 (Unity) LoadUserAssemblies
0x00007ff6c885c091 (Unity) <lambda_f8411f227bdd47da33e064bfa1662688>::operator()
0x00007ff6c889ce92 (Unity) asio::detail::completion_handler<core::mutable_function<void __cdecl(void)>,asio::io_context::basic_executor_type<std::allocator<void>,0> >::do_complete
0x00007ff6c8888d7e (Unity) asio::detail::win_iocp_io_context::do_one
0x00007ff6c888a9e4 (Unity) asio::detail::win_iocp_io_context::run
0x00007ff6c889b27e (Unity) IOService::Run
0x00007ff6c886dbcf (Unity) AssetImportWorkerClient::Run
0x00007ff6c8835d13 (Unity) RunAssetImportWorkerClientV2
0x00007ff6c8835d9b (Unity) RunAssetImporterV2
0x00007ff6c8060c48 (Unity) Application::InitializeProject
0x00007ff6c84b20a0 (Unity) WinMain
0x00007ff6c987fbae (Unity) __scrt_common_main_seh
0x00007ffbd486259d (KERNEL32) BaseThreadInitThunk
0x00007ffbd4e8af78 (ntdll) RtlUserThreadStart

[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
CreateSerializedAsset is not supported while importing out-of-process
0x00007ff6c74fefad (Unity) StackWalker::GetCurrentCallstack
0x00007ff6c7505b39 (Unity) StackWalker::ShowCallstack
0x00007ff6c84a8df3 (Unity) GetStacktrace
0x00007ff6c8b7165d (Unity) DebugStringToFile
0x00007ff6c887b618 (Unity) CreateSerializedAssetV2
0x00007ff6c8810f55 (Unity) CreateSerializedAssetV2
0x00007ff6c877f8ee (Unity) AssetDatabase::CreateSerializedAsset
0x00007ff6c6cea4ff (Unity) AssetDatabaseBindings::CreateAsset
0x00007ff6c6b0b819 (Unity) AssetDatabase_CUSTOM_CreateAsset
0x000001a2c4d0cdb5 (Mono JIT Code) (wrapper managed-to-native) UnityEditor.AssetDatabase:CreateAsset (UnityEngine.Object,string)
0x000001a2c4d0c753 (Mono JIT Code) [PXR_ProjectSetting.cs:121] Unity.XR.PXR.PXR_ProjectSetting:GetProjectConfig () 
0x000001a2c4d0c383 (Mono JIT Code) [PXR_SDKSettingEditor.cs:111] Unity.XR.PXR.Editor.PXR_SDKSettingEditor:InitializeOnLoad () 
0x000001a2bff775c3 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
0x00007ffb3c53697e (mono-2.0-bdwgc) [mini-runtime.c:3445] mono_jit_runtime_invoke 
0x00007ffb3c46cdd4 (mono-2.0-bdwgc) [object.c:3068] do_runtime_invoke 
0x00007ffb3c4746df (mono-2.0-bdwgc) [object.c:5770] mono_runtime_try_invoke_array 
0x00007ffb3c3a4eb2 (mono-2.0-bdwgc) [icall.c:4041] ves_icall_InternalInvoke 
0x00007ffb3c3cb091 (mono-2.0-bdwgc) [icall-def.h:692] ves_icall_InternalInvoke_raw 
0x000001a2c355ae20 (Mono JIT Code) (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo:InternalInvoke (System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
0x000001a2c355a5bb (Mono JIT Code) System.Reflection.RuntimeMethodInfo:Invoke (object,System.Reflection.BindingFlags,System.Reflection.Binder,object[],System.Globalization.CultureInfo)
0x000001a2c355a47e (Mono JIT Code) System.Reflection.MethodBase:Invoke (object,object[])
0x000001a2c4cf905b (Mono JIT Code) UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes ()
0x000001a2c5af6b35 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
0x00007ffb3c53697e (mono-2.0-bdwgc) [mini-runtime.c:3445] mono_jit_runtime_invoke 
0x00007ffb3c46cdd4 (mono-2.0-bdwgc) [object.c:3068] do_runtime_invoke 
0x00007ffb3c46cf4c (mono-2.0-bdwgc) [object.c:3115] mono_runtime_invoke 
0x00007ff6c7421934 (Unity) scripting_method_invoke
0x00007ff6c7400b84 (Unity) ScriptingInvocation::Invoke
0x00007ff6c73fb6e5 (Unity) ScriptingInvocation::Invoke<void>
0x00007ff6c753f53b (Unity) Scripting::UnityEditor::EditorAssembliesProxy::ProcessInitializeOnLoadMethodAttributes
0x00007ff6c73f5733 (Unity) MonoManager::SetupLoadedEditorAssemblies
0x00007ff6c73eb1ed (Unity) MonoManager::EndReloadAssembly
0x00007ff6c73f2d88 (Unity) MonoManager::ReloadAssembly
0x00007ff6c835fa12 (Unity) LoadDomainAndUserAssemblies
0x00007ff6c8360144 (Unity) LoadUserAssemblies
0x00007ff6c885c091 (Unity) <lambda_f8411f227bdd47da33e064bfa1662688>::operator()
0x00007ff6c889ce92 (Unity) asio::detail::completion_handler<core::mutable_function<void __cdecl(void)>,asio::io_context::basic_executor_type<std::allocator<void>,0> >::do_complete
0x00007ff6c8888d7e (Unity) asio::detail::win_iocp_io_context::do_one
0x00007ff6c888a9e4 (Unity) asio::detail::win_iocp_io_context::run
0x00007ff6c889b27e (Unity) IOService::Run
0x00007ff6c886dbcf (Unity) AssetImportWorkerClient::Run
0x00007ff6c8835d13 (Unity) RunAssetImportWorkerClientV2
0x00007ff6c8835d9b (Unity) RunAssetImporterV2
0x00007ff6c8060c48 (Unity) Application::InitializeProject
0x00007ff6c84b20a0 (Unity) WinMain
0x00007ff6c987fbae (Unity) __scrt_common_main_seh
0x00007ffbd486259d (KERNEL32) BaseThreadInitThunk
0x00007ffbd4e8af78 (ntdll) RtlUserThreadStart

System.Reflection.TargetInvocationException: Exception has been thrown by the target of an invocation. ---> UnityEngine.UnityException: Creating asset at path Assets/Resources/PXR_ProjectSetting.asset failed.
  at (wrapper managed-to-native) UnityEditor.AssetDatabase.CreateAsset(UnityEngine.Object,string)
  at Unity.XR.PXR.PXR_ProjectSetting.GetProjectConfig () [0x0015a] in D:\PICO Unity Integration SDK-3.2.0-20250529\Runtime\Scripts\PXR_ProjectSetting.cs:117 
  at Unity.XR.PXR.Editor.PXR_SDKSettingEditor.InitializeOnLoad () [0x00000] in D:\PICO Unity Integration SDK-3.2.0-20250529\Editor\PXR_SDKSettingEditor.cs:111 
  at (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo.InternalInvoke(System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x0006a] in <688f272cc27c4df98c503f9d1f1267d0>:0 
   --- End of inner exception stack trace ---
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x00083] in <688f272cc27c4df98c503f9d1f1267d0>:0 
  at System.Reflection.MethodBase.Invoke (System.Object obj, System.Object[] parameters) [0x00000] in <688f272cc27c4df98c503f9d1f1267d0>:0 
  at UnityEditor.EditorAssemblies.ProcessInitializeOnLoadMethodAttributes () [0x000a5] in <1518627dacca44efa32edd45bc720c48>:0 
0x00007ff6c74fefad (Unity) StackWalker::GetCurrentCallstack
0x00007ff6c7505b39 (Unity) StackWalker::ShowCallstack
0x00007ff6c84a8df3 (Unity) GetStacktrace
0x00007ff6c8b7165d (Unity) DebugStringToFile
0x00007ff6c664ea32 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x000001a2bef9c083 (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
0x000001a2bef9bd5b (Mono JIT Code) UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
0x000001a2bef9bae0 (Mono JIT Code) UnityEngine.Logger:Log (UnityEngine.LogType,object)
0x000001a2c4d0d045 (Mono JIT Code) UnityEngine.Debug:LogError (object)
0x000001a2c4cf907b (Mono JIT Code) UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes ()
0x000001a2c5af6b35 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
0x00007ffb3c53697e (mono-2.0-bdwgc) [mini-runtime.c:3445] mono_jit_runtime_invoke 
0x00007ffb3c46cdd4 (mono-2.0-bdwgc) [object.c:3068] do_runtime_invoke 
0x00007ffb3c46cf4c (mono-2.0-bdwgc) [object.c:3115] mono_runtime_invoke 
0x00007ff6c7421934 (Unity) scripting_method_invoke
0x00007ff6c7400b84 (Unity) ScriptingInvocation::Invoke
0x00007ff6c73fb6e5 (Unity) ScriptingInvocation::Invoke<void>
0x00007ff6c753f53b (Unity) Scripting::UnityEditor::EditorAssembliesProxy::ProcessInitializeOnLoadMethodAttributes
0x00007ff6c73f5733 (Unity) MonoManager::SetupLoadedEditorAssemblies
0x00007ff6c73eb1ed (Unity) MonoManager::EndReloadAssembly
0x00007ff6c73f2d88 (Unity) MonoManager::ReloadAssembly
0x00007ff6c835fa12 (Unity) LoadDomainAndUserAssemblies
0x00007ff6c8360144 (Unity) LoadUserAssemblies
0x00007ff6c885c091 (Unity) <lambda_f8411f227bdd47da33e064bfa1662688>::operator()
0x00007ff6c889ce92 (Unity) asio::detail::completion_handler<core::mutable_function<void __cdecl(void)>,asio::io_context::basic_executor_type<std::allocator<void>,0> >::do_complete
0x00007ff6c8888d7e (Unity) asio::detail::win_iocp_io_context::do_one
0x00007ff6c888a9e4 (Unity) asio::detail::win_iocp_io_context::run
0x00007ff6c889b27e (Unity) IOService::Run
0x00007ff6c886dbcf (Unity) AssetImportWorkerClient::Run
0x00007ff6c8835d13 (Unity) RunAssetImportWorkerClientV2
0x00007ff6c8835d9b (Unity) RunAssetImporterV2
0x00007ff6c8060c48 (Unity) Application::InitializeProject
0x00007ff6c84b20a0 (Unity) WinMain
0x00007ff6c987fbae (Unity) __scrt_common_main_seh
0x00007ffbd486259d (KERNEL32) BaseThreadInitThunk
0x00007ffbd4e8af78 (ntdll) RtlUserThreadStart

Mono: successfully reloaded assembly
- Completed reload, in  1.283 seconds
Domain Reload Profiling:
	ReloadAssembly (1283ms)
		BeginReloadAssembly (160ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (2ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (26ms)
		EndReloadAssembly (1015ms)
			LoadAssemblies (129ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (176ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (26ms)
			SetupLoadedEditorAssemblies (691ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (16ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (59ms)
				ProcessInitializeOnLoadAttributes (570ms)
				ProcessInitializeOnLoadMethodAttributes (21ms)
				AfterProcessingInitializeOnLoad (25ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (4ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 0.63 ms, found 13 plugins.
Preloading 2 native plugins for Editor in 0.20 ms.
Unloading 4753 Unused Serialized files (Serialized files now loaded: 0)
Unloading 50 unused Assets / (95.7 KB). Loaded Objects now: 5340.
Memory consumption went from 192.3 MB to 192.2 MB.
Total: 2.334400 ms (FindLiveObjects: 0.205000 ms CreateObjectMapping: 0.059700 ms MarkObjects: 2.030500 ms  DeleteObjects: 0.037400 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 313.545450 seconds.
  path: Assets/Font/霞鹜铭心宋1.011/LXGWHeartSerif SDF.asset
  artifactKey: Guid(ec82cab83f8fd0b4d8ba62ae9ec6baa5) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Font/霞鹜铭心宋1.011/LXGWHeartSerif SDF.asset using Guid(ec82cab83f8fd0b4d8ba62ae9ec6baa5) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '38734975d306ea694c979c87c3641f2d') in 0.138879 seconds 
Number of asset objects unloaded after import = 6
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.015215 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 0.71 ms, found 13 plugins.
Preloading 2 native plugins for Editor in 0.33 ms.
PXRLog [Build Check]RegisterBuildPlayerHandler,Already Do not show: False
0x00007ff6c74fefad (Unity) StackWalker::GetCurrentCallstack
0x00007ff6c7505b39 (Unity) StackWalker::ShowCallstack
0x00007ff6c84a8df3 (Unity) GetStacktrace
0x00007ff6c8b7165d (Unity) DebugStringToFile
0x00007ff6c664ea32 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x000001a2c6fdc083 (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
0x000001a2c6fdbd5b (Mono JIT Code) UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
0x000001a2c6fdbae0 (Mono JIT Code) UnityEngine.Logger:Log (UnityEngine.LogType,object)
0x000001a2c6fdb9a8 (Mono JIT Code) UnityEngine.Debug:Log (object)
0x000001a2c6fd8a53 (Mono JIT Code) [PXR_SDKBuildCheck.cs:32] Unity.XR.PXR.Editor.PXR_SDKBuildCheck:.cctor () 
0x000001a2c34075e5 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
0x00007ffb3c53697e (mono-2.0-bdwgc) [mini-runtime.c:3445] mono_jit_runtime_invoke 
0x00007ffb3c46cdd4 (mono-2.0-bdwgc) [object.c:3068] do_runtime_invoke 
0x00007ffb3c467b79 (mono-2.0-bdwgc) [object.c:563] mono_runtime_class_init_full 
0x00007ffb3c39ddd7 (mono-2.0-bdwgc) [icall.c:1292] ves_icall_System_Runtime_CompilerServices_RuntimeHelpers_RunClassConstructor 
0x00007ffb3c3cfebc (mono-2.0-bdwgc) [icall-def.h:756] ves_icall_System_Runtime_CompilerServices_RuntimeHelpers_RunClassConstructor_raw 
0x000001a2c3406c8a (Mono JIT Code) (wrapper managed-to-native) System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (intptr)
0x000001a2c3406b9b (Mono JIT Code) System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
0x000001a2c34062e3 (Mono JIT Code) UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])
0x000001a2ba636298 (Mono JIT Code) (wrapper runtime-invoke) <Module>:runtime_invoke_void_object (object,intptr,intptr,intptr)
0x00007ffb3c53697e (mono-2.0-bdwgc) [mini-runtime.c:3445] mono_jit_runtime_invoke 
0x00007ffb3c46cdd4 (mono-2.0-bdwgc) [object.c:3068] do_runtime_invoke 
0x00007ffb3c46cf4c (mono-2.0-bdwgc) [object.c:3115] mono_runtime_invoke 
0x00007ff6c7421934 (Unity) scripting_method_invoke
0x00007ff6c7400b84 (Unity) ScriptingInvocation::Invoke
0x00007ff6c73fb6e5 (Unity) ScriptingInvocation::Invoke<void>
0x00007ff6c753f4c3 (Unity) Scripting::UnityEditor::EditorAssembliesProxy::ProcessInitializeOnLoadAttributes
0x00007ff6c73f570b (Unity) MonoManager::SetupLoadedEditorAssemblies
0x00007ff6c73eb1ed (Unity) MonoManager::EndReloadAssembly
0x00007ff6c73f2d88 (Unity) MonoManager::ReloadAssembly
0x00007ff6c835fa12 (Unity) LoadDomainAndUserAssemblies
0x00007ff6c8360144 (Unity) LoadUserAssemblies
0x00007ff6c885c091 (Unity) <lambda_f8411f227bdd47da33e064bfa1662688>::operator()
0x00007ff6c889ce92 (Unity) asio::detail::completion_handler<core::mutable_function<void __cdecl(void)>,asio::io_context::basic_executor_type<std::allocator<void>,0> >::do_complete
0x00007ff6c8888d7e (Unity) asio::detail::win_iocp_io_context::do_one
0x00007ff6c888a9e4 (Unity) asio::detail::win_iocp_io_context::run
0x00007ff6c889b27e (Unity) IOService::Run
0x00007ff6c886dbcf (Unity) AssetImportWorkerClient::Run
0x00007ff6c8835d13 (Unity) RunAssetImportWorkerClientV2
0x00007ff6c8835d9b (Unity) RunAssetImporterV2
0x00007ff6c8060c48 (Unity) Application::InitializeProject
0x00007ff6c84b20a0 (Unity) WinMain
0x00007ff6c987fbae (Unity) __scrt_common_main_seh
0x00007ffbd486259d (KERNEL32) BaseThreadInitThunk
0x00007ffbd4e8af78 (ntdll) RtlUserThreadStart

[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
CreateSerializedAsset is not supported while importing out-of-process
0x00007ff6c74fefad (Unity) StackWalker::GetCurrentCallstack
0x00007ff6c7505b39 (Unity) StackWalker::ShowCallstack
0x00007ff6c84a8df3 (Unity) GetStacktrace
0x00007ff6c8b7165d (Unity) DebugStringToFile
0x00007ff6c887b618 (Unity) CreateSerializedAssetV2
0x00007ff6c8810f55 (Unity) CreateSerializedAssetV2
0x00007ff6c877f8ee (Unity) AssetDatabase::CreateSerializedAsset
0x00007ff6c6cea4ff (Unity) AssetDatabaseBindings::CreateAsset
0x00007ff6c6b0b819 (Unity) AssetDatabase_CUSTOM_CreateAsset
0x000001a2bff8cdb5 (Mono JIT Code) (wrapper managed-to-native) UnityEditor.AssetDatabase:CreateAsset (UnityEngine.Object,string)
0x000001a2bff8c753 (Mono JIT Code) [PXR_ProjectSetting.cs:121] Unity.XR.PXR.PXR_ProjectSetting:GetProjectConfig () 
0x000001a2bff8c383 (Mono JIT Code) [PXR_SDKSettingEditor.cs:111] Unity.XR.PXR.Editor.PXR_SDKSettingEditor:InitializeOnLoad () 
0x000001a2c3407633 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
0x00007ffb3c53697e (mono-2.0-bdwgc) [mini-runtime.c:3445] mono_jit_runtime_invoke 
0x00007ffb3c46cdd4 (mono-2.0-bdwgc) [object.c:3068] do_runtime_invoke 
0x00007ffb3c4746df (mono-2.0-bdwgc) [object.c:5770] mono_runtime_try_invoke_array 
0x00007ffb3c3a4eb2 (mono-2.0-bdwgc) [icall.c:4041] ves_icall_InternalInvoke 
0x00007ffb3c3cb091 (mono-2.0-bdwgc) [icall-def.h:692] ves_icall_InternalInvoke_raw 
0x000001a32938ae20 (Mono JIT Code) (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo:InternalInvoke (System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
0x000001a32938a5bb (Mono JIT Code) System.Reflection.RuntimeMethodInfo:Invoke (object,System.Reflection.BindingFlags,System.Reflection.Binder,object[],System.Globalization.CultureInfo)
0x000001a32938a47e (Mono JIT Code) System.Reflection.MethodBase:Invoke (object,object[])
0x000001a2bef98c7b (Mono JIT Code) UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes ()
0x000001a2ba636b35 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
0x00007ffb3c53697e (mono-2.0-bdwgc) [mini-runtime.c:3445] mono_jit_runtime_invoke 
0x00007ffb3c46cdd4 (mono-2.0-bdwgc) [object.c:3068] do_runtime_invoke 
0x00007ffb3c46cf4c (mono-2.0-bdwgc) [object.c:3115] mono_runtime_invoke 
0x00007ff6c7421934 (Unity) scripting_method_invoke
0x00007ff6c7400b84 (Unity) ScriptingInvocation::Invoke
0x00007ff6c73fb6e5 (Unity) ScriptingInvocation::Invoke<void>
0x00007ff6c753f53b (Unity) Scripting::UnityEditor::EditorAssembliesProxy::ProcessInitializeOnLoadMethodAttributes
0x00007ff6c73f5733 (Unity) MonoManager::SetupLoadedEditorAssemblies
0x00007ff6c73eb1ed (Unity) MonoManager::EndReloadAssembly
0x00007ff6c73f2d88 (Unity) MonoManager::ReloadAssembly
0x00007ff6c835fa12 (Unity) LoadDomainAndUserAssemblies
0x00007ff6c8360144 (Unity) LoadUserAssemblies
0x00007ff6c885c091 (Unity) <lambda_f8411f227bdd47da33e064bfa1662688>::operator()
0x00007ff6c889ce92 (Unity) asio::detail::completion_handler<core::mutable_function<void __cdecl(void)>,asio::io_context::basic_executor_type<std::allocator<void>,0> >::do_complete
0x00007ff6c8888d7e (Unity) asio::detail::win_iocp_io_context::do_one
0x00007ff6c888a9e4 (Unity) asio::detail::win_iocp_io_context::run
0x00007ff6c889b27e (Unity) IOService::Run
0x00007ff6c886dbcf (Unity) AssetImportWorkerClient::Run
0x00007ff6c8835d13 (Unity) RunAssetImportWorkerClientV2
0x00007ff6c8835d9b (Unity) RunAssetImporterV2
0x00007ff6c8060c48 (Unity) Application::InitializeProject
0x00007ff6c84b20a0 (Unity) WinMain
0x00007ff6c987fbae (Unity) __scrt_common_main_seh
0x00007ffbd486259d (KERNEL32) BaseThreadInitThunk
0x00007ffbd4e8af78 (ntdll) RtlUserThreadStart

System.Reflection.TargetInvocationException: Exception has been thrown by the target of an invocation. ---> UnityEngine.UnityException: Creating asset at path Assets/Resources/PXR_ProjectSetting.asset failed.
  at (wrapper managed-to-native) UnityEditor.AssetDatabase.CreateAsset(UnityEngine.Object,string)
  at Unity.XR.PXR.PXR_ProjectSetting.GetProjectConfig () [0x0015a] in D:\PICO Unity Integration SDK-3.2.0-20250529\Runtime\Scripts\PXR_ProjectSetting.cs:117 
  at Unity.XR.PXR.Editor.PXR_SDKSettingEditor.InitializeOnLoad () [0x00000] in D:\PICO Unity Integration SDK-3.2.0-20250529\Editor\PXR_SDKSettingEditor.cs:111 
  at (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo.InternalInvoke(System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x0006a] in <688f272cc27c4df98c503f9d1f1267d0>:0 
   --- End of inner exception stack trace ---
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x00083] in <688f272cc27c4df98c503f9d1f1267d0>:0 
  at System.Reflection.MethodBase.Invoke (System.Object obj, System.Object[] parameters) [0x00000] in <688f272cc27c4df98c503f9d1f1267d0>:0 
  at UnityEditor.EditorAssemblies.ProcessInitializeOnLoadMethodAttributes () [0x000a5] in <1518627dacca44efa32edd45bc720c48>:0 
0x00007ff6c74fefad (Unity) StackWalker::GetCurrentCallstack
0x00007ff6c7505b39 (Unity) StackWalker::ShowCallstack
0x00007ff6c84a8df3 (Unity) GetStacktrace
0x00007ff6c8b7165d (Unity) DebugStringToFile
0x00007ff6c664ea32 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x000001a2c6fdc083 (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
0x000001a2c6fdbd5b (Mono JIT Code) UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
0x000001a2c6fdbae0 (Mono JIT Code) UnityEngine.Logger:Log (UnityEngine.LogType,object)
0x000001a2bff8d045 (Mono JIT Code) UnityEngine.Debug:LogError (object)
0x000001a2bef98c9b (Mono JIT Code) UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes ()
0x000001a2ba636b35 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
0x00007ffb3c53697e (mono-2.0-bdwgc) [mini-runtime.c:3445] mono_jit_runtime_invoke 
0x00007ffb3c46cdd4 (mono-2.0-bdwgc) [object.c:3068] do_runtime_invoke 
0x00007ffb3c46cf4c (mono-2.0-bdwgc) [object.c:3115] mono_runtime_invoke 
0x00007ff6c7421934 (Unity) scripting_method_invoke
0x00007ff6c7400b84 (Unity) ScriptingInvocation::Invoke
0x00007ff6c73fb6e5 (Unity) ScriptingInvocation::Invoke<void>
0x00007ff6c753f53b (Unity) Scripting::UnityEditor::EditorAssembliesProxy::ProcessInitializeOnLoadMethodAttributes
0x00007ff6c73f5733 (Unity) MonoManager::SetupLoadedEditorAssemblies
0x00007ff6c73eb1ed (Unity) MonoManager::EndReloadAssembly
0x00007ff6c73f2d88 (Unity) MonoManager::ReloadAssembly
0x00007ff6c835fa12 (Unity) LoadDomainAndUserAssemblies
0x00007ff6c8360144 (Unity) LoadUserAssemblies
0x00007ff6c885c091 (Unity) <lambda_f8411f227bdd47da33e064bfa1662688>::operator()
0x00007ff6c889ce92 (Unity) asio::detail::completion_handler<core::mutable_function<void __cdecl(void)>,asio::io_context::basic_executor_type<std::allocator<void>,0> >::do_complete
0x00007ff6c8888d7e (Unity) asio::detail::win_iocp_io_context::do_one
0x00007ff6c888a9e4 (Unity) asio::detail::win_iocp_io_context::run
0x00007ff6c889b27e (Unity) IOService::Run
0x00007ff6c886dbcf (Unity) AssetImportWorkerClient::Run
0x00007ff6c8835d13 (Unity) RunAssetImportWorkerClientV2
0x00007ff6c8835d9b (Unity) RunAssetImporterV2
0x00007ff6c8060c48 (Unity) Application::InitializeProject
0x00007ff6c84b20a0 (Unity) WinMain
0x00007ff6c987fbae (Unity) __scrt_common_main_seh
0x00007ffbd486259d (KERNEL32) BaseThreadInitThunk
0x00007ffbd4e8af78 (ntdll) RtlUserThreadStart

Mono: successfully reloaded assembly
- Completed reload, in  1.384 seconds
Domain Reload Profiling:
	ReloadAssembly (1385ms)
		BeginReloadAssembly (175ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (2ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (27ms)
		EndReloadAssembly (1101ms)
			LoadAssemblies (142ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (188ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (30ms)
			SetupLoadedEditorAssemblies (736ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (18ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (69ms)
				ProcessInitializeOnLoadAttributes (585ms)
				ProcessInitializeOnLoadMethodAttributes (25ms)
				AfterProcessingInitializeOnLoad (38ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (7ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 0.65 ms, found 13 plugins.
Preloading 2 native plugins for Editor in 0.19 ms.
Unloading 4753 Unused Serialized files (Serialized files now loaded: 0)
Unloading 50 unused Assets / (95.8 KB). Loaded Objects now: 5401.
Memory consumption went from 196.9 MB to 196.8 MB.
Total: 2.120300 ms (FindLiveObjects: 0.203400 ms CreateObjectMapping: 0.067100 ms MarkObjects: 1.812500 ms  DeleteObjects: 0.036300 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 15.46 ms, found 13 plugins.
Preloading 2 native plugins for Editor in 0.19 ms.
Unloading 45 Unused Serialized files (Serialized files now loaded: 0)
Unloading 45 unused Assets / (95.6 KB). Loaded Objects now: 5401.
Memory consumption went from 102.7 MB to 102.6 MB.
Total: 2.442200 ms (FindLiveObjects: 0.206800 ms CreateObjectMapping: 0.089200 ms MarkObjects: 2.082700 ms  DeleteObjects: 0.062900 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.024572 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 0.63 ms, found 13 plugins.
Preloading 2 native plugins for Editor in 0.20 ms.
PXRLog [Build Check]RegisterBuildPlayerHandler,Already Do not show: False
0x00007ff6c74fefad (Unity) StackWalker::GetCurrentCallstack
0x00007ff6c7505b39 (Unity) StackWalker::ShowCallstack
0x00007ff6c84a8df3 (Unity) GetStacktrace
0x00007ff6c8b7165d (Unity) DebugStringToFile
0x00007ff6c664ea32 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x000001a2bcc3c083 (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
0x000001a2bcc3bd5b (Mono JIT Code) UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
0x000001a2bcc3bae0 (Mono JIT Code) UnityEngine.Logger:Log (UnityEngine.LogType,object)
0x000001a2bcc3b9a8 (Mono JIT Code) UnityEngine.Debug:Log (object)
0x000001a2bcc38a53 (Mono JIT Code) [PXR_SDKBuildCheck.cs:32] Unity.XR.PXR.Editor.PXR_SDKBuildCheck:.cctor () 
0x000001a2bcc47575 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
0x00007ffb3c53697e (mono-2.0-bdwgc) [mini-runtime.c:3445] mono_jit_runtime_invoke 
0x00007ffb3c46cdd4 (mono-2.0-bdwgc) [object.c:3068] do_runtime_invoke 
0x00007ffb3c467b79 (mono-2.0-bdwgc) [object.c:563] mono_runtime_class_init_full 
0x00007ffb3c39ddd7 (mono-2.0-bdwgc) [icall.c:1292] ves_icall_System_Runtime_CompilerServices_RuntimeHelpers_RunClassConstructor 
0x00007ffb3c3cfebc (mono-2.0-bdwgc) [icall-def.h:756] ves_icall_System_Runtime_CompilerServices_RuntimeHelpers_RunClassConstructor_raw 
0x000001a2bcc46c1a (Mono JIT Code) (wrapper managed-to-native) System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (intptr)
0x000001a2bcc46b2b (Mono JIT Code) System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
0x000001a2bcc46273 (Mono JIT Code) UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])
0x000001a2ba416298 (Mono JIT Code) (wrapper runtime-invoke) <Module>:runtime_invoke_void_object (object,intptr,intptr,intptr)
0x00007ffb3c53697e (mono-2.0-bdwgc) [mini-runtime.c:3445] mono_jit_runtime_invoke 
0x00007ffb3c46cdd4 (mono-2.0-bdwgc) [object.c:3068] do_runtime_invoke 
0x00007ffb3c46cf4c (mono-2.0-bdwgc) [object.c:3115] mono_runtime_invoke 
0x00007ff6c7421934 (Unity) scripting_method_invoke
0x00007ff6c7400b84 (Unity) ScriptingInvocation::Invoke
0x00007ff6c73fb6e5 (Unity) ScriptingInvocation::Invoke<void>
0x00007ff6c753f4c3 (Unity) Scripting::UnityEditor::EditorAssembliesProxy::ProcessInitializeOnLoadAttributes
0x00007ff6c73f570b (Unity) MonoManager::SetupLoadedEditorAssemblies
0x00007ff6c73eb1ed (Unity) MonoManager::EndReloadAssembly
0x00007ff6c73f2d88 (Unity) MonoManager::ReloadAssembly
0x00007ff6c835fa12 (Unity) LoadDomainAndUserAssemblies
0x00007ff6c8360144 (Unity) LoadUserAssemblies
0x00007ff6c885c091 (Unity) <lambda_f8411f227bdd47da33e064bfa1662688>::operator()
0x00007ff6c889ce92 (Unity) asio::detail::completion_handler<core::mutable_function<void __cdecl(void)>,asio::io_context::basic_executor_type<std::allocator<void>,0> >::do_complete
0x00007ff6c8888d7e (Unity) asio::detail::win_iocp_io_context::do_one
0x00007ff6c888a9e4 (Unity) asio::detail::win_iocp_io_context::run
0x00007ff6c889b27e (Unity) IOService::Run
0x00007ff6c886dbcf (Unity) AssetImportWorkerClient::Run
0x00007ff6c8835d13 (Unity) RunAssetImportWorkerClientV2
0x00007ff6c8835d9b (Unity) RunAssetImporterV2
0x00007ff6c8060c48 (Unity) Application::InitializeProject
0x00007ff6c84b20a0 (Unity) WinMain
0x00007ff6c987fbae (Unity) __scrt_common_main_seh
0x00007ffbd486259d (KERNEL32) BaseThreadInitThunk
0x00007ffbd4e8af78 (ntdll) RtlUserThreadStart

[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
CreateSerializedAsset is not supported while importing out-of-process
0x00007ff6c74fefad (Unity) StackWalker::GetCurrentCallstack
0x00007ff6c7505b39 (Unity) StackWalker::ShowCallstack
0x00007ff6c84a8df3 (Unity) GetStacktrace
0x00007ff6c8b7165d (Unity) DebugStringToFile
0x00007ff6c887b618 (Unity) CreateSerializedAssetV2
0x00007ff6c8810f55 (Unity) CreateSerializedAssetV2
0x00007ff6c877f8ee (Unity) AssetDatabase::CreateSerializedAsset
0x00007ff6c6cea4ff (Unity) AssetDatabaseBindings::CreateAsset
0x00007ff6c6b0b819 (Unity) AssetDatabase_CUSTOM_CreateAsset
0x000001a2c4d0cdb5 (Mono JIT Code) (wrapper managed-to-native) UnityEditor.AssetDatabase:CreateAsset (UnityEngine.Object,string)
0x000001a2c4d0c753 (Mono JIT Code) [PXR_ProjectSetting.cs:121] Unity.XR.PXR.PXR_ProjectSetting:GetProjectConfig () 
0x000001a2c4d0c383 (Mono JIT Code) [PXR_SDKSettingEditor.cs:111] Unity.XR.PXR.Editor.PXR_SDKSettingEditor:InitializeOnLoad () 
0x000001a2bcc475c3 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
0x00007ffb3c53697e (mono-2.0-bdwgc) [mini-runtime.c:3445] mono_jit_runtime_invoke 
0x00007ffb3c46cdd4 (mono-2.0-bdwgc) [object.c:3068] do_runtime_invoke 
0x00007ffb3c4746df (mono-2.0-bdwgc) [object.c:5770] mono_runtime_try_invoke_array 
0x00007ffb3c3a4eb2 (mono-2.0-bdwgc) [icall.c:4041] ves_icall_InternalInvoke 
0x00007ffb3c3cb091 (mono-2.0-bdwgc) [icall-def.h:692] ves_icall_InternalInvoke_raw 
0x000001a2bff8ae20 (Mono JIT Code) (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo:InternalInvoke (System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
0x000001a2bff8a5bb (Mono JIT Code) System.Reflection.RuntimeMethodInfo:Invoke (object,System.Reflection.BindingFlags,System.Reflection.Binder,object[],System.Globalization.CultureInfo)
0x000001a2bff8a47e (Mono JIT Code) System.Reflection.MethodBase:Invoke (object,object[])
0x000001a2c4cf8c7b (Mono JIT Code) UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes ()
0x000001a2ba416b35 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
0x00007ffb3c53697e (mono-2.0-bdwgc) [mini-runtime.c:3445] mono_jit_runtime_invoke 
0x00007ffb3c46cdd4 (mono-2.0-bdwgc) [object.c:3068] do_runtime_invoke 
0x00007ffb3c46cf4c (mono-2.0-bdwgc) [object.c:3115] mono_runtime_invoke 
0x00007ff6c7421934 (Unity) scripting_method_invoke
0x00007ff6c7400b84 (Unity) ScriptingInvocation::Invoke
0x00007ff6c73fb6e5 (Unity) ScriptingInvocation::Invoke<void>
0x00007ff6c753f53b (Unity) Scripting::UnityEditor::EditorAssembliesProxy::ProcessInitializeOnLoadMethodAttributes
0x00007ff6c73f5733 (Unity) MonoManager::SetupLoadedEditorAssemblies
0x00007ff6c73eb1ed (Unity) MonoManager::EndReloadAssembly
0x00007ff6c73f2d88 (Unity) MonoManager::ReloadAssembly
0x00007ff6c835fa12 (Unity) LoadDomainAndUserAssemblies
0x00007ff6c8360144 (Unity) LoadUserAssemblies
0x00007ff6c885c091 (Unity) <lambda_f8411f227bdd47da33e064bfa1662688>::operator()
0x00007ff6c889ce92 (Unity) asio::detail::completion_handler<core::mutable_function<void __cdecl(void)>,asio::io_context::basic_executor_type<std::allocator<void>,0> >::do_complete
0x00007ff6c8888d7e (Unity) asio::detail::win_iocp_io_context::do_one
0x00007ff6c888a9e4 (Unity) asio::detail::win_iocp_io_context::run
0x00007ff6c889b27e (Unity) IOService::Run
0x00007ff6c886dbcf (Unity) AssetImportWorkerClient::Run
0x00007ff6c8835d13 (Unity) RunAssetImportWorkerClientV2
0x00007ff6c8835d9b (Unity) RunAssetImporterV2
0x00007ff6c8060c48 (Unity) Application::InitializeProject
0x00007ff6c84b20a0 (Unity) WinMain
0x00007ff6c987fbae (Unity) __scrt_common_main_seh
0x00007ffbd486259d (KERNEL32) BaseThreadInitThunk
0x00007ffbd4e8af78 (ntdll) RtlUserThreadStart

System.Reflection.TargetInvocationException: Exception has been thrown by the target of an invocation. ---> UnityEngine.UnityException: Creating asset at path Assets/Resources/PXR_ProjectSetting.asset failed.
  at (wrapper managed-to-native) UnityEditor.AssetDatabase.CreateAsset(UnityEngine.Object,string)
  at Unity.XR.PXR.PXR_ProjectSetting.GetProjectConfig () [0x0015a] in D:\PICO Unity Integration SDK-3.2.0-20250529\Runtime\Scripts\PXR_ProjectSetting.cs:117 
  at Unity.XR.PXR.Editor.PXR_SDKSettingEditor.InitializeOnLoad () [0x00000] in D:\PICO Unity Integration SDK-3.2.0-20250529\Editor\PXR_SDKSettingEditor.cs:111 
  at (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo.InternalInvoke(System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x0006a] in <688f272cc27c4df98c503f9d1f1267d0>:0 
   --- End of inner exception stack trace ---
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x00083] in <688f272cc27c4df98c503f9d1f1267d0>:0 
  at System.Reflection.MethodBase.Invoke (System.Object obj, System.Object[] parameters) [0x00000] in <688f272cc27c4df98c503f9d1f1267d0>:0 
  at UnityEditor.EditorAssemblies.ProcessInitializeOnLoadMethodAttributes () [0x000a5] in <1518627dacca44efa32edd45bc720c48>:0 
0x00007ff6c74fefad (Unity) StackWalker::GetCurrentCallstack
0x00007ff6c7505b39 (Unity) StackWalker::ShowCallstack
0x00007ff6c84a8df3 (Unity) GetStacktrace
0x00007ff6c8b7165d (Unity) DebugStringToFile
0x00007ff6c664ea32 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x000001a2bcc3c083 (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
0x000001a2bcc3bd5b (Mono JIT Code) UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
0x000001a2bcc3bae0 (Mono JIT Code) UnityEngine.Logger:Log (UnityEngine.LogType,object)
0x000001a2c4d0d045 (Mono JIT Code) UnityEngine.Debug:LogError (object)
0x000001a2c4cf8c9b (Mono JIT Code) UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes ()
0x000001a2ba416b35 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
0x00007ffb3c53697e (mono-2.0-bdwgc) [mini-runtime.c:3445] mono_jit_runtime_invoke 
0x00007ffb3c46cdd4 (mono-2.0-bdwgc) [object.c:3068] do_runtime_invoke 
0x00007ffb3c46cf4c (mono-2.0-bdwgc) [object.c:3115] mono_runtime_invoke 
0x00007ff6c7421934 (Unity) scripting_method_invoke
0x00007ff6c7400b84 (Unity) ScriptingInvocation::Invoke
0x00007ff6c73fb6e5 (Unity) ScriptingInvocation::Invoke<void>
0x00007ff6c753f53b (Unity) Scripting::UnityEditor::EditorAssembliesProxy::ProcessInitializeOnLoadMethodAttributes
0x00007ff6c73f5733 (Unity) MonoManager::SetupLoadedEditorAssemblies
0x00007ff6c73eb1ed (Unity) MonoManager::EndReloadAssembly
0x00007ff6c73f2d88 (Unity) MonoManager::ReloadAssembly
0x00007ff6c835fa12 (Unity) LoadDomainAndUserAssemblies
0x00007ff6c8360144 (Unity) LoadUserAssemblies
0x00007ff6c885c091 (Unity) <lambda_f8411f227bdd47da33e064bfa1662688>::operator()
0x00007ff6c889ce92 (Unity) asio::detail::completion_handler<core::mutable_function<void __cdecl(void)>,asio::io_context::basic_executor_type<std::allocator<void>,0> >::do_complete
0x00007ff6c8888d7e (Unity) asio::detail::win_iocp_io_context::do_one
0x00007ff6c888a9e4 (Unity) asio::detail::win_iocp_io_context::run
0x00007ff6c889b27e (Unity) IOService::Run
0x00007ff6c886dbcf (Unity) AssetImportWorkerClient::Run
0x00007ff6c8835d13 (Unity) RunAssetImportWorkerClientV2
0x00007ff6c8835d9b (Unity) RunAssetImporterV2
0x00007ff6c8060c48 (Unity) Application::InitializeProject
0x00007ff6c84b20a0 (Unity) WinMain
0x00007ff6c987fbae (Unity) __scrt_common_main_seh
0x00007ffbd486259d (KERNEL32) BaseThreadInitThunk
0x00007ffbd4e8af78 (ntdll) RtlUserThreadStart

Mono: successfully reloaded assembly
- Completed reload, in  1.367 seconds
Domain Reload Profiling:
	ReloadAssembly (1368ms)
		BeginReloadAssembly (173ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (2ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (24ms)
		EndReloadAssembly (1080ms)
			LoadAssemblies (144ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (207ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (41ms)
			SetupLoadedEditorAssemblies (687ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (15ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (61ms)
				ProcessInitializeOnLoadAttributes (549ms)
				ProcessInitializeOnLoadMethodAttributes (23ms)
				AfterProcessingInitializeOnLoad (37ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (6ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 0.58 ms, found 13 plugins.
Preloading 2 native plugins for Editor in 0.16 ms.
Unloading 4754 Unused Serialized files (Serialized files now loaded: 0)
Unloading 50 unused Assets / (95.7 KB). Loaded Objects now: 5419.
Memory consumption went from 197.2 MB to 197.2 MB.
Total: 2.307800 ms (FindLiveObjects: 0.226800 ms CreateObjectMapping: 0.073200 ms MarkObjects: 1.969400 ms  DeleteObjects: 0.037500 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.015247 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 0.65 ms, found 13 plugins.
Preloading 2 native plugins for Editor in 0.22 ms.
PXRLog [Build Check]RegisterBuildPlayerHandler,Already Do not show: False
0x00007ff6c74fefad (Unity) StackWalker::GetCurrentCallstack
0x00007ff6c7505b39 (Unity) StackWalker::ShowCallstack
0x00007ff6c84a8df3 (Unity) GetStacktrace
0x00007ff6c8b7165d (Unity) DebugStringToFile
0x00007ff6c664ea32 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x000001a2c341c083 (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
0x000001a2c341bd5b (Mono JIT Code) UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
0x000001a2c341bae0 (Mono JIT Code) UnityEngine.Logger:Log (UnityEngine.LogType,object)
0x000001a2c341b9a8 (Mono JIT Code) UnityEngine.Debug:Log (object)
0x000001a2c3418a53 (Mono JIT Code) [PXR_SDKBuildCheck.cs:32] Unity.XR.PXR.Editor.PXR_SDKBuildCheck:.cctor () 
0x000001a2c3547575 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
0x00007ffb3c53697e (mono-2.0-bdwgc) [mini-runtime.c:3445] mono_jit_runtime_invoke 
0x00007ffb3c46cdd4 (mono-2.0-bdwgc) [object.c:3068] do_runtime_invoke 
0x00007ffb3c467b79 (mono-2.0-bdwgc) [object.c:563] mono_runtime_class_init_full 
0x00007ffb3c39ddd7 (mono-2.0-bdwgc) [icall.c:1292] ves_icall_System_Runtime_CompilerServices_RuntimeHelpers_RunClassConstructor 
0x00007ffb3c3cfebc (mono-2.0-bdwgc) [icall-def.h:756] ves_icall_System_Runtime_CompilerServices_RuntimeHelpers_RunClassConstructor_raw 
0x000001a2c3546c1a (Mono JIT Code) (wrapper managed-to-native) System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (intptr)
0x000001a2c3546b2b (Mono JIT Code) System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
0x000001a2c3546273 (Mono JIT Code) UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])
0x000001a2bcc06298 (Mono JIT Code) (wrapper runtime-invoke) <Module>:runtime_invoke_void_object (object,intptr,intptr,intptr)
0x00007ffb3c53697e (mono-2.0-bdwgc) [mini-runtime.c:3445] mono_jit_runtime_invoke 
0x00007ffb3c46cdd4 (mono-2.0-bdwgc) [object.c:3068] do_runtime_invoke 
0x00007ffb3c46cf4c (mono-2.0-bdwgc) [object.c:3115] mono_runtime_invoke 
0x00007ff6c7421934 (Unity) scripting_method_invoke
0x00007ff6c7400b84 (Unity) ScriptingInvocation::Invoke
0x00007ff6c73fb6e5 (Unity) ScriptingInvocation::Invoke<void>
0x00007ff6c753f4c3 (Unity) Scripting::UnityEditor::EditorAssembliesProxy::ProcessInitializeOnLoadAttributes
0x00007ff6c73f570b (Unity) MonoManager::SetupLoadedEditorAssemblies
0x00007ff6c73eb1ed (Unity) MonoManager::EndReloadAssembly
0x00007ff6c73f2d88 (Unity) MonoManager::ReloadAssembly
0x00007ff6c835fa12 (Unity) LoadDomainAndUserAssemblies
0x00007ff6c8360144 (Unity) LoadUserAssemblies
0x00007ff6c885c091 (Unity) <lambda_f8411f227bdd47da33e064bfa1662688>::operator()
0x00007ff6c889ce92 (Unity) asio::detail::completion_handler<core::mutable_function<void __cdecl(void)>,asio::io_context::basic_executor_type<std::allocator<void>,0> >::do_complete
0x00007ff6c8888d7e (Unity) asio::detail::win_iocp_io_context::do_one
0x00007ff6c888a9e4 (Unity) asio::detail::win_iocp_io_context::run
0x00007ff6c889b27e (Unity) IOService::Run
0x00007ff6c886dbcf (Unity) AssetImportWorkerClient::Run
0x00007ff6c8835d13 (Unity) RunAssetImportWorkerClientV2
0x00007ff6c8835d9b (Unity) RunAssetImporterV2
0x00007ff6c8060c48 (Unity) Application::InitializeProject
0x00007ff6c84b20a0 (Unity) WinMain
0x00007ff6c987fbae (Unity) __scrt_common_main_seh
0x00007ffbd486259d (KERNEL32) BaseThreadInitThunk
0x00007ffbd4e8af78 (ntdll) RtlUserThreadStart

[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
CreateSerializedAsset is not supported while importing out-of-process
0x00007ff6c74fefad (Unity) StackWalker::GetCurrentCallstack
0x00007ff6c7505b39 (Unity) StackWalker::ShowCallstack
0x00007ff6c84a8df3 (Unity) GetStacktrace
0x00007ff6c8b7165d (Unity) DebugStringToFile
0x00007ff6c887b618 (Unity) CreateSerializedAssetV2
0x00007ff6c8810f55 (Unity) CreateSerializedAssetV2
0x00007ff6c877f8ee (Unity) AssetDatabase::CreateSerializedAsset
0x00007ff6c6cea4ff (Unity) AssetDatabaseBindings::CreateAsset
0x00007ff6c6b0b819 (Unity) AssetDatabase_CUSTOM_CreateAsset
0x000001a2bef8cdb5 (Mono JIT Code) (wrapper managed-to-native) UnityEditor.AssetDatabase:CreateAsset (UnityEngine.Object,string)
0x000001a2bef8c753 (Mono JIT Code) [PXR_ProjectSetting.cs:121] Unity.XR.PXR.PXR_ProjectSetting:GetProjectConfig () 
0x000001a2bef8c383 (Mono JIT Code) [PXR_SDKSettingEditor.cs:111] Unity.XR.PXR.Editor.PXR_SDKSettingEditor:InitializeOnLoad () 
0x000001a2c35475c3 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
0x00007ffb3c53697e (mono-2.0-bdwgc) [mini-runtime.c:3445] mono_jit_runtime_invoke 
0x00007ffb3c46cdd4 (mono-2.0-bdwgc) [object.c:3068] do_runtime_invoke 
0x00007ffb3c4746df (mono-2.0-bdwgc) [object.c:5770] mono_runtime_try_invoke_array 
0x00007ffb3c3a4eb2 (mono-2.0-bdwgc) [icall.c:4041] ves_icall_InternalInvoke 
0x00007ffb3c3cb091 (mono-2.0-bdwgc) [icall-def.h:692] ves_icall_InternalInvoke_raw 
0x000001a2c4d0ae20 (Mono JIT Code) (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo:InternalInvoke (System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
0x000001a2c4d0a5bb (Mono JIT Code) System.Reflection.RuntimeMethodInfo:Invoke (object,System.Reflection.BindingFlags,System.Reflection.Binder,object[],System.Globalization.CultureInfo)
0x000001a2c4d0a47e (Mono JIT Code) System.Reflection.MethodBase:Invoke (object,object[])
0x000001a2bdc98c7b (Mono JIT Code) UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes ()
0x000001a2bcc06b35 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
0x00007ffb3c53697e (mono-2.0-bdwgc) [mini-runtime.c:3445] mono_jit_runtime_invoke 
0x00007ffb3c46cdd4 (mono-2.0-bdwgc) [object.c:3068] do_runtime_invoke 
0x00007ffb3c46cf4c (mono-2.0-bdwgc) [object.c:3115] mono_runtime_invoke 
0x00007ff6c7421934 (Unity) scripting_method_invoke
0x00007ff6c7400b84 (Unity) ScriptingInvocation::Invoke
0x00007ff6c73fb6e5 (Unity) ScriptingInvocation::Invoke<void>
0x00007ff6c753f53b (Unity) Scripting::UnityEditor::EditorAssembliesProxy::ProcessInitializeOnLoadMethodAttributes
0x00007ff6c73f5733 (Unity) MonoManager::SetupLoadedEditorAssemblies
0x00007ff6c73eb1ed (Unity) MonoManager::EndReloadAssembly
0x00007ff6c73f2d88 (Unity) MonoManager::ReloadAssembly
0x00007ff6c835fa12 (Unity) LoadDomainAndUserAssemblies
0x00007ff6c8360144 (Unity) LoadUserAssemblies
0x00007ff6c885c091 (Unity) <lambda_f8411f227bdd47da33e064bfa1662688>::operator()
0x00007ff6c889ce92 (Unity) asio::detail::completion_handler<core::mutable_function<void __cdecl(void)>,asio::io_context::basic_executor_type<std::allocator<void>,0> >::do_complete
0x00007ff6c8888d7e (Unity) asio::detail::win_iocp_io_context::do_one
0x00007ff6c888a9e4 (Unity) asio::detail::win_iocp_io_context::run
0x00007ff6c889b27e (Unity) IOService::Run
0x00007ff6c886dbcf (Unity) AssetImportWorkerClient::Run
0x00007ff6c8835d13 (Unity) RunAssetImportWorkerClientV2
0x00007ff6c8835d9b (Unity) RunAssetImporterV2
0x00007ff6c8060c48 (Unity) Application::InitializeProject
0x00007ff6c84b20a0 (Unity) WinMain
0x00007ff6c987fbae (Unity) __scrt_common_main_seh
0x00007ffbd486259d (KERNEL32) BaseThreadInitThunk
0x00007ffbd4e8af78 (ntdll) RtlUserThreadStart

System.Reflection.TargetInvocationException: Exception has been thrown by the target of an invocation. ---> UnityEngine.UnityException: Creating asset at path Assets/Resources/PXR_ProjectSetting.asset failed.
  at (wrapper managed-to-native) UnityEditor.AssetDatabase.CreateAsset(UnityEngine.Object,string)
  at Unity.XR.PXR.PXR_ProjectSetting.GetProjectConfig () [0x0015a] in D:\PICO Unity Integration SDK-3.2.0-20250529\Runtime\Scripts\PXR_ProjectSetting.cs:117 
  at Unity.XR.PXR.Editor.PXR_SDKSettingEditor.InitializeOnLoad () [0x00000] in D:\PICO Unity Integration SDK-3.2.0-20250529\Editor\PXR_SDKSettingEditor.cs:111 
  at (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo.InternalInvoke(System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x0006a] in <688f272cc27c4df98c503f9d1f1267d0>:0 
   --- End of inner exception stack trace ---
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x00083] in <688f272cc27c4df98c503f9d1f1267d0>:0 
  at System.Reflection.MethodBase.Invoke (System.Object obj, System.Object[] parameters) [0x00000] in <688f272cc27c4df98c503f9d1f1267d0>:0 
  at UnityEditor.EditorAssemblies.ProcessInitializeOnLoadMethodAttributes () [0x000a5] in <1518627dacca44efa32edd45bc720c48>:0 
0x00007ff6c74fefad (Unity) StackWalker::GetCurrentCallstack
0x00007ff6c7505b39 (Unity) StackWalker::ShowCallstack
0x00007ff6c84a8df3 (Unity) GetStacktrace
0x00007ff6c8b7165d (Unity) DebugStringToFile
0x00007ff6c664ea32 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x000001a2c341c083 (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
0x000001a2c341bd5b (Mono JIT Code) UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
0x000001a2c341bae0 (Mono JIT Code) UnityEngine.Logger:Log (UnityEngine.LogType,object)
0x000001a2bef8d045 (Mono JIT Code) UnityEngine.Debug:LogError (object)
0x000001a2bdc98c9b (Mono JIT Code) UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes ()
0x000001a2bcc06b35 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
0x00007ffb3c53697e (mono-2.0-bdwgc) [mini-runtime.c:3445] mono_jit_runtime_invoke 
0x00007ffb3c46cdd4 (mono-2.0-bdwgc) [object.c:3068] do_runtime_invoke 
0x00007ffb3c46cf4c (mono-2.0-bdwgc) [object.c:3115] mono_runtime_invoke 
0x00007ff6c7421934 (Unity) scripting_method_invoke
0x00007ff6c7400b84 (Unity) ScriptingInvocation::Invoke
0x00007ff6c73fb6e5 (Unity) ScriptingInvocation::Invoke<void>
0x00007ff6c753f53b (Unity) Scripting::UnityEditor::EditorAssembliesProxy::ProcessInitializeOnLoadMethodAttributes
0x00007ff6c73f5733 (Unity) MonoManager::SetupLoadedEditorAssemblies
0x00007ff6c73eb1ed (Unity) MonoManager::EndReloadAssembly
0x00007ff6c73f2d88 (Unity) MonoManager::ReloadAssembly
0x00007ff6c835fa12 (Unity) LoadDomainAndUserAssemblies
0x00007ff6c8360144 (Unity) LoadUserAssemblies
0x00007ff6c885c091 (Unity) <lambda_f8411f227bdd47da33e064bfa1662688>::operator()
0x00007ff6c889ce92 (Unity) asio::detail::completion_handler<core::mutable_function<void __cdecl(void)>,asio::io_context::basic_executor_type<std::allocator<void>,0> >::do_complete
0x00007ff6c8888d7e (Unity) asio::detail::win_iocp_io_context::do_one
0x00007ff6c888a9e4 (Unity) asio::detail::win_iocp_io_context::run
0x00007ff6c889b27e (Unity) IOService::Run
0x00007ff6c886dbcf (Unity) AssetImportWorkerClient::Run
0x00007ff6c8835d13 (Unity) RunAssetImportWorkerClientV2
0x00007ff6c8835d9b (Unity) RunAssetImporterV2
0x00007ff6c8060c48 (Unity) Application::InitializeProject
0x00007ff6c84b20a0 (Unity) WinMain
0x00007ff6c987fbae (Unity) __scrt_common_main_seh
0x00007ffbd486259d (KERNEL32) BaseThreadInitThunk
0x00007ffbd4e8af78 (ntdll) RtlUserThreadStart

Mono: successfully reloaded assembly
- Completed reload, in  1.332 seconds
Domain Reload Profiling:
	ReloadAssembly (1333ms)
		BeginReloadAssembly (171ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (2ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (25ms)
		EndReloadAssembly (1056ms)
			LoadAssemblies (136ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (187ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (28ms)
			SetupLoadedEditorAssemblies (708ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (16ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (65ms)
				ProcessInitializeOnLoadAttributes (567ms)
				ProcessInitializeOnLoadMethodAttributes (24ms)
				AfterProcessingInitializeOnLoad (34ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (6ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 0.72 ms, found 13 plugins.
Preloading 2 native plugins for Editor in 0.18 ms.
Unloading 4754 Unused Serialized files (Serialized files now loaded: 0)
Unloading 50 unused Assets / (95.6 KB). Loaded Objects now: 5436.
Memory consumption went from 197.3 MB to 197.2 MB.
Total: 2.087100 ms (FindLiveObjects: 0.242300 ms CreateObjectMapping: 0.089500 ms MarkObjects: 1.721900 ms  DeleteObjects: 0.032700 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.015063 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 0.66 ms, found 13 plugins.
Preloading 2 native plugins for Editor in 0.18 ms.
PXRLog [Build Check]RegisterBuildPlayerHandler,Already Do not show: False
0x00007ff6c74fefad (Unity) StackWalker::GetCurrentCallstack
0x00007ff6c7505b39 (Unity) StackWalker::ShowCallstack
0x00007ff6c84a8df3 (Unity) GetStacktrace
0x00007ff6c8b7165d (Unity) DebugStringToFile
0x00007ff6c664ea32 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x000001a2bcc1c083 (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
0x000001a2bcc1bd5b (Mono JIT Code) UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
0x000001a2bcc1bae0 (Mono JIT Code) UnityEngine.Logger:Log (UnityEngine.LogType,object)
0x000001a2bcc1b9a8 (Mono JIT Code) UnityEngine.Debug:Log (object)
0x000001a2bcc18a53 (Mono JIT Code) [PXR_SDKBuildCheck.cs:32] Unity.XR.PXR.Editor.PXR_SDKBuildCheck:.cctor () 
0x000001a2bcc27575 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
0x00007ffb3c53697e (mono-2.0-bdwgc) [mini-runtime.c:3445] mono_jit_runtime_invoke 
0x00007ffb3c46cdd4 (mono-2.0-bdwgc) [object.c:3068] do_runtime_invoke 
0x00007ffb3c467b79 (mono-2.0-bdwgc) [object.c:563] mono_runtime_class_init_full 
0x00007ffb3c39ddd7 (mono-2.0-bdwgc) [icall.c:1292] ves_icall_System_Runtime_CompilerServices_RuntimeHelpers_RunClassConstructor 
0x00007ffb3c3cfebc (mono-2.0-bdwgc) [icall-def.h:756] ves_icall_System_Runtime_CompilerServices_RuntimeHelpers_RunClassConstructor_raw 
0x000001a2bcc26c1a (Mono JIT Code) (wrapper managed-to-native) System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (intptr)
0x000001a2bcc26b2b (Mono JIT Code) System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
0x000001a2bcc26273 (Mono JIT Code) UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])
0x000001a2ba436298 (Mono JIT Code) (wrapper runtime-invoke) <Module>:runtime_invoke_void_object (object,intptr,intptr,intptr)
0x00007ffb3c53697e (mono-2.0-bdwgc) [mini-runtime.c:3445] mono_jit_runtime_invoke 
0x00007ffb3c46cdd4 (mono-2.0-bdwgc) [object.c:3068] do_runtime_invoke 
0x00007ffb3c46cf4c (mono-2.0-bdwgc) [object.c:3115] mono_runtime_invoke 
0x00007ff6c7421934 (Unity) scripting_method_invoke
0x00007ff6c7400b84 (Unity) ScriptingInvocation::Invoke
0x00007ff6c73fb6e5 (Unity) ScriptingInvocation::Invoke<void>
0x00007ff6c753f4c3 (Unity) Scripting::UnityEditor::EditorAssembliesProxy::ProcessInitializeOnLoadAttributes
0x00007ff6c73f570b (Unity) MonoManager::SetupLoadedEditorAssemblies
0x00007ff6c73eb1ed (Unity) MonoManager::EndReloadAssembly
0x00007ff6c73f2d88 (Unity) MonoManager::ReloadAssembly
0x00007ff6c835fa12 (Unity) LoadDomainAndUserAssemblies
0x00007ff6c8360144 (Unity) LoadUserAssemblies
0x00007ff6c885c091 (Unity) <lambda_f8411f227bdd47da33e064bfa1662688>::operator()
0x00007ff6c889ce92 (Unity) asio::detail::completion_handler<core::mutable_function<void __cdecl(void)>,asio::io_context::basic_executor_type<std::allocator<void>,0> >::do_complete
0x00007ff6c8888d7e (Unity) asio::detail::win_iocp_io_context::do_one
0x00007ff6c888a9e4 (Unity) asio::detail::win_iocp_io_context::run
0x00007ff6c889b27e (Unity) IOService::Run
0x00007ff6c886dbcf (Unity) AssetImportWorkerClient::Run
0x00007ff6c8835d13 (Unity) RunAssetImportWorkerClientV2
0x00007ff6c8835d9b (Unity) RunAssetImporterV2
0x00007ff6c8060c48 (Unity) Application::InitializeProject
0x00007ff6c84b20a0 (Unity) WinMain
0x00007ff6c987fbae (Unity) __scrt_common_main_seh
0x00007ffbd486259d (KERNEL32) BaseThreadInitThunk
0x00007ffbd4e8af78 (ntdll) RtlUserThreadStart

[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
CreateSerializedAsset is not supported while importing out-of-process
0x00007ff6c74fefad (Unity) StackWalker::GetCurrentCallstack
0x00007ff6c7505b39 (Unity) StackWalker::ShowCallstack
0x00007ff6c84a8df3 (Unity) GetStacktrace
0x00007ff6c8b7165d (Unity) DebugStringToFile
0x00007ff6c887b618 (Unity) CreateSerializedAssetV2
0x00007ff6c8810f55 (Unity) CreateSerializedAssetV2
0x00007ff6c877f8ee (Unity) AssetDatabase::CreateSerializedAsset
0x00007ff6c6cea4ff (Unity) AssetDatabaseBindings::CreateAsset
0x00007ff6c6b0b819 (Unity) AssetDatabase_CUSTOM_CreateAsset
0x000001a2bb4acdb5 (Mono JIT Code) (wrapper managed-to-native) UnityEditor.AssetDatabase:CreateAsset (UnityEngine.Object,string)
0x000001a2bb4ac753 (Mono JIT Code) [PXR_ProjectSetting.cs:121] Unity.XR.PXR.PXR_ProjectSetting:GetProjectConfig () 
0x000001a2bb4ac383 (Mono JIT Code) [PXR_SDKSettingEditor.cs:111] Unity.XR.PXR.Editor.PXR_SDKSettingEditor:InitializeOnLoad () 
0x000001a2bcc275c3 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
0x00007ffb3c53697e (mono-2.0-bdwgc) [mini-runtime.c:3445] mono_jit_runtime_invoke 
0x00007ffb3c46cdd4 (mono-2.0-bdwgc) [object.c:3068] do_runtime_invoke 
0x00007ffb3c4746df (mono-2.0-bdwgc) [object.c:5770] mono_runtime_try_invoke_array 
0x00007ffb3c3a4eb2 (mono-2.0-bdwgc) [icall.c:4041] ves_icall_InternalInvoke 
0x00007ffb3c3cb091 (mono-2.0-bdwgc) [icall-def.h:692] ves_icall_InternalInvoke_raw 
0x000001a2bef8ae20 (Mono JIT Code) (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo:InternalInvoke (System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
0x000001a2bef8a5bb (Mono JIT Code) System.Reflection.RuntimeMethodInfo:Invoke (object,System.Reflection.BindingFlags,System.Reflection.Binder,object[],System.Globalization.CultureInfo)
0x000001a2bef8a47e (Mono JIT Code) System.Reflection.MethodBase:Invoke (object,object[])
0x000001a2bb498c7b (Mono JIT Code) UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes ()
0x000001a2ba436b35 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
0x00007ffb3c53697e (mono-2.0-bdwgc) [mini-runtime.c:3445] mono_jit_runtime_invoke 
0x00007ffb3c46cdd4 (mono-2.0-bdwgc) [object.c:3068] do_runtime_invoke 
0x00007ffb3c46cf4c (mono-2.0-bdwgc) [object.c:3115] mono_runtime_invoke 
0x00007ff6c7421934 (Unity) scripting_method_invoke
0x00007ff6c7400b84 (Unity) ScriptingInvocation::Invoke
0x00007ff6c73fb6e5 (Unity) ScriptingInvocation::Invoke<void>
0x00007ff6c753f53b (Unity) Scripting::UnityEditor::EditorAssembliesProxy::ProcessInitializeOnLoadMethodAttributes
0x00007ff6c73f5733 (Unity) MonoManager::SetupLoadedEditorAssemblies
0x00007ff6c73eb1ed (Unity) MonoManager::EndReloadAssembly
0x00007ff6c73f2d88 (Unity) MonoManager::ReloadAssembly
0x00007ff6c835fa12 (Unity) LoadDomainAndUserAssemblies
0x00007ff6c8360144 (Unity) LoadUserAssemblies
0x00007ff6c885c091 (Unity) <lambda_f8411f227bdd47da33e064bfa1662688>::operator()
0x00007ff6c889ce92 (Unity) asio::detail::completion_handler<core::mutable_function<void __cdecl(void)>,asio::io_context::basic_executor_type<std::allocator<void>,0> >::do_complete
0x00007ff6c8888d7e (Unity) asio::detail::win_iocp_io_context::do_one
0x00007ff6c888a9e4 (Unity) asio::detail::win_iocp_io_context::run
0x00007ff6c889b27e (Unity) IOService::Run
0x00007ff6c886dbcf (Unity) AssetImportWorkerClient::Run
0x00007ff6c8835d13 (Unity) RunAssetImportWorkerClientV2
0x00007ff6c8835d9b (Unity) RunAssetImporterV2
0x00007ff6c8060c48 (Unity) Application::InitializeProject
0x00007ff6c84b20a0 (Unity) WinMain
0x00007ff6c987fbae (Unity) __scrt_common_main_seh
0x00007ffbd486259d (KERNEL32) BaseThreadInitThunk
0x00007ffbd4e8af78 (ntdll) RtlUserThreadStart

System.Reflection.TargetInvocationException: Exception has been thrown by the target of an invocation. ---> UnityEngine.UnityException: Creating asset at path Assets/Resources/PXR_ProjectSetting.asset failed.
  at (wrapper managed-to-native) UnityEditor.AssetDatabase.CreateAsset(UnityEngine.Object,string)
  at Unity.XR.PXR.PXR_ProjectSetting.GetProjectConfig () [0x0015a] in D:\PICO Unity Integration SDK-3.2.0-20250529\Runtime\Scripts\PXR_ProjectSetting.cs:117 
  at Unity.XR.PXR.Editor.PXR_SDKSettingEditor.InitializeOnLoad () [0x00000] in D:\PICO Unity Integration SDK-3.2.0-20250529\Editor\PXR_SDKSettingEditor.cs:111 
  at (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo.InternalInvoke(System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x0006a] in <688f272cc27c4df98c503f9d1f1267d0>:0 
   --- End of inner exception stack trace ---
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x00083] in <688f272cc27c4df98c503f9d1f1267d0>:0 
  at System.Reflection.MethodBase.Invoke (System.Object obj, System.Object[] parameters) [0x00000] in <688f272cc27c4df98c503f9d1f1267d0>:0 
  at UnityEditor.EditorAssemblies.ProcessInitializeOnLoadMethodAttributes () [0x000a5] in <1518627dacca44efa32edd45bc720c48>:0 
0x00007ff6c74fefad (Unity) StackWalker::GetCurrentCallstack
0x00007ff6c7505b39 (Unity) StackWalker::ShowCallstack
0x00007ff6c84a8df3 (Unity) GetStacktrace
0x00007ff6c8b7165d (Unity) DebugStringToFile
0x00007ff6c664ea32 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x000001a2bcc1c083 (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
0x000001a2bcc1bd5b (Mono JIT Code) UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
0x000001a2bcc1bae0 (Mono JIT Code) UnityEngine.Logger:Log (UnityEngine.LogType,object)
0x000001a2bb4ad045 (Mono JIT Code) UnityEngine.Debug:LogError (object)
0x000001a2bb498c9b (Mono JIT Code) UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes ()
0x000001a2ba436b35 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
0x00007ffb3c53697e (mono-2.0-bdwgc) [mini-runtime.c:3445] mono_jit_runtime_invoke 
0x00007ffb3c46cdd4 (mono-2.0-bdwgc) [object.c:3068] do_runtime_invoke 
0x00007ffb3c46cf4c (mono-2.0-bdwgc) [object.c:3115] mono_runtime_invoke 
0x00007ff6c7421934 (Unity) scripting_method_invoke
0x00007ff6c7400b84 (Unity) ScriptingInvocation::Invoke
0x00007ff6c73fb6e5 (Unity) ScriptingInvocation::Invoke<void>
0x00007ff6c753f53b (Unity) Scripting::UnityEditor::EditorAssembliesProxy::ProcessInitializeOnLoadMethodAttributes
0x00007ff6c73f5733 (Unity) MonoManager::SetupLoadedEditorAssemblies
0x00007ff6c73eb1ed (Unity) MonoManager::EndReloadAssembly
0x00007ff6c73f2d88 (Unity) MonoManager::ReloadAssembly
0x00007ff6c835fa12 (Unity) LoadDomainAndUserAssemblies
0x00007ff6c8360144 (Unity) LoadUserAssemblies
0x00007ff6c885c091 (Unity) <lambda_f8411f227bdd47da33e064bfa1662688>::operator()
0x00007ff6c889ce92 (Unity) asio::detail::completion_handler<core::mutable_function<void __cdecl(void)>,asio::io_context::basic_executor_type<std::allocator<void>,0> >::do_complete
0x00007ff6c8888d7e (Unity) asio::detail::win_iocp_io_context::do_one
0x00007ff6c888a9e4 (Unity) asio::detail::win_iocp_io_context::run
0x00007ff6c889b27e (Unity) IOService::Run
0x00007ff6c886dbcf (Unity) AssetImportWorkerClient::Run
0x00007ff6c8835d13 (Unity) RunAssetImportWorkerClientV2
0x00007ff6c8835d9b (Unity) RunAssetImporterV2
0x00007ff6c8060c48 (Unity) Application::InitializeProject
0x00007ff6c84b20a0 (Unity) WinMain
0x00007ff6c987fbae (Unity) __scrt_common_main_seh
0x00007ffbd486259d (KERNEL32) BaseThreadInitThunk
0x00007ffbd4e8af78 (ntdll) RtlUserThreadStart

Mono: successfully reloaded assembly
- Completed reload, in  1.370 seconds
Domain Reload Profiling:
	ReloadAssembly (1370ms)
		BeginReloadAssembly (166ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (2ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (25ms)
		EndReloadAssembly (1101ms)
			LoadAssemblies (136ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (194ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (32ms)
			SetupLoadedEditorAssemblies (732ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (17ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (68ms)
				ProcessInitializeOnLoadAttributes (584ms)
				ProcessInitializeOnLoadMethodAttributes (25ms)
				AfterProcessingInitializeOnLoad (37ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (7ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 0.70 ms, found 13 plugins.
Preloading 2 native plugins for Editor in 0.23 ms.
Unloading 4754 Unused Serialized files (Serialized files now loaded: 0)
Unloading 50 unused Assets / (95.8 KB). Loaded Objects now: 5453.
Memory consumption went from 197.4 MB to 197.3 MB.
Total: 2.219200 ms (FindLiveObjects: 0.240300 ms CreateObjectMapping: 0.081900 ms MarkObjects: 1.859200 ms  DeleteObjects: 0.037300 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.015646 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 0.67 ms, found 13 plugins.
Preloading 2 native plugins for Editor in 0.21 ms.
PXRLog [Build Check]RegisterBuildPlayerHandler,Already Do not show: False
0x00007ff6c74fefad (Unity) StackWalker::GetCurrentCallstack
0x00007ff6c7505b39 (Unity) StackWalker::ShowCallstack
0x00007ff6c84a8df3 (Unity) GetStacktrace
0x00007ff6c8b7165d (Unity) DebugStringToFile
0x00007ff6c664ea32 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x000001a2bb45c083 (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
0x000001a2bb45bd5b (Mono JIT Code) UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
0x000001a2bb45bae0 (Mono JIT Code) UnityEngine.Logger:Log (UnityEngine.LogType,object)
0x000001a2bb45b9a8 (Mono JIT Code) UnityEngine.Debug:Log (object)
0x000001a2bb458a53 (Mono JIT Code) [PXR_SDKBuildCheck.cs:32] Unity.XR.PXR.Editor.PXR_SDKBuildCheck:.cctor () 
0x000001a2bb467575 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
0x00007ffb3c53697e (mono-2.0-bdwgc) [mini-runtime.c:3445] mono_jit_runtime_invoke 
0x00007ffb3c46cdd4 (mono-2.0-bdwgc) [object.c:3068] do_runtime_invoke 
0x00007ffb3c467b79 (mono-2.0-bdwgc) [object.c:563] mono_runtime_class_init_full 
0x00007ffb3c39ddd7 (mono-2.0-bdwgc) [icall.c:1292] ves_icall_System_Runtime_CompilerServices_RuntimeHelpers_RunClassConstructor 
0x00007ffb3c3cfebc (mono-2.0-bdwgc) [icall-def.h:756] ves_icall_System_Runtime_CompilerServices_RuntimeHelpers_RunClassConstructor_raw 
0x000001a2bb466c1a (Mono JIT Code) (wrapper managed-to-native) System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (intptr)
0x000001a2bb466b2b (Mono JIT Code) System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
0x000001a2bb466273 (Mono JIT Code) UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])
0x000001a2bb406298 (Mono JIT Code) (wrapper runtime-invoke) <Module>:runtime_invoke_void_object (object,intptr,intptr,intptr)
0x00007ffb3c53697e (mono-2.0-bdwgc) [mini-runtime.c:3445] mono_jit_runtime_invoke 
0x00007ffb3c46cdd4 (mono-2.0-bdwgc) [object.c:3068] do_runtime_invoke 
0x00007ffb3c46cf4c (mono-2.0-bdwgc) [object.c:3115] mono_runtime_invoke 
0x00007ff6c7421934 (Unity) scripting_method_invoke
0x00007ff6c7400b84 (Unity) ScriptingInvocation::Invoke
0x00007ff6c73fb6e5 (Unity) ScriptingInvocation::Invoke<void>
0x00007ff6c753f4c3 (Unity) Scripting::UnityEditor::EditorAssembliesProxy::ProcessInitializeOnLoadAttributes
0x00007ff6c73f570b (Unity) MonoManager::SetupLoadedEditorAssemblies
0x00007ff6c73eb1ed (Unity) MonoManager::EndReloadAssembly
0x00007ff6c73f2d88 (Unity) MonoManager::ReloadAssembly
0x00007ff6c835fa12 (Unity) LoadDomainAndUserAssemblies
0x00007ff6c8360144 (Unity) LoadUserAssemblies
0x00007ff6c885c091 (Unity) <lambda_f8411f227bdd47da33e064bfa1662688>::operator()
0x00007ff6c889ce92 (Unity) asio::detail::completion_handler<core::mutable_function<void __cdecl(void)>,asio::io_context::basic_executor_type<std::allocator<void>,0> >::do_complete
0x00007ff6c8888d7e (Unity) asio::detail::win_iocp_io_context::do_one
0x00007ff6c888a9e4 (Unity) asio::detail::win_iocp_io_context::run
0x00007ff6c889b27e (Unity) IOService::Run
0x00007ff6c886dbcf (Unity) AssetImportWorkerClient::Run
0x00007ff6c8835d13 (Unity) RunAssetImportWorkerClientV2
0x00007ff6c8835d9b (Unity) RunAssetImporterV2
0x00007ff6c8060c48 (Unity) Application::InitializeProject
0x00007ff6c84b20a0 (Unity) WinMain
0x00007ff6c987fbae (Unity) __scrt_common_main_seh
0x00007ffbd486259d (KERNEL32) BaseThreadInitThunk
0x00007ffbd4e8af78 (ntdll) RtlUserThreadStart

[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
CreateSerializedAsset is not supported while importing out-of-process
0x00007ff6c74fefad (Unity) StackWalker::GetCurrentCallstack
0x00007ff6c7505b39 (Unity) StackWalker::ShowCallstack
0x00007ff6c84a8df3 (Unity) GetStacktrace
0x00007ff6c8b7165d (Unity) DebugStringToFile
0x00007ff6c887b618 (Unity) CreateSerializedAssetV2
0x00007ff6c8810f55 (Unity) CreateSerializedAssetV2
0x00007ff6c877f8ee (Unity) AssetDatabase::CreateSerializedAsset
0x00007ff6c6cea4ff (Unity) AssetDatabaseBindings::CreateAsset
0x00007ff6c6b0b819 (Unity) AssetDatabase_CUSTOM_CreateAsset
0x000001a2bb5dcdb5 (Mono JIT Code) (wrapper managed-to-native) UnityEditor.AssetDatabase:CreateAsset (UnityEngine.Object,string)
0x000001a2bb5dc753 (Mono JIT Code) [PXR_ProjectSetting.cs:121] Unity.XR.PXR.PXR_ProjectSetting:GetProjectConfig () 
0x000001a2bb5dc383 (Mono JIT Code) [PXR_SDKSettingEditor.cs:111] Unity.XR.PXR.Editor.PXR_SDKSettingEditor:InitializeOnLoad () 
0x000001a2bb4675c3 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
0x00007ffb3c53697e (mono-2.0-bdwgc) [mini-runtime.c:3445] mono_jit_runtime_invoke 
0x00007ffb3c46cdd4 (mono-2.0-bdwgc) [object.c:3068] do_runtime_invoke 
0x00007ffb3c4746df (mono-2.0-bdwgc) [object.c:5770] mono_runtime_try_invoke_array 
0x00007ffb3c3a4eb2 (mono-2.0-bdwgc) [icall.c:4041] ves_icall_InternalInvoke 
0x00007ffb3c3cb091 (mono-2.0-bdwgc) [icall-def.h:692] ves_icall_InternalInvoke_raw 
0x000001a2bb4aae20 (Mono JIT Code) (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo:InternalInvoke (System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
0x000001a2bb4aa5bb (Mono JIT Code) System.Reflection.RuntimeMethodInfo:Invoke (object,System.Reflection.BindingFlags,System.Reflection.Binder,object[],System.Globalization.CultureInfo)
0x000001a2bb4aa47e (Mono JIT Code) System.Reflection.MethodBase:Invoke (object,object[])
0x000001a2bb5c905b (Mono JIT Code) UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes ()
0x000001a2bb406b35 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
0x00007ffb3c53697e (mono-2.0-bdwgc) [mini-runtime.c:3445] mono_jit_runtime_invoke 
0x00007ffb3c46cdd4 (mono-2.0-bdwgc) [object.c:3068] do_runtime_invoke 
0x00007ffb3c46cf4c (mono-2.0-bdwgc) [object.c:3115] mono_runtime_invoke 
0x00007ff6c7421934 (Unity) scripting_method_invoke
0x00007ff6c7400b84 (Unity) ScriptingInvocation::Invoke
0x00007ff6c73fb6e5 (Unity) ScriptingInvocation::Invoke<void>
0x00007ff6c753f53b (Unity) Scripting::UnityEditor::EditorAssembliesProxy::ProcessInitializeOnLoadMethodAttributes
0x00007ff6c73f5733 (Unity) MonoManager::SetupLoadedEditorAssemblies
0x00007ff6c73eb1ed (Unity) MonoManager::EndReloadAssembly
0x00007ff6c73f2d88 (Unity) MonoManager::ReloadAssembly
0x00007ff6c835fa12 (Unity) LoadDomainAndUserAssemblies
0x00007ff6c8360144 (Unity) LoadUserAssemblies
0x00007ff6c885c091 (Unity) <lambda_f8411f227bdd47da33e064bfa1662688>::operator()
0x00007ff6c889ce92 (Unity) asio::detail::completion_handler<core::mutable_function<void __cdecl(void)>,asio::io_context::basic_executor_type<std::allocator<void>,0> >::do_complete
0x00007ff6c8888d7e (Unity) asio::detail::win_iocp_io_context::do_one
0x00007ff6c888a9e4 (Unity) asio::detail::win_iocp_io_context::run
0x00007ff6c889b27e (Unity) IOService::Run
0x00007ff6c886dbcf (Unity) AssetImportWorkerClient::Run
0x00007ff6c8835d13 (Unity) RunAssetImportWorkerClientV2
0x00007ff6c8835d9b (Unity) RunAssetImporterV2
0x00007ff6c8060c48 (Unity) Application::InitializeProject
0x00007ff6c84b20a0 (Unity) WinMain
0x00007ff6c987fbae (Unity) __scrt_common_main_seh
0x00007ffbd486259d (KERNEL32) BaseThreadInitThunk
0x00007ffbd4e8af78 (ntdll) RtlUserThreadStart

System.Reflection.TargetInvocationException: Exception has been thrown by the target of an invocation. ---> UnityEngine.UnityException: Creating asset at path Assets/Resources/PXR_ProjectSetting.asset failed.
  at (wrapper managed-to-native) UnityEditor.AssetDatabase.CreateAsset(UnityEngine.Object,string)
  at Unity.XR.PXR.PXR_ProjectSetting.GetProjectConfig () [0x0015a] in D:\PICO Unity Integration SDK-3.2.0-20250529\Runtime\Scripts\PXR_ProjectSetting.cs:117 
  at Unity.XR.PXR.Editor.PXR_SDKSettingEditor.InitializeOnLoad () [0x00000] in D:\PICO Unity Integration SDK-3.2.0-20250529\Editor\PXR_SDKSettingEditor.cs:111 
  at (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo.InternalInvoke(System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x0006a] in <688f272cc27c4df98c503f9d1f1267d0>:0 
   --- End of inner exception stack trace ---
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x00083] in <688f272cc27c4df98c503f9d1f1267d0>:0 
  at System.Reflection.MethodBase.Invoke (System.Object obj, System.Object[] parameters) [0x00000] in <688f272cc27c4df98c503f9d1f1267d0>:0 
  at UnityEditor.EditorAssemblies.ProcessInitializeOnLoadMethodAttributes () [0x000a5] in <1518627dacca44efa32edd45bc720c48>:0 
0x00007ff6c74fefad (Unity) StackWalker::GetCurrentCallstack
0x00007ff6c7505b39 (Unity) StackWalker::ShowCallstack
0x00007ff6c84a8df3 (Unity) GetStacktrace
0x00007ff6c8b7165d (Unity) DebugStringToFile
0x00007ff6c664ea32 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x000001a2bb45c083 (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
0x000001a2bb45bd5b (Mono JIT Code) UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
0x000001a2bb45bae0 (Mono JIT Code) UnityEngine.Logger:Log (UnityEngine.LogType,object)
0x000001a2bb5dd045 (Mono JIT Code) UnityEngine.Debug:LogError (object)
0x000001a2bb5c907b (Mono JIT Code) UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes ()
0x000001a2bb406b35 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
0x00007ffb3c53697e (mono-2.0-bdwgc) [mini-runtime.c:3445] mono_jit_runtime_invoke 
0x00007ffb3c46cdd4 (mono-2.0-bdwgc) [object.c:3068] do_runtime_invoke 
0x00007ffb3c46cf4c (mono-2.0-bdwgc) [object.c:3115] mono_runtime_invoke 
0x00007ff6c7421934 (Unity) scripting_method_invoke
0x00007ff6c7400b84 (Unity) ScriptingInvocation::Invoke
0x00007ff6c73fb6e5 (Unity) ScriptingInvocation::Invoke<void>
0x00007ff6c753f53b (Unity) Scripting::UnityEditor::EditorAssembliesProxy::ProcessInitializeOnLoadMethodAttributes
0x00007ff6c73f5733 (Unity) MonoManager::SetupLoadedEditorAssemblies
0x00007ff6c73eb1ed (Unity) MonoManager::EndReloadAssembly
0x00007ff6c73f2d88 (Unity) MonoManager::ReloadAssembly
0x00007ff6c835fa12 (Unity) LoadDomainAndUserAssemblies
0x00007ff6c8360144 (Unity) LoadUserAssemblies
0x00007ff6c885c091 (Unity) <lambda_f8411f227bdd47da33e064bfa1662688>::operator()
0x00007ff6c889ce92 (Unity) asio::detail::completion_handler<core::mutable_function<void __cdecl(void)>,asio::io_context::basic_executor_type<std::allocator<void>,0> >::do_complete
0x00007ff6c8888d7e (Unity) asio::detail::win_iocp_io_context::do_one
0x00007ff6c888a9e4 (Unity) asio::detail::win_iocp_io_context::run
0x00007ff6c889b27e (Unity) IOService::Run
0x00007ff6c886dbcf (Unity) AssetImportWorkerClient::Run
0x00007ff6c8835d13 (Unity) RunAssetImportWorkerClientV2
0x00007ff6c8835d9b (Unity) RunAssetImporterV2
0x00007ff6c8060c48 (Unity) Application::InitializeProject
0x00007ff6c84b20a0 (Unity) WinMain
0x00007ff6c987fbae (Unity) __scrt_common_main_seh
0x00007ffbd486259d (KERNEL32) BaseThreadInitThunk
0x00007ffbd4e8af78 (ntdll) RtlUserThreadStart

Mono: successfully reloaded assembly
- Completed reload, in  1.330 seconds
Domain Reload Profiling:
	ReloadAssembly (1331ms)
		BeginReloadAssembly (163ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (2ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (24ms)
		EndReloadAssembly (1059ms)
			LoadAssemblies (135ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (182ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (30ms)
			SetupLoadedEditorAssemblies (708ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (14ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (63ms)
				ProcessInitializeOnLoadAttributes (569ms)
				ProcessInitializeOnLoadMethodAttributes (24ms)
				AfterProcessingInitializeOnLoad (36ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (7ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 0.65 ms, found 13 plugins.
Preloading 2 native plugins for Editor in 0.17 ms.
Unloading 4754 Unused Serialized files (Serialized files now loaded: 0)
Unloading 50 unused Assets / (95.6 KB). Loaded Objects now: 5470.
Memory consumption went from 197.3 MB to 197.2 MB.
Total: 2.073300 ms (FindLiveObjects: 0.231800 ms CreateObjectMapping: 0.074000 ms MarkObjects: 1.731800 ms  DeleteObjects: 0.034000 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.014436 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 0.69 ms, found 13 plugins.
Preloading 2 native plugins for Editor in 0.31 ms.
PXRLog [Build Check]RegisterBuildPlayerHandler,Already Do not show: False
0x00007ff6c74fefad (Unity) StackWalker::GetCurrentCallstack
0x00007ff6c7505b39 (Unity) StackWalker::ShowCallstack
0x00007ff6c84a8df3 (Unity) GetStacktrace
0x00007ff6c8b7165d (Unity) DebugStringToFile
0x00007ff6c664ea32 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x000001a2bb60c083 (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
0x000001a2bb60bd5b (Mono JIT Code) UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
0x000001a2bb60bae0 (Mono JIT Code) UnityEngine.Logger:Log (UnityEngine.LogType,object)
0x000001a2bb60b9a8 (Mono JIT Code) UnityEngine.Debug:Log (object)
0x000001a2bb608a53 (Mono JIT Code) [PXR_SDKBuildCheck.cs:32] Unity.XR.PXR.Editor.PXR_SDKBuildCheck:.cctor () 
0x000001a2bb577575 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
0x00007ffb3c53697e (mono-2.0-bdwgc) [mini-runtime.c:3445] mono_jit_runtime_invoke 
0x00007ffb3c46cdd4 (mono-2.0-bdwgc) [object.c:3068] do_runtime_invoke 
0x00007ffb3c467b79 (mono-2.0-bdwgc) [object.c:563] mono_runtime_class_init_full 
0x00007ffb3c39ddd7 (mono-2.0-bdwgc) [icall.c:1292] ves_icall_System_Runtime_CompilerServices_RuntimeHelpers_RunClassConstructor 
0x00007ffb3c3cfebc (mono-2.0-bdwgc) [icall-def.h:756] ves_icall_System_Runtime_CompilerServices_RuntimeHelpers_RunClassConstructor_raw 
0x000001a2bb576c1a (Mono JIT Code) (wrapper managed-to-native) System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (intptr)
0x000001a2bb576b2b (Mono JIT Code) System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
0x000001a2bb576273 (Mono JIT Code) UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])
0x000001a2bb4d6298 (Mono JIT Code) (wrapper runtime-invoke) <Module>:runtime_invoke_void_object (object,intptr,intptr,intptr)
0x00007ffb3c53697e (mono-2.0-bdwgc) [mini-runtime.c:3445] mono_jit_runtime_invoke 
0x00007ffb3c46cdd4 (mono-2.0-bdwgc) [object.c:3068] do_runtime_invoke 
0x00007ffb3c46cf4c (mono-2.0-bdwgc) [object.c:3115] mono_runtime_invoke 
0x00007ff6c7421934 (Unity) scripting_method_invoke
0x00007ff6c7400b84 (Unity) ScriptingInvocation::Invoke
0x00007ff6c73fb6e5 (Unity) ScriptingInvocation::Invoke<void>
0x00007ff6c753f4c3 (Unity) Scripting::UnityEditor::EditorAssembliesProxy::ProcessInitializeOnLoadAttributes
0x00007ff6c73f570b (Unity) MonoManager::SetupLoadedEditorAssemblies
0x00007ff6c73eb1ed (Unity) MonoManager::EndReloadAssembly
0x00007ff6c73f2d88 (Unity) MonoManager::ReloadAssembly
0x00007ff6c835fa12 (Unity) LoadDomainAndUserAssemblies
0x00007ff6c8360144 (Unity) LoadUserAssemblies
0x00007ff6c885c091 (Unity) <lambda_f8411f227bdd47da33e064bfa1662688>::operator()
0x00007ff6c889ce92 (Unity) asio::detail::completion_handler<core::mutable_function<void __cdecl(void)>,asio::io_context::basic_executor_type<std::allocator<void>,0> >::do_complete
0x00007ff6c8888d7e (Unity) asio::detail::win_iocp_io_context::do_one
0x00007ff6c888a9e4 (Unity) asio::detail::win_iocp_io_context::run
0x00007ff6c889b27e (Unity) IOService::Run
0x00007ff6c886dbcf (Unity) AssetImportWorkerClient::Run
0x00007ff6c8835d13 (Unity) RunAssetImportWorkerClientV2
0x00007ff6c8835d9b (Unity) RunAssetImporterV2
0x00007ff6c8060c48 (Unity) Application::InitializeProject
0x00007ff6c84b20a0 (Unity) WinMain
0x00007ff6c987fbae (Unity) __scrt_common_main_seh
0x00007ffbd486259d (KERNEL32) BaseThreadInitThunk
0x00007ffbd4e8af78 (ntdll) RtlUserThreadStart

[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
CreateSerializedAsset is not supported while importing out-of-process
0x00007ff6c74fefad (Unity) StackWalker::GetCurrentCallstack
0x00007ff6c7505b39 (Unity) StackWalker::ShowCallstack
0x00007ff6c84a8df3 (Unity) GetStacktrace
0x00007ff6c8b7165d (Unity) DebugStringToFile
0x00007ff6c887b618 (Unity) CreateSerializedAssetV2
0x00007ff6c8810f55 (Unity) CreateSerializedAssetV2
0x00007ff6c877f8ee (Unity) AssetDatabase::CreateSerializedAsset
0x00007ff6c6cea4ff (Unity) AssetDatabaseBindings::CreateAsset
0x00007ff6c6b0b819 (Unity) AssetDatabase_CUSTOM_CreateAsset
0x000001a2bb48cdb5 (Mono JIT Code) (wrapper managed-to-native) UnityEditor.AssetDatabase:CreateAsset (UnityEngine.Object,string)
0x000001a2bb48c753 (Mono JIT Code) [PXR_ProjectSetting.cs:121] Unity.XR.PXR.PXR_ProjectSetting:GetProjectConfig () 
0x000001a2bb48c383 (Mono JIT Code) [PXR_SDKSettingEditor.cs:111] Unity.XR.PXR.Editor.PXR_SDKSettingEditor:InitializeOnLoad () 
0x000001a2bb5775c3 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
0x00007ffb3c53697e (mono-2.0-bdwgc) [mini-runtime.c:3445] mono_jit_runtime_invoke 
0x00007ffb3c46cdd4 (mono-2.0-bdwgc) [object.c:3068] do_runtime_invoke 
0x00007ffb3c4746df (mono-2.0-bdwgc) [object.c:5770] mono_runtime_try_invoke_array 
0x00007ffb3c3a4eb2 (mono-2.0-bdwgc) [icall.c:4041] ves_icall_InternalInvoke 
0x00007ffb3c3cb091 (mono-2.0-bdwgc) [icall-def.h:692] ves_icall_InternalInvoke_raw 
0x000001a2bb5aae20 (Mono JIT Code) (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo:InternalInvoke (System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
0x000001a2bb5aa5bb (Mono JIT Code) System.Reflection.RuntimeMethodInfo:Invoke (object,System.Reflection.BindingFlags,System.Reflection.Binder,object[],System.Globalization.CultureInfo)
0x000001a2bb5aa47e (Mono JIT Code) System.Reflection.MethodBase:Invoke (object,object[])
0x000001a2bb478c7b (Mono JIT Code) UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes ()
0x000001a2bb4d6b35 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
0x00007ffb3c53697e (mono-2.0-bdwgc) [mini-runtime.c:3445] mono_jit_runtime_invoke 
0x00007ffb3c46cdd4 (mono-2.0-bdwgc) [object.c:3068] do_runtime_invoke 
0x00007ffb3c46cf4c (mono-2.0-bdwgc) [object.c:3115] mono_runtime_invoke 
0x00007ff6c7421934 (Unity) scripting_method_invoke
0x00007ff6c7400b84 (Unity) ScriptingInvocation::Invoke
0x00007ff6c73fb6e5 (Unity) ScriptingInvocation::Invoke<void>
0x00007ff6c753f53b (Unity) Scripting::UnityEditor::EditorAssembliesProxy::ProcessInitializeOnLoadMethodAttributes
0x00007ff6c73f5733 (Unity) MonoManager::SetupLoadedEditorAssemblies
0x00007ff6c73eb1ed (Unity) MonoManager::EndReloadAssembly
0x00007ff6c73f2d88 (Unity) MonoManager::ReloadAssembly
0x00007ff6c835fa12 (Unity) LoadDomainAndUserAssemblies
0x00007ff6c8360144 (Unity) LoadUserAssemblies
0x00007ff6c885c091 (Unity) <lambda_f8411f227bdd47da33e064bfa1662688>::operator()
0x00007ff6c889ce92 (Unity) asio::detail::completion_handler<core::mutable_function<void __cdecl(void)>,asio::io_context::basic_executor_type<std::allocator<void>,0> >::do_complete
0x00007ff6c8888d7e (Unity) asio::detail::win_iocp_io_context::do_one
0x00007ff6c888a9e4 (Unity) asio::detail::win_iocp_io_context::run
0x00007ff6c889b27e (Unity) IOService::Run
0x00007ff6c886dbcf (Unity) AssetImportWorkerClient::Run
0x00007ff6c8835d13 (Unity) RunAssetImportWorkerClientV2
0x00007ff6c8835d9b (Unity) RunAssetImporterV2
0x00007ff6c8060c48 (Unity) Application::InitializeProject
0x00007ff6c84b20a0 (Unity) WinMain
0x00007ff6c987fbae (Unity) __scrt_common_main_seh
0x00007ffbd486259d (KERNEL32) BaseThreadInitThunk
0x00007ffbd4e8af78 (ntdll) RtlUserThreadStart

System.Reflection.TargetInvocationException: Exception has been thrown by the target of an invocation. ---> UnityEngine.UnityException: Creating asset at path Assets/Resources/PXR_ProjectSetting.asset failed.
  at (wrapper managed-to-native) UnityEditor.AssetDatabase.CreateAsset(UnityEngine.Object,string)
  at Unity.XR.PXR.PXR_ProjectSetting.GetProjectConfig () [0x0015a] in D:\PICO Unity Integration SDK-3.2.0-20250529\Runtime\Scripts\PXR_ProjectSetting.cs:117 
  at Unity.XR.PXR.Editor.PXR_SDKSettingEditor.InitializeOnLoad () [0x00000] in D:\PICO Unity Integration SDK-3.2.0-20250529\Editor\PXR_SDKSettingEditor.cs:111 
  at (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo.InternalInvoke(System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x0006a] in <688f272cc27c4df98c503f9d1f1267d0>:0 
   --- End of inner exception stack trace ---
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x00083] in <688f272cc27c4df98c503f9d1f1267d0>:0 
  at System.Reflection.MethodBase.Invoke (System.Object obj, System.Object[] parameters) [0x00000] in <688f272cc27c4df98c503f9d1f1267d0>:0 
  at UnityEditor.EditorAssemblies.ProcessInitializeOnLoadMethodAttributes () [0x000a5] in <1518627dacca44efa32edd45bc720c48>:0 
0x00007ff6c74fefad (Unity) StackWalker::GetCurrentCallstack
0x00007ff6c7505b39 (Unity) StackWalker::ShowCallstack
0x00007ff6c84a8df3 (Unity) GetStacktrace
0x00007ff6c8b7165d (Unity) DebugStringToFile
0x00007ff6c664ea32 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x000001a2bb60c083 (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
0x000001a2bb60bd5b (Mono JIT Code) UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
0x000001a2bb60bae0 (Mono JIT Code) UnityEngine.Logger:Log (UnityEngine.LogType,object)
0x000001a2bb48d045 (Mono JIT Code) UnityEngine.Debug:LogError (object)
0x000001a2bb478c9b (Mono JIT Code) UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes ()
0x000001a2bb4d6b35 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
0x00007ffb3c53697e (mono-2.0-bdwgc) [mini-runtime.c:3445] mono_jit_runtime_invoke 
0x00007ffb3c46cdd4 (mono-2.0-bdwgc) [object.c:3068] do_runtime_invoke 
0x00007ffb3c46cf4c (mono-2.0-bdwgc) [object.c:3115] mono_runtime_invoke 
0x00007ff6c7421934 (Unity) scripting_method_invoke
0x00007ff6c7400b84 (Unity) ScriptingInvocation::Invoke
0x00007ff6c73fb6e5 (Unity) ScriptingInvocation::Invoke<void>
0x00007ff6c753f53b (Unity) Scripting::UnityEditor::EditorAssembliesProxy::ProcessInitializeOnLoadMethodAttributes
0x00007ff6c73f5733 (Unity) MonoManager::SetupLoadedEditorAssemblies
0x00007ff6c73eb1ed (Unity) MonoManager::EndReloadAssembly
0x00007ff6c73f2d88 (Unity) MonoManager::ReloadAssembly
0x00007ff6c835fa12 (Unity) LoadDomainAndUserAssemblies
0x00007ff6c8360144 (Unity) LoadUserAssemblies
0x00007ff6c885c091 (Unity) <lambda_f8411f227bdd47da33e064bfa1662688>::operator()
0x00007ff6c889ce92 (Unity) asio::detail::completion_handler<core::mutable_function<void __cdecl(void)>,asio::io_context::basic_executor_type<std::allocator<void>,0> >::do_complete
0x00007ff6c8888d7e (Unity) asio::detail::win_iocp_io_context::do_one
0x00007ff6c888a9e4 (Unity) asio::detail::win_iocp_io_context::run
0x00007ff6c889b27e (Unity) IOService::Run
0x00007ff6c886dbcf (Unity) AssetImportWorkerClient::Run
0x00007ff6c8835d13 (Unity) RunAssetImportWorkerClientV2
0x00007ff6c8835d9b (Unity) RunAssetImporterV2
0x00007ff6c8060c48 (Unity) Application::InitializeProject
0x00007ff6c84b20a0 (Unity) WinMain
0x00007ff6c987fbae (Unity) __scrt_common_main_seh
0x00007ffbd486259d (KERNEL32) BaseThreadInitThunk
0x00007ffbd4e8af78 (ntdll) RtlUserThreadStart

Mono: successfully reloaded assembly
- Completed reload, in  1.400 seconds
Domain Reload Profiling:
	ReloadAssembly (1400ms)
		BeginReloadAssembly (169ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (2ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (24ms)
		EndReloadAssembly (1121ms)
			LoadAssemblies (138ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (201ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (31ms)
			SetupLoadedEditorAssemblies (745ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (18ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (70ms)
				ProcessInitializeOnLoadAttributes (591ms)
				ProcessInitializeOnLoadMethodAttributes (26ms)
				AfterProcessingInitializeOnLoad (38ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (6ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 0.80 ms, found 13 plugins.
Preloading 2 native plugins for Editor in 0.25 ms.
Unloading 4754 Unused Serialized files (Serialized files now loaded: 0)
Unloading 50 unused Assets / (95.7 KB). Loaded Objects now: 5487.
Memory consumption went from 197.6 MB to 197.5 MB.
Total: 2.306900 ms (FindLiveObjects: 0.274800 ms CreateObjectMapping: 0.081200 ms MarkObjects: 1.912700 ms  DeleteObjects: 0.037400 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 15.60 ms, found 13 plugins.
Preloading 2 native plugins for Editor in 0.18 ms.
Unloading 45 Unused Serialized files (Serialized files now loaded: 0)
Unloading 45 unused Assets / (95.6 KB). Loaded Objects now: 5487.
Memory consumption went from 103.3 MB to 103.3 MB.
Total: 2.874600 ms (FindLiveObjects: 0.279900 ms CreateObjectMapping: 0.085100 ms MarkObjects: 2.469800 ms  DeleteObjects: 0.039000 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 17.23 ms, found 13 plugins.
Preloading 2 native plugins for Editor in 0.13 ms.
Unloading 45 Unused Serialized files (Serialized files now loaded: 0)
Unloading 45 unused Assets / (95.7 KB). Loaded Objects now: 5487.
Memory consumption went from 103.0 MB to 102.9 MB.
Total: 2.713400 ms (FindLiveObjects: 0.225200 ms CreateObjectMapping: 0.073600 ms MarkObjects: 2.385200 ms  DeleteObjects: 0.028100 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 1656.747166 seconds.
  path: Assets/Scripts/PICODirectInputHandler.cs
  artifactKey: Guid(8deff7a7d0a99d74196449f8ada4b8ea) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Scripts/PICODirectInputHandler.cs using Guid(8deff7a7d0a99d74196449f8ada4b8ea) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '3d1ac40db39666b17e89899b6eab6767') in 0.013132 seconds 
Number of asset objects unloaded after import = 0
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.015836 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 0.62 ms, found 13 plugins.
Preloading 2 native plugins for Editor in 0.21 ms.
PXRLog [Build Check]RegisterBuildPlayerHandler,Already Do not show: False
0x00007ff6c74fefad (Unity) StackWalker::GetCurrentCallstack
0x00007ff6c7505b39 (Unity) StackWalker::ShowCallstack
0x00007ff6c84a8df3 (Unity) GetStacktrace
0x00007ff6c8b7165d (Unity) DebugStringToFile
0x00007ff6c664ea32 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x000001a2bb41c083 (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
0x000001a2bb41bd5b (Mono JIT Code) UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
0x000001a2bb41bae0 (Mono JIT Code) UnityEngine.Logger:Log (UnityEngine.LogType,object)
0x000001a2bb41b9a8 (Mono JIT Code) UnityEngine.Debug:Log (object)
0x000001a2bb418a53 (Mono JIT Code) [PXR_SDKBuildCheck.cs:32] Unity.XR.PXR.Editor.PXR_SDKBuildCheck:.cctor () 
0x000001a2bb427575 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
0x00007ffb3c53697e (mono-2.0-bdwgc) [mini-runtime.c:3445] mono_jit_runtime_invoke 
0x00007ffb3c46cdd4 (mono-2.0-bdwgc) [object.c:3068] do_runtime_invoke 
0x00007ffb3c467b79 (mono-2.0-bdwgc) [object.c:563] mono_runtime_class_init_full 
0x00007ffb3c39ddd7 (mono-2.0-bdwgc) [icall.c:1292] ves_icall_System_Runtime_CompilerServices_RuntimeHelpers_RunClassConstructor 
0x00007ffb3c3cfebc (mono-2.0-bdwgc) [icall-def.h:756] ves_icall_System_Runtime_CompilerServices_RuntimeHelpers_RunClassConstructor_raw 
0x000001a2bb426c1a (Mono JIT Code) (wrapper managed-to-native) System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (intptr)
0x000001a2bb426b2b (Mono JIT Code) System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
0x000001a2bb426273 (Mono JIT Code) UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])
0x000001a2ba756298 (Mono JIT Code) (wrapper runtime-invoke) <Module>:runtime_invoke_void_object (object,intptr,intptr,intptr)
0x00007ffb3c53697e (mono-2.0-bdwgc) [mini-runtime.c:3445] mono_jit_runtime_invoke 
0x00007ffb3c46cdd4 (mono-2.0-bdwgc) [object.c:3068] do_runtime_invoke 
0x00007ffb3c46cf4c (mono-2.0-bdwgc) [object.c:3115] mono_runtime_invoke 
0x00007ff6c7421934 (Unity) scripting_method_invoke
0x00007ff6c7400b84 (Unity) ScriptingInvocation::Invoke
0x00007ff6c73fb6e5 (Unity) ScriptingInvocation::Invoke<void>
0x00007ff6c753f4c3 (Unity) Scripting::UnityEditor::EditorAssembliesProxy::ProcessInitializeOnLoadAttributes
0x00007ff6c73f570b (Unity) MonoManager::SetupLoadedEditorAssemblies
0x00007ff6c73eb1ed (Unity) MonoManager::EndReloadAssembly
0x00007ff6c73f2d88 (Unity) MonoManager::ReloadAssembly
0x00007ff6c835fa12 (Unity) LoadDomainAndUserAssemblies
0x00007ff6c8360144 (Unity) LoadUserAssemblies
0x00007ff6c885c091 (Unity) <lambda_f8411f227bdd47da33e064bfa1662688>::operator()
0x00007ff6c889ce92 (Unity) asio::detail::completion_handler<core::mutable_function<void __cdecl(void)>,asio::io_context::basic_executor_type<std::allocator<void>,0> >::do_complete
0x00007ff6c8888d7e (Unity) asio::detail::win_iocp_io_context::do_one
0x00007ff6c888a9e4 (Unity) asio::detail::win_iocp_io_context::run
0x00007ff6c889b27e (Unity) IOService::Run
0x00007ff6c886dbcf (Unity) AssetImportWorkerClient::Run
0x00007ff6c8835d13 (Unity) RunAssetImportWorkerClientV2
0x00007ff6c8835d9b (Unity) RunAssetImporterV2
0x00007ff6c8060c48 (Unity) Application::InitializeProject
0x00007ff6c84b20a0 (Unity) WinMain
0x00007ff6c987fbae (Unity) __scrt_common_main_seh
0x00007ffbd486259d (KERNEL32) BaseThreadInitThunk
0x00007ffbd4e8af78 (ntdll) RtlUserThreadStart

[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
CreateSerializedAsset is not supported while importing out-of-process
0x00007ff6c74fefad (Unity) StackWalker::GetCurrentCallstack
0x00007ff6c7505b39 (Unity) StackWalker::ShowCallstack
0x00007ff6c84a8df3 (Unity) GetStacktrace
0x00007ff6c8b7165d (Unity) DebugStringToFile
0x00007ff6c887b618 (Unity) CreateSerializedAssetV2
0x00007ff6c8810f55 (Unity) CreateSerializedAssetV2
0x00007ff6c877f8ee (Unity) AssetDatabase::CreateSerializedAsset
0x00007ff6c6cea4ff (Unity) AssetDatabaseBindings::CreateAsset
0x00007ff6c6b0b819 (Unity) AssetDatabase_CUSTOM_CreateAsset
0x000001a2bb4bcdb5 (Mono JIT Code) (wrapper managed-to-native) UnityEditor.AssetDatabase:CreateAsset (UnityEngine.Object,string)
0x000001a2bb4bc753 (Mono JIT Code) [PXR_ProjectSetting.cs:121] Unity.XR.PXR.PXR_ProjectSetting:GetProjectConfig () 
0x000001a2bb4bc383 (Mono JIT Code) [PXR_SDKSettingEditor.cs:111] Unity.XR.PXR.Editor.PXR_SDKSettingEditor:InitializeOnLoad () 
0x000001a2bb4275c3 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
0x00007ffb3c53697e (mono-2.0-bdwgc) [mini-runtime.c:3445] mono_jit_runtime_invoke 
0x00007ffb3c46cdd4 (mono-2.0-bdwgc) [object.c:3068] do_runtime_invoke 
0x00007ffb3c4746df (mono-2.0-bdwgc) [object.c:5770] mono_runtime_try_invoke_array 
0x00007ffb3c3a4eb2 (mono-2.0-bdwgc) [icall.c:4041] ves_icall_InternalInvoke 
0x00007ffb3c3cb091 (mono-2.0-bdwgc) [icall-def.h:692] ves_icall_InternalInvoke_raw 
0x000001a2bb48ae20 (Mono JIT Code) (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo:InternalInvoke (System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
0x000001a2bb48a5bb (Mono JIT Code) System.Reflection.RuntimeMethodInfo:Invoke (object,System.Reflection.BindingFlags,System.Reflection.Binder,object[],System.Globalization.CultureInfo)
0x000001a2bb48a47e (Mono JIT Code) System.Reflection.MethodBase:Invoke (object,object[])
0x000001a2bb4a8c7b (Mono JIT Code) UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes ()
0x000001a2ba756b35 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
0x00007ffb3c53697e (mono-2.0-bdwgc) [mini-runtime.c:3445] mono_jit_runtime_invoke 
0x00007ffb3c46cdd4 (mono-2.0-bdwgc) [object.c:3068] do_runtime_invoke 
0x00007ffb3c46cf4c (mono-2.0-bdwgc) [object.c:3115] mono_runtime_invoke 
0x00007ff6c7421934 (Unity) scripting_method_invoke
0x00007ff6c7400b84 (Unity) ScriptingInvocation::Invoke
0x00007ff6c73fb6e5 (Unity) ScriptingInvocation::Invoke<void>
0x00007ff6c753f53b (Unity) Scripting::UnityEditor::EditorAssembliesProxy::ProcessInitializeOnLoadMethodAttributes
0x00007ff6c73f5733 (Unity) MonoManager::SetupLoadedEditorAssemblies
0x00007ff6c73eb1ed (Unity) MonoManager::EndReloadAssembly
0x00007ff6c73f2d88 (Unity) MonoManager::ReloadAssembly
0x00007ff6c835fa12 (Unity) LoadDomainAndUserAssemblies
0x00007ff6c8360144 (Unity) LoadUserAssemblies
0x00007ff6c885c091 (Unity) <lambda_f8411f227bdd47da33e064bfa1662688>::operator()
0x00007ff6c889ce92 (Unity) asio::detail::completion_handler<core::mutable_function<void __cdecl(void)>,asio::io_context::basic_executor_type<std::allocator<void>,0> >::do_complete
0x00007ff6c8888d7e (Unity) asio::detail::win_iocp_io_context::do_one
0x00007ff6c888a9e4 (Unity) asio::detail::win_iocp_io_context::run
0x00007ff6c889b27e (Unity) IOService::Run
0x00007ff6c886dbcf (Unity) AssetImportWorkerClient::Run
0x00007ff6c8835d13 (Unity) RunAssetImportWorkerClientV2
0x00007ff6c8835d9b (Unity) RunAssetImporterV2
0x00007ff6c8060c48 (Unity) Application::InitializeProject
0x00007ff6c84b20a0 (Unity) WinMain
0x00007ff6c987fbae (Unity) __scrt_common_main_seh
0x00007ffbd486259d (KERNEL32) BaseThreadInitThunk
0x00007ffbd4e8af78 (ntdll) RtlUserThreadStart

System.Reflection.TargetInvocationException: Exception has been thrown by the target of an invocation. ---> UnityEngine.UnityException: Creating asset at path Assets/Resources/PXR_ProjectSetting.asset failed.
  at (wrapper managed-to-native) UnityEditor.AssetDatabase.CreateAsset(UnityEngine.Object,string)
  at Unity.XR.PXR.PXR_ProjectSetting.GetProjectConfig () [0x0015a] in D:\PICO Unity Integration SDK-3.2.0-20250529\Runtime\Scripts\PXR_ProjectSetting.cs:117 
  at Unity.XR.PXR.Editor.PXR_SDKSettingEditor.InitializeOnLoad () [0x00000] in D:\PICO Unity Integration SDK-3.2.0-20250529\Editor\PXR_SDKSettingEditor.cs:111 
  at (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo.InternalInvoke(System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x0006a] in <688f272cc27c4df98c503f9d1f1267d0>:0 
   --- End of inner exception stack trace ---
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x00083] in <688f272cc27c4df98c503f9d1f1267d0>:0 
  at System.Reflection.MethodBase.Invoke (System.Object obj, System.Object[] parameters) [0x00000] in <688f272cc27c4df98c503f9d1f1267d0>:0 
  at UnityEditor.EditorAssemblies.ProcessInitializeOnLoadMethodAttributes () [0x000a5] in <1518627dacca44efa32edd45bc720c48>:0 
0x00007ff6c74fefad (Unity) StackWalker::GetCurrentCallstack
0x00007ff6c7505b39 (Unity) StackWalker::ShowCallstack
0x00007ff6c84a8df3 (Unity) GetStacktrace
0x00007ff6c8b7165d (Unity) DebugStringToFile
0x00007ff6c664ea32 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x000001a2bb41c083 (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
0x000001a2bb41bd5b (Mono JIT Code) UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
0x000001a2bb41bae0 (Mono JIT Code) UnityEngine.Logger:Log (UnityEngine.LogType,object)
0x000001a2bb4bd045 (Mono JIT Code) UnityEngine.Debug:LogError (object)
0x000001a2bb4a8c9b (Mono JIT Code) UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes ()
0x000001a2ba756b35 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
0x00007ffb3c53697e (mono-2.0-bdwgc) [mini-runtime.c:3445] mono_jit_runtime_invoke 
0x00007ffb3c46cdd4 (mono-2.0-bdwgc) [object.c:3068] do_runtime_invoke 
0x00007ffb3c46cf4c (mono-2.0-bdwgc) [object.c:3115] mono_runtime_invoke 
0x00007ff6c7421934 (Unity) scripting_method_invoke
0x00007ff6c7400b84 (Unity) ScriptingInvocation::Invoke
0x00007ff6c73fb6e5 (Unity) ScriptingInvocation::Invoke<void>
0x00007ff6c753f53b (Unity) Scripting::UnityEditor::EditorAssembliesProxy::ProcessInitializeOnLoadMethodAttributes
0x00007ff6c73f5733 (Unity) MonoManager::SetupLoadedEditorAssemblies
0x00007ff6c73eb1ed (Unity) MonoManager::EndReloadAssembly
0x00007ff6c73f2d88 (Unity) MonoManager::ReloadAssembly
0x00007ff6c835fa12 (Unity) LoadDomainAndUserAssemblies
0x00007ff6c8360144 (Unity) LoadUserAssemblies
0x00007ff6c885c091 (Unity) <lambda_f8411f227bdd47da33e064bfa1662688>::operator()
0x00007ff6c889ce92 (Unity) asio::detail::completion_handler<core::mutable_function<void __cdecl(void)>,asio::io_context::basic_executor_type<std::allocator<void>,0> >::do_complete
0x00007ff6c8888d7e (Unity) asio::detail::win_iocp_io_context::do_one
0x00007ff6c888a9e4 (Unity) asio::detail::win_iocp_io_context::run
0x00007ff6c889b27e (Unity) IOService::Run
0x00007ff6c886dbcf (Unity) AssetImportWorkerClient::Run
0x00007ff6c8835d13 (Unity) RunAssetImportWorkerClientV2
0x00007ff6c8835d9b (Unity) RunAssetImporterV2
0x00007ff6c8060c48 (Unity) Application::InitializeProject
0x00007ff6c84b20a0 (Unity) WinMain
0x00007ff6c987fbae (Unity) __scrt_common_main_seh
0x00007ffbd486259d (KERNEL32) BaseThreadInitThunk
0x00007ffbd4e8af78 (ntdll) RtlUserThreadStart

Mono: successfully reloaded assembly
- Completed reload, in  1.317 seconds
Domain Reload Profiling:
	ReloadAssembly (1317ms)
		BeginReloadAssembly (171ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (2ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (27ms)
		EndReloadAssembly (1037ms)
			LoadAssemblies (137ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (189ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (25ms)
			SetupLoadedEditorAssemblies (687ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (15ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (58ms)
				ProcessInitializeOnLoadAttributes (559ms)
				ProcessInitializeOnLoadMethodAttributes (24ms)
				AfterProcessingInitializeOnLoad (30ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (5ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 0.66 ms, found 13 plugins.
Preloading 2 native plugins for Editor in 0.17 ms.
Unloading 4753 Unused Serialized files (Serialized files now loaded: 0)
Unloading 51 unused Assets / (95.7 KB). Loaded Objects now: 5503.
Memory consumption went from 197.6 MB to 197.5 MB.
Total: 2.162400 ms (FindLiveObjects: 0.234100 ms CreateObjectMapping: 0.082600 ms MarkObjects: 1.801600 ms  DeleteObjects: 0.043400 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 17.22 ms, found 13 plugins.
Preloading 2 native plugins for Editor in 0.23 ms.
Unloading 45 Unused Serialized files (Serialized files now loaded: 0)
Unloading 45 unused Assets / (95.7 KB). Loaded Objects now: 5503.
Memory consumption went from 103.4 MB to 103.3 MB.
Total: 2.790400 ms (FindLiveObjects: 0.305800 ms CreateObjectMapping: 0.132500 ms MarkObjects: 2.309900 ms  DeleteObjects: 0.041500 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 17.51 ms, found 13 plugins.
Preloading 2 native plugins for Editor in 0.20 ms.
Unloading 45 Unused Serialized files (Serialized files now loaded: 0)
Unloading 45 unused Assets / (95.8 KB). Loaded Objects now: 5503.
Memory consumption went from 103.1 MB to 103.0 MB.
Total: 2.260700 ms (FindLiveObjects: 0.239800 ms CreateObjectMapping: 0.074200 ms MarkObjects: 1.901000 ms  DeleteObjects: 0.044600 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 318.514863 seconds.
  path: Packages/com.unity.inputsystem/InputSystem/Editor/AssetEditor/Resources/personal/actionTreeBackground.png
  artifactKey: Guid(ce5f11cd993284c4f9c54f854bdc8c9c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Packages/com.unity.inputsystem/InputSystem/Editor/AssetEditor/Resources/personal/actionTreeBackground.png using Guid(ce5f11cd993284c4f9c54f854bdc8c9c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '8c17e59717443d32afc63c8b719bf22a') in 0.157119 seconds 
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.000020 seconds.
  path: Packages/com.unity.inputsystem/InputSystem/Editor/AssetEditor/Resources/personal/actionTreeBackgroundWithoutBorder.png
  artifactKey: Guid(cf632ed80bf1f46c980de6b1b8b903ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Packages/com.unity.inputsystem/InputSystem/Editor/AssetEditor/Resources/personal/actionTreeBackgroundWithoutBorder.png using Guid(cf632ed80bf1f46c980de6b1b8b903ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '827ce3e98489daab43714bbe55cb010d') in 0.032078 seconds 
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.960055 seconds.
  path: Assets/Samples/XR Interaction Toolkit/2.5.2/Starter Assets/Presets/XRI Default Left ActionBasedControllerManager.preset
  artifactKey: Guid(c3d4ecc12d5e0f14c93fd734af32ab63) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Samples/XR Interaction Toolkit/2.5.2/Starter Assets/Presets/XRI Default Left ActionBasedControllerManager.preset using Guid(c3d4ecc12d5e0f14c93fd734af32ab63) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '238c294bac42bd98a3b7d928bd722a12') in 0.008451 seconds 
Number of asset objects unloaded after import = 8
========================================================================
Received Import Request.
  Time since last request: 5.462011 seconds.
  path: Assets/Scripts/PICOActionBasedInputHandler.cs
  artifactKey: Guid(b6438fc35332e484a8b3e9311c386e71) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Scripts/PICOActionBasedInputHandler.cs using Guid(b6438fc35332e484a8b3e9311c386e71) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'b45ea4cd4e8ddccef69636f748b49aad') in 0.001187 seconds 
Number of asset objects unloaded after import = 0
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.020086 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 0.61 ms, found 13 plugins.
Preloading 2 native plugins for Editor in 0.21 ms.
PXRLog [Build Check]RegisterBuildPlayerHandler,Already Do not show: False
0x00007ff6c74fefad (Unity) StackWalker::GetCurrentCallstack
0x00007ff6c7505b39 (Unity) StackWalker::ShowCallstack
0x00007ff6c84a8df3 (Unity) GetStacktrace
0x00007ff6c8b7165d (Unity) DebugStringToFile
0x00007ff6c664ea32 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x000001a2bb4bc083 (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
0x000001a2bb4bbd5b (Mono JIT Code) UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
0x000001a2bb4bbae0 (Mono JIT Code) UnityEngine.Logger:Log (UnityEngine.LogType,object)
0x000001a2bb4bb9a8 (Mono JIT Code) UnityEngine.Debug:Log (object)
0x000001a2bb4b8a53 (Mono JIT Code) [PXR_SDKBuildCheck.cs:32] Unity.XR.PXR.Editor.PXR_SDKBuildCheck:.cctor () 
0x000001a2bb4475e5 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
0x00007ffb3c53697e (mono-2.0-bdwgc) [mini-runtime.c:3445] mono_jit_runtime_invoke 
0x00007ffb3c46cdd4 (mono-2.0-bdwgc) [object.c:3068] do_runtime_invoke 
0x00007ffb3c467b79 (mono-2.0-bdwgc) [object.c:563] mono_runtime_class_init_full 
0x00007ffb3c39ddd7 (mono-2.0-bdwgc) [icall.c:1292] ves_icall_System_Runtime_CompilerServices_RuntimeHelpers_RunClassConstructor 
0x00007ffb3c3cfebc (mono-2.0-bdwgc) [icall-def.h:756] ves_icall_System_Runtime_CompilerServices_RuntimeHelpers_RunClassConstructor_raw 
0x000001a2bb446c8a (Mono JIT Code) (wrapper managed-to-native) System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (intptr)
0x000001a2bb446b9b (Mono JIT Code) System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
0x000001a2bb4462e3 (Mono JIT Code) UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])
0x000001a2bb526298 (Mono JIT Code) (wrapper runtime-invoke) <Module>:runtime_invoke_void_object (object,intptr,intptr,intptr)
0x00007ffb3c53697e (mono-2.0-bdwgc) [mini-runtime.c:3445] mono_jit_runtime_invoke 
0x00007ffb3c46cdd4 (mono-2.0-bdwgc) [object.c:3068] do_runtime_invoke 
0x00007ffb3c46cf4c (mono-2.0-bdwgc) [object.c:3115] mono_runtime_invoke 
0x00007ff6c7421934 (Unity) scripting_method_invoke
0x00007ff6c7400b84 (Unity) ScriptingInvocation::Invoke
0x00007ff6c73fb6e5 (Unity) ScriptingInvocation::Invoke<void>
0x00007ff6c753f4c3 (Unity) Scripting::UnityEditor::EditorAssembliesProxy::ProcessInitializeOnLoadAttributes
0x00007ff6c73f570b (Unity) MonoManager::SetupLoadedEditorAssemblies
0x00007ff6c73eb1ed (Unity) MonoManager::EndReloadAssembly
0x00007ff6c73f2d88 (Unity) MonoManager::ReloadAssembly
0x00007ff6c835fa12 (Unity) LoadDomainAndUserAssemblies
0x00007ff6c8360144 (Unity) LoadUserAssemblies
0x00007ff6c885c091 (Unity) <lambda_f8411f227bdd47da33e064bfa1662688>::operator()
0x00007ff6c889ce92 (Unity) asio::detail::completion_handler<core::mutable_function<void __cdecl(void)>,asio::io_context::basic_executor_type<std::allocator<void>,0> >::do_complete
0x00007ff6c8888d7e (Unity) asio::detail::win_iocp_io_context::do_one
0x00007ff6c888a9e4 (Unity) asio::detail::win_iocp_io_context::run
0x00007ff6c889b27e (Unity) IOService::Run
0x00007ff6c886dbcf (Unity) AssetImportWorkerClient::Run
0x00007ff6c8835d13 (Unity) RunAssetImportWorkerClientV2
0x00007ff6c8835d9b (Unity) RunAssetImporterV2
0x00007ff6c8060c48 (Unity) Application::InitializeProject
0x00007ff6c84b20a0 (Unity) WinMain
0x00007ff6c987fbae (Unity) __scrt_common_main_seh
0x00007ffbd486259d (KERNEL32) BaseThreadInitThunk
0x00007ffbd4e8af78 (ntdll) RtlUserThreadStart

[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
CreateSerializedAsset is not supported while importing out-of-process
0x00007ff6c74fefad (Unity) StackWalker::GetCurrentCallstack
0x00007ff6c7505b39 (Unity) StackWalker::ShowCallstack
0x00007ff6c84a8df3 (Unity) GetStacktrace
0x00007ff6c8b7165d (Unity) DebugStringToFile
0x00007ff6c887b618 (Unity) CreateSerializedAssetV2
0x00007ff6c8810f55 (Unity) CreateSerializedAssetV2
0x00007ff6c877f8ee (Unity) AssetDatabase::CreateSerializedAsset
0x00007ff6c6cea4ff (Unity) AssetDatabaseBindings::CreateAsset
0x00007ff6c6b0b819 (Unity) AssetDatabase_CUSTOM_CreateAsset
0x000001a2bb41cdb5 (Mono JIT Code) (wrapper managed-to-native) UnityEditor.AssetDatabase:CreateAsset (UnityEngine.Object,string)
0x000001a2bb41c753 (Mono JIT Code) [PXR_ProjectSetting.cs:121] Unity.XR.PXR.PXR_ProjectSetting:GetProjectConfig () 
0x000001a2bb41c383 (Mono JIT Code) [PXR_SDKSettingEditor.cs:111] Unity.XR.PXR.Editor.PXR_SDKSettingEditor:InitializeOnLoad () 
0x000001a2bb447633 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
0x00007ffb3c53697e (mono-2.0-bdwgc) [mini-runtime.c:3445] mono_jit_runtime_invoke 
0x00007ffb3c46cdd4 (mono-2.0-bdwgc) [object.c:3068] do_runtime_invoke 
0x00007ffb3c4746df (mono-2.0-bdwgc) [object.c:5770] mono_runtime_try_invoke_array 
0x00007ffb3c3a4eb2 (mono-2.0-bdwgc) [icall.c:4041] ves_icall_InternalInvoke 
0x00007ffb3c3cb091 (mono-2.0-bdwgc) [icall-def.h:692] ves_icall_InternalInvoke_raw 
0x000001a2bb4aae20 (Mono JIT Code) (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo:InternalInvoke (System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
0x000001a2bb4aa5bb (Mono JIT Code) System.Reflection.RuntimeMethodInfo:Invoke (object,System.Reflection.BindingFlags,System.Reflection.Binder,object[],System.Globalization.CultureInfo)
0x000001a2bb4aa47e (Mono JIT Code) System.Reflection.MethodBase:Invoke (object,object[])
0x000001a2ba80905b (Mono JIT Code) UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes ()
0x000001a2bb526b35 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
0x00007ffb3c53697e (mono-2.0-bdwgc) [mini-runtime.c:3445] mono_jit_runtime_invoke 
0x00007ffb3c46cdd4 (mono-2.0-bdwgc) [object.c:3068] do_runtime_invoke 
0x00007ffb3c46cf4c (mono-2.0-bdwgc) [object.c:3115] mono_runtime_invoke 
0x00007ff6c7421934 (Unity) scripting_method_invoke
0x00007ff6c7400b84 (Unity) ScriptingInvocation::Invoke
0x00007ff6c73fb6e5 (Unity) ScriptingInvocation::Invoke<void>
0x00007ff6c753f53b (Unity) Scripting::UnityEditor::EditorAssembliesProxy::ProcessInitializeOnLoadMethodAttributes
0x00007ff6c73f5733 (Unity) MonoManager::SetupLoadedEditorAssemblies
0x00007ff6c73eb1ed (Unity) MonoManager::EndReloadAssembly
0x00007ff6c73f2d88 (Unity) MonoManager::ReloadAssembly
0x00007ff6c835fa12 (Unity) LoadDomainAndUserAssemblies
0x00007ff6c8360144 (Unity) LoadUserAssemblies
0x00007ff6c885c091 (Unity) <lambda_f8411f227bdd47da33e064bfa1662688>::operator()
0x00007ff6c889ce92 (Unity) asio::detail::completion_handler<core::mutable_function<void __cdecl(void)>,asio::io_context::basic_executor_type<std::allocator<void>,0> >::do_complete
0x00007ff6c8888d7e (Unity) asio::detail::win_iocp_io_context::do_one
0x00007ff6c888a9e4 (Unity) asio::detail::win_iocp_io_context::run
0x00007ff6c889b27e (Unity) IOService::Run
0x00007ff6c886dbcf (Unity) AssetImportWorkerClient::Run
0x00007ff6c8835d13 (Unity) RunAssetImportWorkerClientV2
0x00007ff6c8835d9b (Unity) RunAssetImporterV2
0x00007ff6c8060c48 (Unity) Application::InitializeProject
0x00007ff6c84b20a0 (Unity) WinMain
0x00007ff6c987fbae (Unity) __scrt_common_main_seh
0x00007ffbd486259d (KERNEL32) BaseThreadInitThunk
0x00007ffbd4e8af78 (ntdll) RtlUserThreadStart

System.Reflection.TargetInvocationException: Exception has been thrown by the target of an invocation. ---> UnityEngine.UnityException: Creating asset at path Assets/Resources/PXR_ProjectSetting.asset failed.
  at (wrapper managed-to-native) UnityEditor.AssetDatabase.CreateAsset(UnityEngine.Object,string)
  at Unity.XR.PXR.PXR_ProjectSetting.GetProjectConfig () [0x0015a] in D:\PICO Unity Integration SDK-3.2.0-20250529\Runtime\Scripts\PXR_ProjectSetting.cs:117 
  at Unity.XR.PXR.Editor.PXR_SDKSettingEditor.InitializeOnLoad () [0x00000] in D:\PICO Unity Integration SDK-3.2.0-20250529\Editor\PXR_SDKSettingEditor.cs:111 
  at (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo.InternalInvoke(System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x0006a] in <688f272cc27c4df98c503f9d1f1267d0>:0 
   --- End of inner exception stack trace ---
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x00083] in <688f272cc27c4df98c503f9d1f1267d0>:0 
  at System.Reflection.MethodBase.Invoke (System.Object obj, System.Object[] parameters) [0x00000] in <688f272cc27c4df98c503f9d1f1267d0>:0 
  at UnityEditor.EditorAssemblies.ProcessInitializeOnLoadMethodAttributes () [0x000a5] in <1518627dacca44efa32edd45bc720c48>:0 
0x00007ff6c74fefad (Unity) StackWalker::GetCurrentCallstack
0x00007ff6c7505b39 (Unity) StackWalker::ShowCallstack
0x00007ff6c84a8df3 (Unity) GetStacktrace
0x00007ff6c8b7165d (Unity) DebugStringToFile
0x00007ff6c664ea32 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x000001a2bb4bc083 (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
0x000001a2bb4bbd5b (Mono JIT Code) UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
0x000001a2bb4bbae0 (Mono JIT Code) UnityEngine.Logger:Log (UnityEngine.LogType,object)
0x000001a2bb41d045 (Mono JIT Code) UnityEngine.Debug:LogError (object)
0x000001a2ba80907b (Mono JIT Code) UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes ()
0x000001a2bb526b35 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
0x00007ffb3c53697e (mono-2.0-bdwgc) [mini-runtime.c:3445] mono_jit_runtime_invoke 
0x00007ffb3c46cdd4 (mono-2.0-bdwgc) [object.c:3068] do_runtime_invoke 
0x00007ffb3c46cf4c (mono-2.0-bdwgc) [object.c:3115] mono_runtime_invoke 
0x00007ff6c7421934 (Unity) scripting_method_invoke
0x00007ff6c7400b84 (Unity) ScriptingInvocation::Invoke
0x00007ff6c73fb6e5 (Unity) ScriptingInvocation::Invoke<void>
0x00007ff6c753f53b (Unity) Scripting::UnityEditor::EditorAssembliesProxy::ProcessInitializeOnLoadMethodAttributes
0x00007ff6c73f5733 (Unity) MonoManager::SetupLoadedEditorAssemblies
0x00007ff6c73eb1ed (Unity) MonoManager::EndReloadAssembly
0x00007ff6c73f2d88 (Unity) MonoManager::ReloadAssembly
0x00007ff6c835fa12 (Unity) LoadDomainAndUserAssemblies
0x00007ff6c8360144 (Unity) LoadUserAssemblies
0x00007ff6c885c091 (Unity) <lambda_f8411f227bdd47da33e064bfa1662688>::operator()
0x00007ff6c889ce92 (Unity) asio::detail::completion_handler<core::mutable_function<void __cdecl(void)>,asio::io_context::basic_executor_type<std::allocator<void>,0> >::do_complete
0x00007ff6c8888d7e (Unity) asio::detail::win_iocp_io_context::do_one
0x00007ff6c888a9e4 (Unity) asio::detail::win_iocp_io_context::run
0x00007ff6c889b27e (Unity) IOService::Run
0x00007ff6c886dbcf (Unity) AssetImportWorkerClient::Run
0x00007ff6c8835d13 (Unity) RunAssetImportWorkerClientV2
0x00007ff6c8835d9b (Unity) RunAssetImporterV2
0x00007ff6c8060c48 (Unity) Application::InitializeProject
0x00007ff6c84b20a0 (Unity) WinMain
0x00007ff6c987fbae (Unity) __scrt_common_main_seh
0x00007ffbd486259d (KERNEL32) BaseThreadInitThunk
0x00007ffbd4e8af78 (ntdll) RtlUserThreadStart

Mono: successfully reloaded assembly
- Completed reload, in  1.402 seconds
Domain Reload Profiling:
	ReloadAssembly (1402ms)
		BeginReloadAssembly (212ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (2ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (27ms)
		EndReloadAssembly (1061ms)
			LoadAssemblies (174ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (183ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (28ms)
			SetupLoadedEditorAssemblies (706ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (15ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (70ms)
				ProcessInitializeOnLoadAttributes (565ms)
				ProcessInitializeOnLoadMethodAttributes (22ms)
				AfterProcessingInitializeOnLoad (33ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (5ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 0.79 ms, found 13 plugins.
Preloading 2 native plugins for Editor in 0.21 ms.
Unloading 4752 Unused Serialized files (Serialized files now loaded: 0)
Unloading 52 unused Assets / (95.8 KB). Loaded Objects now: 5522.
Memory consumption went from 197.7 MB to 197.6 MB.
Total: 2.334300 ms (FindLiveObjects: 0.257800 ms CreateObjectMapping: 0.102100 ms MarkObjects: 1.937700 ms  DeleteObjects: 0.035800 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 17.89 ms, found 13 plugins.
Preloading 2 native plugins for Editor in 0.35 ms.
Unloading 45 Unused Serialized files (Serialized files now loaded: 0)
Unloading 45 unused Assets / (95.6 KB). Loaded Objects now: 5522.
Memory consumption went from 103.5 MB to 103.4 MB.
Total: 2.282400 ms (FindLiveObjects: 0.241800 ms CreateObjectMapping: 0.074400 ms MarkObjects: 1.935400 ms  DeleteObjects: 0.030200 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 118.989741 seconds.
  path: Packages/com.unity.xr.picoxr/Assets/Resources/Controller/PICO 4U/material/o_com_PICO4U_01_m.mat
  artifactKey: Guid(ad8827df856b77c48911d03893186baa) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Packages/com.unity.xr.picoxr/Assets/Resources/Controller/PICO 4U/material/o_com_PICO4U_01_m.mat using Guid(ad8827df856b77c48911d03893186baa) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '4eecbebfe335595e2ebd3ca9d6f2c610') in 0.112086 seconds 
Number of asset objects unloaded after import = 5
========================================================================
Received Import Request.
  Time since last request: 0.065353 seconds.
  path: Packages/com.unity.xr.picoxr/Assets/Resources/Controller/PICO 4U/Textures/Power/o_com_PICO4U_power_02.png
  artifactKey: Guid(c097f47a26972414d93241c55296c220) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Packages/com.unity.xr.picoxr/Assets/Resources/Controller/PICO 4U/Textures/Power/o_com_PICO4U_power_02.png using Guid(c097f47a26972414d93241c55296c220) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '34ac75f49f44e39cc1bd37a738632aff') in 0.011329 seconds 
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.091615 seconds.
  path: Packages/com.unity.xr.picoxr/Assets/Resources/Controller/PICO 4U/Texture/o_com_PICO4U_power_01.png
  artifactKey: Guid(6a8f8e966738570459db901ed84ef9b6) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Packages/com.unity.xr.picoxr/Assets/Resources/Controller/PICO 4U/Texture/o_com_PICO4U_power_01.png using Guid(6a8f8e966738570459db901ed84ef9b6) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '6bdb09501b4b1ee4585ca9a9139341a7') in 0.007528 seconds 
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.115716 seconds.
  path: Packages/com.unity.xr.picoxr/Assets/Resources/Controller/PICO 4U/Textures/Power/o_com_PICO4U_power_03.png
  artifactKey: Guid(c874b39fd1d35274cb34a60847385eb5) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Packages/com.unity.xr.picoxr/Assets/Resources/Controller/PICO 4U/Textures/Power/o_com_PICO4U_power_03.png using Guid(c874b39fd1d35274cb34a60847385eb5) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '7b4a50b86a77ae06afca9282f5830ac7') in 0.014722 seconds 
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.111678 seconds.
  path: Packages/com.unity.xr.picoxr/Assets/Resources/Controller/ControllerWithHand/Mesh/RightHand/rightHand_X_A_Y_B.fbx
  artifactKey: Guid(be856c9e1a47247068ebfc44ad402df6) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Packages/com.unity.xr.picoxr/Assets/Resources/Controller/ControllerWithHand/Mesh/RightHand/rightHand_X_A_Y_B.fbx using Guid(be856c9e1a47247068ebfc44ad402df6) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '589fa5ccd7e60381fc41bc9a971854e4') in 0.101205 seconds 
Number of asset objects unloaded after import = 93
========================================================================
Received Import Request.
  Time since last request: 0.000018 seconds.
  path: Packages/com.unity.xr.picoxr/Assets/Resources/Controller/PICO 4U/Textures/Power/o_com_PICO4U_power_04.png
  artifactKey: Guid(d7540181fc229e8448f8276f14347b1b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Packages/com.unity.xr.picoxr/Assets/Resources/Controller/PICO 4U/Textures/Power/o_com_PICO4U_power_04.png using Guid(d7540181fc229e8448f8276f14347b1b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '0538fc90ba6f10bc767407f14a42b9bc') in 0.011070 seconds 
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.000020 seconds.
  path: Packages/com.unity.xr.picoxr/Assets/Resources/Controller/PICO 4U/Texture/o_com_PICO4U_02_a.png
  artifactKey: Guid(c4959a0adbe7a874cb1b2b7641284ede) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Packages/com.unity.xr.picoxr/Assets/Resources/Controller/PICO 4U/Texture/o_com_PICO4U_02_a.png using Guid(c4959a0adbe7a874cb1b2b7641284ede) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '3626a23ec88bdada6fcb94de5ca1744b') in 0.021711 seconds 
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.000013 seconds.
  path: Packages/com.unity.xr.picoxr/Assets/Resources/Controller/PICO 4U/material/o_com_PICO4U_power_m.mat
  artifactKey: Guid(a1b3dee6f3915d041b67ebcd32db75de) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Packages/com.unity.xr.picoxr/Assets/Resources/Controller/PICO 4U/material/o_com_PICO4U_power_m.mat using Guid(a1b3dee6f3915d041b67ebcd32db75de) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '9c9db3ff6dbb40058a2cf774c21bbe37') in 0.024428 seconds 
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000012 seconds.
  path: Packages/com.unity.xr.picoxr/Assets/Resources/Controller/PICO 4U/Texture/o_com_PICO4U_power_02.png
  artifactKey: Guid(4f3c1260c5bd8c64985946fc6af20f00) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Packages/com.unity.xr.picoxr/Assets/Resources/Controller/PICO 4U/Texture/o_com_PICO4U_power_02.png using Guid(4f3c1260c5bd8c64985946fc6af20f00) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '3669c5d41e3eef18bfecdba39475d4f2') in 0.011882 seconds 
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.000011 seconds.
  path: Packages/com.unity.xr.picoxr/Assets/Resources/Controller/PICO 4U/Texture/o_com_PICO4U_power_03.png
  artifactKey: Guid(ec194f0bc3d9ae14082c5e7cff8fb17c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Packages/com.unity.xr.picoxr/Assets/Resources/Controller/PICO 4U/Texture/o_com_PICO4U_power_03.png using Guid(ec194f0bc3d9ae14082c5e7cff8fb17c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'fd65b0a873d02e0a5da4cb617b2acb9b') in 0.014278 seconds 
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.000013 seconds.
  path: Packages/com.unity.xr.picoxr/Assets/Resources/Controller/PICO 4U/Texture/o_com_PICO4U_power_05.png
  artifactKey: Guid(0f37039b4a5cd3f48bff00c92911c80c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Packages/com.unity.xr.picoxr/Assets/Resources/Controller/PICO 4U/Texture/o_com_PICO4U_power_05.png using Guid(0f37039b4a5cd3f48bff00c92911c80c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '7eecf594c5f7908bdbde705c2f54111d') in 0.006262 seconds 
Number of asset objects unloaded after import = 2
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.020286 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 0.83 ms, found 13 plugins.
Preloading 2 native plugins for Editor in 0.26 ms.
PXRLog [Build Check]RegisterBuildPlayerHandler,Already Do not show: False
0x00007ff6c74fefad (Unity) StackWalker::GetCurrentCallstack
0x00007ff6c7505b39 (Unity) StackWalker::ShowCallstack
0x00007ff6c84a8df3 (Unity) GetStacktrace
0x00007ff6c8b7165d (Unity) DebugStringToFile
0x00007ff6c664ea32 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x000001a2bb48c083 (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
0x000001a2bb48bd5b (Mono JIT Code) UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
0x000001a2bb48bae0 (Mono JIT Code) UnityEngine.Logger:Log (UnityEngine.LogType,object)
0x000001a2bb48b9a8 (Mono JIT Code) UnityEngine.Debug:Log (object)
0x000001a2bb488a53 (Mono JIT Code) [PXR_SDKBuildCheck.cs:32] Unity.XR.PXR.Editor.PXR_SDKBuildCheck:.cctor () 
0x000001a2bb4775e5 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
0x00007ffb3c53697e (mono-2.0-bdwgc) [mini-runtime.c:3445] mono_jit_runtime_invoke 
0x00007ffb3c46cdd4 (mono-2.0-bdwgc) [object.c:3068] do_runtime_invoke 
0x00007ffb3c467b79 (mono-2.0-bdwgc) [object.c:563] mono_runtime_class_init_full 
0x00007ffb3c39ddd7 (mono-2.0-bdwgc) [icall.c:1292] ves_icall_System_Runtime_CompilerServices_RuntimeHelpers_RunClassConstructor 
0x00007ffb3c3cfebc (mono-2.0-bdwgc) [icall-def.h:756] ves_icall_System_Runtime_CompilerServices_RuntimeHelpers_RunClassConstructor_raw 
0x000001a2bb476c8a (Mono JIT Code) (wrapper managed-to-native) System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (intptr)
0x000001a2bb476b9b (Mono JIT Code) System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
0x000001a2bb4762e3 (Mono JIT Code) UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])
0x000001a2ba436298 (Mono JIT Code) (wrapper runtime-invoke) <Module>:runtime_invoke_void_object (object,intptr,intptr,intptr)
0x00007ffb3c53697e (mono-2.0-bdwgc) [mini-runtime.c:3445] mono_jit_runtime_invoke 
0x00007ffb3c46cdd4 (mono-2.0-bdwgc) [object.c:3068] do_runtime_invoke 
0x00007ffb3c46cf4c (mono-2.0-bdwgc) [object.c:3115] mono_runtime_invoke 
0x00007ff6c7421934 (Unity) scripting_method_invoke
0x00007ff6c7400b84 (Unity) ScriptingInvocation::Invoke
0x00007ff6c73fb6e5 (Unity) ScriptingInvocation::Invoke<void>
0x00007ff6c753f4c3 (Unity) Scripting::UnityEditor::EditorAssembliesProxy::ProcessInitializeOnLoadAttributes
0x00007ff6c73f570b (Unity) MonoManager::SetupLoadedEditorAssemblies
0x00007ff6c73eb1ed (Unity) MonoManager::EndReloadAssembly
0x00007ff6c73f2d88 (Unity) MonoManager::ReloadAssembly
0x00007ff6c835fa12 (Unity) LoadDomainAndUserAssemblies
0x00007ff6c8360144 (Unity) LoadUserAssemblies
0x00007ff6c885c091 (Unity) <lambda_f8411f227bdd47da33e064bfa1662688>::operator()
0x00007ff6c889ce92 (Unity) asio::detail::completion_handler<core::mutable_function<void __cdecl(void)>,asio::io_context::basic_executor_type<std::allocator<void>,0> >::do_complete
0x00007ff6c8888d7e (Unity) asio::detail::win_iocp_io_context::do_one
0x00007ff6c888a9e4 (Unity) asio::detail::win_iocp_io_context::run
0x00007ff6c889b27e (Unity) IOService::Run
0x00007ff6c886dbcf (Unity) AssetImportWorkerClient::Run
0x00007ff6c8835d13 (Unity) RunAssetImportWorkerClientV2
0x00007ff6c8835d9b (Unity) RunAssetImporterV2
0x00007ff6c8060c48 (Unity) Application::InitializeProject
0x00007ff6c84b20a0 (Unity) WinMain
0x00007ff6c987fbae (Unity) __scrt_common_main_seh
0x00007ffbd486259d (KERNEL32) BaseThreadInitThunk
0x00007ffbd4e8af78 (ntdll) RtlUserThreadStart

[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
CreateSerializedAsset is not supported while importing out-of-process
0x00007ff6c74fefad (Unity) StackWalker::GetCurrentCallstack
0x00007ff6c7505b39 (Unity) StackWalker::ShowCallstack
0x00007ff6c84a8df3 (Unity) GetStacktrace
0x00007ff6c8b7165d (Unity) DebugStringToFile
0x00007ff6c887b618 (Unity) CreateSerializedAssetV2
0x00007ff6c8810f55 (Unity) CreateSerializedAssetV2
0x00007ff6c877f8ee (Unity) AssetDatabase::CreateSerializedAsset
0x00007ff6c6cea4ff (Unity) AssetDatabaseBindings::CreateAsset
0x00007ff6c6b0b819 (Unity) AssetDatabase_CUSTOM_CreateAsset
0x000001a2bb51cdb5 (Mono JIT Code) (wrapper managed-to-native) UnityEditor.AssetDatabase:CreateAsset (UnityEngine.Object,string)
0x000001a2bb51c753 (Mono JIT Code) [PXR_ProjectSetting.cs:121] Unity.XR.PXR.PXR_ProjectSetting:GetProjectConfig () 
0x000001a2bb51c383 (Mono JIT Code) [PXR_SDKSettingEditor.cs:111] Unity.XR.PXR.Editor.PXR_SDKSettingEditor:InitializeOnLoad () 
0x000001a2bb477633 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
0x00007ffb3c53697e (mono-2.0-bdwgc) [mini-runtime.c:3445] mono_jit_runtime_invoke 
0x00007ffb3c46cdd4 (mono-2.0-bdwgc) [object.c:3068] do_runtime_invoke 
0x00007ffb3c4746df (mono-2.0-bdwgc) [object.c:5770] mono_runtime_try_invoke_array 
0x00007ffb3c3a4eb2 (mono-2.0-bdwgc) [icall.c:4041] ves_icall_InternalInvoke 
0x00007ffb3c3cb091 (mono-2.0-bdwgc) [icall-def.h:692] ves_icall_InternalInvoke_raw 
0x000001a2bb49ae20 (Mono JIT Code) (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo:InternalInvoke (System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
0x000001a2bb49a5bb (Mono JIT Code) System.Reflection.RuntimeMethodInfo:Invoke (object,System.Reflection.BindingFlags,System.Reflection.Binder,object[],System.Globalization.CultureInfo)
0x000001a2bb49a47e (Mono JIT Code) System.Reflection.MethodBase:Invoke (object,object[])
0x000001a2bb50905b (Mono JIT Code) UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes ()
0x000001a2ba436b35 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
0x00007ffb3c53697e (mono-2.0-bdwgc) [mini-runtime.c:3445] mono_jit_runtime_invoke 
0x00007ffb3c46cdd4 (mono-2.0-bdwgc) [object.c:3068] do_runtime_invoke 
0x00007ffb3c46cf4c (mono-2.0-bdwgc) [object.c:3115] mono_runtime_invoke 
0x00007ff6c7421934 (Unity) scripting_method_invoke
0x00007ff6c7400b84 (Unity) ScriptingInvocation::Invoke
0x00007ff6c73fb6e5 (Unity) ScriptingInvocation::Invoke<void>
0x00007ff6c753f53b (Unity) Scripting::UnityEditor::EditorAssembliesProxy::ProcessInitializeOnLoadMethodAttributes
0x00007ff6c73f5733 (Unity) MonoManager::SetupLoadedEditorAssemblies
0x00007ff6c73eb1ed (Unity) MonoManager::EndReloadAssembly
0x00007ff6c73f2d88 (Unity) MonoManager::ReloadAssembly
0x00007ff6c835fa12 (Unity) LoadDomainAndUserAssemblies
0x00007ff6c8360144 (Unity) LoadUserAssemblies
0x00007ff6c885c091 (Unity) <lambda_f8411f227bdd47da33e064bfa1662688>::operator()
0x00007ff6c889ce92 (Unity) asio::detail::completion_handler<core::mutable_function<void __cdecl(void)>,asio::io_context::basic_executor_type<std::allocator<void>,0> >::do_complete
0x00007ff6c8888d7e (Unity) asio::detail::win_iocp_io_context::do_one
0x00007ff6c888a9e4 (Unity) asio::detail::win_iocp_io_context::run
0x00007ff6c889b27e (Unity) IOService::Run
0x00007ff6c886dbcf (Unity) AssetImportWorkerClient::Run
0x00007ff6c8835d13 (Unity) RunAssetImportWorkerClientV2
0x00007ff6c8835d9b (Unity) RunAssetImporterV2
0x00007ff6c8060c48 (Unity) Application::InitializeProject
0x00007ff6c84b20a0 (Unity) WinMain
0x00007ff6c987fbae (Unity) __scrt_common_main_seh
0x00007ffbd486259d (KERNEL32) BaseThreadInitThunk
0x00007ffbd4e8af78 (ntdll) RtlUserThreadStart

System.Reflection.TargetInvocationException: Exception has been thrown by the target of an invocation. ---> UnityEngine.UnityException: Creating asset at path Assets/Resources/PXR_ProjectSetting.asset failed.
  at (wrapper managed-to-native) UnityEditor.AssetDatabase.CreateAsset(UnityEngine.Object,string)
  at Unity.XR.PXR.PXR_ProjectSetting.GetProjectConfig () [0x0015a] in D:\PICO Unity Integration SDK-3.2.0-20250529\Runtime\Scripts\PXR_ProjectSetting.cs:117 
  at Unity.XR.PXR.Editor.PXR_SDKSettingEditor.InitializeOnLoad () [0x00000] in D:\PICO Unity Integration SDK-3.2.0-20250529\Editor\PXR_SDKSettingEditor.cs:111 
  at (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo.InternalInvoke(System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x0006a] in <688f272cc27c4df98c503f9d1f1267d0>:0 
   --- End of inner exception stack trace ---
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x00083] in <688f272cc27c4df98c503f9d1f1267d0>:0 
  at System.Reflection.MethodBase.Invoke (System.Object obj, System.Object[] parameters) [0x00000] in <688f272cc27c4df98c503f9d1f1267d0>:0 
  at UnityEditor.EditorAssemblies.ProcessInitializeOnLoadMethodAttributes () [0x000a5] in <1518627dacca44efa32edd45bc720c48>:0 
0x00007ff6c74fefad (Unity) StackWalker::GetCurrentCallstack
0x00007ff6c7505b39 (Unity) StackWalker::ShowCallstack
0x00007ff6c84a8df3 (Unity) GetStacktrace
0x00007ff6c8b7165d (Unity) DebugStringToFile
0x00007ff6c664ea32 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x000001a2bb48c083 (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
0x000001a2bb48bd5b (Mono JIT Code) UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
0x000001a2bb48bae0 (Mono JIT Code) UnityEngine.Logger:Log (UnityEngine.LogType,object)
0x000001a2bb51d045 (Mono JIT Code) UnityEngine.Debug:LogError (object)
0x000001a2bb50907b (Mono JIT Code) UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes ()
0x000001a2ba436b35 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
0x00007ffb3c53697e (mono-2.0-bdwgc) [mini-runtime.c:3445] mono_jit_runtime_invoke 
0x00007ffb3c46cdd4 (mono-2.0-bdwgc) [object.c:3068] do_runtime_invoke 
0x00007ffb3c46cf4c (mono-2.0-bdwgc) [object.c:3115] mono_runtime_invoke 
0x00007ff6c7421934 (Unity) scripting_method_invoke
0x00007ff6c7400b84 (Unity) ScriptingInvocation::Invoke
0x00007ff6c73fb6e5 (Unity) ScriptingInvocation::Invoke<void>
0x00007ff6c753f53b (Unity) Scripting::UnityEditor::EditorAssembliesProxy::ProcessInitializeOnLoadMethodAttributes
0x00007ff6c73f5733 (Unity) MonoManager::SetupLoadedEditorAssemblies
0x00007ff6c73eb1ed (Unity) MonoManager::EndReloadAssembly
0x00007ff6c73f2d88 (Unity) MonoManager::ReloadAssembly
0x00007ff6c835fa12 (Unity) LoadDomainAndUserAssemblies
0x00007ff6c8360144 (Unity) LoadUserAssemblies
0x00007ff6c885c091 (Unity) <lambda_f8411f227bdd47da33e064bfa1662688>::operator()
0x00007ff6c889ce92 (Unity) asio::detail::completion_handler<core::mutable_function<void __cdecl(void)>,asio::io_context::basic_executor_type<std::allocator<void>,0> >::do_complete
0x00007ff6c8888d7e (Unity) asio::detail::win_iocp_io_context::do_one
0x00007ff6c888a9e4 (Unity) asio::detail::win_iocp_io_context::run
0x00007ff6c889b27e (Unity) IOService::Run
0x00007ff6c886dbcf (Unity) AssetImportWorkerClient::Run
0x00007ff6c8835d13 (Unity) RunAssetImportWorkerClientV2
0x00007ff6c8835d9b (Unity) RunAssetImporterV2
0x00007ff6c8060c48 (Unity) Application::InitializeProject
0x00007ff6c84b20a0 (Unity) WinMain
0x00007ff6c987fbae (Unity) __scrt_common_main_seh
0x00007ffbd486259d (KERNEL32) BaseThreadInitThunk
0x00007ffbd4e8af78 (ntdll) RtlUserThreadStart

Mono: successfully reloaded assembly
- Completed reload, in  1.871 seconds
Domain Reload Profiling:
	ReloadAssembly (1871ms)
		BeginReloadAssembly (456ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (2ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (29ms)
		EndReloadAssembly (1189ms)
			LoadAssemblies (398ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (223ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (34ms)
			SetupLoadedEditorAssemblies (722ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (18ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (67ms)
				ProcessInitializeOnLoadAttributes (581ms)
				ProcessInitializeOnLoadMethodAttributes (25ms)
				AfterProcessingInitializeOnLoad (29ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (6ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 0.63 ms, found 13 plugins.
Preloading 2 native plugins for Editor in 0.21 ms.
Unloading 4751 Unused Serialized files (Serialized files now loaded: 0)
Unloading 51 unused Assets / (95.7 KB). Loaded Objects now: 5542.
Memory consumption went from 205.1 MB to 205.0 MB.
Total: 4.239500 ms (FindLiveObjects: 0.503800 ms CreateObjectMapping: 0.171800 ms MarkObjects: 3.489400 ms  DeleteObjects: 0.072900 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
Editor requested this worker to shutdown with reason: Scaling down because of idle timeout
AssetImportWorker is now disconnected from the server
Process exiting
Exiting without the bug reporter. Application will terminate with return code 0