using UnityEngine;

#if UNITY_XR_INTERACTION_TOOLKIT
using UnityEngine.XR.Interaction.Toolkit;
using UnityEngine.InputSystem;
#endif

/// <summary>
/// PICO VR输入处理器 (简化版)
/// 专门为Action-based XR Controller设计
/// </summary>
public class PICOVRInputHandler : MonoBehaviour
{
    [Header("输入设置")]
    [SerializeField] private bool enableInput = true;
    [SerializeField] private bool showDebugLogs = true;
    
    [Header("组件引用")]
    [SerializeField] private VRAssemblyDebugger debugger;
    
#if UNITY_XR_INTERACTION_TOOLKIT
    [Header("控制器引用")]
    [SerializeField] private ActionBasedController leftController;
    [SerializeField] private ActionBasedController rightController;
#endif

    [Header("输入映射")]
    [SerializeField] private bool triggerForPositioning = false;    // 扳机键 → 禁用（避免与UI点击冲突）
    [SerializeField] private bool primaryForPositioning = true;     // A按钮(主按钮) → 装配区域定位
    [SerializeField] private bool secondaryForOrientation = true;   // B按钮(副按钮) → 装配面朝向
    [SerializeField] private bool menuForDebugToggle = true;        // 菜单键 → 调试切换

    // 输入状态跟踪
    private bool lastTriggerPressed = false;
    private bool lastPrimaryPressed = false;
    private bool lastSecondaryPressed = false;
    private bool lastMenuPressed = false;

    void Start()
    {
        InitializeComponents();
        LogInitializationStatus();
    }

    void Update()
    {
        if (!enableInput) return;
        
        HandleInput();
    }

    /// <summary>
    /// 初始化组件
    /// </summary>
    private void InitializeComponents()
    {
        // 自动查找VRAssemblyDebugger
        if (debugger == null)
        {
            debugger = FindObjectOfType<VRAssemblyDebugger>();
        }

#if UNITY_XR_INTERACTION_TOOLKIT
        // 自动查找控制器
        if (leftController == null)
        {
            var leftGO = GameObject.Find("Left Controller") ?? GameObject.Find("LeftHand Controller");
            if (leftGO != null)
            {
                leftController = leftGO.GetComponent<ActionBasedController>();
            }
        }

        if (rightController == null)
        {
            var rightGO = GameObject.Find("Right Controller") ?? GameObject.Find("RightHand Controller");
            if (rightGO != null)
            {
                rightController = rightGO.GetComponent<ActionBasedController>();
            }
        }
#endif
    }

    /// <summary>
    /// 记录初始化状态
    /// </summary>
    private void LogInitializationStatus()
    {
        if (!showDebugLogs) return;

        Debug.Log("=== PICO VR输入处理器初始化 ===");
        Debug.Log($"VRAssemblyDebugger: {(debugger != null ? "✅ 已找到" : "❌ 未找到")}");

#if UNITY_XR_INTERACTION_TOOLKIT
        Debug.Log($"左手控制器: {(leftController != null ? "✅ 已找到" : "❌ 未找到")}");
        Debug.Log($"右手控制器: {(rightController != null ? "✅ 已找到" : "❌ 未找到")}");
        Debug.Log("XR Interaction Toolkit: ✅ 可用");
#else
        Debug.Log("XR Interaction Toolkit: ❌ 不可用，将使用备用输入");
#endif
        
        Debug.Log("输入映射:");
        Debug.Log($"  扳机键 → {(triggerForPositioning ? "装配区域定位" : "禁用")}");
        Debug.Log($"  主按钮 → {(primaryForOrientation ? "装配面朝向" : "禁用")}");
        Debug.Log($"  副按钮 → {(secondaryForReset ? "重置位置" : "禁用")}");
        Debug.Log($"  菜单键 → {(menuForDebugToggle ? "调试切换" : "禁用")}");
        Debug.Log("===============================");
    }

    /// <summary>
    /// 处理输入
    /// </summary>
    private void HandleInput()
    {
#if UNITY_XR_INTERACTION_TOOLKIT
        // 优先使用右手控制器
        if (rightController != null)
        {
            HandleControllerInput(rightController, "右手");
        }
        // 如果右手不可用，使用左手
        else if (leftController != null)
        {
            HandleControllerInput(leftController, "左手");
        }
        else
        {
            HandleKeyboardFallback();
        }
#else
        HandleKeyboardFallback();
#endif
    }

#if UNITY_XR_INTERACTION_TOOLKIT
    /// <summary>
    /// 处理控制器输入
    /// </summary>
    private void HandleControllerInput(ActionBasedController controller, string handName)
    {
        // 获取按钮状态
        bool triggerPressed = IsActionPressed(controller.selectAction);
        bool primaryPressed = IsActionPressed(controller.activateAction);
        
        // 注意：PICO控制器的具体按钮映射可能需要调整
        // 这里使用基本的select和activate action
        bool secondaryPressed = false; // 可能需要额外配置
        bool menuPressed = false;      // 可能需要额外配置

        // 检测按钮按下事件（边缘检测）
        // 注意：扳机键现在只用于UI交互，不再控制装配区域
        CheckAndHandleButtonPress(primaryPressed, lastPrimaryPressed, () => OnPrimaryPressed(handName));
        CheckAndHandleButtonPress(secondaryPressed, lastSecondaryPressed, () => OnSecondaryPressed(handName));
        CheckAndHandleButtonPress(menuPressed, lastMenuPressed, () => OnMenuPressed(handName));

        // 更新状态
        lastTriggerPressed = triggerPressed;
        lastPrimaryPressed = primaryPressed;
        lastSecondaryPressed = secondaryPressed;
        lastMenuPressed = menuPressed;

        // 调试信息
        if (showDebugLogs && (triggerPressed || primaryPressed || secondaryPressed || menuPressed))
        {
            Debug.Log($"[PICO输入] {handName} - 扳机:{triggerPressed} 主按钮:{primaryPressed} 副按钮:{secondaryPressed} 菜单:{menuPressed}");
        }
    }

    /// <summary>
    /// 检查Action是否被按下
    /// </summary>
    private bool IsActionPressed(InputActionProperty actionProperty)
    {
        if (actionProperty.action != null && actionProperty.action.enabled)
        {
            return actionProperty.action.ReadValue<float>() > 0.5f;
        }
        return false;
    }
#endif

    /// <summary>
    /// 检查并处理按钮按下事件
    /// </summary>
    private void CheckAndHandleButtonPress(bool currentState, bool lastState, System.Action onPressed)
    {
        if (currentState && !lastState)
        {
            onPressed?.Invoke();
        }
    }

    /// <summary>
    /// A按钮(主按钮)按下 - 装配区域定位
    /// </summary>
    private void OnPrimaryPressed(string handName)
    {
        if (!primaryForPositioning || debugger == null) return;

        if (showDebugLogs)
        {
            Debug.Log($"[PICO输入] {handName}A按钮(主按钮) → 执行装配区域定位");
        }

        debugger.TestCameraBasedPositioning();
    }

    /// <summary>
    /// B按钮(副按钮)按下 - 装配面朝向
    /// </summary>
    private void OnSecondaryPressed(string handName)
    {
        if (!secondaryForOrientation || debugger == null) return;

        if (showDebugLogs)
        {
            Debug.Log($"[PICO输入] {handName}B按钮(副按钮) → 执行装配面朝向");
        }

        debugger.TestOrientation();
    }

    // 重复的OnSecondaryPressed方法已删除 - 使用上面的B按钮朝向功能

    /// <summary>
    /// 菜单按钮按下 - 调试切换
    /// </summary>
    private void OnMenuPressed(string handName)
    {
        if (!menuForDebugToggle || debugger == null) return;

        if (showDebugLogs)
        {
            Debug.Log($"[PICO输入] {handName}菜单键 → 切换调试模式");
        }
        
        debugger.ToggleDebugMode();
    }

    /// <summary>
    /// 键盘备用输入
    /// </summary>
    private void HandleKeyboardFallback()
    {
        // 扳机键功能已移除，现在使用A/B按钮
        if (Input.GetKeyDown(KeyCode.A)) OnPrimaryPressed("键盘A"); // A键 → 装配区域定位
        if (Input.GetKeyDown(KeyCode.B)) OnSecondaryPressed("键盘B"); // B键 → 装配面朝向
        if (Input.GetKeyDown(KeyCode.F1)) OnMenuPressed("键盘");
    }

    /// <summary>
    /// 测试所有功能
    /// </summary>
    [ContextMenu("测试所有功能")]
    public void TestAllFunctions()
    {
        if (debugger == null)
        {
            Debug.LogError("VRAssemblyDebugger未找到！");
            return;
        }

        StartCoroutine(TestSequence());
    }

    /// <summary>
    /// 测试序列
    /// </summary>
    private System.Collections.IEnumerator TestSequence()
    {
        Debug.Log("开始测试所有VR功能...");

        // 测试A按钮功能（装配区域定位）
        OnPrimaryPressed("测试");
        yield return new WaitForSeconds(3f);

        // 测试B按钮功能（装配面朝向）
        OnSecondaryPressed("测试");
        yield return new WaitForSeconds(3f);

        Debug.Log("所有功能测试完成！");
    }

    /// <summary>
    /// 显示使用说明
    /// </summary>
    [ContextMenu("显示使用说明")]
    public void ShowUsageInstructions()
    {
        string instructions = @"
🎮 PICO VR手柄操作说明：

右手控制器：
🔫 扳机键 → 装配区域移动到摄像机固定位置
🅰️ 主按钮 → 装配面朝向摄像机
🅱️ 副按钮 → 重置装配区域位置
📋 菜单键 → 切换调试界面

备用键盘输入：
T键 → 装配区域定位
P键 → 装配面朝向
G键 → 重置位置
F1键 → 切换调试

💡 使用提示：
1. 确保场景中有VRAssemblyDebugger组件
2. 确保控制器正确配置为Action-based
3. 在Unity编辑器中可使用键盘测试
4. 观察Console了解功能执行状态
";
        Debug.Log(instructions);
    }
}
