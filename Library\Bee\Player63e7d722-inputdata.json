{"BeeBuildProgramCommon.Data.ConfigurationData": {"Il2CppDir": "C:\\Program Files\\Unity\\Hub\\Editor\\2021.3.44f1c1\\Editor\\Data\\il2cpp", "UnityLinkerPath": "C:\\Program Files\\Unity\\Hub\\Editor\\2021.3.44f1c1\\Editor\\Data\\il2cpp/build/deploy/UnityLinker.exe", "Il2CppPath": "C:\\Program Files\\Unity\\Hub\\Editor\\2021.3.44f1c1\\Editor\\Data\\il2cpp/build/deploy/il2cpp.exe", "NetCoreRunPath": "C:/Program Files/Unity/Hub/Editor/2021.3.44f1c1/Editor/Data\\Tools\\netcorerun\\netcorerun.exe", "EditorContentsPath": "C:/Program Files/Unity/Hub/Editor/2021.3.44f1c1/Editor/Data", "Packages": [{"Name": "com.unity.cinemachine", "ResolvedPath": "Library/PackageCache/com.unity.cinemachine@2.10.3", "Immutable": true}, {"Name": "com.unity.collab-proxy", "ResolvedPath": "Library/PackageCache/com.unity.collab-proxy@2.5.1", "Immutable": true}, {"Name": "com.unity.feature.development", "ResolvedPath": "Library/PackageCache/com.unity.feature.development@1.0.1", "Immutable": true}, {"Name": "com.unity.ide.rider", "ResolvedPath": "Library/PackageCache/com.unity.ide.rider@3.0.31", "Immutable": true}, {"Name": "com.unity.ide.visualstudio", "ResolvedPath": "Library/PackageCache/com.unity.ide.visualstudio@2.0.22", "Immutable": true}, {"Name": "com.unity.ide.vscode", "ResolvedPath": "Library/PackageCache/com.unity.ide.vscode@1.2.5", "Immutable": true}, {"Name": "com.unity.test-framework", "ResolvedPath": "Library/PackageCache/com.unity.test-framework@1.1.33", "Immutable": true}, {"Name": "com.unity.textmeshpro", "ResolvedPath": "Library/PackageCache/com.unity.textmeshpro@3.0.6", "Immutable": true}, {"Name": "com.unity.timeline", "ResolvedPath": "Library/PackageCache/com.unity.timeline@1.6.5", "Immutable": true}, {"Name": "com.unity.visualscripting", "ResolvedPath": "Library/PackageCache/com.unity.visualscripting@1.9.4", "Immutable": true}, {"Name": "com.unity.xr.interaction.toolkit", "ResolvedPath": "Library/PackageCache/com.unity.xr.interaction.toolkit@2.6.4", "Immutable": true}, {"Name": "com.unity.xr.management", "ResolvedPath": "Library/PackageCache/com.unity.xr.management@4.4.0", "Immutable": true}, {"Name": "com.unity.xr.picoxr", "ResolvedPath": "D:/PICO Unity Integration SDK-3.2.0-20250529", "Immutable": false}, {"Name": "com.unity.modules.ai", "ResolvedPath": "Library/PackageCache/com.unity.modules.ai@1.0.0", "Immutable": true}, {"Name": "com.unity.modules.androidjni", "ResolvedPath": "Library/PackageCache/com.unity.modules.androidjni@1.0.0", "Immutable": true}, {"Name": "com.unity.modules.animation", "ResolvedPath": "Library/PackageCache/com.unity.modules.animation@1.0.0", "Immutable": true}, {"Name": "com.unity.modules.assetbundle", "ResolvedPath": "Library/PackageCache/com.unity.modules.assetbundle@1.0.0", "Immutable": true}, {"Name": "com.unity.modules.audio", "ResolvedPath": "Library/PackageCache/com.unity.modules.audio@1.0.0", "Immutable": true}, {"Name": "com.unity.modules.cloth", "ResolvedPath": "Library/PackageCache/com.unity.modules.cloth@1.0.0", "Immutable": true}, {"Name": "com.unity.modules.director", "ResolvedPath": "Library/PackageCache/com.unity.modules.director@1.0.0", "Immutable": true}, {"Name": "com.unity.modules.imageconversion", "ResolvedPath": "Library/PackageCache/com.unity.modules.imageconversion@1.0.0", "Immutable": true}, {"Name": "com.unity.modules.imgui", "ResolvedPath": "Library/PackageCache/com.unity.modules.imgui@1.0.0", "Immutable": true}, {"Name": "com.unity.modules.jsonserialize", "ResolvedPath": "Library/PackageCache/com.unity.modules.jsonserialize@1.0.0", "Immutable": true}, {"Name": "com.unity.modules.particlesystem", "ResolvedPath": "Library/PackageCache/com.unity.modules.particlesystem@1.0.0", "Immutable": true}, {"Name": "com.unity.modules.physics", "ResolvedPath": "Library/PackageCache/com.unity.modules.physics@1.0.0", "Immutable": true}, {"Name": "com.unity.modules.physics2d", "ResolvedPath": "Library/PackageCache/com.unity.modules.physics2d@1.0.0", "Immutable": true}, {"Name": "com.unity.modules.screencapture", "ResolvedPath": "Library/PackageCache/com.unity.modules.screencapture@1.0.0", "Immutable": true}, {"Name": "com.unity.modules.terrain", "ResolvedPath": "Library/PackageCache/com.unity.modules.terrain@1.0.0", "Immutable": true}, {"Name": "com.unity.modules.terrainphysics", "ResolvedPath": "Library/PackageCache/com.unity.modules.terrainphysics@1.0.0", "Immutable": true}, {"Name": "com.unity.modules.tilemap", "ResolvedPath": "Library/PackageCache/com.unity.modules.tilemap@1.0.0", "Immutable": true}, {"Name": "com.unity.modules.ui", "ResolvedPath": "Library/PackageCache/com.unity.modules.ui@1.0.0", "Immutable": true}, {"Name": "com.unity.modules.uielements", "ResolvedPath": "Library/PackageCache/com.unity.modules.uielements@1.0.0", "Immutable": true}, {"Name": "com.unity.modules.umbra", "ResolvedPath": "Library/PackageCache/com.unity.modules.umbra@1.0.0", "Immutable": true}, {"Name": "com.unity.modules.unityanalytics", "ResolvedPath": "Library/PackageCache/com.unity.modules.unityanalytics@1.0.0", "Immutable": true}, {"Name": "com.unity.modules.unitywebrequest", "ResolvedPath": "Library/PackageCache/com.unity.modules.unitywebrequest@1.0.0", "Immutable": true}, {"Name": "com.unity.modules.unitywebrequestassetbundle", "ResolvedPath": "Library/PackageCache/com.unity.modules.unitywebrequestassetbundle@1.0.0", "Immutable": true}, {"Name": "com.unity.modules.unitywebrequestaudio", "ResolvedPath": "Library/PackageCache/com.unity.modules.unitywebrequestaudio@1.0.0", "Immutable": true}, {"Name": "com.unity.modules.unitywebrequesttexture", "ResolvedPath": "Library/PackageCache/com.unity.modules.unitywebrequesttexture@1.0.0", "Immutable": true}, {"Name": "com.unity.modules.unitywebrequestwww", "ResolvedPath": "Library/PackageCache/com.unity.modules.unitywebrequestwww@1.0.0", "Immutable": true}, {"Name": "com.unity.modules.vehicles", "ResolvedPath": "Library/PackageCache/com.unity.modules.vehicles@1.0.0", "Immutable": true}, {"Name": "com.unity.modules.video", "ResolvedPath": "Library/PackageCache/com.unity.modules.video@1.0.0", "Immutable": true}, {"Name": "com.unity.modules.vr", "ResolvedPath": "Library/PackageCache/com.unity.modules.vr@1.0.0", "Immutable": true}, {"Name": "com.unity.modules.wind", "ResolvedPath": "Library/PackageCache/com.unity.modules.wind@1.0.0", "Immutable": true}, {"Name": "com.unity.modules.xr", "ResolvedPath": "Library/PackageCache/com.unity.modules.xr@1.0.0", "Immutable": true}, {"Name": "com.unity.modules.subsystems", "ResolvedPath": "Library/PackageCache/com.unity.modules.subsystems@1.0.0", "Immutable": true}, {"Name": "com.unity.modules.uielementsnative", "ResolvedPath": "Library/PackageCache/com.unity.modules.uielementsnative@1.0.0", "Immutable": true}, {"Name": "com.unity.xr.core-utils", "ResolvedPath": "Library/PackageCache/com.unity.xr.core-utils@2.3.0", "Immutable": true}, {"Name": "com.unity.xr.legacyinputhelpers", "ResolvedPath": "Library/PackageCache/com.unity.xr.legacyinputhelpers@2.1.10", "Immutable": true}, {"Name": "com.unity.ugui", "ResolvedPath": "Library/PackageCache/com.unity.ugui@1.0.0", "Immutable": true}, {"Name": "com.unity.inputsystem", "ResolvedPath": "Library/PackageCache/com.unity.inputsystem@1.7.0", "Immutable": true}, {"Name": "com.unity.mathematics", "ResolvedPath": "Library/PackageCache/com.unity.mathematics@1.2.6", "Immutable": true}, {"Name": "com.unity.ext.nunit", "ResolvedPath": "Library/PackageCache/com.unity.ext.nunit@1.0.6", "Immutable": true}, {"Name": "com.unity.editorcoroutines", "ResolvedPath": "Library/PackageCache/com.unity.editorcoroutines@1.0.0", "Immutable": true}, {"Name": "com.unity.performance.profile-analyzer", "ResolvedPath": "Library/PackageCache/com.unity.performance.profile-analyzer@1.2.2", "Immutable": true}, {"Name": "com.unity.testtools.codecoverage", "ResolvedPath": "Library/PackageCache/com.unity.testtools.codecoverage@1.2.6", "Immutable": true}, {"Name": "com.unity.settings-manager", "ResolvedPath": "Library/PackageCache/com.unity.settings-manager@1.0.3", "Immutable": true}], "UnityVersion": "2021.3.44f1c1", "AdvancedLicense": false, "EmitDataForBeeWhy": false}, "PlayerBuildProgramLibrary.Data.PlayerBuildConfig": {"DestinationPath": "D:\\nwu\\Assembly\\UnityProjects\\VRAssembly\\Library\\Bee\\Android\\Prj\\IL2CPP\\Gradle", "StagingArea": "Temp/StagingArea", "DataFolder": "Library/PlayerDataCache/Android6/Data", "CompanyName": "DefaultCompany", "ProductName": "VRAssembly", "PlayerPackage": "C:/Program Files/Unity/Hub/Editor/2021.3.44f1c1/Editor/Data/PlaybackEngines/AndroidPlayer", "ApplicationIdentifier": "com.DefaultCompany.VRAssembly", "Architecture": "", "UseIl2Cpp": true, "InstallIntoBuildsFolder": false, "GenerateIdeProject": false, "Development": false, "Services": {"EnableUnityConnect": true, "EnablePerformanceReporting": true, "EnableAnalytics": true, "EnableCrashReporting": false}, "StreamingAssetsFiles": []}, "PlayerBuildProgramLibrary.Data.PluginsData": {"Plugins": [{"AssetPath": "Packages/com.unity.xr.picoxr/Runtime/Windows/x86_64/applogrs.dll", "DestinationPath": "", "Architecture": ""}, {"AssetPath": "Packages/com.unity.xr.management/xrmanifest.androidlib", "DestinationPath": "unityLibrary\\xrmanifest.androidlib", "Architecture": ""}, {"AssetPath": "Packages/com.unity.xr.picoxr/Runtime/Android/loader-1.0.5.ForUnitySDK.aar", "DestinationPath": "unityLibrary\\libs\\loader-1.0.5.ForUnitySDK.aar", "Architecture": ""}, {"AssetPath": "Packages/com.unity.xr.picoxr/Enterprise/android/tob_api-release.aar", "DestinationPath": "unityLibrary\\libs\\tob_api-release.aar", "Architecture": ""}, {"AssetPath": "Packages/com.unity.xr.picoxr/Enterprise/android/gson-2.10.1.jar", "DestinationPath": "unityLibrary\\libs\\gson-2.10.1.jar", "Architecture": ""}, {"AssetPath": "Packages/com.unity.xr.picoxr/Enterprise/android/capturelib-0.0.7.aar", "DestinationPath": "unityLibrary\\libs\\capturelib-0.0.7.aar", "Architecture": ""}, {"AssetPath": "Packages/com.unity.xr.picoxr/Enterprise/android/CameraRenderingPlugin.aar", "DestinationPath": "unityLibrary\\libs\\CameraRenderingPlugin.aar", "Architecture": ""}, {"AssetPath": "Packages/com.unity.xr.picoxr/Platform/Plugins/Android64/libpxrplatformloader.so", "DestinationPath": "unityLibrary\\src\\main\\jniLibs\\arm64-v8a\\libpxrplatformloader.so", "Architecture": "ARM64"}, {"AssetPath": "Packages/com.unity.xr.picoxr/Runtime/Android/PxrPlatform.aar", "DestinationPath": "unityLibrary\\libs\\PxrPlatform.aar", "Architecture": ""}, {"AssetPath": "Packages/com.unity.xr.picoxr/Enterprise/android/tobservicelib-release.aar", "DestinationPath": "unityLibrary\\libs\\tobservicelib-release.aar", "Architecture": ""}, {"AssetPath": "Packages/com.unity.xr.picoxr/SpatialAudio/Plugins/Android/libs/arm64-v8a/libPicoSpatializer.so", "DestinationPath": "unityLibrary\\src\\main\\jniLibs\\arm64-v8a\\libPicoSpatializer.so", "Architecture": "ARM64"}, {"AssetPath": "Packages/com.unity.xr.picoxr/SpatialAudio/Plugins/Android/libs/arm64-v8a/libPicoAmbisonicDecoder.so", "DestinationPath": "unityLibrary\\src\\main\\jniLibs\\arm64-v8a\\libPicoAmbisonicDecoder.so", "Architecture": "ARM64"}, {"AssetPath": "Packages/com.unity.xr.picoxr/SpatialAudio/Plugins/Android/libs/arm64-v8a/libPicoAudioRouter.so", "DestinationPath": "unityLibrary\\src\\main\\jniLibs\\arm64-v8a\\libPicoAudioRouter.so", "Architecture": "ARM64"}, {"AssetPath": "Packages/com.unity.xr.picoxr/Enterprise/android/BAuthLib-1.0.0.aar", "DestinationPath": "unityLibrary\\libs\\BAuthLib-1.0.0.aar", "Architecture": ""}]}, "PlayerBuildProgramLibrary.Data.LinkerConfig": {"LinkXmlFiles": ["D:/nwu/Assembly/UnityProjects/VRAssembly/Temp/StagingArea/Data/Managed\\TypesInScenes.xml", "D:/nwu/Assembly/UnityProjects/VRAssembly/Temp/StagingArea/Data/Managed\\SerializedTypes.xml", "D:/nwu/Assembly/UnityProjects/VRAssembly/Assets\\..\\Temp\\InputSystemLink.xml"], "AssembliesToProcess": ["Temp/StagingArea/Data/Managed/Assembly-CSharp.dll", "Temp/StagingArea/Data/Managed/Unity.TextMeshPro.dll", "Temp/StagingArea/Data/Managed/Unity.XR.Interaction.Toolkit.dll", "Temp/StagingArea/Data/Managed/Unity.InputSystem.dll", "Temp/StagingArea/Data/Managed/UnityEngine.UI.dll", "Temp/StagingArea/Data/Managed/Unity.XR.PICO.dll", "Temp/StagingArea/Data/Managed/Unity.XR.Interaction.Toolkit.Samples.DeviceSimulator.dll", "Temp/StagingArea/Data/Managed/Unity.XR.Interaction.Toolkit.Samples.StarterAssets.dll", "Temp/StagingArea/Data/Managed/Unity.XR.CoreUtils.dll", "Temp/StagingArea/Data/Managed/Unity.XR.Management.dll", "Temp/StagingArea/Data/Managed/PICO.Platform.dll"], "SearchDirectories": ["D:/nwu/Assembly/UnityProjects/VRAssembly/Temp/StagingArea/Data/Managed"], "EditorToLinkerData": "D:/nwu/Assembly/UnityProjects/VRAssembly/Temp/StagingArea/Data/Managed/EditorToUnityLinkerData.json", "Runtime": "il2cpp", "Profile": "unityaot-linux", "Ruleset": "Minimal", "ModulesAssetPath": "C:/Program Files/Unity/Hub/Editor/2021.3.44f1c1/Editor/Data/PlaybackEngines/AndroidPlayer/modules.asset", "AdditionalArgs": [], "AllowDebugging": false, "PerformEngineStripping": true}, "PlayerBuildProgramLibrary.Data.Il2CppConfig": {"EnableDeepProfilingSupport": false, "EnableFullGenericSharing": false, "Profile": "unityaot-linux", "Defines": "ALL_INTERIOR_POINTERS=1;GC_GCJ_SUPPORT=1;JAVA_FINALIZATION=1;NO_EXECUTE_PERMISSION=1;GC_NO_THREADS_DISCOVERY=1;IGNORE_DYNAMIC_LOADING=1;GC_DONT_REGISTER_MAIN_STATIC_DATA=1;GC_VERSION_MAJOR=7;GC_VERSION_MINOR=7;GC_VERSION_MICRO=0;GC_THREADS=1;USE_MMAP=1;USE_MUNMAP=1;NET_4_0=1;UNITY_AOT=1;NET_STANDARD_2_0=1;NET_UNITY_4_8=1;NET_STANDARD=1;IL2CPP_ENABLE_WRITE_BARRIERS=1;IL2CPP_INCREMENTAL_TIME_SLICE=3", "ConfigurationName": "Release", "GcWBarrierValidation": false, "GcIncremental": true, "AdditionalCppFiles": [], "AdditionalArgs": [], "CreateSymbolFiles": true, "AllowDebugging": false}, "AndroidPlayerBuildProgram.Data.AndroidPlayerBuildConfiguration": {"GradleProjectCreateInfo": {"HostPlatform": "Windows", "ApplicationType": "APK", "BuildType": "Release", "AndroidSDKPath": "C:\\Program Files\\Unity\\Hub\\Editor\\2021.3.44f1c1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\SDK", "AndroidNDKPath": "C:/Program Files/Unity/Hub/Editor/2021.3.44f1c1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK", "PreferredHeapSizeForJVM": 4096, "GradleVersion": "7.5.1", "UnityLibraryTemplatePath": "C:/Program Files/Unity/Hub/Editor/2021.3.44f1c1/Editor/Data/PlaybackEngines/AndroidPlayer/Tools\\GradleTemplates\\mainTemplate.gradle", "UnityLibraryTemplatePathUsed": false, "LauncherTemplatePath": "C:/Program Files/Unity/Hub/Editor/2021.3.44f1c1/Editor/Data/PlaybackEngines/AndroidPlayer/Tools\\GradleTemplates\\launcherTemplate.gradle", "LauncherTemplatePathUsed": false, "BaseProjectTemplatePath": "C:/Program Files/Unity/Hub/Editor/2021.3.44f1c1/Editor/Data/PlaybackEngines/AndroidPlayer/Tools\\GradleTemplates\\baseProjectTemplate.gradle", "BaseProjectTemplatePathUsed": false, "GradlePropertiesTemplatePath": "C:/Program Files/Unity/Hub/Editor/2021.3.44f1c1/Editor/Data/PlaybackEngines/AndroidPlayer/Tools\\GradleTemplates\\gradleTemplate.properties", "GradlePropertiesTemplatePathUsed": false, "GradleLibraryTemplatePath": "C:/Program Files/Unity/Hub/Editor/2021.3.44f1c1/Editor/Data/PlaybackEngines/AndroidPlayer/Tools\\GradleTemplates\\libTemplate.gradle", "GradleLibraryTemplatePathUsed": false, "UnityProguardTemplatePath": "C:/Program Files/Unity/Hub/Editor/2021.3.44f1c1/Editor/Data/PlaybackEngines/AndroidPlayer/Tools\\UnityProGuardTemplate.txt", "ProguardUserPath": "", "SettingsTemplatePath": "C:/Program Files/Unity/Hub/Editor/2021.3.44f1c1/Editor/Data/PlaybackEngines/AndroidPlayer/Tools\\GradleTemplates\\settingsTemplate.gradle", "SettingsTemplatePathUsed": false, "BuildTools": "34.0.0", "TargetSDKVersion": 29, "MinSDKVersion": 29, "PackageName": "com.DefaultCompany.VRAssembly", "Architectures": "ARM64", "BuildApkPerCpuArchitecture": false, "VersionCode": 1, "VersionName": "0.1", "Minify": 1, "NoCompressOptions": {"RelativeFilePaths": [], "FileExtensions": []}, "UseCustomKeystore": false, "KeystorePath": "", "KeystoreName": "", "KeystorePassword": "", "KeystoreAliasName": "", "KeystoreAliasPassword": "", "ScriptingImplementation": "IL2CPP", "AndroidLibraries": ["Packages/com.unity.xr.management/xrmanifest.androidlib"], "AARFiles": ["Packages/com.unity.xr.picoxr/Runtime/Android/loader-1.0.5.ForUnitySDK.aar", "Packages/com.unity.xr.picoxr/Enterprise/android/tob_api-release.aar", "Packages/com.unity.xr.picoxr/Enterprise/android/capturelib-0.0.7.aar", "Packages/com.unity.xr.picoxr/Enterprise/android/CameraRenderingPlugin.aar", "Packages/com.unity.xr.picoxr/Runtime/Android/PxrPlatform.aar", "Packages/com.unity.xr.picoxr/Enterprise/android/tobservicelib-release.aar", "Packages/com.unity.xr.picoxr/Enterprise/android/BAuthLib-1.0.0.aar"], "BuiltinJavaSourcePaths": ["com/unity3d/player/UnityPlayerActivity.java"], "JavaSourcePaths": [], "KotlinSourcePaths": [], "PlayerPackage": "C:/Program Files/Unity/Hub/Editor/2021.3.44f1c1/Editor/Data/PlaybackEngines/AndroidPlayer", "PlayerPackageTools": "C:/Program Files/Unity/Hub/Editor/2021.3.44f1c1/Editor/Data/PlaybackEngines/AndroidPlayer/Tools", "SymlinkSources": false, "InstallIntoBuildsFolder": false, "UnityPath": "", "UnityProjectPath": "D:/nwu/Assembly/UnityProjects/VRAssembly", "Dependencies": [], "AAPT2Path": "C:\\Program Files\\Unity\\Hub\\Editor\\2021.3.44f1c1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\SDK\\build-tools\\34.0.0\\aapt2.exe"}, "Architectures": "ARM64", "BuildType": "Release", "BuildSystem": "<PERSON><PERSON><PERSON>", "ScriptingImplementation": "IL2CPP", "CreateSymbols": "Disabled", "ApplicationSplitMode": "Disabled", "GradleResourcesInformation": {"TargetSDKVersion": 29, "RoundIconsAvailable": false, "RoundIconsSupported": true, "AdaptiveIconsSupported": true, "AdaptiveIconsAvailable": false}, "PreloadedJavaClasses": [], "SevenZipPath": "C:/Program Files/Unity/Hub/Editor/2021.3.44f1c1/Editor/Data\\Tools\\7z.exe", "PatchPackage": false, "PostGenerateGradleCallbackUsed": true, "ApplicationName": "VRAssembly", "BuildFingerPrintContents": "2021.3.44f1c1;IL2CPP;Release;StripEngineCode:1;OptimizedFramePacing:1;"}, "AndroidPlayerBuildProgram.Data.AndroidManifestConfiguration": {"TargetSDKVersion": 29, "LauncherManifestTemplatePath": "C:/Program Files/Unity/Hub/Editor/2021.3.44f1c1/Editor/Data/PlaybackEngines/AndroidPlayer\\Apk\\LauncherManifest.xml", "LauncherManifestTemplateUsed": false, "LibraryManifestTemplatePath": "C:/Program Files/Unity/Hub/Editor/2021.3.44f1c1/Editor/Data/PlaybackEngines/AndroidPlayer\\Apk\\UnityManifest.xml", "LibraryManifestCustomTemplateUsed": false, "LauncherManifestPath": "launcher\\src\\main\\AndroidManifest.xml", "LibraryManifestPath": "unityLibrary\\src\\main\\AndroidManifest.xml", "TVCompatibility": false, "BannerEnabled": true, "IsGame": true, "PreferredInstallLocation": "PreferExternal", "TextureSupport": "ASTC", "GamepadSupportLevel": "SupportsDPad", "TargetDevices": "AllDevices", "SupportedAspectRatioMode": 1, "MaxAspectRatio": 2.1, "ForceInternetPermission": false, "UseLowAccuracyLocation": false, "ForceSDCardPermission": false, "PreserveFramebufferAlpha": false, "DefaultInterfaceOrientation": "AutoRotation", "AllowedAutorotateToPortrait": true, "AllowedAutorotateToPortraitUpsideDown": true, "AllowedAutorotateToLandscapeLeft": true, "AllowedAutorotateToLandscapeRight": true, "ARCoreEnabled": false, "SplashScreenScale": "Center", "VREnabled": false, "RenderOutsideSafeArea": true, "GraphicsDevices": ["Vulkan", "OpenGLES3"], "OpenGLRequireES31": false, "OpenGLRequireES31AEP": false, "OpenGLRequireES32": false, "StartInFullscreen": true, "ChromeOSInputEmulation": true, "DefaultWindowWidth": 1920, "DefaultWindowHeight": 1080, "MinimumWindowWidth": 400, "MinimumWindowHeight": 300, "ResizableWindow": false, "FullScreenMode": "FullScreenWindow", "AutoRotationBehavior": "User"}, "AndroidPlayerBuildProgram.Data.AndroidSharedLibraryConfiguration": {"ClangPath": "C:/Program Files/Unity/Hub/Editor/2021.3.44f1c1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/windows-x86_64/bin/clang++.exe", "ABITools": [{"Architecture": "ARM64", "ObjCopyPath": "C:/Program Files/Unity/Hub/Editor/2021.3.44f1c1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/windows-x86_64/bin/aarch64-linux-android-objcopy.exe", "StripPath": "C:/Program Files/Unity/Hub/Editor/2021.3.44f1c1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/windows-x86_64/bin/aarch64-linux-android-strip.exe"}]}}