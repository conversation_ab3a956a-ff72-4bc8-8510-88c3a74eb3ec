using UnityEngine;
using UnityEngine.UI;
using UnityEngine.EventSystems;
using TMPro;

#if UNITY_EDITOR
using UnityEditor;
#endif

/// <summary>
/// VR设置UI设置助手
/// 
/// 按照开始场景的UI风格自动创建设置UI
/// 包括齿轮设置按钮和弹出式设置面板
/// </summary>
public class VRSettingsUISetupHelper : MonoBehaviour
{
    [Header("UI风格配置（参考开始场景）")]
    [SerializeField] private Color backgroundColor = new Color(0.1f, 0.1f, 0.2f, 0.9f);
    [SerializeField] private Color primaryColor = new Color(0.2f, 0.6f, 1f, 1f);
    [SerializeField] private Color textColor = Color.white;
    [SerializeField] private Color buttonNormalColor = new Color(0.2f, 0.4f, 0.8f, 1f);
    [SerializeField] private Color buttonHighlightColor = new Color(0.3f, 0.5f, 0.9f, 1f);
    [SerializeField] private Color buttonPressedColor = new Color(0.1f, 0.3f, 0.7f, 1f);
    
    [Header("VR配置（参考开始场景）")]
    [SerializeField] private float uiDistance = 2.5f;
    [SerializeField] private float uiScale = 0.01f;
    [SerializeField] private Vector3 settingsButtonOffset = new Vector3(-0.8f, 0.4f, 0f);
    [SerializeField] private Vector3 settingsPanelOffset = new Vector3(0f, 0f, 0f);
    
    [Header("字体和大小设置")]
    [SerializeField] private int titleFontSize = 36;
    [SerializeField] private int buttonFontSize = 24;
    [SerializeField] private int normalTextFontSize = 20;
    
    [Header("组件引用")]
    [SerializeField] private Camera vrCamera;
    [SerializeField] private Sprite gearIconSprite; // 齿轮图标
    
    /// <summary>
    /// 创建完整的VR设置UI系统
    /// </summary>
    [ContextMenu("创建VR设置UI")]
    public void CreateVRSettingsUI()
    {
        Debug.Log("[VRSettingsUISetupHelper] 开始创建VR设置UI");
        
        // 1. 查找VR摄像机
        FindVRCamera();
        
        // 2. 创建Canvas
        Canvas settingsCanvas = CreateSettingsCanvas();
        
        // 3. 创建设置按钮
        Button settingsButton = CreateSettingsButton(settingsCanvas);
        
        // 4. 创建设置面板
        GameObject settingsPanel = CreateSettingsPanel(settingsCanvas);
        
        // 5. 添加VRSettingsUI管理脚本
        VRSettingsUI settingsUI = settingsCanvas.gameObject.AddComponent<VRSettingsUI>();
        ConfigureVRSettingsUI(settingsUI, settingsCanvas, settingsButton, settingsPanel);
        
        // 6. 设置VR优化
        OptimizeForVR(settingsCanvas);
        
        Debug.Log("[VRSettingsUISetupHelper] VR设置UI创建完成");
    }
    
    /// <summary>
    /// 查找VR摄像机
    /// </summary>
    private void FindVRCamera()
    {
        if (vrCamera == null)
        {
            vrCamera = Camera.main;
            if (vrCamera == null)
            {
                vrCamera = FindObjectOfType<Camera>();
            }
        }
        
        if (vrCamera == null)
        {
            Debug.LogError("[VRSettingsUISetupHelper] 未找到VR摄像机！");
        }
        else
        {
            Debug.Log($"[VRSettingsUISetupHelper] 找到VR摄像机: {vrCamera.name}");
        }
    }
    
    /// <summary>
    /// 创建设置Canvas
    /// </summary>
    private Canvas CreateSettingsCanvas()
    {
        GameObject canvasGO = new GameObject("VRSettingsCanvas");
        Canvas canvas = canvasGO.AddComponent<Canvas>();
        CanvasScaler canvasScaler = canvasGO.AddComponent<CanvasScaler>();
        GraphicRaycaster graphicRaycaster = canvasGO.AddComponent<GraphicRaycaster>();
        
        // 配置Canvas（参考开始场景风格）
        canvas.renderMode = RenderMode.WorldSpace;
        canvas.worldCamera = vrCamera;
        canvas.sortingOrder = 10; // 确保在其他UI之上
        
        // 配置CanvasScaler
        canvasScaler.uiScaleMode = CanvasScaler.ScaleMode.ScaleWithScreenSize;
        canvasScaler.referenceResolution = new Vector2(1920, 1080);
        canvasScaler.screenMatchMode = CanvasScaler.ScreenMatchMode.MatchWidthOrHeight;
        canvasScaler.matchWidthOrHeight = 0.5f;
        
        // 设置Canvas位置和缩放（参考开始场景）
        RectTransform canvasRect = canvas.GetComponent<RectTransform>();
        canvasRect.sizeDelta = new Vector2(1920, 1080);
        
        return canvas;
    }
    
    /// <summary>
    /// 创建设置按钮
    /// </summary>
    private Button CreateSettingsButton(Canvas parentCanvas)
    {
        // 创建按钮GameObject
        GameObject buttonGO = new GameObject("SettingsButton");
        buttonGO.transform.SetParent(parentCanvas.transform, false);
        
        // 添加组件
        Image buttonImage = buttonGO.AddComponent<Image>();
        Button button = buttonGO.AddComponent<Button>();
        
        // 设置RectTransform
        RectTransform buttonRect = buttonGO.GetComponent<RectTransform>();
        buttonRect.anchorMin = new Vector2(0f, 1f); // 左上角
        buttonRect.anchorMax = new Vector2(0f, 1f);
        buttonRect.anchoredPosition = new Vector2(100, -100);
        buttonRect.sizeDelta = new Vector2(80, 80);
        
        // 设置按钮样式（参考开始场景）
        buttonImage.color = buttonNormalColor;
        buttonImage.sprite = Resources.GetBuiltinResource<Sprite>("UI/Skin/UISprite.psd");
        buttonImage.type = Image.Type.Sliced;
        
        // 配置按钮颜色
        ColorBlock colors = button.colors;
        colors.normalColor = buttonNormalColor;
        colors.highlightedColor = buttonHighlightColor;
        colors.pressedColor = buttonPressedColor;
        colors.disabledColor = Color.gray;
        colors.colorMultiplier = 1f;
        colors.fadeDuration = 0.1f;
        button.colors = colors;
        
        // 创建齿轮图标
        CreateGearIcon(buttonGO);
        
        return button;
    }
    
    /// <summary>
    /// 创建齿轮图标
    /// </summary>
    private void CreateGearIcon(GameObject buttonParent)
    {
        GameObject iconGO = new GameObject("GearIcon");
        iconGO.transform.SetParent(buttonParent.transform, false);
        
        Image iconImage = iconGO.AddComponent<Image>();
        
        // 设置图标
        if (gearIconSprite != null)
        {
            iconImage.sprite = gearIconSprite;
        }
        else
        {
            // 使用默认图标或创建简单的齿轮形状
            iconImage.sprite = Resources.GetBuiltinResource<Sprite>("UI/Skin/Knob.psd");
        }
        
        iconImage.color = textColor;
        iconImage.raycastTarget = false; // 不阻挡按钮点击
        
        // 设置图标大小和位置
        RectTransform iconRect = iconGO.GetComponent<RectTransform>();
        iconRect.anchorMin = Vector2.zero;
        iconRect.anchorMax = Vector2.one;
        iconRect.offsetMin = new Vector2(10, 10);
        iconRect.offsetMax = new Vector2(-10, -10);
    }
    
    /// <summary>
    /// 创建设置面板
    /// </summary>
    private GameObject CreateSettingsPanel(Canvas parentCanvas)
    {
        // 创建主面板
        GameObject panelGO = new GameObject("SettingsPanel");
        panelGO.transform.SetParent(parentCanvas.transform, false);
        
        Image panelImage = panelGO.AddComponent<Image>();
        
        // 设置面板样式（参考开始场景）
        panelImage.color = backgroundColor;
        panelImage.sprite = Resources.GetBuiltinResource<Sprite>("UI/Skin/Background.psd");
        panelImage.type = Image.Type.Sliced;
        
        // 设置面板大小和位置
        RectTransform panelRect = panelGO.GetComponent<RectTransform>();
        panelRect.anchorMin = new Vector2(0.5f, 0.5f);
        panelRect.anchorMax = new Vector2(0.5f, 0.5f);
        panelRect.anchoredPosition = Vector2.zero;
        panelRect.sizeDelta = new Vector2(500, 400);
        
        // 创建面板内容
        CreatePanelContent(panelGO);
        
        // 初始状态为隐藏
        panelGO.SetActive(false);
        
        return panelGO;
    }
    
    /// <summary>
    /// 创建面板内容
    /// </summary>
    private void CreatePanelContent(GameObject panelParent)
    {
        // 创建标题
        CreatePanelTitle(panelParent);
        
        // 创建关闭按钮
        CreateCloseButton(panelParent);
        
        // 创建示例设置按钮
        CreateExampleSettingsButton(panelParent);
    }
    
    /// <summary>
    /// 创建面板标题
    /// </summary>
    private void CreatePanelTitle(GameObject panelParent)
    {
        GameObject titleGO = new GameObject("TitleText");
        titleGO.transform.SetParent(panelParent.transform, false);
        
        TextMeshProUGUI titleText = titleGO.AddComponent<TextMeshProUGUI>();
        
        // 设置文本内容和样式
        titleText.text = "设置";
        titleText.fontSize = titleFontSize;
        titleText.color = textColor;
        titleText.alignment = TextAlignmentOptions.Center;
        titleText.fontStyle = FontStyles.Bold;
        
        // 设置位置
        RectTransform titleRect = titleGO.GetComponent<RectTransform>();
        titleRect.anchorMin = new Vector2(0f, 1f);
        titleRect.anchorMax = new Vector2(1f, 1f);
        titleRect.anchoredPosition = new Vector2(0, -40);
        titleRect.sizeDelta = new Vector2(0, 50);
    }
    
    /// <summary>
    /// 创建关闭按钮
    /// </summary>
    private Button CreateCloseButton(GameObject panelParent)
    {
        GameObject closeButtonGO = new GameObject("CloseButton");
        closeButtonGO.transform.SetParent(panelParent.transform, false);
        
        Image buttonImage = closeButtonGO.AddComponent<Image>();
        Button closeButton = closeButtonGO.AddComponent<Button>();
        
        // 设置按钮样式
        buttonImage.color = buttonNormalColor;
        buttonImage.sprite = Resources.GetBuiltinResource<Sprite>("UI/Skin/UISprite.psd");
        buttonImage.type = Image.Type.Sliced;
        
        // 配置按钮颜色
        ColorBlock colors = closeButton.colors;
        colors.normalColor = buttonNormalColor;
        colors.highlightedColor = buttonHighlightColor;
        colors.pressedColor = buttonPressedColor;
        closeButton.colors = colors;
        
        // 设置位置（右上角）
        RectTransform buttonRect = closeButtonGO.GetComponent<RectTransform>();
        buttonRect.anchorMin = new Vector2(1f, 1f);
        buttonRect.anchorMax = new Vector2(1f, 1f);
        buttonRect.anchoredPosition = new Vector2(-30, -30);
        buttonRect.sizeDelta = new Vector2(40, 40);
        
        // 创建关闭图标文本
        GameObject textGO = new GameObject("Text");
        textGO.transform.SetParent(closeButtonGO.transform, false);
        
        TextMeshProUGUI buttonText = textGO.AddComponent<TextMeshProUGUI>();
        buttonText.text = "×";
        buttonText.fontSize = 28;
        buttonText.color = textColor;
        buttonText.alignment = TextAlignmentOptions.Center;
        
        RectTransform textRect = textGO.GetComponent<RectTransform>();
        textRect.anchorMin = Vector2.zero;
        textRect.anchorMax = Vector2.one;
        textRect.offsetMin = Vector2.zero;
        textRect.offsetMax = Vector2.zero;
        
        return closeButton;
    }
    
    /// <summary>
    /// 创建示例设置按钮
    /// </summary>
    private void CreateExampleSettingsButton(GameObject panelParent)
    {
        GameObject buttonGO = new GameObject("ExampleButton");
        buttonGO.transform.SetParent(panelParent.transform, false);
        
        Image buttonImage = buttonGO.AddComponent<Image>();
        Button button = buttonGO.AddComponent<Button>();
        
        // 设置按钮样式
        buttonImage.color = buttonNormalColor;
        buttonImage.sprite = Resources.GetBuiltinResource<Sprite>("UI/Skin/UISprite.psd");
        buttonImage.type = Image.Type.Sliced;
        
        // 配置按钮颜色
        ColorBlock colors = button.colors;
        colors.normalColor = buttonNormalColor;
        colors.highlightedColor = buttonHighlightColor;
        colors.pressedColor = buttonPressedColor;
        button.colors = colors;
        
        // 设置位置
        RectTransform buttonRect = buttonGO.GetComponent<RectTransform>();
        buttonRect.anchorMin = new Vector2(0.5f, 0.5f);
        buttonRect.anchorMax = new Vector2(0.5f, 0.5f);
        buttonRect.anchoredPosition = new Vector2(0, 0);
        buttonRect.sizeDelta = new Vector2(200, 50);
        
        // 创建按钮文本
        GameObject textGO = new GameObject("Text");
        textGO.transform.SetParent(buttonGO.transform, false);
        
        TextMeshProUGUI buttonText = textGO.AddComponent<TextMeshProUGUI>();
        buttonText.text = "示例功能";
        buttonText.fontSize = buttonFontSize;
        buttonText.color = textColor;
        buttonText.alignment = TextAlignmentOptions.Center;
        
        RectTransform textRect = textGO.GetComponent<RectTransform>();
        textRect.anchorMin = Vector2.zero;
        textRect.anchorMax = Vector2.one;
        textRect.offsetMin = Vector2.zero;
        textRect.offsetMax = Vector2.zero;
    }
    
    /// <summary>
    /// 配置VRSettingsUI组件
    /// </summary>
    private void ConfigureVRSettingsUI(VRSettingsUI settingsUI, Canvas canvas, Button settingsButton, GameObject settingsPanel)
    {
        // 使用反射设置私有字段（因为它们是SerializeField）
        var canvasField = typeof(VRSettingsUI).GetField("settingsCanvas", 
            System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
        var buttonField = typeof(VRSettingsUI).GetField("settingsButton", 
            System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
        var panelField = typeof(VRSettingsUI).GetField("settingsPanel", 
            System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
        var closeButtonField = typeof(VRSettingsUI).GetField("closeButton", 
            System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
        
        if (canvasField != null) canvasField.SetValue(settingsUI, canvas);
        if (buttonField != null) buttonField.SetValue(settingsUI, settingsButton);
        if (panelField != null) panelField.SetValue(settingsUI, settingsPanel);
        
        // 查找关闭按钮
        Button closeButton = settingsPanel.GetComponentInChildren<Button>();
        if (closeButton != null && closeButtonField != null)
        {
            closeButtonField.SetValue(settingsUI, closeButton);
        }
        
        Debug.Log("[VRSettingsUISetupHelper] VRSettingsUI组件配置完成");
    }
    
    /// <summary>
    /// VR优化设置
    /// </summary>
    private void OptimizeForVR(Canvas canvas)
    {
        // 设置Canvas位置（参考开始场景的PositionUIForVR方法）
        if (vrCamera != null)
        {
            Vector3 cameraPosition = vrCamera.transform.position;
            Vector3 cameraForward = vrCamera.transform.forward;
            
            // 计算UI位置
            Vector3 targetPosition = cameraPosition + cameraForward * uiDistance + new Vector3(0, 0.2f, 0);
            canvas.transform.position = targetPosition;
            
            // 设置缩放
            canvas.transform.localScale = Vector3.one * uiScale;
            
            // 面向用户
            canvas.transform.LookAt(vrCamera.transform);
            canvas.transform.Rotate(0, 180, 0); // 翻转让UI正面朝向用户
        }
        
        // 确保EventSystem存在
        if (FindObjectOfType<EventSystem>() == null)
        {
            GameObject eventSystemGO = new GameObject("EventSystem");
            eventSystemGO.AddComponent<EventSystem>();
            eventSystemGO.AddComponent<StandaloneInputModule>();
        }
        
        Debug.Log("[VRSettingsUISetupHelper] VR优化设置完成");
    }

#if UNITY_EDITOR
    /// <summary>
    /// 编辑器菜单：创建VR设置UI
    /// </summary>
    [MenuItem("VR Assembly/Create VR Settings UI")]
    public static void CreateVRSettingsUIFromMenu()
    {
        // 查找或创建VRSettingsUISetupHelper
        VRSettingsUISetupHelper helper = FindObjectOfType<VRSettingsUISetupHelper>();
        if (helper == null)
        {
            GameObject helperGO = new GameObject("VRSettingsUISetupHelper");
            helper = helperGO.AddComponent<VRSettingsUISetupHelper>();
        }
        
        helper.CreateVRSettingsUI();
        
        EditorUtility.DisplayDialog("VR设置UI创建", "VR设置UI创建完成！\n\n请在Inspector中配置齿轮图标Sprite。", "确定");
    }
#endif
}
