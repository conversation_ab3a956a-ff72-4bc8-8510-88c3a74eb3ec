﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





// 0x00000001 UnityEngine.UI.Text UnityEngine.XR.Interaction.Toolkit.Samples.StarterAssets.IncrementUIText::get_text()
extern void IncrementUIText_get_text_mEE9AC522102D09B1CDE167EA4E3379C6405FCC4E (void);
// 0x00000002 System.Void UnityEngine.XR.Interaction.Toolkit.Samples.StarterAssets.IncrementUIText::set_text(UnityEngine.UI.Text)
extern void IncrementUIText_set_text_m77A3581107A2AD2C94A2410B6E2AF024DA5E6A7F (void);
// 0x00000003 System.Void UnityEngine.XR.Interaction.Toolkit.Samples.StarterAssets.IncrementUIText::Awake()
extern void IncrementUIText_Awake_mC97B06670534A04F0A2C68E119D8D1391236BA56 (void);
// 0x00000004 System.Void UnityEngine.XR.Interaction.Toolkit.Samples.StarterAssets.IncrementUIText::IncrementText()
extern void IncrementUIText_IncrementText_m8E724ACEA196D506DEEA23752B3950D25883BFEA (void);
// 0x00000005 System.Void UnityEngine.XR.Interaction.Toolkit.Samples.StarterAssets.IncrementUIText::.ctor()
extern void IncrementUIText__ctor_m4699F30CF0F521FCC7C05391C13F1711B78CA9EA (void);
// 0x00000006 System.Boolean UnityEngine.XR.Interaction.Toolkit.Samples.StarterAssets.ActionBasedControllerManager::get_smoothMotionEnabled()
extern void ActionBasedControllerManager_get_smoothMotionEnabled_m8B330E329AE71CE007B9DE78AFA9A87D4E2C4F61 (void);
// 0x00000007 System.Void UnityEngine.XR.Interaction.Toolkit.Samples.StarterAssets.ActionBasedControllerManager::set_smoothMotionEnabled(System.Boolean)
extern void ActionBasedControllerManager_set_smoothMotionEnabled_mAD21DF2AADD6B279F352C7D7CEBBCC372B78BF96 (void);
// 0x00000008 System.Boolean UnityEngine.XR.Interaction.Toolkit.Samples.StarterAssets.ActionBasedControllerManager::get_smoothTurnEnabled()
extern void ActionBasedControllerManager_get_smoothTurnEnabled_m9AEFB376258A823C5C42D9128BF7F5F6331419A3 (void);
// 0x00000009 System.Void UnityEngine.XR.Interaction.Toolkit.Samples.StarterAssets.ActionBasedControllerManager::set_smoothTurnEnabled(System.Boolean)
extern void ActionBasedControllerManager_set_smoothTurnEnabled_m7503E78C50CEEE5F3F51D885F6363A264DA7B24F (void);
// 0x0000000A System.Boolean UnityEngine.XR.Interaction.Toolkit.Samples.StarterAssets.ActionBasedControllerManager::get_uiScrollingEnabled()
extern void ActionBasedControllerManager_get_uiScrollingEnabled_mA196A96F97069B3365FF8C2B1CFF55091720A9B9 (void);
// 0x0000000B System.Void UnityEngine.XR.Interaction.Toolkit.Samples.StarterAssets.ActionBasedControllerManager::set_uiScrollingEnabled(System.Boolean)
extern void ActionBasedControllerManager_set_uiScrollingEnabled_mEB0544A9670EA0F80ED951B6159C0787B01930E2 (void);
// 0x0000000C System.Void UnityEngine.XR.Interaction.Toolkit.Samples.StarterAssets.ActionBasedControllerManager::SetupInteractorEvents()
extern void ActionBasedControllerManager_SetupInteractorEvents_mE9FCCBD1C9BEEBBE22AD9B10F017A9EE1EBDA852 (void);
// 0x0000000D System.Void UnityEngine.XR.Interaction.Toolkit.Samples.StarterAssets.ActionBasedControllerManager::TeardownInteractorEvents()
extern void ActionBasedControllerManager_TeardownInteractorEvents_mD09E6747F7DEBAC01AC1D58D78FA53503A2B68C8 (void);
// 0x0000000E System.Void UnityEngine.XR.Interaction.Toolkit.Samples.StarterAssets.ActionBasedControllerManager::OnStartTeleport(UnityEngine.InputSystem.InputAction/CallbackContext)
extern void ActionBasedControllerManager_OnStartTeleport_m146507128AA7DEDE85FF3DDE8570389F9F7F948E (void);
// 0x0000000F System.Void UnityEngine.XR.Interaction.Toolkit.Samples.StarterAssets.ActionBasedControllerManager::OnCancelTeleport(UnityEngine.InputSystem.InputAction/CallbackContext)
extern void ActionBasedControllerManager_OnCancelTeleport_mC59ABFDCA878A574A8C9DD05B45EA430A7E36DED (void);
// 0x00000010 System.Void UnityEngine.XR.Interaction.Toolkit.Samples.StarterAssets.ActionBasedControllerManager::OnStartLocomotion(UnityEngine.InputSystem.InputAction/CallbackContext)
extern void ActionBasedControllerManager_OnStartLocomotion_mF7DE7C7684F1EC6E994236B253C6BF836013A60F (void);
// 0x00000011 System.Void UnityEngine.XR.Interaction.Toolkit.Samples.StarterAssets.ActionBasedControllerManager::OnStopLocomotion(UnityEngine.InputSystem.InputAction/CallbackContext)
extern void ActionBasedControllerManager_OnStopLocomotion_m1DE893579564A76634647EA3D0B9197B69C160B3 (void);
// 0x00000012 System.Void UnityEngine.XR.Interaction.Toolkit.Samples.StarterAssets.ActionBasedControllerManager::OnRaySelectEntered(UnityEngine.XR.Interaction.Toolkit.SelectEnterEventArgs)
extern void ActionBasedControllerManager_OnRaySelectEntered_m4939EA779DE4C940346028241B357379B18DC99F (void);
// 0x00000013 System.Void UnityEngine.XR.Interaction.Toolkit.Samples.StarterAssets.ActionBasedControllerManager::OnRaySelectExited(UnityEngine.XR.Interaction.Toolkit.SelectExitEventArgs)
extern void ActionBasedControllerManager_OnRaySelectExited_m8236E24078DD99AA472BCFBD19F1E6E0D56B06F5 (void);
// 0x00000014 System.Void UnityEngine.XR.Interaction.Toolkit.Samples.StarterAssets.ActionBasedControllerManager::OnUIHoverEntered(UnityEngine.XR.Interaction.Toolkit.UI.UIHoverEventArgs)
extern void ActionBasedControllerManager_OnUIHoverEntered_m0D61FC4C73078E5CB303A6875F2EE19CDD9D1F45 (void);
// 0x00000015 System.Void UnityEngine.XR.Interaction.Toolkit.Samples.StarterAssets.ActionBasedControllerManager::OnUIHoverExited(UnityEngine.XR.Interaction.Toolkit.UI.UIHoverEventArgs)
extern void ActionBasedControllerManager_OnUIHoverExited_m3F1275A8FA61278C2E8715D01A0915CD36436908 (void);
// 0x00000016 System.Void UnityEngine.XR.Interaction.Toolkit.Samples.StarterAssets.ActionBasedControllerManager::Awake()
extern void ActionBasedControllerManager_Awake_m218F72512646A09591011C77FC350C86E8761271 (void);
// 0x00000017 System.Void UnityEngine.XR.Interaction.Toolkit.Samples.StarterAssets.ActionBasedControllerManager::OnEnable()
extern void ActionBasedControllerManager_OnEnable_m51D610223E8BB80FA2AA9945D7BE39E54DB2431B (void);
// 0x00000018 System.Void UnityEngine.XR.Interaction.Toolkit.Samples.StarterAssets.ActionBasedControllerManager::OnDisable()
extern void ActionBasedControllerManager_OnDisable_m8B966E4F807DA7CF20155AD1239C1CE86658C535 (void);
// 0x00000019 System.Void UnityEngine.XR.Interaction.Toolkit.Samples.StarterAssets.ActionBasedControllerManager::Start()
extern void ActionBasedControllerManager_Start_m18D931BF912208154FC2425A4C2565B74BCE5897 (void);
// 0x0000001A System.Collections.IEnumerator UnityEngine.XR.Interaction.Toolkit.Samples.StarterAssets.ActionBasedControllerManager::OnAfterInteractionEvents()
extern void ActionBasedControllerManager_OnAfterInteractionEvents_mCF40B0815F54413521A227CA9945ACAE6EBB83FD (void);
// 0x0000001B System.Void UnityEngine.XR.Interaction.Toolkit.Samples.StarterAssets.ActionBasedControllerManager::UpdateLocomotionActions()
extern void ActionBasedControllerManager_UpdateLocomotionActions_m90E85E149A202B3BEE453D8CCE7B9DC95F9442CA (void);
// 0x0000001C System.Void UnityEngine.XR.Interaction.Toolkit.Samples.StarterAssets.ActionBasedControllerManager::DisableLocomotionActions()
extern void ActionBasedControllerManager_DisableLocomotionActions_mE6A2290813F1BD07280BF4A6E9B6BBA800E81E8C (void);
// 0x0000001D System.Void UnityEngine.XR.Interaction.Toolkit.Samples.StarterAssets.ActionBasedControllerManager::UpdateUIActions()
extern void ActionBasedControllerManager_UpdateUIActions_m4228CBE3F66C86C0241412811F9D855DDE76F4B5 (void);
// 0x0000001E System.Void UnityEngine.XR.Interaction.Toolkit.Samples.StarterAssets.ActionBasedControllerManager::SetEnabled(UnityEngine.InputSystem.InputActionReference,System.Boolean)
extern void ActionBasedControllerManager_SetEnabled_m2EEABE3C85A63159A290D3812A96EA5A725D028F (void);
// 0x0000001F System.Void UnityEngine.XR.Interaction.Toolkit.Samples.StarterAssets.ActionBasedControllerManager::EnableAction(UnityEngine.InputSystem.InputActionReference)
extern void ActionBasedControllerManager_EnableAction_m4F594C5139322B9A68E0A809201BFB76538E9AD8 (void);
// 0x00000020 System.Void UnityEngine.XR.Interaction.Toolkit.Samples.StarterAssets.ActionBasedControllerManager::DisableAction(UnityEngine.InputSystem.InputActionReference)
extern void ActionBasedControllerManager_DisableAction_m7DB0495AE24BDF1E2F626F4EAFEBF580B911FA9F (void);
// 0x00000021 UnityEngine.InputSystem.InputAction UnityEngine.XR.Interaction.Toolkit.Samples.StarterAssets.ActionBasedControllerManager::GetInputAction(UnityEngine.InputSystem.InputActionReference)
extern void ActionBasedControllerManager_GetInputAction_mB065776536731D218E38E2CD6E46BA7837AEBC44 (void);
// 0x00000022 System.Void UnityEngine.XR.Interaction.Toolkit.Samples.StarterAssets.ActionBasedControllerManager::.ctor()
extern void ActionBasedControllerManager__ctor_m3CC27D6ED7208EDE2640264C9C20DBDCF01B130C (void);
// 0x00000023 System.Void UnityEngine.XR.Interaction.Toolkit.Samples.StarterAssets.ActionBasedControllerManager::.cctor()
extern void ActionBasedControllerManager__cctor_m4D99D01660DA757E177F62D5C4574BBDC3517EFC (void);
// 0x00000024 System.Void UnityEngine.XR.Interaction.Toolkit.Samples.StarterAssets.ActionBasedControllerManager/<OnAfterInteractionEvents>d__44::.ctor(System.Int32)
extern void U3COnAfterInteractionEventsU3Ed__44__ctor_m719C3ED47A74C3B8513F3CA84AE24DA71B33F509 (void);
// 0x00000025 System.Void UnityEngine.XR.Interaction.Toolkit.Samples.StarterAssets.ActionBasedControllerManager/<OnAfterInteractionEvents>d__44::System.IDisposable.Dispose()
extern void U3COnAfterInteractionEventsU3Ed__44_System_IDisposable_Dispose_m9B5D7748EF006EA911EBDE8F2C22A602F7480B5D (void);
// 0x00000026 System.Boolean UnityEngine.XR.Interaction.Toolkit.Samples.StarterAssets.ActionBasedControllerManager/<OnAfterInteractionEvents>d__44::MoveNext()
extern void U3COnAfterInteractionEventsU3Ed__44_MoveNext_m0052E3B6B8FCE5A33EA729710C0265E5ED089D1E (void);
// 0x00000027 System.Object UnityEngine.XR.Interaction.Toolkit.Samples.StarterAssets.ActionBasedControllerManager/<OnAfterInteractionEvents>d__44::System.Collections.Generic.IEnumerator<System.Object>.get_Current()
extern void U3COnAfterInteractionEventsU3Ed__44_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m215F48F4FE1AFFD4664862A8A91D43772B715519 (void);
// 0x00000028 System.Void UnityEngine.XR.Interaction.Toolkit.Samples.StarterAssets.ActionBasedControllerManager/<OnAfterInteractionEvents>d__44::System.Collections.IEnumerator.Reset()
extern void U3COnAfterInteractionEventsU3Ed__44_System_Collections_IEnumerator_Reset_mFC10E8CB8C5DC3F54F9568EBB02E78713644CF0B (void);
// 0x00000029 System.Object UnityEngine.XR.Interaction.Toolkit.Samples.StarterAssets.ActionBasedControllerManager/<OnAfterInteractionEvents>d__44::System.Collections.IEnumerator.get_Current()
extern void U3COnAfterInteractionEventsU3Ed__44_System_Collections_IEnumerator_get_Current_m1CF683A9DA7D1D5106C8718C20F3A0C657725993 (void);
// 0x0000002A System.Single UnityEngine.XR.Interaction.Toolkit.Samples.StarterAssets.DestroySelf::get_lifetime()
extern void DestroySelf_get_lifetime_m3AB080F878918FDBF5C39438374FBDDCD9E22458 (void);
// 0x0000002B System.Void UnityEngine.XR.Interaction.Toolkit.Samples.StarterAssets.DestroySelf::set_lifetime(System.Single)
extern void DestroySelf_set_lifetime_mCB3CE0FF94854B5CB42D3C4E93C7783410123F3C (void);
// 0x0000002C System.Void UnityEngine.XR.Interaction.Toolkit.Samples.StarterAssets.DestroySelf::Start()
extern void DestroySelf_Start_mDA06F1CBD548235D9376E609CD54DDD7AD50AFCB (void);
// 0x0000002D System.Void UnityEngine.XR.Interaction.Toolkit.Samples.StarterAssets.DestroySelf::.ctor()
extern void DestroySelf__ctor_mE6AE809A5A18196F03764F925582D12C3AD8523F (void);
// 0x0000002E UnityEngine.Transform UnityEngine.XR.Interaction.Toolkit.Samples.StarterAssets.DynamicMoveProvider::get_headTransform()
extern void DynamicMoveProvider_get_headTransform_m303EF17C689C6B515494F07F2259A0BA6B8D0605 (void);
// 0x0000002F System.Void UnityEngine.XR.Interaction.Toolkit.Samples.StarterAssets.DynamicMoveProvider::set_headTransform(UnityEngine.Transform)
extern void DynamicMoveProvider_set_headTransform_mCB442704F390FE95E70DE479145A5F2E2E138885 (void);
// 0x00000030 UnityEngine.Transform UnityEngine.XR.Interaction.Toolkit.Samples.StarterAssets.DynamicMoveProvider::get_leftControllerTransform()
extern void DynamicMoveProvider_get_leftControllerTransform_mAA1DA9B68DEEA326FC1560FDADDF454DEC69C8A8 (void);
// 0x00000031 System.Void UnityEngine.XR.Interaction.Toolkit.Samples.StarterAssets.DynamicMoveProvider::set_leftControllerTransform(UnityEngine.Transform)
extern void DynamicMoveProvider_set_leftControllerTransform_mF1E47F46BFE01F5355FC71BD67DD1A9CFD2C3DB1 (void);
// 0x00000032 UnityEngine.Transform UnityEngine.XR.Interaction.Toolkit.Samples.StarterAssets.DynamicMoveProvider::get_rightControllerTransform()
extern void DynamicMoveProvider_get_rightControllerTransform_m6FAEE855A31155E21184B32D086F15CE9DA7C92E (void);
// 0x00000033 System.Void UnityEngine.XR.Interaction.Toolkit.Samples.StarterAssets.DynamicMoveProvider::set_rightControllerTransform(UnityEngine.Transform)
extern void DynamicMoveProvider_set_rightControllerTransform_mD138BE95DF4A34447A88F1817806C4BAE2239CFA (void);
// 0x00000034 UnityEngine.XR.Interaction.Toolkit.Samples.StarterAssets.DynamicMoveProvider/MovementDirection UnityEngine.XR.Interaction.Toolkit.Samples.StarterAssets.DynamicMoveProvider::get_leftHandMovementDirection()
extern void DynamicMoveProvider_get_leftHandMovementDirection_m5D0D0D4A984DC0B0CAED6DE32722BE640296ABBF (void);
// 0x00000035 System.Void UnityEngine.XR.Interaction.Toolkit.Samples.StarterAssets.DynamicMoveProvider::set_leftHandMovementDirection(UnityEngine.XR.Interaction.Toolkit.Samples.StarterAssets.DynamicMoveProvider/MovementDirection)
extern void DynamicMoveProvider_set_leftHandMovementDirection_mB73B1139104B7F11E0360C8E900789C0E05881F2 (void);
// 0x00000036 UnityEngine.XR.Interaction.Toolkit.Samples.StarterAssets.DynamicMoveProvider/MovementDirection UnityEngine.XR.Interaction.Toolkit.Samples.StarterAssets.DynamicMoveProvider::get_rightHandMovementDirection()
extern void DynamicMoveProvider_get_rightHandMovementDirection_m88088CD035CDA2ED6597F19DC5C93D8730089C7C (void);
// 0x00000037 System.Void UnityEngine.XR.Interaction.Toolkit.Samples.StarterAssets.DynamicMoveProvider::set_rightHandMovementDirection(UnityEngine.XR.Interaction.Toolkit.Samples.StarterAssets.DynamicMoveProvider/MovementDirection)
extern void DynamicMoveProvider_set_rightHandMovementDirection_m985C438DA67D7479D5D0AA020A8EB411856E51E5 (void);
// 0x00000038 System.Void UnityEngine.XR.Interaction.Toolkit.Samples.StarterAssets.DynamicMoveProvider::Awake()
extern void DynamicMoveProvider_Awake_m3BAB88EE73C713431D822EB7323817318C4C0711 (void);
// 0x00000039 UnityEngine.Vector3 UnityEngine.XR.Interaction.Toolkit.Samples.StarterAssets.DynamicMoveProvider::ComputeDesiredMove(UnityEngine.Vector2)
extern void DynamicMoveProvider_ComputeDesiredMove_mADD1C5EB231A3EDA633059109A91BC4361274733 (void);
// 0x0000003A System.Void UnityEngine.XR.Interaction.Toolkit.Samples.StarterAssets.DynamicMoveProvider::.ctor()
extern void DynamicMoveProvider__ctor_mABE04D06EBE852E39DDBBB9A305090A5A7B04650 (void);
// 0x0000003B System.Boolean UnityEngine.XR.Interaction.Toolkit.Samples.StarterAssets.GazeInputManager::get_fallbackIfEyeTrackingUnavailable()
extern void GazeInputManager_get_fallbackIfEyeTrackingUnavailable_m4FB13F913AD2277E5F65AB7D7EF0729644F2D657 (void);
// 0x0000003C System.Void UnityEngine.XR.Interaction.Toolkit.Samples.StarterAssets.GazeInputManager::set_fallbackIfEyeTrackingUnavailable(System.Boolean)
extern void GazeInputManager_set_fallbackIfEyeTrackingUnavailable_mE6650150E8EC6483E35CF60782A877B7434DE288 (void);
// 0x0000003D System.Void UnityEngine.XR.Interaction.Toolkit.Samples.StarterAssets.GazeInputManager::Awake()
extern void GazeInputManager_Awake_m913673B2763F78D3AC484A62A8CB66DC93CAF6B7 (void);
// 0x0000003E System.Void UnityEngine.XR.Interaction.Toolkit.Samples.StarterAssets.GazeInputManager::OnDestroy()
extern void GazeInputManager_OnDestroy_m3D9C35EE01B4439AE17459009DD257CC22B45A4A (void);
// 0x0000003F System.Void UnityEngine.XR.Interaction.Toolkit.Samples.StarterAssets.GazeInputManager::OnDeviceConnected(UnityEngine.XR.InputDevice)
extern void GazeInputManager_OnDeviceConnected_mFD98427178DBDAF1C7B406FEA28161D3186F2E68 (void);
// 0x00000040 System.Void UnityEngine.XR.Interaction.Toolkit.Samples.StarterAssets.GazeInputManager::OnDeviceChange(UnityEngine.InputSystem.InputDevice,UnityEngine.InputSystem.InputDeviceChange)
extern void GazeInputManager_OnDeviceChange_m5A5EA7E87A46CAEC0CA21EA476B0DAEBB8F2885E (void);
// 0x00000041 System.Void UnityEngine.XR.Interaction.Toolkit.Samples.StarterAssets.GazeInputManager::.ctor()
extern void GazeInputManager__ctor_m745DE4041AB26B99172061A4E8050A0FD507BEB8 (void);
// 0x00000042 UnityEngine.Camera UnityEngine.XR.Interaction.Toolkit.Samples.StarterAssets.ObjectSpawner::get_cameraToFace()
extern void ObjectSpawner_get_cameraToFace_m99ED804DC1AEAFF1FA3BE5D9CB96E15C17A907B8 (void);
// 0x00000043 System.Void UnityEngine.XR.Interaction.Toolkit.Samples.StarterAssets.ObjectSpawner::set_cameraToFace(UnityEngine.Camera)
extern void ObjectSpawner_set_cameraToFace_m13429AE345196ACC129BFC53BA7DEEC6627F445C (void);
// 0x00000044 System.Collections.Generic.List`1<UnityEngine.GameObject> UnityEngine.XR.Interaction.Toolkit.Samples.StarterAssets.ObjectSpawner::get_objectPrefabs()
extern void ObjectSpawner_get_objectPrefabs_mC013B08B69C29F13A750E8715FAB01E8681320CF (void);
// 0x00000045 System.Void UnityEngine.XR.Interaction.Toolkit.Samples.StarterAssets.ObjectSpawner::set_objectPrefabs(System.Collections.Generic.List`1<UnityEngine.GameObject>)
extern void ObjectSpawner_set_objectPrefabs_m7C706F87CB6076934AA85A8DC02C11C94DBC2DE3 (void);
// 0x00000046 UnityEngine.GameObject UnityEngine.XR.Interaction.Toolkit.Samples.StarterAssets.ObjectSpawner::get_spawnVisualizationPrefab()
extern void ObjectSpawner_get_spawnVisualizationPrefab_m005CB7E8829438D62559966C3175138AF6B8E7E2 (void);
// 0x00000047 System.Void UnityEngine.XR.Interaction.Toolkit.Samples.StarterAssets.ObjectSpawner::set_spawnVisualizationPrefab(UnityEngine.GameObject)
extern void ObjectSpawner_set_spawnVisualizationPrefab_mD4A5C8D799FCC9B21228E6055795C373AC896842 (void);
// 0x00000048 System.Int32 UnityEngine.XR.Interaction.Toolkit.Samples.StarterAssets.ObjectSpawner::get_spawnOptionIndex()
extern void ObjectSpawner_get_spawnOptionIndex_m8B127FEF82FD7AAB77E7D4F6EE3E869E25EFB254 (void);
// 0x00000049 System.Void UnityEngine.XR.Interaction.Toolkit.Samples.StarterAssets.ObjectSpawner::set_spawnOptionIndex(System.Int32)
extern void ObjectSpawner_set_spawnOptionIndex_m5984E7B1712BB73A2E982FE4487FCA033A922711 (void);
// 0x0000004A System.Boolean UnityEngine.XR.Interaction.Toolkit.Samples.StarterAssets.ObjectSpawner::get_isSpawnOptionRandomized()
extern void ObjectSpawner_get_isSpawnOptionRandomized_m332805694C4805511AD6D8124E6038E3A5D59C2B (void);
// 0x0000004B System.Boolean UnityEngine.XR.Interaction.Toolkit.Samples.StarterAssets.ObjectSpawner::get_onlySpawnInView()
extern void ObjectSpawner_get_onlySpawnInView_m4AE6CAF69146EE5F55128F520ED6C77FBF33B376 (void);
// 0x0000004C System.Void UnityEngine.XR.Interaction.Toolkit.Samples.StarterAssets.ObjectSpawner::set_onlySpawnInView(System.Boolean)
extern void ObjectSpawner_set_onlySpawnInView_m39467C97467506B3012A050F3849BC896ECFE07A (void);
// 0x0000004D System.Single UnityEngine.XR.Interaction.Toolkit.Samples.StarterAssets.ObjectSpawner::get_viewportPeriphery()
extern void ObjectSpawner_get_viewportPeriphery_m9D81A0039316F881F4F12D8F42E67ED86907DF8E (void);
// 0x0000004E System.Void UnityEngine.XR.Interaction.Toolkit.Samples.StarterAssets.ObjectSpawner::set_viewportPeriphery(System.Single)
extern void ObjectSpawner_set_viewportPeriphery_mF79358BA74C413586E09E1DD628FC9A36EA948BA (void);
// 0x0000004F System.Boolean UnityEngine.XR.Interaction.Toolkit.Samples.StarterAssets.ObjectSpawner::get_applyRandomAngleAtSpawn()
extern void ObjectSpawner_get_applyRandomAngleAtSpawn_mEEF19E6B8A0232670426D51CE44AFD5C7D721289 (void);
// 0x00000050 System.Void UnityEngine.XR.Interaction.Toolkit.Samples.StarterAssets.ObjectSpawner::set_applyRandomAngleAtSpawn(System.Boolean)
extern void ObjectSpawner_set_applyRandomAngleAtSpawn_m249E985B18D47F1280DA892475C0F8A743A172C8 (void);
// 0x00000051 System.Single UnityEngine.XR.Interaction.Toolkit.Samples.StarterAssets.ObjectSpawner::get_spawnAngleRange()
extern void ObjectSpawner_get_spawnAngleRange_mA5AB99C204A4669D2805081FF2B27F2C6CFB97B6 (void);
// 0x00000052 System.Void UnityEngine.XR.Interaction.Toolkit.Samples.StarterAssets.ObjectSpawner::set_spawnAngleRange(System.Single)
extern void ObjectSpawner_set_spawnAngleRange_m5CA22E8E5559EAE3CAFED5E7E5D114DFD5E22EFE (void);
// 0x00000053 System.Boolean UnityEngine.XR.Interaction.Toolkit.Samples.StarterAssets.ObjectSpawner::get_spawnAsChildren()
extern void ObjectSpawner_get_spawnAsChildren_m4498447AB9BD7DA4F8B6B31E2C8FF0F3A25D6483 (void);
// 0x00000054 System.Void UnityEngine.XR.Interaction.Toolkit.Samples.StarterAssets.ObjectSpawner::set_spawnAsChildren(System.Boolean)
extern void ObjectSpawner_set_spawnAsChildren_mF600D54942D35CF9B9BF4A6F8D53843AC65EDD32 (void);
// 0x00000055 System.Void UnityEngine.XR.Interaction.Toolkit.Samples.StarterAssets.ObjectSpawner::add_objectSpawned(System.Action`1<UnityEngine.GameObject>)
extern void ObjectSpawner_add_objectSpawned_m6283F4141293DABF487C47EE6D77A54E4A7C1808 (void);
// 0x00000056 System.Void UnityEngine.XR.Interaction.Toolkit.Samples.StarterAssets.ObjectSpawner::remove_objectSpawned(System.Action`1<UnityEngine.GameObject>)
extern void ObjectSpawner_remove_objectSpawned_mE95E288198D756A164416D64782A9468099DE0B5 (void);
// 0x00000057 System.Void UnityEngine.XR.Interaction.Toolkit.Samples.StarterAssets.ObjectSpawner::Awake()
extern void ObjectSpawner_Awake_mCFDE2E8399B486248DD536633294AC9627F1B9A8 (void);
// 0x00000058 System.Void UnityEngine.XR.Interaction.Toolkit.Samples.StarterAssets.ObjectSpawner::EnsureFacingCamera()
extern void ObjectSpawner_EnsureFacingCamera_mDE91C9175F12CB11CDED0B3C82D5D68ED9CAB7B5 (void);
// 0x00000059 System.Void UnityEngine.XR.Interaction.Toolkit.Samples.StarterAssets.ObjectSpawner::RandomizeSpawnOption()
extern void ObjectSpawner_RandomizeSpawnOption_mAAB168851C517000BFFEE2EB598D0DE478BB0FC7 (void);
// 0x0000005A System.Boolean UnityEngine.XR.Interaction.Toolkit.Samples.StarterAssets.ObjectSpawner::TrySpawnObject(UnityEngine.Vector3,UnityEngine.Vector3)
extern void ObjectSpawner_TrySpawnObject_mBE0345EF7F3D3EF69F20321A4190214DB09D65B1 (void);
// 0x0000005B System.Void UnityEngine.XR.Interaction.Toolkit.Samples.StarterAssets.ObjectSpawner::.ctor()
extern void ObjectSpawner__ctor_m1A51792399FE9484F9E6061BC6736BF2E561544C (void);
// 0x0000005C UnityEngine.Transform UnityEngine.XR.Interaction.Toolkit.Samples.StarterAssets.XRPokeFollowAffordance::get_pokeFollowTransform()
extern void XRPokeFollowAffordance_get_pokeFollowTransform_m73344435C2867D159F2F7E02F49376F558087F1E (void);
// 0x0000005D System.Void UnityEngine.XR.Interaction.Toolkit.Samples.StarterAssets.XRPokeFollowAffordance::set_pokeFollowTransform(UnityEngine.Transform)
extern void XRPokeFollowAffordance_set_pokeFollowTransform_m349E957CDD31FC13ABF0136717A3CACDD1451546 (void);
// 0x0000005E System.Single UnityEngine.XR.Interaction.Toolkit.Samples.StarterAssets.XRPokeFollowAffordance::get_smoothingSpeed()
extern void XRPokeFollowAffordance_get_smoothingSpeed_m6DA02413915676F5754E172BAF1C07C0C517BB75 (void);
// 0x0000005F System.Void UnityEngine.XR.Interaction.Toolkit.Samples.StarterAssets.XRPokeFollowAffordance::set_smoothingSpeed(System.Single)
extern void XRPokeFollowAffordance_set_smoothingSpeed_m6F6EDC78B71DE4F9223FB1DC41EA3EB1FBEC210D (void);
// 0x00000060 System.Boolean UnityEngine.XR.Interaction.Toolkit.Samples.StarterAssets.XRPokeFollowAffordance::get_returnToInitialPosition()
extern void XRPokeFollowAffordance_get_returnToInitialPosition_m8D6FC7BDF8A2419ACEAF06B9E46321416A22412A (void);
// 0x00000061 System.Void UnityEngine.XR.Interaction.Toolkit.Samples.StarterAssets.XRPokeFollowAffordance::set_returnToInitialPosition(System.Boolean)
extern void XRPokeFollowAffordance_set_returnToInitialPosition_m77573EFC79B33125B097570F60A87EBA1BA1BA91 (void);
// 0x00000062 System.Boolean UnityEngine.XR.Interaction.Toolkit.Samples.StarterAssets.XRPokeFollowAffordance::get_applyIfChildIsTarget()
extern void XRPokeFollowAffordance_get_applyIfChildIsTarget_m1023FE2DD7E8622125AB3E6261B00B38DC1CA302 (void);
// 0x00000063 System.Void UnityEngine.XR.Interaction.Toolkit.Samples.StarterAssets.XRPokeFollowAffordance::set_applyIfChildIsTarget(System.Boolean)
extern void XRPokeFollowAffordance_set_applyIfChildIsTarget_mBBE196D3022D66890463276AAFDFB54444A0BFE2 (void);
// 0x00000064 System.Boolean UnityEngine.XR.Interaction.Toolkit.Samples.StarterAssets.XRPokeFollowAffordance::get_clampToMaxDistance()
extern void XRPokeFollowAffordance_get_clampToMaxDistance_m6F811E71D7755E06CC1F40B6566FACE71EADC78C (void);
// 0x00000065 System.Void UnityEngine.XR.Interaction.Toolkit.Samples.StarterAssets.XRPokeFollowAffordance::set_clampToMaxDistance(System.Boolean)
extern void XRPokeFollowAffordance_set_clampToMaxDistance_mD0D40453025EF106A1D04AD65A8FC4315B83AA2E (void);
// 0x00000066 System.Single UnityEngine.XR.Interaction.Toolkit.Samples.StarterAssets.XRPokeFollowAffordance::get_maxDistance()
extern void XRPokeFollowAffordance_get_maxDistance_m72B9FC2D76C8CE120CAFA3C35A30CB7E61711F5D (void);
// 0x00000067 System.Void UnityEngine.XR.Interaction.Toolkit.Samples.StarterAssets.XRPokeFollowAffordance::set_maxDistance(System.Single)
extern void XRPokeFollowAffordance_set_maxDistance_m10822A5C3A8814B606594E089B6E8CBB4268AC49 (void);
// 0x00000068 UnityEngine.Vector3 UnityEngine.XR.Interaction.Toolkit.Samples.StarterAssets.XRPokeFollowAffordance::get_initialPosition()
extern void XRPokeFollowAffordance_get_initialPosition_m2076FCE50726A24B4A56CEA94A5AE7CC2BFE7401 (void);
// 0x00000069 System.Void UnityEngine.XR.Interaction.Toolkit.Samples.StarterAssets.XRPokeFollowAffordance::set_initialPosition(UnityEngine.Vector3)
extern void XRPokeFollowAffordance_set_initialPosition_mDEC89AA206367BCE6753B8BB9DAC0D3272B3947A (void);
// 0x0000006A System.Void UnityEngine.XR.Interaction.Toolkit.Samples.StarterAssets.XRPokeFollowAffordance::Awake()
extern void XRPokeFollowAffordance_Awake_m610BBBA2BD47341BB933845C9C005C3C5211F342 (void);
// 0x0000006B System.Void UnityEngine.XR.Interaction.Toolkit.Samples.StarterAssets.XRPokeFollowAffordance::Start()
extern void XRPokeFollowAffordance_Start_m4499F1A6DA2148644B4AE565B55C82612943C8F9 (void);
// 0x0000006C System.Void UnityEngine.XR.Interaction.Toolkit.Samples.StarterAssets.XRPokeFollowAffordance::OnDestroy()
extern void XRPokeFollowAffordance_OnDestroy_mE345311D68D5D9E9877A7384691B230B1B0073FB (void);
// 0x0000006D System.Void UnityEngine.XR.Interaction.Toolkit.Samples.StarterAssets.XRPokeFollowAffordance::LateUpdate()
extern void XRPokeFollowAffordance_LateUpdate_m4C59B8156DE2AF9D63A61A9E523A059A41DD9099 (void);
// 0x0000006E System.Void UnityEngine.XR.Interaction.Toolkit.Samples.StarterAssets.XRPokeFollowAffordance::OnTransformTweenableVariableUpdated(Unity.Mathematics.float3)
extern void XRPokeFollowAffordance_OnTransformTweenableVariableUpdated_m45F75BBA331A940327AD949A7C0939F24F6B57B2 (void);
// 0x0000006F System.Void UnityEngine.XR.Interaction.Toolkit.Samples.StarterAssets.XRPokeFollowAffordance::OnPokeStateDataUpdated(UnityEngine.XR.Interaction.Toolkit.Filtering.PokeStateData)
extern void XRPokeFollowAffordance_OnPokeStateDataUpdated_m47E524438FBC3D238855A42C176A43355387137F (void);
// 0x00000070 System.Void UnityEngine.XR.Interaction.Toolkit.Samples.StarterAssets.XRPokeFollowAffordance::ResetFollowTransform()
extern void XRPokeFollowAffordance_ResetFollowTransform_m56E4E198D3EBA4057BD93763D169309CF7910E80 (void);
// 0x00000071 System.Void UnityEngine.XR.Interaction.Toolkit.Samples.StarterAssets.XRPokeFollowAffordance::.ctor()
extern void XRPokeFollowAffordance__ctor_mCBB6E96BA2E8B60AE508F619BFDBC0BAAD5CEBE5 (void);
static Il2CppMethodPointer s_methodPointers[113] = 
{
	IncrementUIText_get_text_mEE9AC522102D09B1CDE167EA4E3379C6405FCC4E,
	IncrementUIText_set_text_m77A3581107A2AD2C94A2410B6E2AF024DA5E6A7F,
	IncrementUIText_Awake_mC97B06670534A04F0A2C68E119D8D1391236BA56,
	IncrementUIText_IncrementText_m8E724ACEA196D506DEEA23752B3950D25883BFEA,
	IncrementUIText__ctor_m4699F30CF0F521FCC7C05391C13F1711B78CA9EA,
	ActionBasedControllerManager_get_smoothMotionEnabled_m8B330E329AE71CE007B9DE78AFA9A87D4E2C4F61,
	ActionBasedControllerManager_set_smoothMotionEnabled_mAD21DF2AADD6B279F352C7D7CEBBCC372B78BF96,
	ActionBasedControllerManager_get_smoothTurnEnabled_m9AEFB376258A823C5C42D9128BF7F5F6331419A3,
	ActionBasedControllerManager_set_smoothTurnEnabled_m7503E78C50CEEE5F3F51D885F6363A264DA7B24F,
	ActionBasedControllerManager_get_uiScrollingEnabled_mA196A96F97069B3365FF8C2B1CFF55091720A9B9,
	ActionBasedControllerManager_set_uiScrollingEnabled_mEB0544A9670EA0F80ED951B6159C0787B01930E2,
	ActionBasedControllerManager_SetupInteractorEvents_mE9FCCBD1C9BEEBBE22AD9B10F017A9EE1EBDA852,
	ActionBasedControllerManager_TeardownInteractorEvents_mD09E6747F7DEBAC01AC1D58D78FA53503A2B68C8,
	ActionBasedControllerManager_OnStartTeleport_m146507128AA7DEDE85FF3DDE8570389F9F7F948E,
	ActionBasedControllerManager_OnCancelTeleport_mC59ABFDCA878A574A8C9DD05B45EA430A7E36DED,
	ActionBasedControllerManager_OnStartLocomotion_mF7DE7C7684F1EC6E994236B253C6BF836013A60F,
	ActionBasedControllerManager_OnStopLocomotion_m1DE893579564A76634647EA3D0B9197B69C160B3,
	ActionBasedControllerManager_OnRaySelectEntered_m4939EA779DE4C940346028241B357379B18DC99F,
	ActionBasedControllerManager_OnRaySelectExited_m8236E24078DD99AA472BCFBD19F1E6E0D56B06F5,
	ActionBasedControllerManager_OnUIHoverEntered_m0D61FC4C73078E5CB303A6875F2EE19CDD9D1F45,
	ActionBasedControllerManager_OnUIHoverExited_m3F1275A8FA61278C2E8715D01A0915CD36436908,
	ActionBasedControllerManager_Awake_m218F72512646A09591011C77FC350C86E8761271,
	ActionBasedControllerManager_OnEnable_m51D610223E8BB80FA2AA9945D7BE39E54DB2431B,
	ActionBasedControllerManager_OnDisable_m8B966E4F807DA7CF20155AD1239C1CE86658C535,
	ActionBasedControllerManager_Start_m18D931BF912208154FC2425A4C2565B74BCE5897,
	ActionBasedControllerManager_OnAfterInteractionEvents_mCF40B0815F54413521A227CA9945ACAE6EBB83FD,
	ActionBasedControllerManager_UpdateLocomotionActions_m90E85E149A202B3BEE453D8CCE7B9DC95F9442CA,
	ActionBasedControllerManager_DisableLocomotionActions_mE6A2290813F1BD07280BF4A6E9B6BBA800E81E8C,
	ActionBasedControllerManager_UpdateUIActions_m4228CBE3F66C86C0241412811F9D855DDE76F4B5,
	ActionBasedControllerManager_SetEnabled_m2EEABE3C85A63159A290D3812A96EA5A725D028F,
	ActionBasedControllerManager_EnableAction_m4F594C5139322B9A68E0A809201BFB76538E9AD8,
	ActionBasedControllerManager_DisableAction_m7DB0495AE24BDF1E2F626F4EAFEBF580B911FA9F,
	ActionBasedControllerManager_GetInputAction_mB065776536731D218E38E2CD6E46BA7837AEBC44,
	ActionBasedControllerManager__ctor_m3CC27D6ED7208EDE2640264C9C20DBDCF01B130C,
	ActionBasedControllerManager__cctor_m4D99D01660DA757E177F62D5C4574BBDC3517EFC,
	U3COnAfterInteractionEventsU3Ed__44__ctor_m719C3ED47A74C3B8513F3CA84AE24DA71B33F509,
	U3COnAfterInteractionEventsU3Ed__44_System_IDisposable_Dispose_m9B5D7748EF006EA911EBDE8F2C22A602F7480B5D,
	U3COnAfterInteractionEventsU3Ed__44_MoveNext_m0052E3B6B8FCE5A33EA729710C0265E5ED089D1E,
	U3COnAfterInteractionEventsU3Ed__44_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m215F48F4FE1AFFD4664862A8A91D43772B715519,
	U3COnAfterInteractionEventsU3Ed__44_System_Collections_IEnumerator_Reset_mFC10E8CB8C5DC3F54F9568EBB02E78713644CF0B,
	U3COnAfterInteractionEventsU3Ed__44_System_Collections_IEnumerator_get_Current_m1CF683A9DA7D1D5106C8718C20F3A0C657725993,
	DestroySelf_get_lifetime_m3AB080F878918FDBF5C39438374FBDDCD9E22458,
	DestroySelf_set_lifetime_mCB3CE0FF94854B5CB42D3C4E93C7783410123F3C,
	DestroySelf_Start_mDA06F1CBD548235D9376E609CD54DDD7AD50AFCB,
	DestroySelf__ctor_mE6AE809A5A18196F03764F925582D12C3AD8523F,
	DynamicMoveProvider_get_headTransform_m303EF17C689C6B515494F07F2259A0BA6B8D0605,
	DynamicMoveProvider_set_headTransform_mCB442704F390FE95E70DE479145A5F2E2E138885,
	DynamicMoveProvider_get_leftControllerTransform_mAA1DA9B68DEEA326FC1560FDADDF454DEC69C8A8,
	DynamicMoveProvider_set_leftControllerTransform_mF1E47F46BFE01F5355FC71BD67DD1A9CFD2C3DB1,
	DynamicMoveProvider_get_rightControllerTransform_m6FAEE855A31155E21184B32D086F15CE9DA7C92E,
	DynamicMoveProvider_set_rightControllerTransform_mD138BE95DF4A34447A88F1817806C4BAE2239CFA,
	DynamicMoveProvider_get_leftHandMovementDirection_m5D0D0D4A984DC0B0CAED6DE32722BE640296ABBF,
	DynamicMoveProvider_set_leftHandMovementDirection_mB73B1139104B7F11E0360C8E900789C0E05881F2,
	DynamicMoveProvider_get_rightHandMovementDirection_m88088CD035CDA2ED6597F19DC5C93D8730089C7C,
	DynamicMoveProvider_set_rightHandMovementDirection_m985C438DA67D7479D5D0AA020A8EB411856E51E5,
	DynamicMoveProvider_Awake_m3BAB88EE73C713431D822EB7323817318C4C0711,
	DynamicMoveProvider_ComputeDesiredMove_mADD1C5EB231A3EDA633059109A91BC4361274733,
	DynamicMoveProvider__ctor_mABE04D06EBE852E39DDBBB9A305090A5A7B04650,
	GazeInputManager_get_fallbackIfEyeTrackingUnavailable_m4FB13F913AD2277E5F65AB7D7EF0729644F2D657,
	GazeInputManager_set_fallbackIfEyeTrackingUnavailable_mE6650150E8EC6483E35CF60782A877B7434DE288,
	GazeInputManager_Awake_m913673B2763F78D3AC484A62A8CB66DC93CAF6B7,
	GazeInputManager_OnDestroy_m3D9C35EE01B4439AE17459009DD257CC22B45A4A,
	GazeInputManager_OnDeviceConnected_mFD98427178DBDAF1C7B406FEA28161D3186F2E68,
	GazeInputManager_OnDeviceChange_m5A5EA7E87A46CAEC0CA21EA476B0DAEBB8F2885E,
	GazeInputManager__ctor_m745DE4041AB26B99172061A4E8050A0FD507BEB8,
	ObjectSpawner_get_cameraToFace_m99ED804DC1AEAFF1FA3BE5D9CB96E15C17A907B8,
	ObjectSpawner_set_cameraToFace_m13429AE345196ACC129BFC53BA7DEEC6627F445C,
	ObjectSpawner_get_objectPrefabs_mC013B08B69C29F13A750E8715FAB01E8681320CF,
	ObjectSpawner_set_objectPrefabs_m7C706F87CB6076934AA85A8DC02C11C94DBC2DE3,
	ObjectSpawner_get_spawnVisualizationPrefab_m005CB7E8829438D62559966C3175138AF6B8E7E2,
	ObjectSpawner_set_spawnVisualizationPrefab_mD4A5C8D799FCC9B21228E6055795C373AC896842,
	ObjectSpawner_get_spawnOptionIndex_m8B127FEF82FD7AAB77E7D4F6EE3E869E25EFB254,
	ObjectSpawner_set_spawnOptionIndex_m5984E7B1712BB73A2E982FE4487FCA033A922711,
	ObjectSpawner_get_isSpawnOptionRandomized_m332805694C4805511AD6D8124E6038E3A5D59C2B,
	ObjectSpawner_get_onlySpawnInView_m4AE6CAF69146EE5F55128F520ED6C77FBF33B376,
	ObjectSpawner_set_onlySpawnInView_m39467C97467506B3012A050F3849BC896ECFE07A,
	ObjectSpawner_get_viewportPeriphery_m9D81A0039316F881F4F12D8F42E67ED86907DF8E,
	ObjectSpawner_set_viewportPeriphery_mF79358BA74C413586E09E1DD628FC9A36EA948BA,
	ObjectSpawner_get_applyRandomAngleAtSpawn_mEEF19E6B8A0232670426D51CE44AFD5C7D721289,
	ObjectSpawner_set_applyRandomAngleAtSpawn_m249E985B18D47F1280DA892475C0F8A743A172C8,
	ObjectSpawner_get_spawnAngleRange_mA5AB99C204A4669D2805081FF2B27F2C6CFB97B6,
	ObjectSpawner_set_spawnAngleRange_m5CA22E8E5559EAE3CAFED5E7E5D114DFD5E22EFE,
	ObjectSpawner_get_spawnAsChildren_m4498447AB9BD7DA4F8B6B31E2C8FF0F3A25D6483,
	ObjectSpawner_set_spawnAsChildren_mF600D54942D35CF9B9BF4A6F8D53843AC65EDD32,
	ObjectSpawner_add_objectSpawned_m6283F4141293DABF487C47EE6D77A54E4A7C1808,
	ObjectSpawner_remove_objectSpawned_mE95E288198D756A164416D64782A9468099DE0B5,
	ObjectSpawner_Awake_mCFDE2E8399B486248DD536633294AC9627F1B9A8,
	ObjectSpawner_EnsureFacingCamera_mDE91C9175F12CB11CDED0B3C82D5D68ED9CAB7B5,
	ObjectSpawner_RandomizeSpawnOption_mAAB168851C517000BFFEE2EB598D0DE478BB0FC7,
	ObjectSpawner_TrySpawnObject_mBE0345EF7F3D3EF69F20321A4190214DB09D65B1,
	ObjectSpawner__ctor_m1A51792399FE9484F9E6061BC6736BF2E561544C,
	XRPokeFollowAffordance_get_pokeFollowTransform_m73344435C2867D159F2F7E02F49376F558087F1E,
	XRPokeFollowAffordance_set_pokeFollowTransform_m349E957CDD31FC13ABF0136717A3CACDD1451546,
	XRPokeFollowAffordance_get_smoothingSpeed_m6DA02413915676F5754E172BAF1C07C0C517BB75,
	XRPokeFollowAffordance_set_smoothingSpeed_m6F6EDC78B71DE4F9223FB1DC41EA3EB1FBEC210D,
	XRPokeFollowAffordance_get_returnToInitialPosition_m8D6FC7BDF8A2419ACEAF06B9E46321416A22412A,
	XRPokeFollowAffordance_set_returnToInitialPosition_m77573EFC79B33125B097570F60A87EBA1BA1BA91,
	XRPokeFollowAffordance_get_applyIfChildIsTarget_m1023FE2DD7E8622125AB3E6261B00B38DC1CA302,
	XRPokeFollowAffordance_set_applyIfChildIsTarget_mBBE196D3022D66890463276AAFDFB54444A0BFE2,
	XRPokeFollowAffordance_get_clampToMaxDistance_m6F811E71D7755E06CC1F40B6566FACE71EADC78C,
	XRPokeFollowAffordance_set_clampToMaxDistance_mD0D40453025EF106A1D04AD65A8FC4315B83AA2E,
	XRPokeFollowAffordance_get_maxDistance_m72B9FC2D76C8CE120CAFA3C35A30CB7E61711F5D,
	XRPokeFollowAffordance_set_maxDistance_m10822A5C3A8814B606594E089B6E8CBB4268AC49,
	XRPokeFollowAffordance_get_initialPosition_m2076FCE50726A24B4A56CEA94A5AE7CC2BFE7401,
	XRPokeFollowAffordance_set_initialPosition_mDEC89AA206367BCE6753B8BB9DAC0D3272B3947A,
	XRPokeFollowAffordance_Awake_m610BBBA2BD47341BB933845C9C005C3C5211F342,
	XRPokeFollowAffordance_Start_m4499F1A6DA2148644B4AE565B55C82612943C8F9,
	XRPokeFollowAffordance_OnDestroy_mE345311D68D5D9E9877A7384691B230B1B0073FB,
	XRPokeFollowAffordance_LateUpdate_m4C59B8156DE2AF9D63A61A9E523A059A41DD9099,
	XRPokeFollowAffordance_OnTransformTweenableVariableUpdated_m45F75BBA331A940327AD949A7C0939F24F6B57B2,
	XRPokeFollowAffordance_OnPokeStateDataUpdated_m47E524438FBC3D238855A42C176A43355387137F,
	XRPokeFollowAffordance_ResetFollowTransform_m56E4E198D3EBA4057BD93763D169309CF7910E80,
	XRPokeFollowAffordance__ctor_mCBB6E96BA2E8B60AE508F619BFDBC0BAAD5CEBE5,
};
static const int32_t s_InvokerIndices[113] = 
{
	6135,
	4921,
	6262,
	6262,
	6262,
	6041,
	4823,
	6041,
	4823,
	6041,
	4823,
	6262,
	6262,
	5125,
	5125,
	5125,
	5125,
	4921,
	4921,
	4921,
	4921,
	6262,
	6262,
	6262,
	6262,
	6135,
	6262,
	6262,
	6262,
	8846,
	10145,
	10145,
	9859,
	6262,
	10977,
	4894,
	6262,
	6041,
	6135,
	6262,
	6135,
	6194,
	4976,
	6262,
	6262,
	6135,
	4921,
	6135,
	4921,
	6135,
	4921,
	6106,
	4894,
	6106,
	4894,
	6262,
	4547,
	6262,
	6041,
	4823,
	6262,
	6262,
	4886,
	2667,
	6262,
	6135,
	4921,
	6135,
	4921,
	6135,
	4921,
	6106,
	4894,
	6041,
	6041,
	4823,
	6194,
	4976,
	6041,
	4823,
	6194,
	4976,
	6041,
	4823,
	4921,
	4921,
	6262,
	6262,
	6262,
	1688,
	6262,
	6135,
	4921,
	6194,
	4976,
	6041,
	4823,
	6041,
	4823,
	6041,
	4823,
	6194,
	4976,
	6254,
	5028,
	6262,
	6262,
	6262,
	6262,
	5070,
	4934,
	6262,
	6262,
};
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_Unity_XR_Interaction_Toolkit_Samples_StarterAssets_CodeGenModule;
const Il2CppCodeGenModule g_Unity_XR_Interaction_Toolkit_Samples_StarterAssets_CodeGenModule = 
{
	"Unity.XR.Interaction.Toolkit.Samples.StarterAssets.dll",
	113,
	s_methodPointers,
	0,
	NULL,
	s_InvokerIndices,
	0,
	NULL,
	0,
	NULL,
	0,
	NULL,
	NULL,
	NULL, // module initializer,
	NULL,
	NULL,
	NULL,
};
