using UnityEngine;
using TMPro;

/// <summary>
/// VR用户名输入测试器
/// 
/// 用于测试汉字输入和用户名验证功能
/// </summary>
public class VRUsernameInputTest : MonoBehaviour
{
    [Header("测试设置")]
    [SerializeField] private bool enableDebugMode = true;
    [SerializeField] private bool testChineseInput = true;
    
    [Header("测试用例")]
    [SerializeField] private string[] testUsernames = {
        "",                    // 空用户名
        "   ",                 // 只有空格
        "张三",                // 纯汉字
        "李四123",             // 汉字+数字
        "Wang五",              // 英文+汉字
        "用户名Test",          // 汉字+英文
        "很长的用户名测试12345678901234567890"  // 超长用户名
    };
    
    private VRStartMenuManager menuManager;
    private TMP_InputField inputField;
    private int currentTestIndex = 0;
    
    void Start()
    {
        if (enableDebugMode)
        {
            StartCoroutine(InitializeTest());
        }
    }
    
    /// <summary>
    /// 初始化测试
    /// </summary>
    private System.Collections.IEnumerator InitializeTest()
    {
        // 等待一帧确保所有组件都已加载
        yield return null;
        
        // 查找组件
        menuManager = FindObjectOfType<VRStartMenuManager>();
        inputField = FindObjectOfType<TMP_InputField>();
        
        if (menuManager == null)
        {
            Debug.LogError("[VRUsernameInputTest] 找不到VRStartMenuManager");
            yield break;
        }
        
        if (inputField == null)
        {
            Debug.LogError("[VRUsernameInputTest] 找不到TMP_InputField");
            yield break;
        }
        
        Debug.Log("[VRUsernameInputTest] 测试初始化完成");
        Debug.Log("按键说明：");
        Debug.Log("T键 - 运行所有测试用例");
        Debug.Log("N键 - 测试下一个用例");
        Debug.Log("C键 - 清空输入框");
        Debug.Log("S键 - 模拟点击开始按钮");
    }
    
    void Update()
    {
        if (!enableDebugMode) return;
        
        // 快捷键测试
        if (Input.GetKeyDown(KeyCode.T))
        {
            StartCoroutine(RunAllTests());
        }
        else if (Input.GetKeyDown(KeyCode.N))
        {
            TestNextUsername();
        }
        else if (Input.GetKeyDown(KeyCode.C))
        {
            ClearInput();
        }
        else if (Input.GetKeyDown(KeyCode.S))
        {
            SimulateStartButtonClick();
        }
    }
    
    /// <summary>
    /// 运行所有测试
    /// </summary>
    private System.Collections.IEnumerator RunAllTests()
    {
        Debug.Log("=== 开始运行所有用户名测试 ===");
        
        for (int i = 0; i < testUsernames.Length; i++)
        {
            string testName = testUsernames[i];
            Debug.Log($"\n--- 测试用例 {i + 1}: '{testName}' ---");
            
            // 设置输入
            SetInputFieldText(testName);
            
            // 等待一帧让事件处理
            yield return null;
            
            // 模拟点击开始按钮
            SimulateStartButtonClick();
            
            // 等待2秒观察结果
            yield return new WaitForSeconds(2f);
        }
        
        Debug.Log("=== 所有测试完成 ===");
    }
    
    /// <summary>
    /// 测试下一个用户名
    /// </summary>
    private void TestNextUsername()
    {
        if (testUsernames.Length == 0) return;
        
        string testName = testUsernames[currentTestIndex];
        Debug.Log($"测试用例 {currentTestIndex + 1}: '{testName}'");
        
        SetInputFieldText(testName);
        
        currentTestIndex = (currentTestIndex + 1) % testUsernames.Length;
    }
    
    /// <summary>
    /// 设置输入框文本
    /// </summary>
    private void SetInputFieldText(string text)
    {
        if (inputField == null) return;
        
        inputField.text = text;
        
        // 手动触发输入改变事件
        inputField.onValueChanged.Invoke(text);
        
        Debug.Log($"设置输入框文本: '{text}'");
    }
    
    /// <summary>
    /// 清空输入框
    /// </summary>
    private void ClearInput()
    {
        SetInputFieldText("");
        Debug.Log("清空输入框");
    }
    
    /// <summary>
    /// 模拟点击开始按钮
    /// </summary>
    private void SimulateStartButtonClick()
    {
        var startButton = FindObjectOfType<UnityEngine.UI.Button>();
        if (startButton != null)
        {
            Debug.Log("模拟点击开始按钮");
            startButton.onClick.Invoke();
        }
        else
        {
            Debug.LogWarning("找不到开始按钮");
        }
    }
    
    /// <summary>
    /// 测试汉字输入
    /// </summary>
    [ContextMenu("测试汉字输入")]
    public void TestChineseCharacters()
    {
        string[] chineseTests = {
            "张三",
            "李四五",
            "王小明",
            "赵钱孙李",
            "中文用户名测试"
        };
        
        Debug.Log("=== 汉字输入测试 ===");
        
        foreach (string test in chineseTests)
        {
            Debug.Log($"测试汉字: '{test}'");
            SetInputFieldText(test);
            
            // 检查是否被正确处理
            if (inputField.text == test)
            {
                Debug.Log($"✓ 汉字输入成功: '{test}'");
            }
            else
            {
                Debug.LogWarning($"✗ 汉字输入失败: 期望'{test}', 实际'{inputField.text}'");
            }
        }
    }
    
    /// <summary>
    /// 测试字符过滤
    /// </summary>
    [ContextMenu("测试字符过滤")]
    public void TestCharacterFiltering()
    {
        string[] filterTests = {
            "test@#$%",           // 特殊字符
            "用户名!@#",          // 汉字+特殊字符
            "user123",            // 正常英文数字
            "用户123",            // 汉字+数字
            "very_long_username_that_exceeds_limit_12345678901234567890"  // 超长
        };
        
        Debug.Log("=== 字符过滤测试 ===");
        
        foreach (string test in filterTests)
        {
            Debug.Log($"测试过滤: '{test}'");
            SetInputFieldText(test);
            Debug.Log($"过滤结果: '{inputField.text}'");
        }
    }
    
    /// <summary>
    /// 显示当前输入状态
    /// </summary>
    [ContextMenu("显示输入状态")]
    public void ShowInputStatus()
    {
        if (inputField == null)
        {
            Debug.LogWarning("输入框未找到");
            return;
        }
        
        string currentText = inputField.text;
        bool isEmpty = string.IsNullOrWhiteSpace(currentText);
        
        Debug.Log("=== 当前输入状态 ===");
        Debug.Log($"输入文本: '{currentText}'");
        Debug.Log($"文本长度: {currentText.Length}");
        Debug.Log($"是否为空: {isEmpty}");
        Debug.Log($"包含汉字: {ContainsChinese(currentText)}");
    }
    
    /// <summary>
    /// 检查是否包含汉字
    /// </summary>
    private bool ContainsChinese(string text)
    {
        if (string.IsNullOrEmpty(text)) return false;
        
        foreach (char c in text)
        {
            if (c >= '\u4e00' && c <= '\u9fff')
            {
                return true;
            }
        }
        return false;
    }
}
