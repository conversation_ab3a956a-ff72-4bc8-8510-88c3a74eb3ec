using UnityEngine;
using UnityEngine.UI;
using TMPro;
using System.Collections;

/// <summary>
/// VR Start Menu Demo Script
/// 
/// Demo script for testing VR start menu functionality in StartMenu scene
/// Provides complete setup, testing and debugging features
/// </summary>
public class VRStartMenuDemo_EN : MonoBehaviour
{
    [Header("Demo Settings")]
    [SerializeField] private bool autoSetup = true;
    [SerializeField] private bool enableDebugMode = true;
    [SerializeField] private bool showInstructions = true;
    
    [Header("Test Configuration")]
    [SerializeField] private bool simulateVRInput = false;
    [SerializeField] private string testUsername = "TestUser";
    [SerializeField] private float simulationDelay = 2f;
    
    [Header("UI References")]
    [SerializeField] private Canvas instructionCanvas;
    [SerializeField] private TextMeshProUGUI instructionText;
    [SerializeField] private Button setupButton;
    [SerializeField] private Button testButton;
    
    // Private variables
    private VRStartMenuSetupHelper_EN setupHelper;
    private VRStartMenuManager_EN menuManager;
    private VRStartMenuInputHandler inputHandler;
    private VRSceneManager sceneManager;
    private bool isSetupComplete = false;
    
    void Start()
    {
        StartCoroutine(InitializeDemo());
    }
    
    /// <summary>
    /// Initialize demo
    /// </summary>
    private IEnumerator InitializeDemo()
    {
        Debug.Log("[VRStartMenuDemo] Starting demo initialization");
        
        // Wait one frame to ensure all components are loaded
        yield return null;
        
        // Create instruction UI
        if (showInstructions)
        {
            CreateInstructionUI();
        }
        
        // Auto setup
        if (autoSetup)
        {
            yield return new WaitForSeconds(1f);
            SetupVRStartMenu();
        }
        
        // Setup event listeners
        SetupEventListeners();
        
        Debug.Log("[VRStartMenuDemo] Demo initialization completed");
    }
    
    /// <summary>
    /// Create instruction UI
    /// </summary>
    private void CreateInstructionUI()
    {
        // Create instruction Canvas
        GameObject canvasGO = new GameObject("Instruction Canvas");
        instructionCanvas = canvasGO.AddComponent<Canvas>();
        instructionCanvas.renderMode = RenderMode.ScreenSpaceOverlay;
        instructionCanvas.sortingOrder = 100;
        
        canvasGO.AddComponent<CanvasScaler>();
        canvasGO.AddComponent<GraphicRaycaster>();
        
        // Create background panel
        GameObject panelGO = new GameObject("Instruction Panel");
        panelGO.transform.SetParent(canvasGO.transform, false);
        
        Image panelImage = panelGO.AddComponent<Image>();
        panelImage.color = new Color(0, 0, 0, 0.7f);
        
        RectTransform panelRect = panelGO.GetComponent<RectTransform>();
        panelRect.anchorMin = Vector2.zero;
        panelRect.anchorMax = Vector2.one;
        panelRect.offsetMin = Vector2.zero;
        panelRect.offsetMax = Vector2.zero;
        
        // Create instruction text
        GameObject textGO = new GameObject("Instruction Text");
        textGO.transform.SetParent(panelGO.transform, false);
        
        instructionText = textGO.AddComponent<TextMeshProUGUI>();
        instructionText.text = GetInstructionText();
        instructionText.fontSize = 18;
        instructionText.color = Color.white;
        instructionText.alignment = TextAlignmentOptions.Center;
        
        RectTransform textRect = textGO.GetComponent<RectTransform>();
        textRect.anchorMin = new Vector2(0.1f, 0.3f);
        textRect.anchorMax = new Vector2(0.9f, 0.8f);
        textRect.offsetMin = Vector2.zero;
        textRect.offsetMax = Vector2.zero;
        
        // Create setup button
        CreateInstructionButton("Setup VR Menu", new Vector2(0.3f, 0.2f), new Vector2(0.45f, 0.25f), SetupVRStartMenu);
        
        // Create test button
        CreateInstructionButton("Test Function", new Vector2(0.55f, 0.2f), new Vector2(0.7f, 0.25f), TestVRStartMenu);
        
        // Create close button
        CreateInstructionButton("Close Instructions", new Vector2(0.75f, 0.2f), new Vector2(0.9f, 0.25f), HideInstructions);
    }
    
    /// <summary>
    /// Create instruction button
    /// </summary>
    private void CreateInstructionButton(string text, Vector2 anchorMin, Vector2 anchorMax, System.Action action)
    {
        GameObject buttonGO = new GameObject($"Button_{text}");
        buttonGO.transform.SetParent(instructionCanvas.transform, false);
        
        Image buttonImage = buttonGO.AddComponent<Image>();
        buttonImage.color = new Color(0.2f, 0.6f, 1f, 1f);
        
        Button button = buttonGO.AddComponent<Button>();
        button.onClick.AddListener(() => action?.Invoke());
        
        RectTransform buttonRect = buttonGO.GetComponent<RectTransform>();
        buttonRect.anchorMin = anchorMin;
        buttonRect.anchorMax = anchorMax;
        buttonRect.offsetMin = Vector2.zero;
        buttonRect.offsetMax = Vector2.zero;
        
        // Add button text
        GameObject textGO = new GameObject("Text");
        textGO.transform.SetParent(buttonGO.transform, false);
        
        TextMeshProUGUI buttonText = textGO.AddComponent<TextMeshProUGUI>();
        buttonText.text = text;
        buttonText.fontSize = 14;
        buttonText.color = Color.white;
        buttonText.alignment = TextAlignmentOptions.Center;
        
        RectTransform textRect = textGO.GetComponent<RectTransform>();
        textRect.anchorMin = Vector2.zero;
        textRect.anchorMax = Vector2.one;
        textRect.offsetMin = Vector2.zero;
        textRect.offsetMax = Vector2.zero;
    }
    
    /// <summary>
    /// Get instruction text
    /// </summary>
    private string GetInstructionText()
    {
        return @"VR Start Menu Demo

This demo shows how to create and use start interface in PICO VR environment.

Features:
• VR compatible UI layout and interaction
• Controller input support (trigger, buttons)
• Virtual keyboard for text input
• Haptic feedback and audio
• Scene transition management
• User data saving

VR Controls:
• Menu Button: Show/Hide virtual keyboard
• Primary Button: Confirm input
• Secondary Button: Delete character
• Trigger: Click UI elements

Usage Steps:
1. Click 'Setup VR Menu' to create VR interface
2. Enter username in VR
3. Click start button to switch to main scene

Note: Make sure Animation scene is added to Build Settings";
    }
    
    /// <summary>
    /// Setup VR start menu
    /// </summary>
    public void SetupVRStartMenu()
    {
        Debug.Log("[VRStartMenuDemo] Starting VR start menu setup");
        
        // Create setup helper
        if (setupHelper == null)
        {
            GameObject helperGO = new GameObject("VRStartMenuSetupHelper_EN");
            setupHelper = helperGO.AddComponent<VRStartMenuSetupHelper_EN>();
        }
        
        // Create VR menu
        setupHelper.CreateVRStartMenu();
        
        // Get component references
        menuManager = FindObjectOfType<VRStartMenuManager_EN>();
        inputHandler = FindObjectOfType<VRStartMenuInputHandler>();
        
        // Ensure scene manager exists
        if (sceneManager == null)
        {
            sceneManager = VRSceneManager.Instance;
        }
        
        // Setup event listeners
        SetupVRMenuEvents();
        
        isSetupComplete = true;
        
        // Update instruction text
        if (instructionText != null)
        {
            instructionText.text = "VR start menu setup completed!\n\nNow you can:\n• Use VR controller to interact with interface\n• Enter username\n• Click start button to switch scene\n\nOr click 'Test Function' for automatic test";
        }
        
        Debug.Log("[VRStartMenuDemo] VR start menu setup completed");
    }
    
    /// <summary>
    /// Setup VR menu events
    /// </summary>
    private void SetupVRMenuEvents()
    {
        if (menuManager != null)
        {
            menuManager.OnUsernameChanged += OnUsernameChanged;
            menuManager.OnStartButtonClicked += OnStartButtonClicked;
            menuManager.OnSceneTransition += OnSceneTransition;
        }
        
        if (inputHandler != null)
        {
            inputHandler.OnInputChanged += OnInputChanged;
            inputHandler.OnInputConfirmed += OnInputConfirmed;
        }
        
        if (sceneManager != null)
        {
            sceneManager.OnSceneLoadStarted += OnSceneLoadStarted;
            sceneManager.OnSceneLoadProgress += OnSceneLoadProgress;
            sceneManager.OnSceneLoadCompleted += OnSceneLoadCompleted;
            sceneManager.OnSceneLoadFailed += OnSceneLoadFailed;
        }
    }
    
    /// <summary>
    /// Test VR start menu
    /// </summary>
    public void TestVRStartMenu()
    {
        if (!isSetupComplete)
        {
            SetupVRStartMenu();
        }
        
        if (simulateVRInput)
        {
            StartCoroutine(SimulateVRInput());
        }
        else
        {
            Debug.Log("[VRStartMenuDemo] Please test functionality manually in VR");
        }
    }
    
    /// <summary>
    /// Simulate VR input
    /// </summary>
    private IEnumerator SimulateVRInput()
    {
        Debug.Log("[VRStartMenuDemo] Starting VR input simulation");
        
        yield return new WaitForSeconds(simulationDelay);
        
        // Simulate username input
        if (menuManager != null)
        {
            var inputField = FindObjectOfType<TMP_InputField>();
            if (inputField != null)
            {
                inputField.text = testUsername;
                Debug.Log($"[VRStartMenuDemo] Simulated username input: {testUsername}");
            }
        }
        
        yield return new WaitForSeconds(simulationDelay);
        
        // Simulate start button click
        var startButton = FindObjectOfType<Button>();
        if (startButton != null && startButton.interactable)
        {
            startButton.onClick.Invoke();
            Debug.Log("[VRStartMenuDemo] Simulated start button click");
        }
    }
    
    /// <summary>
    /// Hide instructions
    /// </summary>
    public void HideInstructions()
    {
        if (instructionCanvas != null)
        {
            instructionCanvas.gameObject.SetActive(false);
        }
    }
    
    /// <summary>
    /// Show instructions
    /// </summary>
    public void ShowInstructions()
    {
        if (instructionCanvas != null)
        {
            instructionCanvas.gameObject.SetActive(true);
        }
    }
    
    /// <summary>
    /// Setup event listeners
    /// </summary>
    private void SetupEventListeners()
    {
        // Keyboard shortcuts
        if (enableDebugMode)
        {
            Debug.Log("[VRStartMenuDemo] Debug mode enabled");
            Debug.Log("Shortcuts: F1=Show instructions, F2=Setup menu, F3=Test function");
        }
    }
    
    void Update()
    {
        // Debug shortcuts
        if (enableDebugMode)
        {
            if (Input.GetKeyDown(KeyCode.F1))
            {
                ShowInstructions();
            }
            else if (Input.GetKeyDown(KeyCode.F2))
            {
                SetupVRStartMenu();
            }
            else if (Input.GetKeyDown(KeyCode.F3))
            {
                TestVRStartMenu();
            }
        }
    }
    
    // Event handling methods
    private void OnUsernameChanged(string username)
    {
        Debug.Log($"[VRStartMenuDemo] Username changed: {username}");
    }
    
    private void OnStartButtonClicked()
    {
        Debug.Log("[VRStartMenuDemo] Start button clicked");
    }
    
    private void OnSceneTransition(string sceneName)
    {
        Debug.Log($"[VRStartMenuDemo] Scene transition: {sceneName}");
    }
    
    private void OnInputChanged(string input)
    {
        Debug.Log($"[VRStartMenuDemo] Input changed: {input}");
    }
    
    private void OnInputConfirmed(string input)
    {
        Debug.Log($"[VRStartMenuDemo] Input confirmed: {input}");
    }
    
    private void OnSceneLoadStarted(string sceneName)
    {
        Debug.Log($"[VRStartMenuDemo] Scene load started: {sceneName}");
    }
    
    private void OnSceneLoadProgress(string sceneName, float progress)
    {
        Debug.Log($"[VRStartMenuDemo] Scene load progress: {sceneName} - {progress:P}");
    }
    
    private void OnSceneLoadCompleted(string sceneName)
    {
        Debug.Log($"[VRStartMenuDemo] Scene load completed: {sceneName}");
    }
    
    private void OnSceneLoadFailed(string sceneName)
    {
        Debug.LogError($"[VRStartMenuDemo] Scene load failed: {sceneName}");
    }
    
    void OnDestroy()
    {
        // Clean up event listeners
        if (menuManager != null)
        {
            menuManager.OnUsernameChanged -= OnUsernameChanged;
            menuManager.OnStartButtonClicked -= OnStartButtonClicked;
            menuManager.OnSceneTransition -= OnSceneTransition;
        }
        
        if (inputHandler != null)
        {
            inputHandler.OnInputChanged -= OnInputChanged;
            inputHandler.OnInputConfirmed -= OnInputConfirmed;
        }
    }
}
