{"report": {"modules": [{"name": "AndroidJNI", "dependencies": []}, {"name": "Animation", "dependencies": [{"name": "AnimationClip", "scenes": ["Assets/Scenes/StartMenu.unity", "Assets/Scenes/小U和电机.unity"], "dependencyType": 0, "icon": null}, {"name": "Animator", "scenes": ["Assets/Scenes/StartMenu.unity", "Assets/Scenes/小U和电机.unity"], "dependencyType": 0, "icon": null}, {"name": "AnimatorController", "scenes": ["Assets/Scenes/StartMenu.unity", "Assets/Scenes/小U和电机.unity"], "dependencyType": 1, "icon": null}, {"name": "AnimatorOverrideController", "scenes": ["Assets/Scenes/StartMenu.unity", "Assets/Scenes/小U和电机.unity"], "dependencyType": 0, "icon": null}, {"name": "Avatar", "scenes": ["Assets/Scenes/StartMenu.unity"], "dependencyType": 0, "icon": null}, {"name": "AvatarMask", "scenes": ["Assets/Scenes/StartMenu.unity"], "dependencyType": 0, "icon": null}, {"name": "Motion", "scenes": [], "dependencyType": 1, "icon": null}, {"name": "RuntimeAnimatorController", "scenes": [], "dependencyType": 1, "icon": null}]}, {"name": "Audio", "dependencies": [{"name": "AudioBehaviour", "scenes": ["Assets/Scenes/StartMenu.unity", "Assets/Scenes/小U和电机.unity"], "dependencyType": 0, "icon": null}, {"name": "AudioClip", "scenes": [], "dependencyType": 0, "icon": null}, {"name": "AudioListener", "scenes": ["Assets/Scenes/StartMenu.unity", "Assets/Scenes/小U和电机.unity"], "dependencyType": 0, "icon": null}, {"name": "AudioManager", "scenes": [], "dependencyType": 1, "icon": null}, {"name": "AudioSource", "scenes": [], "dependencyType": 0, "icon": null}, {"name": "SampleClip", "scenes": [], "dependencyType": 1, "icon": null}]}, {"name": "Core", "dependencies": [{"name": "Behaviour", "scenes": [], "dependencyType": 1, "icon": null}, {"name": "BuildSettings", "scenes": [], "dependencyType": 1, "icon": null}, {"name": "Camera", "scenes": ["Assets/Scenes/StartMenu.unity", "Assets/Scenes/小U和电机.unity"], "dependencyType": 0, "icon": null}, {"name": "Component", "scenes": [], "dependencyType": 1, "icon": null}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "scenes": [], "dependencyType": 0, "icon": null}, {"name": "Cubemap", "scenes": ["Assets/Scenes/StartMenu.unity", "Assets/Scenes/小U和电机.unity"], "dependencyType": 0, "icon": null}, {"name": "CubemapArray", "scenes": [], "dependencyType": 0, "icon": null}, {"name": "DelayedCallManager", "scenes": [], "dependencyType": 1, "icon": null}, {"name": "EditorExtension", "scenes": ["Assets/Scenes/StartMenu.unity", "Assets/Scenes/小U和电机.unity"], "dependencyType": 1, "icon": null}, {"name": "GameManager", "scenes": [], "dependencyType": 1, "icon": null}, {"name": "GameObject", "scenes": ["Assets/Scenes/StartMenu.unity", "Assets/Scenes/小U和电机.unity"], "dependencyType": 0, "icon": null}, {"name": "GlobalGameManager", "scenes": [], "dependencyType": 1, "icon": null}, {"name": "GraphicsSettings", "scenes": [], "dependencyType": 0, "icon": null}, {"name": "InputManager", "scenes": [], "dependencyType": 1, "icon": null}, {"name": "LevelGameManager", "scenes": ["Assets/Scenes/StartMenu.unity", "Assets/Scenes/小U和电机.unity"], "dependencyType": 1, "icon": null}, {"name": "Light", "scenes": ["Assets/Scenes/StartMenu.unity", "Assets/Scenes/小U和电机.unity"], "dependencyType": 0, "icon": null}, {"name": "LightingSettings", "scenes": [], "dependencyType": 0, "icon": null}, {"name": "LightmapSettings", "scenes": ["Assets/Scenes/StartMenu.unity", "Assets/Scenes/小U和电机.unity"], "dependencyType": 0, "icon": null}, {"name": "LightProbes", "scenes": [], "dependencyType": 0, "icon": null}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "scenes": ["Assets/Scenes/StartMenu.unity", "Assets/Scenes/小U和电机.unity"], "dependencyType": 0, "icon": null}, {"name": "LowerResBlitTexture", "scenes": [], "dependencyType": 0, "icon": null}, {"name": "Material", "scenes": ["Assets/Scenes/StartMenu.unity", "Assets/Scenes/小U和电机.unity"], "dependencyType": 0, "icon": null}, {"name": "<PERSON><PERSON>", "scenes": ["Assets/Scenes/StartMenu.unity", "Assets/Scenes/小U和电机.unity"], "dependencyType": 0, "icon": null}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "scenes": ["Assets/Scenes/StartMenu.unity", "Assets/Scenes/小U和电机.unity"], "dependencyType": 0, "icon": null}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "scenes": ["Assets/Scenes/StartMenu.unity", "Assets/Scenes/小U和电机.unity"], "dependencyType": 0, "icon": null}, {"name": "MonoBehaviour", "scenes": ["Assets/Scenes/StartMenu.unity", "Assets/Scenes/小U和电机.unity"], "dependencyType": 0, "icon": null}, {"name": "MonoManager", "scenes": [], "dependencyType": 1, "icon": null}, {"name": "MonoScript", "scenes": ["Assets/Scenes/StartMenu.unity", "Assets/Scenes/小U和电机.unity"], "dependencyType": 1, "icon": null}, {"name": "NamedObject", "scenes": ["Assets/Scenes/StartMenu.unity", "Assets/Scenes/小U和电机.unity"], "dependencyType": 1, "icon": null}, {"name": "Object", "scenes": [], "dependencyType": 1, "icon": null}, {"name": "PlayerSettings", "scenes": [], "dependencyType": 1, "icon": null}, {"name": "PreloadData", "scenes": [], "dependencyType": 0, "icon": null}, {"name": "QualitySettings", "scenes": [], "dependencyType": 0, "icon": null}, {"name": "RectTransform", "scenes": ["Assets/Scenes/StartMenu.unity", "Assets/Scenes/小U和电机.unity"], "dependencyType": 0, "icon": null}, {"name": "ReflectionProbe", "scenes": [], "dependencyType": 0, "icon": null}, {"name": "<PERSON><PERSON><PERSON>", "scenes": [], "dependencyType": 1, "icon": null}, {"name": "RenderSettings", "scenes": ["Assets/Scenes/StartMenu.unity", "Assets/Scenes/小U和电机.unity"], "dependencyType": 0, "icon": null}, {"name": "RenderTexture", "scenes": [], "dependencyType": 0, "icon": null}, {"name": "ResourceManager", "scenes": [], "dependencyType": 1, "icon": null}, {"name": "RuntimeInitializeOnLoadManager", "scenes": [], "dependencyType": 1, "icon": null}, {"name": "Shader", "scenes": ["Assets/Scenes/StartMenu.unity", "Assets/Scenes/小U和电机.unity"], "dependencyType": 0, "icon": null}, {"name": "ShaderNameRegistry", "scenes": [], "dependencyType": 1, "icon": null}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "scenes": ["Assets/Scenes/StartMenu.unity", "Assets/Scenes/小U和电机.unity"], "dependencyType": 0, "icon": null}, {"name": "SortingGroup", "scenes": ["Assets/Scenes/StartMenu.unity", "Assets/Scenes/小U和电机.unity"], "dependencyType": 0, "icon": null}, {"name": "Sprite", "scenes": ["Assets/Scenes/StartMenu.unity", "Assets/Scenes/小U和电机.unity"], "dependencyType": 0, "icon": null}, {"name": "SpriteAtlas", "scenes": [], "dependencyType": 0, "icon": null}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "scenes": [], "dependencyType": 0, "icon": null}, {"name": "TagManager", "scenes": [], "dependencyType": 1, "icon": null}, {"name": "TextAsset", "scenes": [], "dependencyType": 1, "icon": null}, {"name": "Texture", "scenes": [], "dependencyType": 1, "icon": null}, {"name": "Texture2D", "scenes": [], "dependencyType": 1, "icon": null}, {"name": "Texture2DArray", "scenes": [], "dependencyType": 0, "icon": null}, {"name": "Texture3D", "scenes": [], "dependencyType": 0, "icon": null}, {"name": "TimeManager", "scenes": [], "dependencyType": 1, "icon": null}, {"name": "Transform", "scenes": [], "dependencyType": 1, "icon": null}, {"name": "Required by AndroidJNI Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.androidjni"}, {"name": "Required by Animation Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.animation"}, {"name": "Required by Audio Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.audio"}, {"name": "Required by ImageConversion Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.imageconversion"}, {"name": "Required by IMGUI Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.imgui"}, {"name": "Required by Input Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.input"}, {"name": "Required by InputLegacy Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.inputlegacy"}, {"name": "Required by JSONSerialize Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.jsonserialize"}, {"name": "Required by PerformanceReporting Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.performancereporting"}, {"name": "Required by Physics Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.physics"}, {"name": "Required by Physics2D Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.physics2d"}, {"name": "Required by RuntimeInitializeOnLoadManagerInitializer Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.runtimeinitializeonloadmanagerinitializer"}, {"name": "Required by Subsystems Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.subsystems"}, {"name": "Required by TextCoreFontEngine Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.textcorefontengine"}, {"name": "Required by TextCoreTextEngine Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.textcoretextengine"}, {"name": "Required by TextRendering Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.textrendering"}, {"name": "Required by TLS Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.tls"}, {"name": "Required by UI Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.ui"}, {"name": "Required by UIElements <PERSON>", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.uielements"}, {"name": "Required by UIElementsNative Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.uielementsnative"}, {"name": "Required by UnityAnalytics Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.unityanalytics"}, {"name": "Required by UnityAnalyticsCommon <PERSON>dule", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.unityanalyticscommon"}, {"name": "Required by UnityConnect Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.unityconnect"}, {"name": "Required by UnityWebRequest Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.unitywebrequest"}, {"name": "Required by UnityWebRequestTexture Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.unitywebrequesttexture"}, {"name": "Required by VR Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.vr"}, {"name": "Required by XR Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.xr"}]}, {"name": "ImageConversion", "dependencies": [{"name": "Required by UnityWebRequestTexture Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.unitywebrequesttexture"}]}, {"name": "IMGUI", "dependencies": [{"name": "Required by UIElements <PERSON>", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.uielements"}, {"name": "Required by UIElementsNative Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.uielementsnative"}]}, {"name": "Input", "dependencies": [{"name": "Required by VR Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.vr"}, {"name": "Required by XR Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.xr"}]}, {"name": "InputLegacy", "dependencies": [{"name": "Required by IMGUI Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.imgui"}, {"name": "Required by UIElements <PERSON>", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.uielements"}, {"name": "Required by UIElementsNative Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.uielementsnative"}]}, {"name": "JSONSerialize", "dependencies": [{"name": "Required by Input Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.input"}, {"name": "Required by PerformanceReporting Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.performancereporting"}, {"name": "Required by RuntimeInitializeOnLoadManagerInitializer Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.runtimeinitializeonloadmanagerinitializer"}, {"name": "Required by Subsystems Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.subsystems"}, {"name": "Required by UIElements <PERSON>", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.uielements"}, {"name": "Required by UIElementsNative Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.uielementsnative"}, {"name": "Required by UnityAnalytics Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.unityanalytics"}, {"name": "Required by VR Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.vr"}, {"name": "Required by XR Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.xr"}]}, {"name": "PerformanceReporting", "dependencies": [{"name": "Required by Analytics Performance Reporting (See Analytics Services Window)", "scenes": [], "dependencyType": 3, "icon": "class/PlayerSettings"}, {"name": "Required by UnityAnalytics Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.unityanalytics"}]}, {"name": "Physics", "dependencies": [{"name": "BoxCollider", "scenes": ["Assets/Scenes/StartMenu.unity"], "dependencyType": 0, "icon": null}, {"name": "CapsuleCollider", "scenes": ["Assets/Scenes/StartMenu.unity"], "dependencyType": 0, "icon": null}, {"name": "CharacterController", "scenes": [], "dependencyType": 0, "icon": null}, {"name": "Collider", "scenes": [], "dependencyType": 1, "icon": null}, {"name": "MeshCollider", "scenes": ["Assets/Scenes/StartMenu.unity", "Assets/Scenes/小U和电机.unity"], "dependencyType": 0, "icon": null}, {"name": "PhysicsManager", "scenes": [], "dependencyType": 1, "icon": null}, {"name": "Rigidbody", "scenes": ["Assets/Scenes/StartMenu.unity"], "dependencyType": 0, "icon": null}, {"name": "SphereCollider", "scenes": ["Assets/Scenes/StartMenu.unity"], "dependencyType": 0, "icon": null}, {"name": "Required by VR Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.vr"}, {"name": "Required by XR Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.xr"}]}, {"name": "Physics2D", "dependencies": [{"name": "Physics2DSettings", "scenes": [], "dependencyType": 1, "icon": null}]}, {"name": "RuntimeInitializeOnLoadManagerInitializer", "dependencies": [{"name": "RuntimeInitializeOnLoadManagerInitializer is always required", "scenes": [], "dependencyType": 3, "icon": "class/DefaultAsset"}]}, {"name": "SharedInternals", "dependencies": [{"name": "Required by AndroidJNI Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.androidjni"}, {"name": "Required by Animation Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.animation"}, {"name": "Required by Audio Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.audio"}, {"name": "Required by Core Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.core"}, {"name": "Required by ImageConversion Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.imageconversion"}, {"name": "Required by IMGUI Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.imgui"}, {"name": "Required by Input Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.input"}, {"name": "Required by InputLegacy Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.inputlegacy"}, {"name": "Required by JSONSerialize Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.jsonserialize"}, {"name": "Required by PerformanceReporting Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.performancereporting"}, {"name": "Required by Physics Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.physics"}, {"name": "Required by Physics2D Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.physics2d"}, {"name": "Required by RuntimeInitializeOnLoadManagerInitializer Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.runtimeinitializeonloadmanagerinitializer"}, {"name": "Required by Subsystems Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.subsystems"}, {"name": "Required by TextCoreFontEngine Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.textcorefontengine"}, {"name": "Required by TextCoreTextEngine Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.textcoretextengine"}, {"name": "Required by TextRendering Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.textrendering"}, {"name": "Required by TLS Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.tls"}, {"name": "Required by UI Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.ui"}, {"name": "Required by UIElements <PERSON>", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.uielements"}, {"name": "Required by UIElementsNative Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.uielementsnative"}, {"name": "Required by UnityAnalytics Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.unityanalytics"}, {"name": "Required by UnityAnalyticsCommon <PERSON>dule", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.unityanalyticscommon"}, {"name": "Required by UnityConnect Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.unityconnect"}, {"name": "Required by UnityWebRequest Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.unitywebrequest"}, {"name": "Required by UnityWebRequestTexture Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.unitywebrequesttexture"}, {"name": "Required by VR Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.vr"}, {"name": "Required by XR Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.xr"}]}, {"name": "Subsystems", "dependencies": [{"name": "Required by XR Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.xr"}]}, {"name": "TextCoreFontEngine", "dependencies": [{"name": "Required by TextCoreTextEngine Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.textcoretextengine"}, {"name": "Required by UIElements <PERSON>", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.uielements"}]}, {"name": "TextCoreTextEngine", "dependencies": [{"name": "Required by UIElements <PERSON>", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.uielements"}, {"name": "Required by UIElementsNative Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.uielementsnative"}]}, {"name": "TextRendering", "dependencies": [{"name": "Font", "scenes": ["Assets/Scenes/StartMenu.unity", "Assets/Scenes/小U和电机.unity"], "dependencyType": 0, "icon": null}, {"name": "Required by IMGUI Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.imgui"}, {"name": "Required by TextCoreFontEngine Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.textcorefontengine"}, {"name": "Required by TextCoreTextEngine Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.textcoretextengine"}, {"name": "Required by UI Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.ui"}, {"name": "Required by UIElements <PERSON>", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.uielements"}, {"name": "Required by UIElementsNative Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.uielementsnative"}]}, {"name": "TLS", "dependencies": [{"name": "Required by UnityWebRequest Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.unitywebrequest"}]}, {"name": "UI", "dependencies": [{"name": "<PERSON><PERSON>", "scenes": ["Assets/Scenes/StartMenu.unity", "Assets/Scenes/小U和电机.unity"], "dependencyType": 0, "icon": null}, {"name": "CanvasGroup", "scenes": [], "dependencyType": 0, "icon": null}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "scenes": ["Assets/Scenes/StartMenu.unity", "Assets/Scenes/小U和电机.unity"], "dependencyType": 0, "icon": null}, {"name": "Required by UIElements <PERSON>", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.uielements"}, {"name": "Required by UIElementsNative Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.uielementsnative"}]}, {"name": "UIElements", "dependencies": []}, {"name": "UIElementsNative", "dependencies": [{"name": "Required by UIElements <PERSON>", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.uielements"}]}, {"name": "UnityAnalytics", "dependencies": [{"name": "Required by Analytics Performance Reporting (See Analytics Services Window)", "scenes": [], "dependencyType": 3, "icon": "class/PlayerSettings"}, {"name": "Required by PerformanceReporting Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.performancereporting"}, {"name": "Required by UnityAnalytics (See Services Window)", "scenes": [], "dependencyType": 3, "icon": "class/PlayerSettings"}]}, {"name": "UnityAnalyticsCommon", "dependencies": [{"name": "Required by UnityAnalytics Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.unityanalytics"}]}, {"name": "UnityConnect", "dependencies": [{"name": "UnityConnectSettings", "scenes": [], "dependencyType": 1, "icon": null}, {"name": "Required by PerformanceReporting Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.performancereporting"}, {"name": "Required by UnityAnalytics (See Services Window)", "scenes": [], "dependencyType": 3, "icon": "class/PlayerSettings"}, {"name": "Required by UnityAnalytics Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.unityanalytics"}]}, {"name": "UnityWebRequest", "dependencies": [{"name": "Required by UnityAnalytics Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.unityanalytics"}, {"name": "Required by UnityWebRequestTexture Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.unitywebrequesttexture"}]}, {"name": "UnityWebRequestTexture", "dependencies": []}, {"name": "VR", "dependencies": []}, {"name": "XR", "dependencies": [{"name": "Required by VR Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.vr"}]}]}}