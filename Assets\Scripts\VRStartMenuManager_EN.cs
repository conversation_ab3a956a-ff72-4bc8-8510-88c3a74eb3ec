using UnityEngine;
using UnityEngine.UI;
using UnityEngine.SceneManagement;
using TMPro;
using System.Collections;

/// <summary>
/// VR Start Menu Manager
/// 
/// Start menu management system designed specifically for PICO VR environment
/// Provides username input, scene switching and VR interaction functions
/// </summary>
public class VRStartMenuManager_EN : MonoBehaviour
{
    [Header("UI Components")]
    [SerializeField] private Canvas menuCanvas;
    [SerializeField] private TextMeshProUGUI titleText;
    [SerializeField] private TMP_InputField usernameInput;
    [SerializeField] private Button startButton;
    [SerializeField] private TextMeshProUGUI welcomeText;
    
    [Header("VR Settings")]
    [SerializeField] private bool enableVRMode = true;
    [SerializeField] private Camera vrCamera;
    [SerializeField] private float uiDistance = 2.5f;
    [SerializeField] private float uiScale = 0.01f;
    [SerializeField] private Vector3 uiOffset = new Vector3(0, 0.2f, 0);
    
    [Header("Scene Settings")]
    [SerializeField] private string nextSceneName = "Animation";
    [SerializeField] private bool validateUsername = true;
    [SerializeField] private int minUsernameLength = 2;
    
    [Header("Audio Settings")]
    [SerializeField] private AudioSource audioSource;
    [SerializeField] private AudioClip buttonClickSound;
    [SerializeField] private AudioClip errorSound;
    
    [Header("VR UI Components")]
    private VRUIManager vrUIManager;
    private VRUIInteractor vrUIInteractor;
    private VRUILayoutOptimizer vrUIOptimizer;
    
    // Private variables
    private bool isInitialized = false;
    private string currentUsername = "";
    private bool isTransitioning = false;
    
    // Events
    public System.Action<string> OnUsernameChanged;
    public System.Action OnStartButtonClicked;
    public System.Action<string> OnSceneTransition;
    
    void Start()
    {
        StartCoroutine(InitializeStartMenu());
    }
    
    /// <summary>
    /// Initialize start menu
    /// </summary>
    private IEnumerator InitializeStartMenu()
    {
        Debug.Log("[VRStartMenuManager] Starting VR start menu initialization");
        
        // Wait one frame to ensure all components are loaded
        yield return null;
        
        // Find or create necessary components
        SetupComponents();
        
        // Configure VR UI system
        if (enableVRMode)
        {
            SetupVRUI();
        }
        
        // Configure UI elements
        SetupUIElements();
        
        // Setup event listeners
        SetupEventListeners();
        
        // Optimize VR layout
        if (enableVRMode && vrUIOptimizer != null)
        {
            vrUIOptimizer.OptimizeAllUIElements();
        }
        
        isInitialized = true;
        Debug.Log("[VRStartMenuManager] VR start menu initialization completed");
    }
    
    /// <summary>
    /// Setup components
    /// </summary>
    private void SetupComponents()
    {
        // Find camera
        if (vrCamera == null)
        {
            vrCamera = Camera.main;
            if (vrCamera == null)
            {
                vrCamera = FindObjectOfType<Camera>();
            }
        }
        
        // Find Canvas
        if (menuCanvas == null)
        {
            menuCanvas = FindObjectOfType<Canvas>();
        }
        
        // Find audio source
        if (audioSource == null)
        {
            audioSource = GetComponent<AudioSource>();
            if (audioSource == null)
            {
                audioSource = gameObject.AddComponent<AudioSource>();
            }
        }
    }
    
    /// <summary>
    /// Setup VR UI system
    /// </summary>
    private void SetupVRUI()
    {
        if (menuCanvas == null)
        {
            Debug.LogError("[VRStartMenuManager] Cannot find Canvas, unable to setup VR UI");
            return;
        }
        
        // Configure Canvas as World Space
        menuCanvas.renderMode = RenderMode.WorldSpace;
        menuCanvas.worldCamera = vrCamera;
        
        // Add VR UI Manager
        vrUIManager = menuCanvas.GetComponent<VRUIManager>();
        if (vrUIManager == null)
        {
            vrUIManager = menuCanvas.gameObject.AddComponent<VRUIManager>();
        }
        
        // Add VR UI Interactor
        vrUIInteractor = menuCanvas.GetComponent<VRUIInteractor>();
        if (vrUIInteractor == null)
        {
            vrUIInteractor = menuCanvas.gameObject.AddComponent<VRUIInteractor>();
        }
        
        // Add VR UI Layout Optimizer
        vrUIOptimizer = menuCanvas.GetComponent<VRUILayoutOptimizer>();
        if (vrUIOptimizer == null)
        {
            vrUIOptimizer = menuCanvas.gameObject.AddComponent<VRUILayoutOptimizer>();
        }
        
        // Add GraphicRaycaster for VR interaction
        if (!menuCanvas.TryGetComponent<GraphicRaycaster>(out _))
        {
            menuCanvas.gameObject.AddComponent<GraphicRaycaster>();
        }
        
        // Set UI position
        PositionUIForVR();
        
        Debug.Log("[VRStartMenuManager] VR UI system setup completed");
    }
    
    /// <summary>
    /// Position UI for VR
    /// </summary>
    private void PositionUIForVR()
    {
        if (menuCanvas == null || vrCamera == null) return;
        
        Vector3 cameraPosition = vrCamera.transform.position;
        Vector3 cameraForward = vrCamera.transform.forward;
        
        // Calculate UI position
        Vector3 targetPosition = cameraPosition + cameraForward * uiDistance + uiOffset;
        menuCanvas.transform.position = targetPosition;
        
        // Set scale
        menuCanvas.transform.localScale = Vector3.one * uiScale;
        
        // Face user
        menuCanvas.transform.LookAt(vrCamera.transform);
        menuCanvas.transform.Rotate(0, 180, 0); // Flip to face user
    }
    
    /// <summary>
    /// Setup UI elements
    /// </summary>
    private void SetupUIElements()
    {
        // Setup title text
        if (titleText != null)
        {
            titleText.text = "VR Assembly Animation System";
            titleText.fontSize = enableVRMode ? 48 : 36;
        }
        
        // Setup welcome text
        if (welcomeText != null)
        {
            welcomeText.text = "Welcome to VR Assembly Animation System\nPlease enter your username to start";
            welcomeText.fontSize = enableVRMode ? 24 : 18;
        }
        
        // Setup input field
        if (usernameInput != null)
        {
            if (usernameInput.placeholder != null && 
                usernameInput.placeholder.TryGetComponent<TextMeshProUGUI>(out var placeholderText))
            {
                placeholderText.text = "Enter username...";
            }
            
            if (usernameInput.textComponent != null)
            {
                usernameInput.textComponent.fontSize = enableVRMode ? 20 : 16;
            }
        }
        
        // Setup start button
        if (startButton != null)
        {
            var buttonText = startButton.GetComponentInChildren<TextMeshProUGUI>();
            if (buttonText != null)
            {
                buttonText.text = "Start Experience";
                buttonText.fontSize = enableVRMode ? 24 : 18;
            }
            
            // Initially disable button
            startButton.interactable = false;
        }
    }
    
    /// <summary>
    /// Setup event listeners
    /// </summary>
    private void SetupEventListeners()
    {
        // Username input events
        if (usernameInput != null)
        {
            usernameInput.onValueChanged.AddListener(OnUsernameInputChanged);
        }
        
        // Start button click event
        if (startButton != null)
        {
            startButton.onClick.AddListener(OnStartButtonPressed);
        }
    }
    
    /// <summary>
    /// Username input changed event
    /// </summary>
    private void OnUsernameInputChanged(string username)
    {
        currentUsername = username.Trim();
        
        // Validate username
        bool isValid = ValidateUsername(currentUsername);
        
        // Update button state
        if (startButton != null)
        {
            startButton.interactable = isValid;
        }
        
        // Trigger event
        OnUsernameChanged?.Invoke(currentUsername);
        
        Debug.Log($"[VRStartMenuManager] Username input: {currentUsername}, Valid: {isValid}");
    }
    
    /// <summary>
    /// Validate username
    /// </summary>
    private bool ValidateUsername(string username)
    {
        if (!validateUsername) return true;
        
        return !string.IsNullOrEmpty(username) && username.Length >= minUsernameLength;
    }
    
    /// <summary>
    /// Start button pressed event
    /// </summary>
    private void OnStartButtonPressed()
    {
        if (isTransitioning) return;
        
        Debug.Log($"[VRStartMenuManager] Start button clicked, Username: {currentUsername}");
        
        // Play click sound
        PlaySound(buttonClickSound);
        
        // Trigger event
        OnStartButtonClicked?.Invoke();
        
        // Start scene transition
        StartCoroutine(TransitionToNextScene());
    }
    
    /// <summary>
    /// Transition to next scene
    /// </summary>
    private IEnumerator TransitionToNextScene()
    {
        isTransitioning = true;

        // Save username to scene data
        if (!string.IsNullOrEmpty(currentUsername))
        {
            VRSceneManager.Instance.SetSceneData("Username", currentUsername);
            VRSceneManager.Instance.SetSceneData("StartTime", System.DateTime.Now.ToString());
        }

        // Trigger scene transition event
        OnSceneTransition?.Invoke(nextSceneName);

        Debug.Log($"[VRStartMenuManager] Starting transition to scene: {nextSceneName}");

        // Add transition animation here if needed
        yield return new WaitForSeconds(0.5f);

        // Use VRSceneManager to load next scene
        VRSceneManager.Instance.LoadScene(nextSceneName);
    }
    
    /// <summary>
    /// Play sound
    /// </summary>
    private void PlaySound(AudioClip clip)
    {
        if (audioSource != null && clip != null)
        {
            audioSource.PlayOneShot(clip);
        }
    }
    
    /// <summary>
    /// Get current username
    /// </summary>
    public string GetCurrentUsername()
    {
        return currentUsername;
    }
    
    /// <summary>
    /// Set next scene name
    /// </summary>
    public void SetNextSceneName(string sceneName)
    {
        nextSceneName = sceneName;
    }
    
    /// <summary>
    /// Reposition UI to user front
    /// </summary>
    public void RepositionUI()
    {
        if (enableVRMode)
        {
            PositionUIForVR();
        }
    }
    
    void OnDestroy()
    {
        // Clean up event listeners
        if (usernameInput != null)
        {
            usernameInput.onValueChanged.RemoveListener(OnUsernameInputChanged);
        }
        
        if (startButton != null)
        {
            startButton.onClick.RemoveListener(OnStartButtonPressed);
        }
    }
}
