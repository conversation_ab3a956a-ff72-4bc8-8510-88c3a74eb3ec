# VR设置UI布置指导

## 概述
本指导将帮助您在小U和电机场景中创建一个VR设置UI，包括：
- 固定在用户视角左上角的齿轮设置按钮
- 点击后弹出的设置菜单面板
- 使用资源商店的齿轮图标

## 第一步：创建UI结构

### 1. 创建Canvas
```
1. 在场景中右键 → UI → Canvas
2. 重命名为 "VRSettingsCanvas"
3. 设置Canvas组件：
   - Render Mode: World Space
   - Event Camera: 拖入场景中的主摄像机
   - Sorting Layer: Default
   - Order in Layer: 10 (确保在其他UI之上)
```

### 2. 创建设置按钮
```
1. 在VRSettingsCanvas下右键 → UI → Button - TextMeshPro
2. 重命名为 "SettingsButton"
3. 删除按钮下的Text子对象（我们将使用图标）
4. 设置RectTransform：
   - Anchor: Top Left
   - Pos X: 100, Pos Y: -100, Pos Z: 0
   - Width: 80, Height: 80
5. 设置Button组件：
   - Interactable: ✓
   - Transition: Color Tint
   - Normal Color: 白色 (1, 1, 1, 0.8)
   - Highlighted Color: 浅蓝色 (0.8, 0.9, 1, 1)
   - Pressed Color: 深蓝色 (0.6, 0.7, 0.9, 1)
```

### 3. 添加齿轮图标
```
1. 在SettingsButton下右键 → UI → Image
2. 重命名为 "GearIcon"
3. 将资源商店的齿轮图片拖到Image组件的Source Image
4. 设置RectTransform：
   - Anchor: Stretch (填满父对象)
   - Left: 10, Right: 10, Top: 10, Bottom: 10
5. 设置Image组件：
   - Color: 深灰色 (0.3, 0.3, 0.3, 1)
   - Raycast Target: ✗ (取消勾选，让点击穿透到按钮)
```

### 4. 创建设置面板
```
1. 在VRSettingsCanvas下右键 → UI → Panel
2. 重命名为 "SettingsPanel"
3. 设置RectTransform：
   - Anchor: Center
   - Pos X: 0, Pos Y: 0, Pos Z: 0
   - Width: 400, Height: 300
4. 设置Image组件（背景）：
   - Color: 半透明深色 (0.1, 0.1, 0.1, 0.9)
   - Source Image: UI-Panel-Background (Unity默认)
```

### 5. 创建关闭按钮
```
1. 在SettingsPanel下右键 → UI → Button - TextMeshPro
2. 重命名为 "CloseButton"
3. 设置RectTransform：
   - Anchor: Top Right
   - Pos X: -20, Pos Y: -20, Pos Z: 0
   - Width: 40, Height: 40
4. 设置Text内容为 "×" 或使用关闭图标
5. 设置Text样式：
   - Font Size: 24
   - Color: 白色
   - Alignment: Center
```

## 第二步：应用VRSettingsUI脚本

### 1. 添加脚本组件
```
1. 选择VRSettingsCanvas
2. 在Inspector中点击 "Add Component"
3. 搜索并添加 "VRSettingsUI" 脚本
```

### 2. 配置脚本参数
```
UI组件引用：
- Settings Canvas: 拖入VRSettingsCanvas
- Settings Button: 拖入SettingsButton
- Settings Panel: 拖入SettingsPanel
- Close Button: 拖入CloseButton

设置按钮配置：
- Button Offset: (-0.8, 0.4, 1.5) // 左上角位置
- Button Scale: (0.5, 0.5, 0.5) // 适中大小
- Follow Camera Rotation: ✗ // 始终面向用户

设置面板配置：
- Panel Offset: (0, 0, 2) // 用户前方2米
- Panel Scale: (1, 1, 1) // 正常大小
- Panel Face Camera: ✓ // 始终面向用户

动画设置：
- Enable Animations: ✓
- Animation Duration: 0.3
- Animation Curve: EaseInOut

音效设置：
- Audio Source: 添加AudioSource组件并拖入
- Button Click Sound: 拖入点击音效
- Panel Open Sound: 拖入打开音效
- Panel Close Sound: 拖入关闭音效
```

## 第三步：添加设置菜单内容

### 1. 创建菜单标题
```
1. 在SettingsPanel下右键 → UI → Text - TextMeshPro
2. 重命名为 "TitleText"
3. 设置内容为 "设置"
4. 设置RectTransform：
   - Anchor: Top Center
   - Pos X: 0, Pos Y: -30, Pos Z: 0
   - Width: 200, Height: 40
5. 设置文本样式：
   - Font Size: 28
   - Color: 白色
   - Alignment: Center
```

### 2. 创建示例按钮
```
1. 在SettingsPanel下右键 → UI → Button - TextMeshPro
2. 重命名为 "ExampleButton"
3. 设置Text内容为 "示例功能"
4. 设置RectTransform：
   - Anchor: Center
   - Pos X: 0, Pos Y: 0, Pos Z: 0
   - Width: 200, Height: 50
5. 根据需要添加更多按钮
```

## 第四步：测试和调整

### 1. 运行测试
```
1. 进入Play模式
2. 观察设置按钮是否出现在左上角
3. 点击设置按钮测试面板开关
4. 检查UI是否跟随头部移动
```

### 2. 位置调整
```
如果位置不合适，调整VRSettingsUI脚本中的参数：
- Button Offset: 调整按钮相对位置
- Panel Offset: 调整面板相对位置
- 缩放参数: 调整UI大小
```

### 3. VR环境优化
```
1. 确保Canvas的Event Camera设置正确
2. 检查UI元素的Raycast Target设置
3. 测试VR控制器的射线交互
4. 调整UI距离避免过近或过远
```

## 第五步：扩展功能

### 1. 添加更多设置选项
```
可以在设置面板中添加：
- 音量控制滑块
- 画质设置下拉菜单
- 返回主菜单按钮
- 重新开始按钮
- 帮助说明按钮
```

### 2. 数据持久化
```
使用PlayerPrefs保存用户设置：
PlayerPrefs.SetFloat("Volume", volumeValue);
PlayerPrefs.SetInt("Quality", qualityIndex);
```

### 3. 与摄像机记录器集成
```
可以在设置面板中添加：
- 开始/停止记录按钮
- 记录状态显示
- 保存数据按钮
```

## 注意事项

1. **VR适配**：确保UI元素大小适合VR环境，不要太小或太大
2. **性能优化**：避免在Update中进行复杂计算
3. **用户体验**：保持UI简洁，避免过多选项
4. **测试**：在实际VR设备上测试交互效果
5. **可访问性**：确保按钮足够大，易于点击

## 常见问题

**Q: 设置按钮不跟随头部移动？**
A: 检查VRSettingsUI脚本是否正确获取到VR摄像机引用

**Q: 点击没有反应？**
A: 检查Canvas的Event Camera设置和UI元素的Raycast Target

**Q: UI太近或太远？**
A: 调整Button Offset和Panel Offset的Z值

**Q: 齿轮图标显示不正确？**
A: 确保图片导入设置为Sprite (2D and UI)模式
