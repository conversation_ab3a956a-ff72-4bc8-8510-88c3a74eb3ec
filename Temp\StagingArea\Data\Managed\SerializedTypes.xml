<linker>
	<assembly fullname="Unity.InputSystem">
		<type fullname="UnityEngine.InputSystem.InputAction" preserve="nothing" serialized="true"/>
		<type fullname="UnityEngine.InputSystem.InputActionMap" preserve="nothing" serialized="true"/>
		<type fullname="UnityEngine.InputSystem.InputActionProperty" preserve="nothing" serialized="true"/>
		<type fullname="UnityEngine.InputSystem.InputBinding" preserve="nothing" serialized="true"/>
		<type fullname="UnityEngine.InputSystem.InputControlScheme" preserve="nothing" serialized="true"/>
		<type fullname="UnityEngine.InputSystem.InputControlScheme/DeviceRequirement" preserve="nothing" serialized="true"/>
	</assembly>
	<assembly fullname="Unity.TextMeshPro">
		<type fullname="TMPro.FaceInfo_Legacy" preserve="nothing" serialized="true"/>
		<type fullname="TMPro.FontAssetCreationSettings" preserve="nothing" serialized="true"/>
		<type fullname="TMPro.KerningTable" preserve="nothing" serialized="true"/>
		<type fullname="TMPro.TMP_Character" preserve="nothing" serialized="true"/>
		<type fullname="TMPro.TMP_FontFeatureTable" preserve="nothing" serialized="true"/>
		<type fullname="TMPro.TMP_FontWeightPair" preserve="nothing" serialized="true"/>
		<type fullname="TMPro.TMP_GlyphAdjustmentRecord" preserve="nothing" serialized="true"/>
		<type fullname="TMPro.TMP_GlyphPairAdjustmentRecord" preserve="nothing" serialized="true"/>
		<type fullname="TMPro.TMP_GlyphValueRecord" preserve="nothing" serialized="true"/>
		<type fullname="TMPro.TMP_InputField/OnChangeEvent" preserve="nothing" serialized="true"/>
		<type fullname="TMPro.TMP_InputField/SelectionEvent" preserve="nothing" serialized="true"/>
		<type fullname="TMPro.TMP_InputField/SubmitEvent" preserve="nothing" serialized="true"/>
		<type fullname="TMPro.TMP_InputField/TextSelectionEvent" preserve="nothing" serialized="true"/>
		<type fullname="TMPro.TMP_InputField/TouchScreenKeyboardEvent" preserve="nothing" serialized="true"/>
		<type fullname="TMPro.TMP_Sprite" preserve="nothing" serialized="true"/>
		<type fullname="TMPro.TMP_SpriteCharacter" preserve="nothing" serialized="true"/>
		<type fullname="TMPro.TMP_SpriteGlyph" preserve="nothing" serialized="true"/>
		<type fullname="TMPro.TMP_Style" preserve="nothing" serialized="true"/>
		<type fullname="TMPro.VertexGradient" preserve="nothing" serialized="true"/>
	</assembly>
	<assembly fullname="Unity.XR.Interaction.Toolkit">
		<type fullname="UnityEngine.XR.Interaction.Toolkit.ActivateEvent" preserve="nothing" serialized="true"/>
		<type fullname="UnityEngine.XR.Interaction.Toolkit.DeactivateEvent" preserve="nothing" serialized="true"/>
		<type fullname="UnityEngine.XR.Interaction.Toolkit.FocusEnterEvent" preserve="nothing" serialized="true"/>
		<type fullname="UnityEngine.XR.Interaction.Toolkit.FocusExitEvent" preserve="nothing" serialized="true"/>
		<type fullname="UnityEngine.XR.Interaction.Toolkit.HoverEnterEvent" preserve="nothing" serialized="true"/>
		<type fullname="UnityEngine.XR.Interaction.Toolkit.HoverExitEvent" preserve="nothing" serialized="true"/>
		<type fullname="UnityEngine.XR.Interaction.Toolkit.InteractionLayerMask" preserve="nothing" serialized="true"/>
		<type fullname="UnityEngine.XR.Interaction.Toolkit.SelectEnterEvent" preserve="nothing" serialized="true"/>
		<type fullname="UnityEngine.XR.Interaction.Toolkit.SelectExitEvent" preserve="nothing" serialized="true"/>
		<type fullname="UnityEngine.XR.Interaction.Toolkit.UI.UIHoverEnterEvent" preserve="nothing" serialized="true"/>
		<type fullname="UnityEngine.XR.Interaction.Toolkit.UI.UIHoverExitEvent" preserve="nothing" serialized="true"/>
		<type fullname="UnityEngine.XR.Interaction.Toolkit.XRInteractableEvent" preserve="nothing" serialized="true"/>
		<type fullname="UnityEngine.XR.Interaction.Toolkit.XRInteractorEvent" preserve="nothing" serialized="true"/>
	</assembly>
	<assembly fullname="Unity.XR.PICO">
		<type fullname="PXR_HandPosePreview/ModelFinger" preserve="nothing" serialized="true"/>
		<type fullname="Unity.XR.PXR.BonesRecognizer/BonesGroup" preserve="nothing" serialized="true"/>
		<type fullname="Unity.XR.PXR.ShapesRecognizer/Finger" preserve="nothing" serialized="true"/>
		<type fullname="Unity.XR.PXR.ShapesRecognizer/FingerConfigs" preserve="nothing" serialized="true"/>
		<type fullname="Unity.XR.PXR.ShapesRecognizer/RangeConfigs" preserve="nothing" serialized="true"/>
		<type fullname="Unity.XR.PXR.ShapesRecognizer/RangeConfigsAbduction" preserve="nothing" serialized="true"/>
	</assembly>
	<assembly fullname="UnityEngine.CoreModule">
		<type fullname="UnityEngine.Events.ArgumentCache" preserve="nothing" serialized="true"/>
		<type fullname="UnityEngine.Events.PersistentCallGroup" preserve="nothing" serialized="true"/>
		<type fullname="UnityEngine.Events.PersistentListenerMode" preserve="nothing" serialized="true"/>
	</assembly>
	<assembly fullname="UnityEngine.TextCoreFontEngineModule">
		<type fullname="UnityEngine.TextCore.FaceInfo" preserve="nothing" serialized="true"/>
		<type fullname="UnityEngine.TextCore.Glyph" preserve="nothing" serialized="true"/>
		<type fullname="UnityEngine.TextCore.GlyphMetrics" preserve="nothing" serialized="true"/>
		<type fullname="UnityEngine.TextCore.GlyphRect" preserve="nothing" serialized="true"/>
	</assembly>
	<assembly fullname="UnityEngine.UI">
		<type fullname="UnityEngine.EventSystems.EventTrigger/Entry" preserve="nothing" serialized="true"/>
		<type fullname="UnityEngine.EventSystems.EventTrigger/TriggerEvent" preserve="nothing" serialized="true"/>
		<type fullname="UnityEngine.UI.AnimationTriggers" preserve="nothing" serialized="true"/>
		<type fullname="UnityEngine.UI.Button/ButtonClickedEvent" preserve="nothing" serialized="true"/>
		<type fullname="UnityEngine.UI.ColorBlock" preserve="nothing" serialized="true"/>
		<type fullname="UnityEngine.UI.FontData" preserve="nothing" serialized="true"/>
		<type fullname="UnityEngine.UI.MaskableGraphic/CullStateChangedEvent" preserve="nothing" serialized="true"/>
		<type fullname="UnityEngine.UI.Navigation" preserve="nothing" serialized="true"/>
		<type fullname="UnityEngine.UI.Slider/SliderEvent" preserve="nothing" serialized="true"/>
		<type fullname="UnityEngine.UI.SpriteState" preserve="nothing" serialized="true"/>
		<type fullname="UnityEngine.UI.Toggle/ToggleEvent" preserve="nothing" serialized="true"/>
	</assembly>
</linker>
