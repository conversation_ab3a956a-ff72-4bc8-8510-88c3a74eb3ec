﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





// 0x00000001 System.Int32 UnityEngine.Touch::get_fingerId()
extern void Touch_get_fingerId_mC1DCE93BFA0574960A3AE5329AE6C5F7E06962BD (void);
// 0x00000002 UnityEngine.Vector2 UnityEngine.Touch::get_position()
extern void Touch_get_position_m41B9EB0F3F3E1BE98CEB388253A9E31979CB964A (void);
// 0x00000003 System.Void UnityEngine.Touch::set_position(UnityEngine.Vector2)
extern void Touch_set_position_mF024C46352D1CB82991022138D48BC84D9248E6B (void);
// 0x00000004 UnityEngine.Vector2 UnityEngine.Touch::get_rawPosition()
extern void Touch_get_rawPosition_m15F230BC7B4B672380BF221E9BA1DC275180863D (void);
// 0x00000005 System.Void UnityEngine.Touch::set_rawPosition(UnityEngine.Vector2)
extern void Touch_set_rawPosition_m734916CD0826F5CD242A0C6647AC55E53272590B (void);
// 0x00000006 UnityEngine.Vector2 UnityEngine.Touch::get_deltaPosition()
extern void Touch_get_deltaPosition_m2D51F960B74C94821ED0F6A09E44C80FD796D299 (void);
// 0x00000007 System.Void UnityEngine.Touch::set_deltaPosition(UnityEngine.Vector2)
extern void Touch_set_deltaPosition_mD2323B6E679DA9CE9FE1E3F4D3E2D12A33328E7A (void);
// 0x00000008 System.Single UnityEngine.Touch::get_deltaTime()
extern void Touch_get_deltaTime_mD07672B54CBA02C226097B54E286C1DFE96EC3BC (void);
// 0x00000009 System.Int32 UnityEngine.Touch::get_tapCount()
extern void Touch_get_tapCount_mE75D2783AC38FCF536C99F36AB9F76AFA3EB7EB6 (void);
// 0x0000000A UnityEngine.TouchPhase UnityEngine.Touch::get_phase()
extern void Touch_get_phase_mB82409FB2BE1C32ABDBA6A72E52A099D28AB70B0 (void);
// 0x0000000B System.Single UnityEngine.Touch::get_pressure()
extern void Touch_get_pressure_mB8214D0E920156CA4679BAC03E86106E8E4BDA5C (void);
// 0x0000000C System.Single UnityEngine.Touch::get_maximumPossiblePressure()
extern void Touch_get_maximumPossiblePressure_m2D147A58465EB39B397722D8597CF9E06AC85FAE (void);
// 0x0000000D UnityEngine.TouchType UnityEngine.Touch::get_type()
extern void Touch_get_type_mB505EF2DCF13160DFA0C6AAF406DCB4CBED20745 (void);
// 0x0000000E System.Single UnityEngine.Touch::get_altitudeAngle()
extern void Touch_get_altitudeAngle_m26DEF010E2CDC23F4FADE8E49A986D557C07D391 (void);
// 0x0000000F System.Single UnityEngine.Touch::get_azimuthAngle()
extern void Touch_get_azimuthAngle_m2F11532183492E608922A2F9D9EC9AC31D34F490 (void);
// 0x00000010 System.Single UnityEngine.Touch::get_radius()
extern void Touch_get_radius_m5BC9C50DABBB17B07742BAFC6CC36A6736AE7960 (void);
// 0x00000011 System.Single UnityEngine.Touch::get_radiusVariance()
extern void Touch_get_radiusVariance_m6F54BE964B91C3B2F8FA2A483E1FDB644B282B21 (void);
// 0x00000012 UnityEngine.GameObject UnityEngine.CameraRaycastHelper::RaycastTry(UnityEngine.Camera,UnityEngine.Ray,System.Single,System.Int32)
extern void CameraRaycastHelper_RaycastTry_m79A654495BD2C09623E9067BCC70D23A0DA3BF58 (void);
// 0x00000013 UnityEngine.GameObject UnityEngine.CameraRaycastHelper::RaycastTry2D(UnityEngine.Camera,UnityEngine.Ray,System.Single,System.Int32)
extern void CameraRaycastHelper_RaycastTry2D_m132832B9171CD030AD231A63BF70D1226ED1F373 (void);
// 0x00000014 UnityEngine.GameObject UnityEngine.CameraRaycastHelper::RaycastTry_Injected(UnityEngine.Camera,UnityEngine.Ray&,System.Single,System.Int32)
extern void CameraRaycastHelper_RaycastTry_Injected_m4A9EA285FB7B24B7B3D894E7EE997B41ED302DEF (void);
// 0x00000015 UnityEngine.GameObject UnityEngine.CameraRaycastHelper::RaycastTry2D_Injected(UnityEngine.Camera,UnityEngine.Ray&,System.Single,System.Int32)
extern void CameraRaycastHelper_RaycastTry2D_Injected_m2620821FE8CB793C314AAE43E3B4C7BEAE5D4C9E (void);
// 0x00000016 System.Boolean UnityEngine.Input::GetKeyInt(UnityEngine.KeyCode)
extern void Input_GetKeyInt_m2FFCC49AF36B74247CC1B412E9787A15D0984E95 (void);
// 0x00000017 System.Boolean UnityEngine.Input::GetKeyDownInt(UnityEngine.KeyCode)
extern void Input_GetKeyDownInt_m0B655F969FCBC011BC2616E3E5A657CF7D76568A (void);
// 0x00000018 System.Single UnityEngine.Input::GetAxis(System.String)
extern void Input_GetAxis_m10372E6C5FF591668D2DC5F58C58D213CC598A62 (void);
// 0x00000019 System.Single UnityEngine.Input::GetAxisRaw(System.String)
extern void Input_GetAxisRaw_m47C0CF8E090561A2F407A4E11D5F2A45044EB8E4 (void);
// 0x0000001A System.Boolean UnityEngine.Input::GetButton(System.String)
extern void Input_GetButton_m2F217DAE69DB3D1324FB848B3C9C84F19A80989E (void);
// 0x0000001B System.Boolean UnityEngine.Input::GetButtonDown(System.String)
extern void Input_GetButtonDown_mEF5F80C9E8F04104E807D9CBD6F70CDB98751579 (void);
// 0x0000001C System.Boolean UnityEngine.Input::GetMouseButton(System.Int32)
extern void Input_GetMouseButton_m4995DD4A2D4F916565C1B1B5AAF7DF17C126B3EA (void);
// 0x0000001D System.Boolean UnityEngine.Input::GetMouseButtonDown(System.Int32)
extern void Input_GetMouseButtonDown_m8DFC792D15FFF15D311614D5CC6C5D055E5A1DE3 (void);
// 0x0000001E System.Boolean UnityEngine.Input::GetMouseButtonUp(System.Int32)
extern void Input_GetMouseButtonUp_mBE89CC9C69BBEA9A863819E77EA54411B0476ED6 (void);
// 0x0000001F System.String[] UnityEngine.Input::GetJoystickNames()
extern void Input_GetJoystickNames_m506FC5C5D06CE7A15EBB9ACEC9DCF546E2DDCC0B (void);
// 0x00000020 UnityEngine.Touch UnityEngine.Input::GetTouch(System.Int32)
extern void Input_GetTouch_m75D99FE801A94279874FA8DC6B6ADAD35F5123B1 (void);
// 0x00000021 System.Boolean UnityEngine.Input::GetKey(UnityEngine.KeyCode)
extern void Input_GetKey_mE5681EF775F3CEBA7EAD7C63984F7B34C8E8D434 (void);
// 0x00000022 System.Boolean UnityEngine.Input::GetKeyDown(UnityEngine.KeyCode)
extern void Input_GetKeyDown_mB237DEA6244132670D38990BAB77D813FBB028D2 (void);
// 0x00000023 UnityEngine.Vector3 UnityEngine.Input::get_mousePosition()
extern void Input_get_mousePosition_mFF21FBD2647DAE2A23BD4C45571CA95D05A0A42C (void);
// 0x00000024 UnityEngine.Vector2 UnityEngine.Input::get_mouseScrollDelta()
extern void Input_get_mouseScrollDelta_mD112408E9182AA0F529179FF31E21D8DCD5CFA74 (void);
// 0x00000025 UnityEngine.IMECompositionMode UnityEngine.Input::get_imeCompositionMode()
extern void Input_get_imeCompositionMode_mAD9C0224B3845A9132D4265AF468FF203AA43BAC (void);
// 0x00000026 System.Void UnityEngine.Input::set_imeCompositionMode(UnityEngine.IMECompositionMode)
extern void Input_set_imeCompositionMode_m0399964447DDFE54E04F516A01696862F7174C9A (void);
// 0x00000027 System.String UnityEngine.Input::get_compositionString()
extern void Input_get_compositionString_mC9E603E4FB61090827F77A3D509BF3AA0A48C9A9 (void);
// 0x00000028 UnityEngine.Vector2 UnityEngine.Input::get_compositionCursorPos()
extern void Input_get_compositionCursorPos_mE1E48997CA0C30D206D08FAF06455123D8D24D15 (void);
// 0x00000029 System.Void UnityEngine.Input::set_compositionCursorPos(UnityEngine.Vector2)
extern void Input_set_compositionCursorPos_m16A856BFBF1DAE42B0089696906F530334861E98 (void);
// 0x0000002A System.Boolean UnityEngine.Input::get_mousePresent()
extern void Input_get_mousePresent_mAD77FFD987CD5B998AFAD4DAECADBC76034026BF (void);
// 0x0000002B System.Int32 UnityEngine.Input::get_touchCount()
extern void Input_get_touchCount_m057388BFC67A0F4CA53764B1022867ED81D01E39 (void);
// 0x0000002C System.Boolean UnityEngine.Input::get_touchSupported()
extern void Input_get_touchSupported_m2A4FA398A793861AE1BC5971A1363552AB33BEEF (void);
// 0x0000002D System.Boolean UnityEngine.Input::CheckDisabled()
extern void Input_CheckDisabled_m359B281F7F5DDAB74780E1898311AECD9B0ECCE1 (void);
// 0x0000002E System.Void UnityEngine.Input::GetTouch_Injected(System.Int32,UnityEngine.Touch&)
extern void Input_GetTouch_Injected_m04E25DD035583531339AB310FBDD4F5A30817F87 (void);
// 0x0000002F System.Void UnityEngine.Input::get_mousePosition_Injected(UnityEngine.Vector3&)
extern void Input_get_mousePosition_Injected_m7EF43ADB535051F9182A366CA84951F946984E1A (void);
// 0x00000030 System.Void UnityEngine.Input::get_mouseScrollDelta_Injected(UnityEngine.Vector2&)
extern void Input_get_mouseScrollDelta_Injected_m31BF633C98E1BBA4583E7FCE0573BDECB1BA4A29 (void);
// 0x00000031 System.Void UnityEngine.Input::get_compositionCursorPos_Injected(UnityEngine.Vector2&)
extern void Input_get_compositionCursorPos_Injected_m67C1CB8A21F4708CA76FAB39E3BC436DE33C214E (void);
// 0x00000032 System.Void UnityEngine.Input::set_compositionCursorPos_Injected(UnityEngine.Vector2&)
extern void Input_set_compositionCursorPos_Injected_m46E4934CD2A9F2E97B8A86D52169C848EF6D91E8 (void);
// 0x00000033 System.Void UnityEngine.SendMouseEvents::UpdateMouse()
extern void SendMouseEvents_UpdateMouse_m7EC9A21B75612D3AA9ECEE2BB142A27481147FF1 (void);
// 0x00000034 System.Void UnityEngine.SendMouseEvents::SetMouseMoved()
extern void SendMouseEvents_SetMouseMoved_mDA82278267CC62E9942C9D6154610AD7F3308B51 (void);
// 0x00000035 System.Void UnityEngine.SendMouseEvents::DoSendMouseEvents(System.Int32)
extern void SendMouseEvents_DoSendMouseEvents_m17FCC3A684C7BC4A7A6AA7EBB62E3F56AAB416A7 (void);
// 0x00000036 System.Void UnityEngine.SendMouseEvents::SendEvents(System.Int32,UnityEngine.SendMouseEvents/HitInfo)
extern void SendMouseEvents_SendEvents_m3DA609154485AAA0F9501BAA602F63A9E357D35C (void);
// 0x00000037 System.Void UnityEngine.SendMouseEvents::.cctor()
extern void SendMouseEvents__cctor_m6B1E043BF3142442AC8312E9B28A54C487A5A755 (void);
// 0x00000038 System.Void UnityEngine.SendMouseEvents/HitInfo::SendMessage(System.String)
extern void HitInfo_SendMessage_m7834418ACE250BBCBA38ADCF0892E475BD1AD541 (void);
// 0x00000039 System.Boolean UnityEngine.SendMouseEvents/HitInfo::op_Implicit(UnityEngine.SendMouseEvents/HitInfo)
extern void HitInfo_op_Implicit_m4162F5E6640E1D2CB82AB0AE00090AB46CE997AC (void);
// 0x0000003A System.Boolean UnityEngine.SendMouseEvents/HitInfo::Compare(UnityEngine.SendMouseEvents/HitInfo,UnityEngine.SendMouseEvents/HitInfo)
extern void HitInfo_Compare_m374F9DF7CFE9C31264CD38D42FFFCA4DB0E6CD05 (void);
static Il2CppMethodPointer s_methodPointers[58] = 
{
	Touch_get_fingerId_mC1DCE93BFA0574960A3AE5329AE6C5F7E06962BD,
	Touch_get_position_m41B9EB0F3F3E1BE98CEB388253A9E31979CB964A,
	Touch_set_position_mF024C46352D1CB82991022138D48BC84D9248E6B,
	Touch_get_rawPosition_m15F230BC7B4B672380BF221E9BA1DC275180863D,
	Touch_set_rawPosition_m734916CD0826F5CD242A0C6647AC55E53272590B,
	Touch_get_deltaPosition_m2D51F960B74C94821ED0F6A09E44C80FD796D299,
	Touch_set_deltaPosition_mD2323B6E679DA9CE9FE1E3F4D3E2D12A33328E7A,
	Touch_get_deltaTime_mD07672B54CBA02C226097B54E286C1DFE96EC3BC,
	Touch_get_tapCount_mE75D2783AC38FCF536C99F36AB9F76AFA3EB7EB6,
	Touch_get_phase_mB82409FB2BE1C32ABDBA6A72E52A099D28AB70B0,
	Touch_get_pressure_mB8214D0E920156CA4679BAC03E86106E8E4BDA5C,
	Touch_get_maximumPossiblePressure_m2D147A58465EB39B397722D8597CF9E06AC85FAE,
	Touch_get_type_mB505EF2DCF13160DFA0C6AAF406DCB4CBED20745,
	Touch_get_altitudeAngle_m26DEF010E2CDC23F4FADE8E49A986D557C07D391,
	Touch_get_azimuthAngle_m2F11532183492E608922A2F9D9EC9AC31D34F490,
	Touch_get_radius_m5BC9C50DABBB17B07742BAFC6CC36A6736AE7960,
	Touch_get_radiusVariance_m6F54BE964B91C3B2F8FA2A483E1FDB644B282B21,
	CameraRaycastHelper_RaycastTry_m79A654495BD2C09623E9067BCC70D23A0DA3BF58,
	CameraRaycastHelper_RaycastTry2D_m132832B9171CD030AD231A63BF70D1226ED1F373,
	CameraRaycastHelper_RaycastTry_Injected_m4A9EA285FB7B24B7B3D894E7EE997B41ED302DEF,
	CameraRaycastHelper_RaycastTry2D_Injected_m2620821FE8CB793C314AAE43E3B4C7BEAE5D4C9E,
	Input_GetKeyInt_m2FFCC49AF36B74247CC1B412E9787A15D0984E95,
	Input_GetKeyDownInt_m0B655F969FCBC011BC2616E3E5A657CF7D76568A,
	Input_GetAxis_m10372E6C5FF591668D2DC5F58C58D213CC598A62,
	Input_GetAxisRaw_m47C0CF8E090561A2F407A4E11D5F2A45044EB8E4,
	Input_GetButton_m2F217DAE69DB3D1324FB848B3C9C84F19A80989E,
	Input_GetButtonDown_mEF5F80C9E8F04104E807D9CBD6F70CDB98751579,
	Input_GetMouseButton_m4995DD4A2D4F916565C1B1B5AAF7DF17C126B3EA,
	Input_GetMouseButtonDown_m8DFC792D15FFF15D311614D5CC6C5D055E5A1DE3,
	Input_GetMouseButtonUp_mBE89CC9C69BBEA9A863819E77EA54411B0476ED6,
	Input_GetJoystickNames_m506FC5C5D06CE7A15EBB9ACEC9DCF546E2DDCC0B,
	Input_GetTouch_m75D99FE801A94279874FA8DC6B6ADAD35F5123B1,
	Input_GetKey_mE5681EF775F3CEBA7EAD7C63984F7B34C8E8D434,
	Input_GetKeyDown_mB237DEA6244132670D38990BAB77D813FBB028D2,
	Input_get_mousePosition_mFF21FBD2647DAE2A23BD4C45571CA95D05A0A42C,
	Input_get_mouseScrollDelta_mD112408E9182AA0F529179FF31E21D8DCD5CFA74,
	Input_get_imeCompositionMode_mAD9C0224B3845A9132D4265AF468FF203AA43BAC,
	Input_set_imeCompositionMode_m0399964447DDFE54E04F516A01696862F7174C9A,
	Input_get_compositionString_mC9E603E4FB61090827F77A3D509BF3AA0A48C9A9,
	Input_get_compositionCursorPos_mE1E48997CA0C30D206D08FAF06455123D8D24D15,
	Input_set_compositionCursorPos_m16A856BFBF1DAE42B0089696906F530334861E98,
	Input_get_mousePresent_mAD77FFD987CD5B998AFAD4DAECADBC76034026BF,
	Input_get_touchCount_m057388BFC67A0F4CA53764B1022867ED81D01E39,
	Input_get_touchSupported_m2A4FA398A793861AE1BC5971A1363552AB33BEEF,
	Input_CheckDisabled_m359B281F7F5DDAB74780E1898311AECD9B0ECCE1,
	Input_GetTouch_Injected_m04E25DD035583531339AB310FBDD4F5A30817F87,
	Input_get_mousePosition_Injected_m7EF43ADB535051F9182A366CA84951F946984E1A,
	Input_get_mouseScrollDelta_Injected_m31BF633C98E1BBA4583E7FCE0573BDECB1BA4A29,
	Input_get_compositionCursorPos_Injected_m67C1CB8A21F4708CA76FAB39E3BC436DE33C214E,
	Input_set_compositionCursorPos_Injected_m46E4934CD2A9F2E97B8A86D52169C848EF6D91E8,
	SendMouseEvents_UpdateMouse_m7EC9A21B75612D3AA9ECEE2BB142A27481147FF1,
	SendMouseEvents_SetMouseMoved_mDA82278267CC62E9942C9D6154610AD7F3308B51,
	SendMouseEvents_DoSendMouseEvents_m17FCC3A684C7BC4A7A6AA7EBB62E3F56AAB416A7,
	SendMouseEvents_SendEvents_m3DA609154485AAA0F9501BAA602F63A9E357D35C,
	SendMouseEvents__cctor_m6B1E043BF3142442AC8312E9B28A54C487A5A755,
	HitInfo_SendMessage_m7834418ACE250BBCBA38ADCF0892E475BD1AD541,
	HitInfo_op_Implicit_m4162F5E6640E1D2CB82AB0AE00090AB46CE997AC,
	HitInfo_Compare_m374F9DF7CFE9C31264CD38D42FFFCA4DB0E6CD05,
};
extern void Touch_get_fingerId_mC1DCE93BFA0574960A3AE5329AE6C5F7E06962BD_AdjustorThunk (void);
extern void Touch_get_position_m41B9EB0F3F3E1BE98CEB388253A9E31979CB964A_AdjustorThunk (void);
extern void Touch_set_position_mF024C46352D1CB82991022138D48BC84D9248E6B_AdjustorThunk (void);
extern void Touch_get_rawPosition_m15F230BC7B4B672380BF221E9BA1DC275180863D_AdjustorThunk (void);
extern void Touch_set_rawPosition_m734916CD0826F5CD242A0C6647AC55E53272590B_AdjustorThunk (void);
extern void Touch_get_deltaPosition_m2D51F960B74C94821ED0F6A09E44C80FD796D299_AdjustorThunk (void);
extern void Touch_set_deltaPosition_mD2323B6E679DA9CE9FE1E3F4D3E2D12A33328E7A_AdjustorThunk (void);
extern void Touch_get_deltaTime_mD07672B54CBA02C226097B54E286C1DFE96EC3BC_AdjustorThunk (void);
extern void Touch_get_tapCount_mE75D2783AC38FCF536C99F36AB9F76AFA3EB7EB6_AdjustorThunk (void);
extern void Touch_get_phase_mB82409FB2BE1C32ABDBA6A72E52A099D28AB70B0_AdjustorThunk (void);
extern void Touch_get_pressure_mB8214D0E920156CA4679BAC03E86106E8E4BDA5C_AdjustorThunk (void);
extern void Touch_get_maximumPossiblePressure_m2D147A58465EB39B397722D8597CF9E06AC85FAE_AdjustorThunk (void);
extern void Touch_get_type_mB505EF2DCF13160DFA0C6AAF406DCB4CBED20745_AdjustorThunk (void);
extern void Touch_get_altitudeAngle_m26DEF010E2CDC23F4FADE8E49A986D557C07D391_AdjustorThunk (void);
extern void Touch_get_azimuthAngle_m2F11532183492E608922A2F9D9EC9AC31D34F490_AdjustorThunk (void);
extern void Touch_get_radius_m5BC9C50DABBB17B07742BAFC6CC36A6736AE7960_AdjustorThunk (void);
extern void Touch_get_radiusVariance_m6F54BE964B91C3B2F8FA2A483E1FDB644B282B21_AdjustorThunk (void);
extern void HitInfo_SendMessage_m7834418ACE250BBCBA38ADCF0892E475BD1AD541_AdjustorThunk (void);
static Il2CppTokenAdjustorThunkPair s_adjustorThunks[18] = 
{
	{ 0x06000001, Touch_get_fingerId_mC1DCE93BFA0574960A3AE5329AE6C5F7E06962BD_AdjustorThunk },
	{ 0x06000002, Touch_get_position_m41B9EB0F3F3E1BE98CEB388253A9E31979CB964A_AdjustorThunk },
	{ 0x06000003, Touch_set_position_mF024C46352D1CB82991022138D48BC84D9248E6B_AdjustorThunk },
	{ 0x06000004, Touch_get_rawPosition_m15F230BC7B4B672380BF221E9BA1DC275180863D_AdjustorThunk },
	{ 0x06000005, Touch_set_rawPosition_m734916CD0826F5CD242A0C6647AC55E53272590B_AdjustorThunk },
	{ 0x06000006, Touch_get_deltaPosition_m2D51F960B74C94821ED0F6A09E44C80FD796D299_AdjustorThunk },
	{ 0x06000007, Touch_set_deltaPosition_mD2323B6E679DA9CE9FE1E3F4D3E2D12A33328E7A_AdjustorThunk },
	{ 0x06000008, Touch_get_deltaTime_mD07672B54CBA02C226097B54E286C1DFE96EC3BC_AdjustorThunk },
	{ 0x06000009, Touch_get_tapCount_mE75D2783AC38FCF536C99F36AB9F76AFA3EB7EB6_AdjustorThunk },
	{ 0x0600000A, Touch_get_phase_mB82409FB2BE1C32ABDBA6A72E52A099D28AB70B0_AdjustorThunk },
	{ 0x0600000B, Touch_get_pressure_mB8214D0E920156CA4679BAC03E86106E8E4BDA5C_AdjustorThunk },
	{ 0x0600000C, Touch_get_maximumPossiblePressure_m2D147A58465EB39B397722D8597CF9E06AC85FAE_AdjustorThunk },
	{ 0x0600000D, Touch_get_type_mB505EF2DCF13160DFA0C6AAF406DCB4CBED20745_AdjustorThunk },
	{ 0x0600000E, Touch_get_altitudeAngle_m26DEF010E2CDC23F4FADE8E49A986D557C07D391_AdjustorThunk },
	{ 0x0600000F, Touch_get_azimuthAngle_m2F11532183492E608922A2F9D9EC9AC31D34F490_AdjustorThunk },
	{ 0x06000010, Touch_get_radius_m5BC9C50DABBB17B07742BAFC6CC36A6736AE7960_AdjustorThunk },
	{ 0x06000011, Touch_get_radiusVariance_m6F54BE964B91C3B2F8FA2A483E1FDB644B282B21_AdjustorThunk },
	{ 0x06000038, HitInfo_SendMessage_m7834418ACE250BBCBA38ADCF0892E475BD1AD541_AdjustorThunk },
};
static const int32_t s_InvokerIndices[58] = 
{
	6104,
	6250,
	5024,
	6250,
	5024,
	6250,
	5024,
	6192,
	6104,
	6104,
	6192,
	6192,
	6104,
	6192,
	6192,
	6192,
	6192,
	7355,
	7355,
	7337,
	7337,
	9619,
	9619,
	9950,
	9950,
	9622,
	9622,
	9619,
	9619,
	9619,
	10933,
	9988,
	9619,
	9619,
	10973,
	10972,
	10925,
	10139,
	10933,
	10972,
	10156,
	10903,
	10925,
	10903,
	10903,
	8799,
	10130,
	10130,
	10130,
	10130,
	10975,
	10975,
	10139,
	8807,
	10975,
	4919,
	9653,
	8394,
};
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_UnityEngine_InputLegacyModule_CodeGenModule;
const Il2CppCodeGenModule g_UnityEngine_InputLegacyModule_CodeGenModule = 
{
	"UnityEngine.InputLegacyModule.dll",
	58,
	s_methodPointers,
	18,
	s_adjustorThunks,
	s_InvokerIndices,
	0,
	NULL,
	0,
	NULL,
	0,
	NULL,
	NULL,
	NULL, // module initializer,
	NULL,
	NULL,
	NULL,
};
