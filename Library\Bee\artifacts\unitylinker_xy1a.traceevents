{ "pid": "host", "ph":"M", "name": "process_name", "args": {"name": "host"} },
{ "pid": "host", "ph":"M", "name": "process_sort_index", "args": {"sort_index": 0} },
{ "pid": "host", "tid": 1, "ph":"M", "name": "thread_name", "args": {"name": ""} },
{ "pid": "host", "tid": 1,"ts": 1751352510855682,"dur": 3194614, "ph":"X", "name": "UnityLinker.exe"},
{ "pid": "host", "tid": 1,"ts": 1751352510856113,"dur": 61786, "ph":"X", "name": "Step : Unity.Linker.Steps.SetupAndRegisterUnityEngineSteps"},
{ "pid": "host", "tid": 1,"ts": 1751352510917927,"dur": 14304, "ph":"X", "name": "Step : Unity.Linker.Steps.Rooting.ResolveUnityEngine"},
{ "pid": "host", "tid": 1,"ts": 1751352510932361,"dur": 39405, "ph":"X", "name": "Step : UnityUnityResolveFromXmlStep: D:\\nwu\\Assembly\\UnityProjects\\VRAssembly\\Temp\\StagingArea\\Data\\Managed\\TypesInScenes.xml"},
{ "pid": "host", "tid": 1,"ts": 1751352510971773,"dur": 137547, "ph":"X", "name": "Step : Unity.Linker.Steps.InitializeEngineStrippingStep"},
{ "pid": "host", "tid": 1,"ts": 1751352511109326,"dur": 2256, "ph":"X", "name": "Step : Unity.Linker.Steps.Rooting.ResolveForEngineModuleStrippingEnabledStep"},
{ "pid": "host", "tid": 1,"ts": 1751352511112720,"dur": 3036, "ph":"X", "name": "Step : UnityUnityResolveFromXmlStep: D:\\nwu\\Assembly\\UnityProjects\\VRAssembly\\Temp\\InputSystemLink.xml"},
{ "pid": "host", "tid": 1,"ts": 1751352511116016,"dur": 12140, "ph":"X", "name": "Step : Unity.Linker.Steps.Resolution.ResolveAssemblyDirectoryStep"},
{ "pid": "host", "tid": 1,"ts": 1751352511128162,"dur": 10029, "ph":"X", "name": "Step : Unity.Linker.Steps.SetupAndRegisterUnityRootsSteps"},
{ "pid": "host", "tid": 1,"ts": 1751352511138198,"dur": 5299, "ph":"X", "name": "Step : Mono.Linker.Steps.ResolveFromAssemblyStep"},
{ "pid": "host", "tid": 1,"ts": 1751352511144349,"dur": 2382, "ph":"X", "name": "Step : Mono.Linker.Steps.ResolveFromAssemblyStep"},
{ "pid": "host", "tid": 1,"ts": 1751352511146733,"dur": 2568, "ph":"X", "name": "Step : Mono.Linker.Steps.ResolveFromAssemblyStep"},
{ "pid": "host", "tid": 1,"ts": 1751352511150034,"dur": 1472, "ph":"X", "name": "Step : Mono.Linker.Steps.ResolveFromAssemblyStep"},
{ "pid": "host", "tid": 1,"ts": 1751352511152465,"dur": 1233, "ph":"X", "name": "Step : Mono.Linker.Steps.ResolveFromAssemblyStep"},
{ "pid": "host", "tid": 1,"ts": 1751352511153704,"dur": 1045, "ph":"X", "name": "Step : Unity.Linker.Steps.UnityLoadReferencesStep"},
{ "pid": "host", "tid": 1,"ts": 1751352511156260,"dur": 2667, "ph":"X", "name": "Step : Unity.Linker.Steps.Resolution.ResolveFromDescriptorsStep"},
{ "pid": "host", "tid": 1,"ts": 1751352511159209,"dur": 4129, "ph":"X", "name": "Step : UnityUnityResolveFromXmlStep: C:\\Program Files\\Unity\\Hub\\Editor\\2021.3.44f1c1\\Editor\\Data\\il2cpp\\LinkerDescriptors\\Default\\mscorlib.xml"},
{ "pid": "host", "tid": 1,"ts": 1751352511164587,"dur": 7350, "ph":"X", "name": "Step : Unity.Linker.Steps.Resolution.UnityBlacklistStep"},
{ "pid": "host", "tid": 1,"ts": 1751352511171938,"dur": 22090, "ph":"X", "name": "Step : UnityUnityResolveFromXmlStep: mscorlib Resource: mscorlib.xml"},
{ "pid": "host", "tid": 1,"ts": 1751352511194038,"dur": 200301, "ph":"X", "name": "Step : Mono.Linker.Steps.DynamicDependencyLookupStep"},
{ "pid": "host", "tid": 1,"ts": 1751352511396493,"dur": 3090, "ph":"X", "name": "Step : Unity.Linker.Steps.Rooting.ResolveFromMonoBehaviours"},
{ "pid": "host", "tid": 1,"ts": 1751352511399988,"dur": 128047, "ph":"X", "name": "Step : Unity.Linker.Steps.Rooting.ResolveFromPreserveAttribute"},
{ "pid": "host", "tid": 1,"ts": 1751352511528054,"dur": 7711, "ph":"X", "name": "Step : Unity.Linker.Steps.EngineStrippingAnnotationStep"},
{ "pid": "host", "tid": 1,"ts": 1751352511535780,"dur": 161006, "ph":"X", "name": "Step : Unity.Linker.Steps.UnityTypeMapStep"},
{ "pid": "host", "tid": 1,"ts": 1751352511696981,"dur": 101272, "ph":"X", "name": "Step : Unity.Linker.Steps.Analytics.BeforeMarkAnalyticsStep"},
{ "pid": "host", "tid": 1,"ts": 1751352511798264,"dur": 13915, "ph":"X", "name": "Step : Mono.Linker.Steps.RemoveSecurityStep"},
{ "pid": "host", "tid": 1,"ts": 1751352511812191,"dur": 3527, "ph":"X", "name": "Step : Unity.Linker.Steps.RemoveSecurityFromCopyAssemblies"},
{ "pid": "host", "tid": 1,"ts": 1751352511815725,"dur": 9166, "ph":"X", "name": "Step : Mono.Linker.Steps.RemoveFeaturesStep"},
{ "pid": "host", "tid": 1,"ts": 1751352511824901,"dur": 11423, "ph":"X", "name": "Step : Mono.Linker.Steps.RemoveUnreachableBlocksStep"},
{ "pid": "host", "tid": 1,"ts": 1751352511836333,"dur": 1577005, "ph":"X", "name": "Step : Unity.Linker.Steps.UnityMarkStep"},
{ "pid": "host", "tid": 1,"ts": 1751352513413796,"dur": 3662, "ph":"X", "name": "Step : Mono.Linker.Steps.ProcessWarningsStep"},
{ "pid": "host", "tid": 1,"ts": 1751352513417465,"dur": 57461, "ph":"X", "name": "Step : Unity.Linker.Steps.UnitySweepStep"},
{ "pid": "host", "tid": 1,"ts": 1751352513474936,"dur": 2194, "ph":"X", "name": "Step : Unity.Linker.Steps.UnityCodeRewriterStep"},
{ "pid": "host", "tid": 1,"ts": 1751352513477137,"dur": 2756, "ph":"X", "name": "Step : Mono.Linker.Steps.CleanStep"},
{ "pid": "host", "tid": 1,"ts": 1751352513479899,"dur": 1908, "ph":"X", "name": "Step : Unity.Linker.Steps.StubifyStep"},
{ "pid": "host", "tid": 1,"ts": 1751352513481813,"dur": 1725, "ph":"X", "name": "Step : Unity.Linker.Steps.AddUnresolvedStubsStep"},
{ "pid": "host", "tid": 1,"ts": 1751352513483543,"dur": 2318, "ph":"X", "name": "Step : Unity.Linker.Steps.Analytics.BeforeOutputAnalyticsStep"},
{ "pid": "host", "tid": 1,"ts": 1751352513486536,"dur": 521625, "ph":"X", "name": "Step : Unity.Linker.Steps.UnityOutputStep"},
{ "pid": "host", "tid": 1,"ts": 1751352514008169,"dur": 19129, "ph":"X", "name": "Step : Unity.Linker.Steps.LinkerToEditorDataGenerationStep"},
{ "pid": "host", "tid": 1,"ts": 1751352514030108,"dur": 20187, "ph":"X", "name": "Analytics"},
{ "pid": "host", "tid": 0, "ph":"M", "name": "thread_name", "args": {"name": "GC"} },
{ "pid": "host", "tid": 0,"ts": 1751352510949000,"dur": 2000, "ph":"X", "name": "GC - Gen 0", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC during background GC"}
},
{ "pid": "host", "tid": 0,"ts": 1751352511135000,"dur": 3000, "ph":"X", "name": "GC - Gen 0", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": 0,"ts": 1751352511177000,"dur": 18000, "ph":"X", "name": "GC - Gen 0", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": 0,"ts": 1751352511273000,"dur": 9000, "ph":"X", "name": "GC - Gen 0", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": 0,"ts": 1751352511342000,"dur": 9000, "ph":"X", "name": "GC - Gen 0", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": 0,"ts": 1751352511454000,"dur": 28000, "ph":"X", "name": "GC - Gen 0", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": 0,"ts": 1751352511574000,"dur": 28000, "ph":"X", "name": "GC - Gen 0", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": 0,"ts": 1751352511713000,"dur": 23000, "ph":"X", "name": "GC - Gen 0", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": 0,"ts": 1751352511784000,"dur": 0, "ph":"X", "name": "GC - Gen 0", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": 0,"ts": 1751352511926000,"dur": 5000, "ph":"X", "name": "GC - Gen 0", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": 0,"ts": 1751352511944000,"dur": 2000, "ph":"X", "name": "GC - Gen 0", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": 0,"ts": 1751352511977000,"dur": 1000, "ph":"X", "name": "GC - Gen 0", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": 0,"ts": 1751352512009000,"dur": 0, "ph":"X", "name": "GC - Gen 0", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": 0,"ts": 1751352512023000,"dur": 2000, "ph":"X", "name": "GC - Gen 0", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": 0,"ts": 1751352512043000,"dur": 1000, "ph":"X", "name": "GC - Gen 0", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": 0,"ts": 1751352512080000,"dur": 2000, "ph":"X", "name": "GC - Gen 0", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": 0,"ts": 1751352512099000,"dur": 1000, "ph":"X", "name": "GC - Gen 0", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": 0,"ts": 1751352512144000,"dur": 2000, "ph":"X", "name": "GC - Gen 0", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": 0,"ts": 1751352512340000,"dur": 16000, "ph":"X", "name": "GC - Gen 0", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": 0,"ts": 1751352512478000,"dur": 16000, "ph":"X", "name": "GC - Gen 0", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": 0,"ts": 1751352512891000,"dur": 7000, "ph":"X", "name": "GC - Gen 0", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": 0,"ts": 1751352513098000,"dur": 11000, "ph":"X", "name": "GC - Gen 0", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": 0,"ts": 1751352513271000,"dur": 8000, "ph":"X", "name": "GC - Gen 0", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": 0,"ts": 1751352513444000,"dur": 20000, "ph":"X", "name": "GC - Gen 0", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": 0,"ts": 1751352513774000,"dur": 16000, "ph":"X", "name": "GC - Gen 0", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": 0,"ts": 1751352513895000,"dur": 14000, "ph":"X", "name": "GC - Gen 0", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": 0,"ts": 1751352513955000,"dur": 17000, "ph":"X", "name": "GC - Gen 0", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": -1, "ph":"M", "name": "thread_name", "args": {"name": "GC"} },
{ "pid": "host", "tid": -1,"ts": 1751352511027000,"dur": 4000, "ph":"X", "name": "GC - Gen 1", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": -1,"ts": 1751352511219000,"dur": 18000, "ph":"X", "name": "GC - Gen 1", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": -1,"ts": 1751352511300000,"dur": 27000, "ph":"X", "name": "GC - Gen 1", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": -1,"ts": 1751352511365000,"dur": 26000, "ph":"X", "name": "GC - Gen 1", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": -1,"ts": 1751352511497000,"dur": 30000, "ph":"X", "name": "GC - Gen 1", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": -1,"ts": 1751352511631000,"dur": 55000, "ph":"X", "name": "GC - Gen 1", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": -1,"ts": 1751352511750000,"dur": 23000, "ph":"X", "name": "GC - Gen 1", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": -1,"ts": 1751352511794000,"dur": 1000, "ph":"X", "name": "GC - Gen 1", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": -1,"ts": 1751352511959000,"dur": 5000, "ph":"X", "name": "GC - Gen 1", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": -1,"ts": 1751352511992000,"dur": 3000, "ph":"X", "name": "GC - Gen 1", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": -1,"ts": 1751352512060000,"dur": 2000, "ph":"X", "name": "GC - Gen 1", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": -1,"ts": 1751352512123000,"dur": 6000, "ph":"X", "name": "GC - Gen 1", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": -1,"ts": 1751352512275000,"dur": 7000, "ph":"X", "name": "GC - Gen 1", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": -1,"ts": 1751352512407000,"dur": 16000, "ph":"X", "name": "GC - Gen 1", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": -1,"ts": 1751352512536000,"dur": 18000, "ph":"X", "name": "GC - Gen 1", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": -1,"ts": 1751352512657000,"dur": 24000, "ph":"X", "name": "GC - Gen 1", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": -1,"ts": 1751352512726000,"dur": 17000, "ph":"X", "name": "GC - Gen 1", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": -1,"ts": 1751352512793000,"dur": 20000, "ph":"X", "name": "GC - Gen 1", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": -1,"ts": 1751352513012000,"dur": 31000, "ph":"X", "name": "GC - Gen 1", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": -1,"ts": 1751352513166000,"dur": 38000, "ph":"X", "name": "GC - Gen 1", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": -1,"ts": 1751352513339000,"dur": 21000, "ph":"X", "name": "GC - Gen 1", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": -1,"ts": 1751352513678000,"dur": 42000, "ph":"X", "name": "GC - Gen 1", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": -1,"ts": 1751352513833000,"dur": 21000, "ph":"X", "name": "GC - Gen 1", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": -2, "ph":"M", "name": "thread_name", "args": {"name": "GC"} },
{ "pid": "host", "tid": -2,"ts": 1751352510901000,"dur": 2000, "ph":"X", "name": "GC - Gen 2", "args": { "detail": "Reason: LOH allocation, Type: Background GC"}
},
{ "pid": "host", "tid": -2,"ts": 1751352510945000,"dur": 6000, "ph":"X", "name": "GC - Gen 2", "args": { "detail": "Reason: LOH allocation, Type: Background GC"}
},
{ "pid": "host", "tid": -2,"ts": 1751352511219000,"dur": 38000, "ph":"X", "name": "GC - Gen 2", "args": { "detail": "Reason: SOH allocation, Type: Background GC"}
},
{ "pid": "host", "tid": -2,"ts": 1751352511365000,"dur": 64000, "ph":"X", "name": "GC - Gen 2", "args": { "detail": "Reason: SOH allocation, Type: Background GC"}
},
{ "pid": "host", "tid": -2,"ts": 1751352512536000,"dur": 91000, "ph":"X", "name": "GC - Gen 2", "args": { "detail": "Reason: SOH allocation, Type: Background GC"}
},
