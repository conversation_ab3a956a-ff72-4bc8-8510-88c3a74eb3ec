{ "pid": "host", "ph":"M", "name": "process_name", "args": {"name": "host"} },
{ "pid": "host", "ph":"M", "name": "process_sort_index", "args": {"sort_index": 0} },
{ "pid": "host", "tid": 1, "ph":"M", "name": "thread_name", "args": {"name": ""} },
{ "pid": "host", "tid": 1,"ts": 1751360312404428,"dur": 3211267, "ph":"X", "name": "UnityLinker.exe"},
{ "pid": "host", "tid": 1,"ts": 1751360312404896,"dur": 58609, "ph":"X", "name": "Step : Unity.Linker.Steps.SetupAndRegisterUnityEngineSteps"},
{ "pid": "host", "tid": 1,"ts": 1751360312463530,"dur": 14248, "ph":"X", "name": "Step : Unity.Linker.Steps.Rooting.ResolveUnityEngine"},
{ "pid": "host", "tid": 1,"ts": 1751360312477911,"dur": 32278, "ph":"X", "name": "Step : UnityUnityResolveFromXmlStep: D:\\nwu\\Assembly\\UnityProjects\\VRAssembly\\Temp\\StagingArea\\Data\\Managed\\MethodsToPreserve.xml"},
{ "pid": "host", "tid": 1,"ts": 1751360312510196,"dur": 143478, "ph":"X", "name": "Step : Unity.Linker.Steps.InitializeEngineStrippingStep"},
{ "pid": "host", "tid": 1,"ts": 1751360312653683,"dur": 2272, "ph":"X", "name": "Step : Unity.Linker.Steps.Rooting.ResolveForEngineModuleStrippingEnabledStep"},
{ "pid": "host", "tid": 1,"ts": 1751360312655959,"dur": 8278, "ph":"X", "name": "Step : UnityUnityResolveFromXmlStep: D:\\nwu\\Assembly\\UnityProjects\\VRAssembly\\Temp\\StagingArea\\Data\\Managed\\TypesInScenes.xml"},
{ "pid": "host", "tid": 1,"ts": 1751360312665171,"dur": 2818, "ph":"X", "name": "Step : UnityUnityResolveFromXmlStep: D:\\nwu\\Assembly\\UnityProjects\\VRAssembly\\Temp\\InputSystemLink.xml"},
{ "pid": "host", "tid": 1,"ts": 1751360312668221,"dur": 12310, "ph":"X", "name": "Step : Unity.Linker.Steps.Resolution.ResolveAssemblyDirectoryStep"},
{ "pid": "host", "tid": 1,"ts": 1751360312680536,"dur": 7842, "ph":"X", "name": "Step : Unity.Linker.Steps.SetupAndRegisterUnityRootsSteps"},
{ "pid": "host", "tid": 1,"ts": 1751360312688385,"dur": 4491, "ph":"X", "name": "Step : Mono.Linker.Steps.ResolveFromAssemblyStep"},
{ "pid": "host", "tid": 1,"ts": 1751360312693799,"dur": 2344, "ph":"X", "name": "Step : Mono.Linker.Steps.ResolveFromAssemblyStep"},
{ "pid": "host", "tid": 1,"ts": 1751360312696145,"dur": 2817, "ph":"X", "name": "Step : Mono.Linker.Steps.ResolveFromAssemblyStep"},
{ "pid": "host", "tid": 1,"ts": 1751360312699851,"dur": 1405, "ph":"X", "name": "Step : Mono.Linker.Steps.ResolveFromAssemblyStep"},
{ "pid": "host", "tid": 1,"ts": 1751360312702255,"dur": 1238, "ph":"X", "name": "Step : Mono.Linker.Steps.ResolveFromAssemblyStep"},
{ "pid": "host", "tid": 1,"ts": 1751360312703500,"dur": 1063, "ph":"X", "name": "Step : Unity.Linker.Steps.UnityLoadReferencesStep"},
{ "pid": "host", "tid": 1,"ts": 1751360312706287,"dur": 2527, "ph":"X", "name": "Step : Unity.Linker.Steps.Resolution.ResolveFromDescriptorsStep"},
{ "pid": "host", "tid": 1,"ts": 1751360312709034,"dur": 2135, "ph":"X", "name": "Step : UnityUnityResolveFromXmlStep: C:\\Program Files\\Unity\\Hub\\Editor\\2021.3.44f1c1\\Editor\\Data\\il2cpp\\LinkerDescriptors\\Default\\mscorlib.xml"},
{ "pid": "host", "tid": 1,"ts": 1751360312712533,"dur": 7358, "ph":"X", "name": "Step : Unity.Linker.Steps.Resolution.UnityBlacklistStep"},
{ "pid": "host", "tid": 1,"ts": 1751360312719893,"dur": 27130, "ph":"X", "name": "Step : UnityUnityResolveFromXmlStep: mscorlib Resource: mscorlib.xml"},
{ "pid": "host", "tid": 1,"ts": 1751360312747031,"dur": 236653, "ph":"X", "name": "Step : Mono.Linker.Steps.DynamicDependencyLookupStep"},
{ "pid": "host", "tid": 1,"ts": 1751360312985709,"dur": 2635, "ph":"X", "name": "Step : Unity.Linker.Steps.Rooting.ResolveFromMonoBehaviours"},
{ "pid": "host", "tid": 1,"ts": 1751360312988762,"dur": 131674, "ph":"X", "name": "Step : Unity.Linker.Steps.Rooting.ResolveFromPreserveAttribute"},
{ "pid": "host", "tid": 1,"ts": 1751360313120452,"dur": 7189, "ph":"X", "name": "Step : Unity.Linker.Steps.EngineStrippingAnnotationStep"},
{ "pid": "host", "tid": 1,"ts": 1751360313127651,"dur": 170742, "ph":"X", "name": "Step : Unity.Linker.Steps.UnityTypeMapStep"},
{ "pid": "host", "tid": 1,"ts": 1751360313298663,"dur": 128502, "ph":"X", "name": "Step : Unity.Linker.Steps.Analytics.BeforeMarkAnalyticsStep"},
{ "pid": "host", "tid": 1,"ts": 1751360313427178,"dur": 11512, "ph":"X", "name": "Step : Mono.Linker.Steps.RemoveSecurityStep"},
{ "pid": "host", "tid": 1,"ts": 1751360313438700,"dur": 3130, "ph":"X", "name": "Step : Unity.Linker.Steps.RemoveSecurityFromCopyAssemblies"},
{ "pid": "host", "tid": 1,"ts": 1751360313441836,"dur": 8955, "ph":"X", "name": "Step : Mono.Linker.Steps.RemoveFeaturesStep"},
{ "pid": "host", "tid": 1,"ts": 1751360313450799,"dur": 10749, "ph":"X", "name": "Step : Mono.Linker.Steps.RemoveUnreachableBlocksStep"},
{ "pid": "host", "tid": 1,"ts": 1751360313461559,"dur": 1597578, "ph":"X", "name": "Step : Unity.Linker.Steps.UnityMarkStep"},
{ "pid": "host", "tid": 1,"ts": 1751360315059642,"dur": 4161, "ph":"X", "name": "Step : Mono.Linker.Steps.ProcessWarningsStep"},
{ "pid": "host", "tid": 1,"ts": 1751360315063808,"dur": 33552, "ph":"X", "name": "Step : Unity.Linker.Steps.UnitySweepStep"},
{ "pid": "host", "tid": 1,"ts": 1751360315097368,"dur": 1825, "ph":"X", "name": "Step : Unity.Linker.Steps.UnityCodeRewriterStep"},
{ "pid": "host", "tid": 1,"ts": 1751360315099199,"dur": 2358, "ph":"X", "name": "Step : Mono.Linker.Steps.CleanStep"},
{ "pid": "host", "tid": 1,"ts": 1751360315101562,"dur": 1928, "ph":"X", "name": "Step : Unity.Linker.Steps.StubifyStep"},
{ "pid": "host", "tid": 1,"ts": 1751360315103495,"dur": 2502, "ph":"X", "name": "Step : Unity.Linker.Steps.AddUnresolvedStubsStep"},
{ "pid": "host", "tid": 1,"ts": 1751360315106002,"dur": 2399, "ph":"X", "name": "Step : Unity.Linker.Steps.Analytics.BeforeOutputAnalyticsStep"},
{ "pid": "host", "tid": 1,"ts": 1751360315109093,"dur": 463448, "ph":"X", "name": "Step : Unity.Linker.Steps.UnityOutputStep"},
{ "pid": "host", "tid": 1,"ts": 1751360315572548,"dur": 19783, "ph":"X", "name": "Step : Unity.Linker.Steps.LinkerToEditorDataGenerationStep"},
{ "pid": "host", "tid": 1,"ts": 1751360315594576,"dur": 21119, "ph":"X", "name": "Analytics"},
{ "pid": "host", "tid": 0, "ph":"M", "name": "thread_name", "args": {"name": "GC"} },
{ "pid": "host", "tid": 0,"ts": 1751360312493000,"dur": 2000, "ph":"X", "name": "GC - Gen 0", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC during background GC"}
},
{ "pid": "host", "tid": 0,"ts": 1751360312684000,"dur": 1000, "ph":"X", "name": "GC - Gen 0", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": 0,"ts": 1751360312722000,"dur": 18000, "ph":"X", "name": "GC - Gen 0", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": 0,"ts": 1751360312814000,"dur": 15000, "ph":"X", "name": "GC - Gen 0", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": 0,"ts": 1751360312932000,"dur": 17000, "ph":"X", "name": "GC - Gen 0", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": 0,"ts": 1751360313015000,"dur": 23000, "ph":"X", "name": "GC - Gen 0", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": 0,"ts": 1751360313157000,"dur": 27000, "ph":"X", "name": "GC - Gen 0", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": 0,"ts": 1751360313212000,"dur": 26000, "ph":"X", "name": "GC - Gen 0", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": 0,"ts": 1751360313374000,"dur": 11000, "ph":"X", "name": "GC - Gen 0", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": 0,"ts": 1751360313421000,"dur": 0, "ph":"X", "name": "GC - Gen 0", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": 0,"ts": 1751360313538000,"dur": 2000, "ph":"X", "name": "GC - Gen 0", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": 0,"ts": 1751360313569000,"dur": 2000, "ph":"X", "name": "GC - Gen 0", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": 0,"ts": 1751360313586000,"dur": 2000, "ph":"X", "name": "GC - Gen 0", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": 0,"ts": 1751360313620000,"dur": 1000, "ph":"X", "name": "GC - Gen 0", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": 0,"ts": 1751360313647000,"dur": 1000, "ph":"X", "name": "GC - Gen 0", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": 0,"ts": 1751360313666000,"dur": 1000, "ph":"X", "name": "GC - Gen 0", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": 0,"ts": 1751360313682000,"dur": 1000, "ph":"X", "name": "GC - Gen 0", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": 0,"ts": 1751360313717000,"dur": 1000, "ph":"X", "name": "GC - Gen 0", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": 0,"ts": 1751360313741000,"dur": 5000, "ph":"X", "name": "GC - Gen 0", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": 0,"ts": 1751360313761000,"dur": 2000, "ph":"X", "name": "GC - Gen 0", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": 0,"ts": 1751360313943000,"dur": 16000, "ph":"X", "name": "GC - Gen 0", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": 0,"ts": 1751360314096000,"dur": 14000, "ph":"X", "name": "GC - Gen 0", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": 0,"ts": 1751360314227000,"dur": 13000, "ph":"X", "name": "GC - Gen 0", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": 0,"ts": 1751360314364000,"dur": 9000, "ph":"X", "name": "GC - Gen 0", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": 0,"ts": 1751360314765000,"dur": 14000, "ph":"X", "name": "GC - Gen 0", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": 0,"ts": 1751360314925000,"dur": 18000, "ph":"X", "name": "GC - Gen 0", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": 0,"ts": 1751360315209000,"dur": 15000, "ph":"X", "name": "GC - Gen 0", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": 0,"ts": 1751360315295000,"dur": 17000, "ph":"X", "name": "GC - Gen 0", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": 0,"ts": 1751360315487000,"dur": 12000, "ph":"X", "name": "GC - Gen 0", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": 0,"ts": 1751360315538000,"dur": 17000, "ph":"X", "name": "GC - Gen 0", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": -1, "ph":"M", "name": "thread_name", "args": {"name": "GC"} },
{ "pid": "host", "tid": -1,"ts": 1751360312645000,"dur": 4000, "ph":"X", "name": "GC - Gen 1", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": -1,"ts": 1751360312770000,"dur": 32000, "ph":"X", "name": "GC - Gen 1", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": -1,"ts": 1751360312846000,"dur": 43000, "ph":"X", "name": "GC - Gen 1", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": -1,"ts": 1751360312964000,"dur": 18000, "ph":"X", "name": "GC - Gen 1", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": -1,"ts": 1751360313054000,"dur": 64000, "ph":"X", "name": "GC - Gen 1", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": -1,"ts": 1751360313313000,"dur": 47000, "ph":"X", "name": "GC - Gen 1", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": -1,"ts": 1751360313396000,"dur": 16000, "ph":"X", "name": "GC - Gen 1", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": -1,"ts": 1751360313600000,"dur": 7000, "ph":"X", "name": "GC - Gen 1", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": -1,"ts": 1751360313633000,"dur": 1000, "ph":"X", "name": "GC - Gen 1", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": -1,"ts": 1751360313698000,"dur": 3000, "ph":"X", "name": "GC - Gen 1", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": -1,"ts": 1751360313884000,"dur": 13000, "ph":"X", "name": "GC - Gen 1", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": -1,"ts": 1751360314010000,"dur": 33000, "ph":"X", "name": "GC - Gen 1", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": -1,"ts": 1751360314152000,"dur": 31000, "ph":"X", "name": "GC - Gen 1", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": -1,"ts": 1751360314286000,"dur": 28000, "ph":"X", "name": "GC - Gen 1", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": -1,"ts": 1751360314435000,"dur": 25000, "ph":"X", "name": "GC - Gen 1", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": -1,"ts": 1751360314634000,"dur": 15000, "ph":"X", "name": "GC - Gen 1", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": -1,"ts": 1751360314696000,"dur": 18000, "ph":"X", "name": "GC - Gen 1", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": -1,"ts": 1751360314842000,"dur": 37000, "ph":"X", "name": "GC - Gen 1", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": -1,"ts": 1751360315016000,"dur": 40000, "ph":"X", "name": "GC - Gen 1", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": -1,"ts": 1751360315372000,"dur": 14000, "ph":"X", "name": "GC - Gen 1", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": -1,"ts": 1751360315426000,"dur": 18000, "ph":"X", "name": "GC - Gen 1", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": -2, "ph":"M", "name": "thread_name", "args": {"name": "GC"} },
{ "pid": "host", "tid": -2,"ts": 1751360312447000,"dur": 2000, "ph":"X", "name": "GC - Gen 2", "args": { "detail": "Reason: LOH allocation, Type: Background GC"}
},
{ "pid": "host", "tid": -2,"ts": 1751360312490000,"dur": 5000, "ph":"X", "name": "GC - Gen 2", "args": { "detail": "Reason: LOH allocation, Type: Background GC"}
},
{ "pid": "host", "tid": -2,"ts": 1751360312722000,"dur": 25000, "ph":"X", "name": "GC - Gen 2", "args": { "detail": "Reason: SOH allocation, Type: Background GC"}
},
{ "pid": "host", "tid": -2,"ts": 1751360312846000,"dur": 74000, "ph":"X", "name": "GC - Gen 2", "args": { "detail": "Reason: SOH allocation, Type: Background GC"}
},
{ "pid": "host", "tid": -2,"ts": 1751360313212000,"dur": 67000, "ph":"X", "name": "GC - Gen 2", "args": { "detail": "Reason: SOH allocation, Type: Background GC"}
},
{ "pid": "host", "tid": -2,"ts": 1751360314435000,"dur": 97000, "ph":"X", "name": "GC - Gen 2", "args": { "detail": "Reason: SOH allocation, Type: Background GC"}
},
