Using pre-set license
Built from '2021.3/china_unity/release' branch; Version is '2021.3.44f1c1 (0c301d0bd4e3) revision 798749'; Using compiler version '192829333'; Build Type 'Release'
OS: 'Windows 11  (10.0.22631) 64bit CoreCountrySpecific' Language: 'zh' Physical Memory: 16091 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1

COMMAND LINE ARGUMENTS:
C:\Program Files\Unity\Hub\Editor\2021.3.44f1c1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker7
-projectPath
D:/nwu/Assembly/UnityProjects/VRAssembly
-logFile
Logs/AssetImportWorker7.log
-srvPort
4081
Successfully changed project path to: D:/nwu/Assembly/UnityProjects/VRAssembly
D:/nwu/Assembly/UnityProjects/VRAssembly
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [43020] Host "[IP] ********** [Port] 0 [Flags] 2 [Guid] 2163556534 [EditorId] 2163556534 [Version] 1048832 [Id] WindowsEditor(7,PC-SHENSHEN) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" joined multi-casting on [***********:54997]...

Player connection [43020] Host "[IP] ********** [Port] 0 [Flags] 2 [Guid] 2163556534 [EditorId] 2163556534 [Version] 1048832 [Id] WindowsEditor(7,PC-SHENSHEN) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" joined alternative multi-casting on [***********:34997]...

AS: AutoStreaming module initializing.
[Physics::Module] Initialized MultithreadedJobDispatcher with {0} workers.
Refreshing native plugins compatible for Editor in 56.31 ms, found 13 plugins.
Preloading 2 native plugins for Editor in 3.15 ms.
Initialize engine version: 2021.3.44f1c1 (0c301d0bd4e3)
[Subsystems] Discovering subsystems at path C:/Program Files/Unity/Hub/Editor/2021.3.44f1c1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path D:/nwu/Assembly/UnityProjects/VRAssembly/Assets
GfxDevice: creating device client; threaded=0; jobified=0
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce RTX 4070 Laptop GPU (ID=0x2860)
    Vendor:   NVIDIA
    VRAM:     7948 MB
    Driver:   32.0.15.6624
Initialize mono
Mono path[0] = 'C:/Program Files/Unity/Hub/Editor/2021.3.44f1c1/Editor/Data/Managed'
Mono path[1] = 'C:/Program Files/Unity/Hub/Editor/2021.3.44f1c1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'C:/Program Files/Unity/Hub/Editor/2021.3.44f1c1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56932
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: C:/Program Files/Unity/Hub/Editor/2021.3.44f1c1/Editor/Data/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll
Register platform support module: C:/Program Files/Unity/Hub/Editor/2021.3.44f1c1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.019579 seconds.
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Android Extension - Scanning For ADB Devices 626 ms
Refreshing native plugins compatible for Editor in 38.72 ms, found 13 plugins.
Preloading 2 native plugins for Editor in 0.33 ms.
Mono: successfully reloaded assembly
- Completed reload, in  1.180 seconds
Domain Reload Profiling:
	ReloadAssembly (1180ms)
		BeginReloadAssembly (121ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (0ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (0ms)
		EndReloadAssembly (939ms)
			LoadAssemblies (110ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (71ms)
			ReleaseScriptCaches (0ms)
			RebuildScriptCaches (16ms)
			SetupLoadedEditorAssemblies (802ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (677ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (39ms)
				BeforeProcessingInitializeOnLoad (1ms)
				ProcessInitializeOnLoadAttributes (58ms)
				ProcessInitializeOnLoadMethodAttributes (26ms)
				AfterProcessingInitializeOnLoad (0ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (0ms)
Platform modules already initialized, skipping
Registering precompiled user dll's ...
Registered in 0.015574 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 40.50 ms, found 13 plugins.
Preloading 2 native plugins for Editor in 0.36 ms.
PXRLog [Build Check]RegisterBuildPlayerHandler,Already Do not show: False
0x00007ff6c74fefad (Unity) StackWalker::GetCurrentCallstack
0x00007ff6c7505b39 (Unity) StackWalker::ShowCallstack
0x00007ff6c84a8df3 (Unity) GetStacktrace
0x00007ff6c8b7165d (Unity) DebugStringToFile
0x00007ff6c664ea32 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x000001cc532ae1e3 (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
0x000001cc532adebb (Mono JIT Code) UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
0x000001cc532adc40 (Mono JIT Code) UnityEngine.Logger:Log (UnityEngine.LogType,object)
0x000001cc532adb08 (Mono JIT Code) UnityEngine.Debug:Log (object)
0x000001cc532aaf43 (Mono JIT Code) [PXR_SDKBuildCheck.cs:32] Unity.XR.PXR.Editor.PXR_SDKBuildCheck:.cctor () 
0x000001cc530aa9f5 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
0x00007ffb3c53697e (mono-2.0-bdwgc) [mini-runtime.c:3445] mono_jit_runtime_invoke 
0x00007ffb3c46cdd4 (mono-2.0-bdwgc) [object.c:3068] do_runtime_invoke 
0x00007ffb3c467b79 (mono-2.0-bdwgc) [object.c:563] mono_runtime_class_init_full 
0x00007ffb3c39ddd7 (mono-2.0-bdwgc) [icall.c:1292] ves_icall_System_Runtime_CompilerServices_RuntimeHelpers_RunClassConstructor 
0x00007ffb3c3cfebc (mono-2.0-bdwgc) [icall-def.h:756] ves_icall_System_Runtime_CompilerServices_RuntimeHelpers_RunClassConstructor_raw 
0x000001cc530aa09a (Mono JIT Code) (wrapper managed-to-native) System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (intptr)
0x000001cc530a9fab (Mono JIT Code) System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
0x000001cc530a96f3 (Mono JIT Code) UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])
0x000001cc505f6298 (Mono JIT Code) (wrapper runtime-invoke) <Module>:runtime_invoke_void_object (object,intptr,intptr,intptr)
0x00007ffb3c53697e (mono-2.0-bdwgc) [mini-runtime.c:3445] mono_jit_runtime_invoke 
0x00007ffb3c46cdd4 (mono-2.0-bdwgc) [object.c:3068] do_runtime_invoke 
0x00007ffb3c46cf4c (mono-2.0-bdwgc) [object.c:3115] mono_runtime_invoke 
0x00007ff6c7421934 (Unity) scripting_method_invoke
0x00007ff6c7400b84 (Unity) ScriptingInvocation::Invoke
0x00007ff6c73fb6e5 (Unity) ScriptingInvocation::Invoke<void>
0x00007ff6c753f4c3 (Unity) Scripting::UnityEditor::EditorAssembliesProxy::ProcessInitializeOnLoadAttributes
0x00007ff6c73f570b (Unity) MonoManager::SetupLoadedEditorAssemblies
0x00007ff6c73eb1ed (Unity) MonoManager::EndReloadAssembly
0x00007ff6c73f2d88 (Unity) MonoManager::ReloadAssembly
0x00007ff6c835fa12 (Unity) LoadDomainAndUserAssemblies
0x00007ff6c8360144 (Unity) LoadUserAssemblies
0x00007ff6c8060c43 (Unity) Application::InitializeProject
0x00007ff6c84b20a0 (Unity) WinMain
0x00007ff6c987fbae (Unity) __scrt_common_main_seh
0x00007ffbd486259d (KERNEL32) BaseThreadInitThunk
0x00007ffbd4e8af78 (ntdll) RtlUserThreadStart

Package Manager log level set to [2]
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  1.734 seconds
Domain Reload Profiling:
	ReloadAssembly (1735ms)
		BeginReloadAssembly (146ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (3ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (13ms)
		EndReloadAssembly (1478ms)
			LoadAssemblies (139ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (198ms)
			ReleaseScriptCaches (0ms)
			RebuildScriptCaches (43ms)
			SetupLoadedEditorAssemblies (1102ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (16ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (41ms)
				BeforeProcessingInitializeOnLoad (77ms)
				ProcessInitializeOnLoadAttributes (910ms)
				ProcessInitializeOnLoadMethodAttributes (23ms)
				AfterProcessingInitializeOnLoad (34ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (6ms)
Platform modules already initialized, skipping
========================================================================
Worker process is ready to serve import requests
Launched and connected shader compiler UnityShaderCompiler.exe after 0.08 seconds
Refreshing native plugins compatible for Editor in 0.61 ms, found 13 plugins.
Preloading 2 native plugins for Editor in 0.20 ms.
Unloading 4800 Unused Serialized files (Serialized files now loaded: 0)
Unloading 69 unused Assets / (122.4 KB). Loaded Objects now: 5254.
Memory consumption went from 196.1 MB to 196.0 MB.
Total: 2.278700 ms (FindLiveObjects: 0.209200 ms CreateObjectMapping: 0.095800 ms MarkObjects: 1.898800 ms  DeleteObjects: 0.074100 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.015604 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 0.68 ms, found 13 plugins.
Preloading 2 native plugins for Editor in 0.19 ms.
PXRLog [Build Check]RegisterBuildPlayerHandler,Already Do not show: False
0x00007ff6c74fefad (Unity) StackWalker::GetCurrentCallstack
0x00007ff6c7505b39 (Unity) StackWalker::ShowCallstack
0x00007ff6c84a8df3 (Unity) GetStacktrace
0x00007ff6c8b7165d (Unity) DebugStringToFile
0x00007ff6c664ea32 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x000001cc5c58c083 (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
0x000001cc5c58bd5b (Mono JIT Code) UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
0x000001cc5c58bae0 (Mono JIT Code) UnityEngine.Logger:Log (UnityEngine.LogType,object)
0x000001cc5c58b9a8 (Mono JIT Code) UnityEngine.Debug:Log (object)
0x000001cc5c588a53 (Mono JIT Code) [PXR_SDKBuildCheck.cs:32] Unity.XR.PXR.Editor.PXR_SDKBuildCheck:.cctor () 
0x000001cc5c6f7575 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
0x00007ffb3c53697e (mono-2.0-bdwgc) [mini-runtime.c:3445] mono_jit_runtime_invoke 
0x00007ffb3c46cdd4 (mono-2.0-bdwgc) [object.c:3068] do_runtime_invoke 
0x00007ffb3c467b79 (mono-2.0-bdwgc) [object.c:563] mono_runtime_class_init_full 
0x00007ffb3c39ddd7 (mono-2.0-bdwgc) [icall.c:1292] ves_icall_System_Runtime_CompilerServices_RuntimeHelpers_RunClassConstructor 
0x00007ffb3c3cfebc (mono-2.0-bdwgc) [icall-def.h:756] ves_icall_System_Runtime_CompilerServices_RuntimeHelpers_RunClassConstructor_raw 
0x000001cc5c6f6c1a (Mono JIT Code) (wrapper managed-to-native) System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (intptr)
0x000001cc5c6f6b2b (Mono JIT Code) System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
0x000001cc5c6f6273 (Mono JIT Code) UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])
0x000001cc5be56298 (Mono JIT Code) (wrapper runtime-invoke) <Module>:runtime_invoke_void_object (object,intptr,intptr,intptr)
0x00007ffb3c53697e (mono-2.0-bdwgc) [mini-runtime.c:3445] mono_jit_runtime_invoke 
0x00007ffb3c46cdd4 (mono-2.0-bdwgc) [object.c:3068] do_runtime_invoke 
0x00007ffb3c46cf4c (mono-2.0-bdwgc) [object.c:3115] mono_runtime_invoke 
0x00007ff6c7421934 (Unity) scripting_method_invoke
0x00007ff6c7400b84 (Unity) ScriptingInvocation::Invoke
0x00007ff6c73fb6e5 (Unity) ScriptingInvocation::Invoke<void>
0x00007ff6c753f4c3 (Unity) Scripting::UnityEditor::EditorAssembliesProxy::ProcessInitializeOnLoadAttributes
0x00007ff6c73f570b (Unity) MonoManager::SetupLoadedEditorAssemblies
0x00007ff6c73eb1ed (Unity) MonoManager::EndReloadAssembly
0x00007ff6c73f2d88 (Unity) MonoManager::ReloadAssembly
0x00007ff6c835fa12 (Unity) LoadDomainAndUserAssemblies
0x00007ff6c8360144 (Unity) LoadUserAssemblies
0x00007ff6c885c091 (Unity) <lambda_f8411f227bdd47da33e064bfa1662688>::operator()
0x00007ff6c889ce92 (Unity) asio::detail::completion_handler<core::mutable_function<void __cdecl(void)>,asio::io_context::basic_executor_type<std::allocator<void>,0> >::do_complete
0x00007ff6c8888d7e (Unity) asio::detail::win_iocp_io_context::do_one
0x00007ff6c888a9e4 (Unity) asio::detail::win_iocp_io_context::run
0x00007ff6c889b27e (Unity) IOService::Run
0x00007ff6c886dbcf (Unity) AssetImportWorkerClient::Run
0x00007ff6c8835d13 (Unity) RunAssetImportWorkerClientV2
0x00007ff6c8835d9b (Unity) RunAssetImporterV2
0x00007ff6c8060c48 (Unity) Application::InitializeProject
0x00007ff6c84b20a0 (Unity) WinMain
0x00007ff6c987fbae (Unity) __scrt_common_main_seh
0x00007ffbd486259d (KERNEL32) BaseThreadInitThunk
0x00007ffbd4e8af78 (ntdll) RtlUserThreadStart

[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
CreateSerializedAsset is not supported while importing out-of-process
0x00007ff6c74fefad (Unity) StackWalker::GetCurrentCallstack
0x00007ff6c7505b39 (Unity) StackWalker::ShowCallstack
0x00007ff6c84a8df3 (Unity) GetStacktrace
0x00007ff6c8b7165d (Unity) DebugStringToFile
0x00007ff6c887b618 (Unity) CreateSerializedAssetV2
0x00007ff6c8810f55 (Unity) CreateSerializedAssetV2
0x00007ff6c877f8ee (Unity) AssetDatabase::CreateSerializedAsset
0x00007ff6c6cea4ff (Unity) AssetDatabaseBindings::CreateAsset
0x00007ff6c6b0b819 (Unity) AssetDatabase_CUSTOM_CreateAsset
0x000001cc5bf8cf35 (Mono JIT Code) (wrapper managed-to-native) UnityEditor.AssetDatabase:CreateAsset (UnityEngine.Object,string)
0x000001cc5bf8c753 (Mono JIT Code) [PXR_ProjectSetting.cs:121] Unity.XR.PXR.PXR_ProjectSetting:GetProjectConfig () 
0x000001cc5bf8c383 (Mono JIT Code) [PXR_SDKSettingEditor.cs:111] Unity.XR.PXR.Editor.PXR_SDKSettingEditor:InitializeOnLoad () 
0x000001cc5c6f75c3 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
0x00007ffb3c53697e (mono-2.0-bdwgc) [mini-runtime.c:3445] mono_jit_runtime_invoke 
0x00007ffb3c46cdd4 (mono-2.0-bdwgc) [object.c:3068] do_runtime_invoke 
0x00007ffb3c4746df (mono-2.0-bdwgc) [object.c:5770] mono_runtime_try_invoke_array 
0x00007ffb3c3a4eb2 (mono-2.0-bdwgc) [icall.c:4041] ves_icall_InternalInvoke 
0x00007ffb3c3cb091 (mono-2.0-bdwgc) [icall-def.h:692] ves_icall_InternalInvoke_raw 
0x000001cc5c57a9d0 (Mono JIT Code) (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo:InternalInvoke (System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
0x000001cc5c57a16b (Mono JIT Code) System.Reflection.RuntimeMethodInfo:Invoke (object,System.Reflection.BindingFlags,System.Reflection.Binder,object[],System.Globalization.CultureInfo)
0x000001cc5c57a02e (Mono JIT Code) System.Reflection.MethodBase:Invoke (object,object[])
0x000001cc565a905b (Mono JIT Code) UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes ()
0x000001cc5be56b35 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
0x00007ffb3c53697e (mono-2.0-bdwgc) [mini-runtime.c:3445] mono_jit_runtime_invoke 
0x00007ffb3c46cdd4 (mono-2.0-bdwgc) [object.c:3068] do_runtime_invoke 
0x00007ffb3c46cf4c (mono-2.0-bdwgc) [object.c:3115] mono_runtime_invoke 
0x00007ff6c7421934 (Unity) scripting_method_invoke
0x00007ff6c7400b84 (Unity) ScriptingInvocation::Invoke
0x00007ff6c73fb6e5 (Unity) ScriptingInvocation::Invoke<void>
0x00007ff6c753f53b (Unity) Scripting::UnityEditor::EditorAssembliesProxy::ProcessInitializeOnLoadMethodAttributes
0x00007ff6c73f5733 (Unity) MonoManager::SetupLoadedEditorAssemblies
0x00007ff6c73eb1ed (Unity) MonoManager::EndReloadAssembly
0x00007ff6c73f2d88 (Unity) MonoManager::ReloadAssembly
0x00007ff6c835fa12 (Unity) LoadDomainAndUserAssemblies
0x00007ff6c8360144 (Unity) LoadUserAssemblies
0x00007ff6c885c091 (Unity) <lambda_f8411f227bdd47da33e064bfa1662688>::operator()
0x00007ff6c889ce92 (Unity) asio::detail::completion_handler<core::mutable_function<void __cdecl(void)>,asio::io_context::basic_executor_type<std::allocator<void>,0> >::do_complete
0x00007ff6c8888d7e (Unity) asio::detail::win_iocp_io_context::do_one
0x00007ff6c888a9e4 (Unity) asio::detail::win_iocp_io_context::run
0x00007ff6c889b27e (Unity) IOService::Run
0x00007ff6c886dbcf (Unity) AssetImportWorkerClient::Run
0x00007ff6c8835d13 (Unity) RunAssetImportWorkerClientV2
0x00007ff6c8835d9b (Unity) RunAssetImporterV2
0x00007ff6c8060c48 (Unity) Application::InitializeProject
0x00007ff6c84b20a0 (Unity) WinMain
0x00007ff6c987fbae (Unity) __scrt_common_main_seh
0x00007ffbd486259d (KERNEL32) BaseThreadInitThunk
0x00007ffbd4e8af78 (ntdll) RtlUserThreadStart

System.Reflection.TargetInvocationException: Exception has been thrown by the target of an invocation. ---> UnityEngine.UnityException: Creating asset at path Assets/Resources/PXR_ProjectSetting.asset failed.
  at (wrapper managed-to-native) UnityEditor.AssetDatabase.CreateAsset(UnityEngine.Object,string)
  at Unity.XR.PXR.PXR_ProjectSetting.GetProjectConfig () [0x0015a] in D:\PICO Unity Integration SDK-3.2.0-20250529\Runtime\Scripts\PXR_ProjectSetting.cs:117 
  at Unity.XR.PXR.Editor.PXR_SDKSettingEditor.InitializeOnLoad () [0x00000] in D:\PICO Unity Integration SDK-3.2.0-20250529\Editor\PXR_SDKSettingEditor.cs:111 
  at (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo.InternalInvoke(System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x0006a] in <688f272cc27c4df98c503f9d1f1267d0>:0 
   --- End of inner exception stack trace ---
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x00083] in <688f272cc27c4df98c503f9d1f1267d0>:0 
  at System.Reflection.MethodBase.Invoke (System.Object obj, System.Object[] parameters) [0x00000] in <688f272cc27c4df98c503f9d1f1267d0>:0 
  at UnityEditor.EditorAssemblies.ProcessInitializeOnLoadMethodAttributes () [0x000a5] in <1518627dacca44efa32edd45bc720c48>:0 
0x00007ff6c74fefad (Unity) StackWalker::GetCurrentCallstack
0x00007ff6c7505b39 (Unity) StackWalker::ShowCallstack
0x00007ff6c84a8df3 (Unity) GetStacktrace
0x00007ff6c8b7165d (Unity) DebugStringToFile
0x00007ff6c664ea32 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x000001cc5c58c083 (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
0x000001cc5c58bd5b (Mono JIT Code) UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
0x000001cc5c58bae0 (Mono JIT Code) UnityEngine.Logger:Log (UnityEngine.LogType,object)
0x000001cc5bf8d1c5 (Mono JIT Code) UnityEngine.Debug:LogError (object)
0x000001cc565a907b (Mono JIT Code) UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes ()
0x000001cc5be56b35 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
0x00007ffb3c53697e (mono-2.0-bdwgc) [mini-runtime.c:3445] mono_jit_runtime_invoke 
0x00007ffb3c46cdd4 (mono-2.0-bdwgc) [object.c:3068] do_runtime_invoke 
0x00007ffb3c46cf4c (mono-2.0-bdwgc) [object.c:3115] mono_runtime_invoke 
0x00007ff6c7421934 (Unity) scripting_method_invoke
0x00007ff6c7400b84 (Unity) ScriptingInvocation::Invoke
0x00007ff6c73fb6e5 (Unity) ScriptingInvocation::Invoke<void>
0x00007ff6c753f53b (Unity) Scripting::UnityEditor::EditorAssembliesProxy::ProcessInitializeOnLoadMethodAttributes
0x00007ff6c73f5733 (Unity) MonoManager::SetupLoadedEditorAssemblies
0x00007ff6c73eb1ed (Unity) MonoManager::EndReloadAssembly
0x00007ff6c73f2d88 (Unity) MonoManager::ReloadAssembly
0x00007ff6c835fa12 (Unity) LoadDomainAndUserAssemblies
0x00007ff6c8360144 (Unity) LoadUserAssemblies
0x00007ff6c885c091 (Unity) <lambda_f8411f227bdd47da33e064bfa1662688>::operator()
0x00007ff6c889ce92 (Unity) asio::detail::completion_handler<core::mutable_function<void __cdecl(void)>,asio::io_context::basic_executor_type<std::allocator<void>,0> >::do_complete
0x00007ff6c8888d7e (Unity) asio::detail::win_iocp_io_context::do_one
0x00007ff6c888a9e4 (Unity) asio::detail::win_iocp_io_context::run
0x00007ff6c889b27e (Unity) IOService::Run
0x00007ff6c886dbcf (Unity) AssetImportWorkerClient::Run
0x00007ff6c8835d13 (Unity) RunAssetImportWorkerClientV2
0x00007ff6c8835d9b (Unity) RunAssetImporterV2
0x00007ff6c8060c48 (Unity) Application::InitializeProject
0x00007ff6c84b20a0 (Unity) WinMain
0x00007ff6c987fbae (Unity) __scrt_common_main_seh
0x00007ffbd486259d (KERNEL32) BaseThreadInitThunk
0x00007ffbd4e8af78 (ntdll) RtlUserThreadStart

Mono: successfully reloaded assembly
- Completed reload, in  1.444 seconds
Domain Reload Profiling:
	ReloadAssembly (1445ms)
		BeginReloadAssembly (179ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (3ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (26ms)
		EndReloadAssembly (1160ms)
			LoadAssemblies (145ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (209ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (30ms)
			SetupLoadedEditorAssemblies (779ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (19ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (70ms)
				ProcessInitializeOnLoadAttributes (612ms)
				ProcessInitializeOnLoadMethodAttributes (39ms)
				AfterProcessingInitializeOnLoad (37ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (6ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 0.71 ms, found 13 plugins.
Preloading 2 native plugins for Editor in 0.26 ms.
Unloading 4752 Unused Serialized files (Serialized files now loaded: 0)
Unloading 50 unused Assets / (95.7 KB). Loaded Objects now: 5271.
Memory consumption went from 192.0 MB to 191.9 MB.
Total: 2.177100 ms (FindLiveObjects: 0.205000 ms CreateObjectMapping: 0.083700 ms MarkObjects: 1.851500 ms  DeleteObjects: 0.036200 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.015018 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 0.71 ms, found 13 plugins.
Preloading 2 native plugins for Editor in 0.21 ms.
PXRLog [Build Check]RegisterBuildPlayerHandler,Already Do not show: False
0x00007ff6c74fefad (Unity) StackWalker::GetCurrentCallstack
0x00007ff6c7505b39 (Unity) StackWalker::ShowCallstack
0x00007ff6c84a8df3 (Unity) GetStacktrace
0x00007ff6c8b7165d (Unity) DebugStringToFile
0x00007ff6c664ea32 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x000001cc532ac083 (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
0x000001cc532abd5b (Mono JIT Code) UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
0x000001cc532abae0 (Mono JIT Code) UnityEngine.Logger:Log (UnityEngine.LogType,object)
0x000001cc532ab9a8 (Mono JIT Code) UnityEngine.Debug:Log (object)
0x000001cc532a8a53 (Mono JIT Code) [PXR_SDKBuildCheck.cs:32] Unity.XR.PXR.Editor.PXR_SDKBuildCheck:.cctor () 
0x000001cc52e97575 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
0x00007ffb3c53697e (mono-2.0-bdwgc) [mini-runtime.c:3445] mono_jit_runtime_invoke 
0x00007ffb3c46cdd4 (mono-2.0-bdwgc) [object.c:3068] do_runtime_invoke 
0x00007ffb3c467b79 (mono-2.0-bdwgc) [object.c:563] mono_runtime_class_init_full 
0x00007ffb3c39ddd7 (mono-2.0-bdwgc) [icall.c:1292] ves_icall_System_Runtime_CompilerServices_RuntimeHelpers_RunClassConstructor 
0x00007ffb3c3cfebc (mono-2.0-bdwgc) [icall-def.h:756] ves_icall_System_Runtime_CompilerServices_RuntimeHelpers_RunClassConstructor_raw 
0x000001cc52e96c1a (Mono JIT Code) (wrapper managed-to-native) System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (intptr)
0x000001cc52e96b2b (Mono JIT Code) System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
0x000001cc52e96273 (Mono JIT Code) UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])
0x000001cc5c706298 (Mono JIT Code) (wrapper runtime-invoke) <Module>:runtime_invoke_void_object (object,intptr,intptr,intptr)
0x00007ffb3c53697e (mono-2.0-bdwgc) [mini-runtime.c:3445] mono_jit_runtime_invoke 
0x00007ffb3c46cdd4 (mono-2.0-bdwgc) [object.c:3068] do_runtime_invoke 
0x00007ffb3c46cf4c (mono-2.0-bdwgc) [object.c:3115] mono_runtime_invoke 
0x00007ff6c7421934 (Unity) scripting_method_invoke
0x00007ff6c7400b84 (Unity) ScriptingInvocation::Invoke
0x00007ff6c73fb6e5 (Unity) ScriptingInvocation::Invoke<void>
0x00007ff6c753f4c3 (Unity) Scripting::UnityEditor::EditorAssembliesProxy::ProcessInitializeOnLoadAttributes
0x00007ff6c73f570b (Unity) MonoManager::SetupLoadedEditorAssemblies
0x00007ff6c73eb1ed (Unity) MonoManager::EndReloadAssembly
0x00007ff6c73f2d88 (Unity) MonoManager::ReloadAssembly
0x00007ff6c835fa12 (Unity) LoadDomainAndUserAssemblies
0x00007ff6c8360144 (Unity) LoadUserAssemblies
0x00007ff6c885c091 (Unity) <lambda_f8411f227bdd47da33e064bfa1662688>::operator()
0x00007ff6c889ce92 (Unity) asio::detail::completion_handler<core::mutable_function<void __cdecl(void)>,asio::io_context::basic_executor_type<std::allocator<void>,0> >::do_complete
0x00007ff6c8888d7e (Unity) asio::detail::win_iocp_io_context::do_one
0x00007ff6c888a9e4 (Unity) asio::detail::win_iocp_io_context::run
0x00007ff6c889b27e (Unity) IOService::Run
0x00007ff6c886dbcf (Unity) AssetImportWorkerClient::Run
0x00007ff6c8835d13 (Unity) RunAssetImportWorkerClientV2
0x00007ff6c8835d9b (Unity) RunAssetImporterV2
0x00007ff6c8060c48 (Unity) Application::InitializeProject
0x00007ff6c84b20a0 (Unity) WinMain
0x00007ff6c987fbae (Unity) __scrt_common_main_seh
0x00007ffbd486259d (KERNEL32) BaseThreadInitThunk
0x00007ffbd4e8af78 (ntdll) RtlUserThreadStart

[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
CreateSerializedAsset is not supported while importing out-of-process
0x00007ff6c74fefad (Unity) StackWalker::GetCurrentCallstack
0x00007ff6c7505b39 (Unity) StackWalker::ShowCallstack
0x00007ff6c84a8df3 (Unity) GetStacktrace
0x00007ff6c8b7165d (Unity) DebugStringToFile
0x00007ff6c887b618 (Unity) CreateSerializedAssetV2
0x00007ff6c8810f55 (Unity) CreateSerializedAssetV2
0x00007ff6c877f8ee (Unity) AssetDatabase::CreateSerializedAsset
0x00007ff6c6cea4ff (Unity) AssetDatabaseBindings::CreateAsset
0x00007ff6c6b0b819 (Unity) AssetDatabase_CUSTOM_CreateAsset
0x000001cc5c74cdb5 (Mono JIT Code) (wrapper managed-to-native) UnityEditor.AssetDatabase:CreateAsset (UnityEngine.Object,string)
0x000001cc5c74c753 (Mono JIT Code) [PXR_ProjectSetting.cs:121] Unity.XR.PXR.PXR_ProjectSetting:GetProjectConfig () 
0x000001cc5c74c383 (Mono JIT Code) [PXR_SDKSettingEditor.cs:111] Unity.XR.PXR.Editor.PXR_SDKSettingEditor:InitializeOnLoad () 
0x000001cc52e975c3 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
0x00007ffb3c53697e (mono-2.0-bdwgc) [mini-runtime.c:3445] mono_jit_runtime_invoke 
0x00007ffb3c46cdd4 (mono-2.0-bdwgc) [object.c:3068] do_runtime_invoke 
0x00007ffb3c4746df (mono-2.0-bdwgc) [object.c:5770] mono_runtime_try_invoke_array 
0x00007ffb3c3a4eb2 (mono-2.0-bdwgc) [icall.c:4041] ves_icall_InternalInvoke 
0x00007ffb3c3cb091 (mono-2.0-bdwgc) [icall-def.h:692] ves_icall_InternalInvoke_raw 
0x000001cc5555ae20 (Mono JIT Code) (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo:InternalInvoke (System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
0x000001cc5555a5bb (Mono JIT Code) System.Reflection.RuntimeMethodInfo:Invoke (object,System.Reflection.BindingFlags,System.Reflection.Binder,object[],System.Globalization.CultureInfo)
0x000001cc5555a47e (Mono JIT Code) System.Reflection.MethodBase:Invoke (object,object[])
0x000001cc5c738c7b (Mono JIT Code) UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes ()
0x000001cc5c706b35 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
0x00007ffb3c53697e (mono-2.0-bdwgc) [mini-runtime.c:3445] mono_jit_runtime_invoke 
0x00007ffb3c46cdd4 (mono-2.0-bdwgc) [object.c:3068] do_runtime_invoke 
0x00007ffb3c46cf4c (mono-2.0-bdwgc) [object.c:3115] mono_runtime_invoke 
0x00007ff6c7421934 (Unity) scripting_method_invoke
0x00007ff6c7400b84 (Unity) ScriptingInvocation::Invoke
0x00007ff6c73fb6e5 (Unity) ScriptingInvocation::Invoke<void>
0x00007ff6c753f53b (Unity) Scripting::UnityEditor::EditorAssembliesProxy::ProcessInitializeOnLoadMethodAttributes
0x00007ff6c73f5733 (Unity) MonoManager::SetupLoadedEditorAssemblies
0x00007ff6c73eb1ed (Unity) MonoManager::EndReloadAssembly
0x00007ff6c73f2d88 (Unity) MonoManager::ReloadAssembly
0x00007ff6c835fa12 (Unity) LoadDomainAndUserAssemblies
0x00007ff6c8360144 (Unity) LoadUserAssemblies
0x00007ff6c885c091 (Unity) <lambda_f8411f227bdd47da33e064bfa1662688>::operator()
0x00007ff6c889ce92 (Unity) asio::detail::completion_handler<core::mutable_function<void __cdecl(void)>,asio::io_context::basic_executor_type<std::allocator<void>,0> >::do_complete
0x00007ff6c8888d7e (Unity) asio::detail::win_iocp_io_context::do_one
0x00007ff6c888a9e4 (Unity) asio::detail::win_iocp_io_context::run
0x00007ff6c889b27e (Unity) IOService::Run
0x00007ff6c886dbcf (Unity) AssetImportWorkerClient::Run
0x00007ff6c8835d13 (Unity) RunAssetImportWorkerClientV2
0x00007ff6c8835d9b (Unity) RunAssetImporterV2
0x00007ff6c8060c48 (Unity) Application::InitializeProject
0x00007ff6c84b20a0 (Unity) WinMain
0x00007ff6c987fbae (Unity) __scrt_common_main_seh
0x00007ffbd486259d (KERNEL32) BaseThreadInitThunk
0x00007ffbd4e8af78 (ntdll) RtlUserThreadStart

System.Reflection.TargetInvocationException: Exception has been thrown by the target of an invocation. ---> UnityEngine.UnityException: Creating asset at path Assets/Resources/PXR_ProjectSetting.asset failed.
  at (wrapper managed-to-native) UnityEditor.AssetDatabase.CreateAsset(UnityEngine.Object,string)
  at Unity.XR.PXR.PXR_ProjectSetting.GetProjectConfig () [0x0015a] in D:\PICO Unity Integration SDK-3.2.0-20250529\Runtime\Scripts\PXR_ProjectSetting.cs:117 
  at Unity.XR.PXR.Editor.PXR_SDKSettingEditor.InitializeOnLoad () [0x00000] in D:\PICO Unity Integration SDK-3.2.0-20250529\Editor\PXR_SDKSettingEditor.cs:111 
  at (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo.InternalInvoke(System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x0006a] in <688f272cc27c4df98c503f9d1f1267d0>:0 
   --- End of inner exception stack trace ---
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x00083] in <688f272cc27c4df98c503f9d1f1267d0>:0 
  at System.Reflection.MethodBase.Invoke (System.Object obj, System.Object[] parameters) [0x00000] in <688f272cc27c4df98c503f9d1f1267d0>:0 
  at UnityEditor.EditorAssemblies.ProcessInitializeOnLoadMethodAttributes () [0x000a5] in <1518627dacca44efa32edd45bc720c48>:0 
0x00007ff6c74fefad (Unity) StackWalker::GetCurrentCallstack
0x00007ff6c7505b39 (Unity) StackWalker::ShowCallstack
0x00007ff6c84a8df3 (Unity) GetStacktrace
0x00007ff6c8b7165d (Unity) DebugStringToFile
0x00007ff6c664ea32 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x000001cc532ac083 (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
0x000001cc532abd5b (Mono JIT Code) UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
0x000001cc532abae0 (Mono JIT Code) UnityEngine.Logger:Log (UnityEngine.LogType,object)
0x000001cc5c74d045 (Mono JIT Code) UnityEngine.Debug:LogError (object)
0x000001cc5c738c9b (Mono JIT Code) UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes ()
0x000001cc5c706b35 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
0x00007ffb3c53697e (mono-2.0-bdwgc) [mini-runtime.c:3445] mono_jit_runtime_invoke 
0x00007ffb3c46cdd4 (mono-2.0-bdwgc) [object.c:3068] do_runtime_invoke 
0x00007ffb3c46cf4c (mono-2.0-bdwgc) [object.c:3115] mono_runtime_invoke 
0x00007ff6c7421934 (Unity) scripting_method_invoke
0x00007ff6c7400b84 (Unity) ScriptingInvocation::Invoke
0x00007ff6c73fb6e5 (Unity) ScriptingInvocation::Invoke<void>
0x00007ff6c753f53b (Unity) Scripting::UnityEditor::EditorAssembliesProxy::ProcessInitializeOnLoadMethodAttributes
0x00007ff6c73f5733 (Unity) MonoManager::SetupLoadedEditorAssemblies
0x00007ff6c73eb1ed (Unity) MonoManager::EndReloadAssembly
0x00007ff6c73f2d88 (Unity) MonoManager::ReloadAssembly
0x00007ff6c835fa12 (Unity) LoadDomainAndUserAssemblies
0x00007ff6c8360144 (Unity) LoadUserAssemblies
0x00007ff6c885c091 (Unity) <lambda_f8411f227bdd47da33e064bfa1662688>::operator()
0x00007ff6c889ce92 (Unity) asio::detail::completion_handler<core::mutable_function<void __cdecl(void)>,asio::io_context::basic_executor_type<std::allocator<void>,0> >::do_complete
0x00007ff6c8888d7e (Unity) asio::detail::win_iocp_io_context::do_one
0x00007ff6c888a9e4 (Unity) asio::detail::win_iocp_io_context::run
0x00007ff6c889b27e (Unity) IOService::Run
0x00007ff6c886dbcf (Unity) AssetImportWorkerClient::Run
0x00007ff6c8835d13 (Unity) RunAssetImportWorkerClientV2
0x00007ff6c8835d9b (Unity) RunAssetImporterV2
0x00007ff6c8060c48 (Unity) Application::InitializeProject
0x00007ff6c84b20a0 (Unity) WinMain
0x00007ff6c987fbae (Unity) __scrt_common_main_seh
0x00007ffbd486259d (KERNEL32) BaseThreadInitThunk
0x00007ffbd4e8af78 (ntdll) RtlUserThreadStart

Mono: successfully reloaded assembly
- Completed reload, in  1.427 seconds
Domain Reload Profiling:
	ReloadAssembly (1428ms)
		BeginReloadAssembly (168ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (2ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (27ms)
		EndReloadAssembly (1153ms)
			LoadAssemblies (137ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (206ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (30ms)
			SetupLoadedEditorAssemblies (773ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (18ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (71ms)
				ProcessInitializeOnLoadAttributes (615ms)
				ProcessInitializeOnLoadMethodAttributes (28ms)
				AfterProcessingInitializeOnLoad (39ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (8ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 0.84 ms, found 13 plugins.
Preloading 2 native plugins for Editor in 0.21 ms.
Unloading 4752 Unused Serialized files (Serialized files now loaded: 0)
Unloading 50 unused Assets / (95.7 KB). Loaded Objects now: 5288.
Memory consumption went from 192.1 MB to 192.0 MB.
Total: 2.117400 ms (FindLiveObjects: 0.181700 ms CreateObjectMapping: 0.057200 ms MarkObjects: 1.842000 ms  DeleteObjects: 0.035600 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 13.48 ms, found 13 plugins.
Preloading 2 native plugins for Editor in 0.14 ms.
Unloading 45 Unused Serialized files (Serialized files now loaded: 0)
Unloading 45 unused Assets / (95.6 KB). Loaded Objects now: 5288.
Memory consumption went from 97.9 MB to 97.8 MB.
Total: 3.064400 ms (FindLiveObjects: 0.224200 ms CreateObjectMapping: 0.192900 ms MarkObjects: 2.617700 ms  DeleteObjects: 0.027800 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.018948 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.04 ms, found 13 plugins.
Preloading 2 native plugins for Editor in 0.22 ms.
PXRLog [Build Check]RegisterBuildPlayerHandler,Already Do not show: False
0x00007ff6c74fefad (Unity) StackWalker::GetCurrentCallstack
0x00007ff6c7505b39 (Unity) StackWalker::ShowCallstack
0x00007ff6c84a8df3 (Unity) GetStacktrace
0x00007ff6c8b7165d (Unity) DebugStringToFile
0x00007ff6c664ea32 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x000001cc5be6c083 (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
0x000001cc5be6bd5b (Mono JIT Code) UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
0x000001cc5be6bae0 (Mono JIT Code) UnityEngine.Logger:Log (UnityEngine.LogType,object)
0x000001cc5be6b9a8 (Mono JIT Code) UnityEngine.Debug:Log (object)
0x000001cc5be68a53 (Mono JIT Code) [PXR_SDKBuildCheck.cs:32] Unity.XR.PXR.Editor.PXR_SDKBuildCheck:.cctor () 
0x000001cc5bf97575 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
0x00007ffb3c53697e (mono-2.0-bdwgc) [mini-runtime.c:3445] mono_jit_runtime_invoke 
0x00007ffb3c46cdd4 (mono-2.0-bdwgc) [object.c:3068] do_runtime_invoke 
0x00007ffb3c467b79 (mono-2.0-bdwgc) [object.c:563] mono_runtime_class_init_full 
0x00007ffb3c39ddd7 (mono-2.0-bdwgc) [icall.c:1292] ves_icall_System_Runtime_CompilerServices_RuntimeHelpers_RunClassConstructor 
0x00007ffb3c3cfebc (mono-2.0-bdwgc) [icall-def.h:756] ves_icall_System_Runtime_CompilerServices_RuntimeHelpers_RunClassConstructor_raw 
0x000001cc5bf96c1a (Mono JIT Code) (wrapper managed-to-native) System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (intptr)
0x000001cc5bf96b2b (Mono JIT Code) System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
0x000001cc5bf96273 (Mono JIT Code) UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])
0x000001cc5bf86298 (Mono JIT Code) (wrapper runtime-invoke) <Module>:runtime_invoke_void_object (object,intptr,intptr,intptr)
0x00007ffb3c53697e (mono-2.0-bdwgc) [mini-runtime.c:3445] mono_jit_runtime_invoke 
0x00007ffb3c46cdd4 (mono-2.0-bdwgc) [object.c:3068] do_runtime_invoke 
0x00007ffb3c46cf4c (mono-2.0-bdwgc) [object.c:3115] mono_runtime_invoke 
0x00007ff6c7421934 (Unity) scripting_method_invoke
0x00007ff6c7400b84 (Unity) ScriptingInvocation::Invoke
0x00007ff6c73fb6e5 (Unity) ScriptingInvocation::Invoke<void>
0x00007ff6c753f4c3 (Unity) Scripting::UnityEditor::EditorAssembliesProxy::ProcessInitializeOnLoadAttributes
0x00007ff6c73f570b (Unity) MonoManager::SetupLoadedEditorAssemblies
0x00007ff6c73eb1ed (Unity) MonoManager::EndReloadAssembly
0x00007ff6c73f2d88 (Unity) MonoManager::ReloadAssembly
0x00007ff6c835fa12 (Unity) LoadDomainAndUserAssemblies
0x00007ff6c8360144 (Unity) LoadUserAssemblies
0x00007ff6c885c091 (Unity) <lambda_f8411f227bdd47da33e064bfa1662688>::operator()
0x00007ff6c889ce92 (Unity) asio::detail::completion_handler<core::mutable_function<void __cdecl(void)>,asio::io_context::basic_executor_type<std::allocator<void>,0> >::do_complete
0x00007ff6c8888d7e (Unity) asio::detail::win_iocp_io_context::do_one
0x00007ff6c888a9e4 (Unity) asio::detail::win_iocp_io_context::run
0x00007ff6c889b27e (Unity) IOService::Run
0x00007ff6c886dbcf (Unity) AssetImportWorkerClient::Run
0x00007ff6c8835d13 (Unity) RunAssetImportWorkerClientV2
0x00007ff6c8835d9b (Unity) RunAssetImporterV2
0x00007ff6c8060c48 (Unity) Application::InitializeProject
0x00007ff6c84b20a0 (Unity) WinMain
0x00007ff6c987fbae (Unity) __scrt_common_main_seh
0x00007ffbd486259d (KERNEL32) BaseThreadInitThunk
0x00007ffbd4e8af78 (ntdll) RtlUserThreadStart

[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
CreateSerializedAsset is not supported while importing out-of-process
0x00007ff6c74fefad (Unity) StackWalker::GetCurrentCallstack
0x00007ff6c7505b39 (Unity) StackWalker::ShowCallstack
0x00007ff6c84a8df3 (Unity) GetStacktrace
0x00007ff6c8b7165d (Unity) DebugStringToFile
0x00007ff6c887b618 (Unity) CreateSerializedAssetV2
0x00007ff6c8810f55 (Unity) CreateSerializedAssetV2
0x00007ff6c877f8ee (Unity) AssetDatabase::CreateSerializedAsset
0x00007ff6c6cea4ff (Unity) AssetDatabaseBindings::CreateAsset
0x00007ff6c6b0b819 (Unity) AssetDatabase_CUSTOM_CreateAsset
0x000001cc5c75cdb5 (Mono JIT Code) (wrapper managed-to-native) UnityEditor.AssetDatabase:CreateAsset (UnityEngine.Object,string)
0x000001cc5c75c753 (Mono JIT Code) [PXR_ProjectSetting.cs:121] Unity.XR.PXR.PXR_ProjectSetting:GetProjectConfig () 
0x000001cc5c75c383 (Mono JIT Code) [PXR_SDKSettingEditor.cs:111] Unity.XR.PXR.Editor.PXR_SDKSettingEditor:InitializeOnLoad () 
0x000001cc5bf975c3 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
0x00007ffb3c53697e (mono-2.0-bdwgc) [mini-runtime.c:3445] mono_jit_runtime_invoke 
0x00007ffb3c46cdd4 (mono-2.0-bdwgc) [object.c:3068] do_runtime_invoke 
0x00007ffb3c4746df (mono-2.0-bdwgc) [object.c:5770] mono_runtime_try_invoke_array 
0x00007ffb3c3a4eb2 (mono-2.0-bdwgc) [icall.c:4041] ves_icall_InternalInvoke 
0x00007ffb3c3cb091 (mono-2.0-bdwgc) [icall-def.h:692] ves_icall_InternalInvoke_raw 
0x000001cc5c74ae20 (Mono JIT Code) (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo:InternalInvoke (System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
0x000001cc5c74a5bb (Mono JIT Code) System.Reflection.RuntimeMethodInfo:Invoke (object,System.Reflection.BindingFlags,System.Reflection.Binder,object[],System.Globalization.CultureInfo)
0x000001cc5c74a47e (Mono JIT Code) System.Reflection.MethodBase:Invoke (object,object[])
0x000001cc5c728c7b (Mono JIT Code) UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes ()
0x000001cc5bf86b35 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
0x00007ffb3c53697e (mono-2.0-bdwgc) [mini-runtime.c:3445] mono_jit_runtime_invoke 
0x00007ffb3c46cdd4 (mono-2.0-bdwgc) [object.c:3068] do_runtime_invoke 
0x00007ffb3c46cf4c (mono-2.0-bdwgc) [object.c:3115] mono_runtime_invoke 
0x00007ff6c7421934 (Unity) scripting_method_invoke
0x00007ff6c7400b84 (Unity) ScriptingInvocation::Invoke
0x00007ff6c73fb6e5 (Unity) ScriptingInvocation::Invoke<void>
0x00007ff6c753f53b (Unity) Scripting::UnityEditor::EditorAssembliesProxy::ProcessInitializeOnLoadMethodAttributes
0x00007ff6c73f5733 (Unity) MonoManager::SetupLoadedEditorAssemblies
0x00007ff6c73eb1ed (Unity) MonoManager::EndReloadAssembly
0x00007ff6c73f2d88 (Unity) MonoManager::ReloadAssembly
0x00007ff6c835fa12 (Unity) LoadDomainAndUserAssemblies
0x00007ff6c8360144 (Unity) LoadUserAssemblies
0x00007ff6c885c091 (Unity) <lambda_f8411f227bdd47da33e064bfa1662688>::operator()
0x00007ff6c889ce92 (Unity) asio::detail::completion_handler<core::mutable_function<void __cdecl(void)>,asio::io_context::basic_executor_type<std::allocator<void>,0> >::do_complete
0x00007ff6c8888d7e (Unity) asio::detail::win_iocp_io_context::do_one
0x00007ff6c888a9e4 (Unity) asio::detail::win_iocp_io_context::run
0x00007ff6c889b27e (Unity) IOService::Run
0x00007ff6c886dbcf (Unity) AssetImportWorkerClient::Run
0x00007ff6c8835d13 (Unity) RunAssetImportWorkerClientV2
0x00007ff6c8835d9b (Unity) RunAssetImporterV2
0x00007ff6c8060c48 (Unity) Application::InitializeProject
0x00007ff6c84b20a0 (Unity) WinMain
0x00007ff6c987fbae (Unity) __scrt_common_main_seh
0x00007ffbd486259d (KERNEL32) BaseThreadInitThunk
0x00007ffbd4e8af78 (ntdll) RtlUserThreadStart

System.Reflection.TargetInvocationException: Exception has been thrown by the target of an invocation. ---> UnityEngine.UnityException: Creating asset at path Assets/Resources/PXR_ProjectSetting.asset failed.
  at (wrapper managed-to-native) UnityEditor.AssetDatabase.CreateAsset(UnityEngine.Object,string)
  at Unity.XR.PXR.PXR_ProjectSetting.GetProjectConfig () [0x0015a] in D:\PICO Unity Integration SDK-3.2.0-20250529\Runtime\Scripts\PXR_ProjectSetting.cs:117 
  at Unity.XR.PXR.Editor.PXR_SDKSettingEditor.InitializeOnLoad () [0x00000] in D:\PICO Unity Integration SDK-3.2.0-20250529\Editor\PXR_SDKSettingEditor.cs:111 
  at (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo.InternalInvoke(System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x0006a] in <688f272cc27c4df98c503f9d1f1267d0>:0 
   --- End of inner exception stack trace ---
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x00083] in <688f272cc27c4df98c503f9d1f1267d0>:0 
  at System.Reflection.MethodBase.Invoke (System.Object obj, System.Object[] parameters) [0x00000] in <688f272cc27c4df98c503f9d1f1267d0>:0 
  at UnityEditor.EditorAssemblies.ProcessInitializeOnLoadMethodAttributes () [0x000a5] in <1518627dacca44efa32edd45bc720c48>:0 
0x00007ff6c74fefad (Unity) StackWalker::GetCurrentCallstack
0x00007ff6c7505b39 (Unity) StackWalker::ShowCallstack
0x00007ff6c84a8df3 (Unity) GetStacktrace
0x00007ff6c8b7165d (Unity) DebugStringToFile
0x00007ff6c664ea32 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x000001cc5be6c083 (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
0x000001cc5be6bd5b (Mono JIT Code) UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
0x000001cc5be6bae0 (Mono JIT Code) UnityEngine.Logger:Log (UnityEngine.LogType,object)
0x000001cc5c75d045 (Mono JIT Code) UnityEngine.Debug:LogError (object)
0x000001cc5c728c9b (Mono JIT Code) UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes ()
0x000001cc5bf86b35 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
0x00007ffb3c53697e (mono-2.0-bdwgc) [mini-runtime.c:3445] mono_jit_runtime_invoke 
0x00007ffb3c46cdd4 (mono-2.0-bdwgc) [object.c:3068] do_runtime_invoke 
0x00007ffb3c46cf4c (mono-2.0-bdwgc) [object.c:3115] mono_runtime_invoke 
0x00007ff6c7421934 (Unity) scripting_method_invoke
0x00007ff6c7400b84 (Unity) ScriptingInvocation::Invoke
0x00007ff6c73fb6e5 (Unity) ScriptingInvocation::Invoke<void>
0x00007ff6c753f53b (Unity) Scripting::UnityEditor::EditorAssembliesProxy::ProcessInitializeOnLoadMethodAttributes
0x00007ff6c73f5733 (Unity) MonoManager::SetupLoadedEditorAssemblies
0x00007ff6c73eb1ed (Unity) MonoManager::EndReloadAssembly
0x00007ff6c73f2d88 (Unity) MonoManager::ReloadAssembly
0x00007ff6c835fa12 (Unity) LoadDomainAndUserAssemblies
0x00007ff6c8360144 (Unity) LoadUserAssemblies
0x00007ff6c885c091 (Unity) <lambda_f8411f227bdd47da33e064bfa1662688>::operator()
0x00007ff6c889ce92 (Unity) asio::detail::completion_handler<core::mutable_function<void __cdecl(void)>,asio::io_context::basic_executor_type<std::allocator<void>,0> >::do_complete
0x00007ff6c8888d7e (Unity) asio::detail::win_iocp_io_context::do_one
0x00007ff6c888a9e4 (Unity) asio::detail::win_iocp_io_context::run
0x00007ff6c889b27e (Unity) IOService::Run
0x00007ff6c886dbcf (Unity) AssetImportWorkerClient::Run
0x00007ff6c8835d13 (Unity) RunAssetImportWorkerClientV2
0x00007ff6c8835d9b (Unity) RunAssetImporterV2
0x00007ff6c8060c48 (Unity) Application::InitializeProject
0x00007ff6c84b20a0 (Unity) WinMain
0x00007ff6c987fbae (Unity) __scrt_common_main_seh
0x00007ffbd486259d (KERNEL32) BaseThreadInitThunk
0x00007ffbd4e8af78 (ntdll) RtlUserThreadStart

Mono: successfully reloaded assembly
- Completed reload, in  1.616 seconds
Domain Reload Profiling:
	ReloadAssembly (1616ms)
		BeginReloadAssembly (222ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (3ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (47ms)
		EndReloadAssembly (1264ms)
			LoadAssemblies (177ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (238ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (41ms)
			SetupLoadedEditorAssemblies (802ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (20ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (76ms)
				ProcessInitializeOnLoadAttributes (630ms)
				ProcessInitializeOnLoadMethodAttributes (33ms)
				AfterProcessingInitializeOnLoad (41ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (10ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 0.75 ms, found 13 plugins.
Preloading 2 native plugins for Editor in 0.20 ms.
Unloading 4752 Unused Serialized files (Serialized files now loaded: 0)
Unloading 50 unused Assets / (95.6 KB). Loaded Objects now: 5305.
Memory consumption went from 192.2 MB to 192.1 MB.
Total: 3.232100 ms (FindLiveObjects: 0.597100 ms CreateObjectMapping: 0.103000 ms MarkObjects: 2.489200 ms  DeleteObjects: 0.040400 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.014455 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 0.72 ms, found 13 plugins.
Preloading 2 native plugins for Editor in 0.19 ms.
PXRLog [Build Check]RegisterBuildPlayerHandler,Already Do not show: False
0x00007ff6c74fefad (Unity) StackWalker::GetCurrentCallstack
0x00007ff6c7505b39 (Unity) StackWalker::ShowCallstack
0x00007ff6c84a8df3 (Unity) GetStacktrace
0x00007ff6c8b7165d (Unity) DebugStringToFile
0x00007ff6c664ea32 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x000001cc5c58c083 (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
0x000001cc5c58bd5b (Mono JIT Code) UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
0x000001cc5c58bae0 (Mono JIT Code) UnityEngine.Logger:Log (UnityEngine.LogType,object)
0x000001cc5c58b9a8 (Mono JIT Code) UnityEngine.Debug:Log (object)
0x000001cc5c588a53 (Mono JIT Code) [PXR_SDKBuildCheck.cs:32] Unity.XR.PXR.Editor.PXR_SDKBuildCheck:.cctor () 
0x000001cc5c6f7575 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
0x00007ffb3c53697e (mono-2.0-bdwgc) [mini-runtime.c:3445] mono_jit_runtime_invoke 
0x00007ffb3c46cdd4 (mono-2.0-bdwgc) [object.c:3068] do_runtime_invoke 
0x00007ffb3c467b79 (mono-2.0-bdwgc) [object.c:563] mono_runtime_class_init_full 
0x00007ffb3c39ddd7 (mono-2.0-bdwgc) [icall.c:1292] ves_icall_System_Runtime_CompilerServices_RuntimeHelpers_RunClassConstructor 
0x00007ffb3c3cfebc (mono-2.0-bdwgc) [icall-def.h:756] ves_icall_System_Runtime_CompilerServices_RuntimeHelpers_RunClassConstructor_raw 
0x000001cc5c6f6c1a (Mono JIT Code) (wrapper managed-to-native) System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (intptr)
0x000001cc5c6f6b2b (Mono JIT Code) System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
0x000001cc5c6f6273 (Mono JIT Code) UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])
0x000001cc532a6298 (Mono JIT Code) (wrapper runtime-invoke) <Module>:runtime_invoke_void_object (object,intptr,intptr,intptr)
0x00007ffb3c53697e (mono-2.0-bdwgc) [mini-runtime.c:3445] mono_jit_runtime_invoke 
0x00007ffb3c46cdd4 (mono-2.0-bdwgc) [object.c:3068] do_runtime_invoke 
0x00007ffb3c46cf4c (mono-2.0-bdwgc) [object.c:3115] mono_runtime_invoke 
0x00007ff6c7421934 (Unity) scripting_method_invoke
0x00007ff6c7400b84 (Unity) ScriptingInvocation::Invoke
0x00007ff6c73fb6e5 (Unity) ScriptingInvocation::Invoke<void>
0x00007ff6c753f4c3 (Unity) Scripting::UnityEditor::EditorAssembliesProxy::ProcessInitializeOnLoadAttributes
0x00007ff6c73f570b (Unity) MonoManager::SetupLoadedEditorAssemblies
0x00007ff6c73eb1ed (Unity) MonoManager::EndReloadAssembly
0x00007ff6c73f2d88 (Unity) MonoManager::ReloadAssembly
0x00007ff6c835fa12 (Unity) LoadDomainAndUserAssemblies
0x00007ff6c8360144 (Unity) LoadUserAssemblies
0x00007ff6c885c091 (Unity) <lambda_f8411f227bdd47da33e064bfa1662688>::operator()
0x00007ff6c889ce92 (Unity) asio::detail::completion_handler<core::mutable_function<void __cdecl(void)>,asio::io_context::basic_executor_type<std::allocator<void>,0> >::do_complete
0x00007ff6c8888d7e (Unity) asio::detail::win_iocp_io_context::do_one
0x00007ff6c888a9e4 (Unity) asio::detail::win_iocp_io_context::run
0x00007ff6c889b27e (Unity) IOService::Run
0x00007ff6c886dbcf (Unity) AssetImportWorkerClient::Run
0x00007ff6c8835d13 (Unity) RunAssetImportWorkerClientV2
0x00007ff6c8835d9b (Unity) RunAssetImporterV2
0x00007ff6c8060c48 (Unity) Application::InitializeProject
0x00007ff6c84b20a0 (Unity) WinMain
0x00007ff6c987fbae (Unity) __scrt_common_main_seh
0x00007ffbd486259d (KERNEL32) BaseThreadInitThunk
0x00007ffbd4e8af78 (ntdll) RtlUserThreadStart

[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
CreateSerializedAsset is not supported while importing out-of-process
0x00007ff6c74fefad (Unity) StackWalker::GetCurrentCallstack
0x00007ff6c7505b39 (Unity) StackWalker::ShowCallstack
0x00007ff6c84a8df3 (Unity) GetStacktrace
0x00007ff6c8b7165d (Unity) DebugStringToFile
0x00007ff6c887b618 (Unity) CreateSerializedAssetV2
0x00007ff6c8810f55 (Unity) CreateSerializedAssetV2
0x00007ff6c877f8ee (Unity) AssetDatabase::CreateSerializedAsset
0x00007ff6c6cea4ff (Unity) AssetDatabaseBindings::CreateAsset
0x00007ff6c6b0b819 (Unity) AssetDatabase_CUSTOM_CreateAsset
0x000001cc53dacdb5 (Mono JIT Code) (wrapper managed-to-native) UnityEditor.AssetDatabase:CreateAsset (UnityEngine.Object,string)
0x000001cc53dac753 (Mono JIT Code) [PXR_ProjectSetting.cs:121] Unity.XR.PXR.PXR_ProjectSetting:GetProjectConfig () 
0x000001cc53dac383 (Mono JIT Code) [PXR_SDKSettingEditor.cs:111] Unity.XR.PXR.Editor.PXR_SDKSettingEditor:InitializeOnLoad () 
0x000001cc5c6f75c3 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
0x00007ffb3c53697e (mono-2.0-bdwgc) [mini-runtime.c:3445] mono_jit_runtime_invoke 
0x00007ffb3c46cdd4 (mono-2.0-bdwgc) [object.c:3068] do_runtime_invoke 
0x00007ffb3c4746df (mono-2.0-bdwgc) [object.c:5770] mono_runtime_try_invoke_array 
0x00007ffb3c3a4eb2 (mono-2.0-bdwgc) [icall.c:4041] ves_icall_InternalInvoke 
0x00007ffb3c3cb091 (mono-2.0-bdwgc) [icall-def.h:692] ves_icall_InternalInvoke_raw 
0x000001cc5c57ae20 (Mono JIT Code) (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo:InternalInvoke (System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
0x000001cc5c57a5bb (Mono JIT Code) System.Reflection.RuntimeMethodInfo:Invoke (object,System.Reflection.BindingFlags,System.Reflection.Binder,object[],System.Globalization.CultureInfo)
0x000001cc5c57a47e (Mono JIT Code) System.Reflection.MethodBase:Invoke (object,object[])
0x000001cc53d98c7b (Mono JIT Code) UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes ()
0x000001cc532a6b35 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
0x00007ffb3c53697e (mono-2.0-bdwgc) [mini-runtime.c:3445] mono_jit_runtime_invoke 
0x00007ffb3c46cdd4 (mono-2.0-bdwgc) [object.c:3068] do_runtime_invoke 
0x00007ffb3c46cf4c (mono-2.0-bdwgc) [object.c:3115] mono_runtime_invoke 
0x00007ff6c7421934 (Unity) scripting_method_invoke
0x00007ff6c7400b84 (Unity) ScriptingInvocation::Invoke
0x00007ff6c73fb6e5 (Unity) ScriptingInvocation::Invoke<void>
0x00007ff6c753f53b (Unity) Scripting::UnityEditor::EditorAssembliesProxy::ProcessInitializeOnLoadMethodAttributes
0x00007ff6c73f5733 (Unity) MonoManager::SetupLoadedEditorAssemblies
0x00007ff6c73eb1ed (Unity) MonoManager::EndReloadAssembly
0x00007ff6c73f2d88 (Unity) MonoManager::ReloadAssembly
0x00007ff6c835fa12 (Unity) LoadDomainAndUserAssemblies
0x00007ff6c8360144 (Unity) LoadUserAssemblies
0x00007ff6c885c091 (Unity) <lambda_f8411f227bdd47da33e064bfa1662688>::operator()
0x00007ff6c889ce92 (Unity) asio::detail::completion_handler<core::mutable_function<void __cdecl(void)>,asio::io_context::basic_executor_type<std::allocator<void>,0> >::do_complete
0x00007ff6c8888d7e (Unity) asio::detail::win_iocp_io_context::do_one
0x00007ff6c888a9e4 (Unity) asio::detail::win_iocp_io_context::run
0x00007ff6c889b27e (Unity) IOService::Run
0x00007ff6c886dbcf (Unity) AssetImportWorkerClient::Run
0x00007ff6c8835d13 (Unity) RunAssetImportWorkerClientV2
0x00007ff6c8835d9b (Unity) RunAssetImporterV2
0x00007ff6c8060c48 (Unity) Application::InitializeProject
0x00007ff6c84b20a0 (Unity) WinMain
0x00007ff6c987fbae (Unity) __scrt_common_main_seh
0x00007ffbd486259d (KERNEL32) BaseThreadInitThunk
0x00007ffbd4e8af78 (ntdll) RtlUserThreadStart

System.Reflection.TargetInvocationException: Exception has been thrown by the target of an invocation. ---> UnityEngine.UnityException: Creating asset at path Assets/Resources/PXR_ProjectSetting.asset failed.
  at (wrapper managed-to-native) UnityEditor.AssetDatabase.CreateAsset(UnityEngine.Object,string)
  at Unity.XR.PXR.PXR_ProjectSetting.GetProjectConfig () [0x0015a] in D:\PICO Unity Integration SDK-3.2.0-20250529\Runtime\Scripts\PXR_ProjectSetting.cs:117 
  at Unity.XR.PXR.Editor.PXR_SDKSettingEditor.InitializeOnLoad () [0x00000] in D:\PICO Unity Integration SDK-3.2.0-20250529\Editor\PXR_SDKSettingEditor.cs:111 
  at (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo.InternalInvoke(System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x0006a] in <688f272cc27c4df98c503f9d1f1267d0>:0 
   --- End of inner exception stack trace ---
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x00083] in <688f272cc27c4df98c503f9d1f1267d0>:0 
  at System.Reflection.MethodBase.Invoke (System.Object obj, System.Object[] parameters) [0x00000] in <688f272cc27c4df98c503f9d1f1267d0>:0 
  at UnityEditor.EditorAssemblies.ProcessInitializeOnLoadMethodAttributes () [0x000a5] in <1518627dacca44efa32edd45bc720c48>:0 
0x00007ff6c74fefad (Unity) StackWalker::GetCurrentCallstack
0x00007ff6c7505b39 (Unity) StackWalker::ShowCallstack
0x00007ff6c84a8df3 (Unity) GetStacktrace
0x00007ff6c8b7165d (Unity) DebugStringToFile
0x00007ff6c664ea32 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x000001cc5c58c083 (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
0x000001cc5c58bd5b (Mono JIT Code) UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
0x000001cc5c58bae0 (Mono JIT Code) UnityEngine.Logger:Log (UnityEngine.LogType,object)
0x000001cc53dad045 (Mono JIT Code) UnityEngine.Debug:LogError (object)
0x000001cc53d98c9b (Mono JIT Code) UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes ()
0x000001cc532a6b35 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
0x00007ffb3c53697e (mono-2.0-bdwgc) [mini-runtime.c:3445] mono_jit_runtime_invoke 
0x00007ffb3c46cdd4 (mono-2.0-bdwgc) [object.c:3068] do_runtime_invoke 
0x00007ffb3c46cf4c (mono-2.0-bdwgc) [object.c:3115] mono_runtime_invoke 
0x00007ff6c7421934 (Unity) scripting_method_invoke
0x00007ff6c7400b84 (Unity) ScriptingInvocation::Invoke
0x00007ff6c73fb6e5 (Unity) ScriptingInvocation::Invoke<void>
0x00007ff6c753f53b (Unity) Scripting::UnityEditor::EditorAssembliesProxy::ProcessInitializeOnLoadMethodAttributes
0x00007ff6c73f5733 (Unity) MonoManager::SetupLoadedEditorAssemblies
0x00007ff6c73eb1ed (Unity) MonoManager::EndReloadAssembly
0x00007ff6c73f2d88 (Unity) MonoManager::ReloadAssembly
0x00007ff6c835fa12 (Unity) LoadDomainAndUserAssemblies
0x00007ff6c8360144 (Unity) LoadUserAssemblies
0x00007ff6c885c091 (Unity) <lambda_f8411f227bdd47da33e064bfa1662688>::operator()
0x00007ff6c889ce92 (Unity) asio::detail::completion_handler<core::mutable_function<void __cdecl(void)>,asio::io_context::basic_executor_type<std::allocator<void>,0> >::do_complete
0x00007ff6c8888d7e (Unity) asio::detail::win_iocp_io_context::do_one
0x00007ff6c888a9e4 (Unity) asio::detail::win_iocp_io_context::run
0x00007ff6c889b27e (Unity) IOService::Run
0x00007ff6c886dbcf (Unity) AssetImportWorkerClient::Run
0x00007ff6c8835d13 (Unity) RunAssetImportWorkerClientV2
0x00007ff6c8835d9b (Unity) RunAssetImporterV2
0x00007ff6c8060c48 (Unity) Application::InitializeProject
0x00007ff6c84b20a0 (Unity) WinMain
0x00007ff6c987fbae (Unity) __scrt_common_main_seh
0x00007ffbd486259d (KERNEL32) BaseThreadInitThunk
0x00007ffbd4e8af78 (ntdll) RtlUserThreadStart

Mono: successfully reloaded assembly
- Completed reload, in  1.391 seconds
Domain Reload Profiling:
	ReloadAssembly (1391ms)
		BeginReloadAssembly (176ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (2ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (28ms)
		EndReloadAssembly (1109ms)
			LoadAssemblies (141ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (205ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (28ms)
			SetupLoadedEditorAssemblies (731ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (17ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (68ms)
				ProcessInitializeOnLoadAttributes (582ms)
				ProcessInitializeOnLoadMethodAttributes (26ms)
				AfterProcessingInitializeOnLoad (36ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (6ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 0.70 ms, found 13 plugins.
Preloading 2 native plugins for Editor in 0.22 ms.
Unloading 4752 Unused Serialized files (Serialized files now loaded: 0)
Unloading 50 unused Assets / (95.7 KB). Loaded Objects now: 5322.
Memory consumption went from 192.2 MB to 192.1 MB.
Total: 2.133800 ms (FindLiveObjects: 0.182000 ms CreateObjectMapping: 0.075300 ms MarkObjects: 1.839200 ms  DeleteObjects: 0.036700 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.027733 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 0.67 ms, found 13 plugins.
Preloading 2 native plugins for Editor in 0.32 ms.
PXRLog [Build Check]RegisterBuildPlayerHandler,Already Do not show: False
0x00007ff6c74fefad (Unity) StackWalker::GetCurrentCallstack
0x00007ff6c7505b39 (Unity) StackWalker::ShowCallstack
0x00007ff6c84a8df3 (Unity) GetStacktrace
0x00007ff6c8b7165d (Unity) DebugStringToFile
0x00007ff6c664ea32 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x000001cc53d6c083 (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
0x000001cc53d6bd5b (Mono JIT Code) UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
0x000001cc53d6bae0 (Mono JIT Code) UnityEngine.Logger:Log (UnityEngine.LogType,object)
0x000001cc53d6b9a8 (Mono JIT Code) UnityEngine.Debug:Log (object)
0x000001cc53d68a53 (Mono JIT Code) [PXR_SDKBuildCheck.cs:32] Unity.XR.PXR.Editor.PXR_SDKBuildCheck:.cctor () 
0x000001cc53d77575 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
0x00007ffb3c53697e (mono-2.0-bdwgc) [mini-runtime.c:3445] mono_jit_runtime_invoke 
0x00007ffb3c46cdd4 (mono-2.0-bdwgc) [object.c:3068] do_runtime_invoke 
0x00007ffb3c467b79 (mono-2.0-bdwgc) [object.c:563] mono_runtime_class_init_full 
0x00007ffb3c39ddd7 (mono-2.0-bdwgc) [icall.c:1292] ves_icall_System_Runtime_CompilerServices_RuntimeHelpers_RunClassConstructor 
0x00007ffb3c3cfebc (mono-2.0-bdwgc) [icall-def.h:756] ves_icall_System_Runtime_CompilerServices_RuntimeHelpers_RunClassConstructor_raw 
0x000001cc53d76c1a (Mono JIT Code) (wrapper managed-to-native) System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (intptr)
0x000001cc53d76b2b (Mono JIT Code) System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
0x000001cc53d76273 (Mono JIT Code) UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])
0x000001cc530a6298 (Mono JIT Code) (wrapper runtime-invoke) <Module>:runtime_invoke_void_object (object,intptr,intptr,intptr)
0x00007ffb3c53697e (mono-2.0-bdwgc) [mini-runtime.c:3445] mono_jit_runtime_invoke 
0x00007ffb3c46cdd4 (mono-2.0-bdwgc) [object.c:3068] do_runtime_invoke 
0x00007ffb3c46cf4c (mono-2.0-bdwgc) [object.c:3115] mono_runtime_invoke 
0x00007ff6c7421934 (Unity) scripting_method_invoke
0x00007ff6c7400b84 (Unity) ScriptingInvocation::Invoke
0x00007ff6c73fb6e5 (Unity) ScriptingInvocation::Invoke<void>
0x00007ff6c753f4c3 (Unity) Scripting::UnityEditor::EditorAssembliesProxy::ProcessInitializeOnLoadAttributes
0x00007ff6c73f570b (Unity) MonoManager::SetupLoadedEditorAssemblies
0x00007ff6c73eb1ed (Unity) MonoManager::EndReloadAssembly
0x00007ff6c73f2d88 (Unity) MonoManager::ReloadAssembly
0x00007ff6c835fa12 (Unity) LoadDomainAndUserAssemblies
0x00007ff6c8360144 (Unity) LoadUserAssemblies
0x00007ff6c885c091 (Unity) <lambda_f8411f227bdd47da33e064bfa1662688>::operator()
0x00007ff6c889ce92 (Unity) asio::detail::completion_handler<core::mutable_function<void __cdecl(void)>,asio::io_context::basic_executor_type<std::allocator<void>,0> >::do_complete
0x00007ff6c8888d7e (Unity) asio::detail::win_iocp_io_context::do_one
0x00007ff6c888a9e4 (Unity) asio::detail::win_iocp_io_context::run
0x00007ff6c889b27e (Unity) IOService::Run
0x00007ff6c886dbcf (Unity) AssetImportWorkerClient::Run
0x00007ff6c8835d13 (Unity) RunAssetImportWorkerClientV2
0x00007ff6c8835d9b (Unity) RunAssetImporterV2
0x00007ff6c8060c48 (Unity) Application::InitializeProject
0x00007ff6c84b20a0 (Unity) WinMain
0x00007ff6c987fbae (Unity) __scrt_common_main_seh
0x00007ffbd486259d (KERNEL32) BaseThreadInitThunk
0x00007ffbd4e8af78 (ntdll) RtlUserThreadStart

[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
CreateSerializedAsset is not supported while importing out-of-process
0x00007ff6c74fefad (Unity) StackWalker::GetCurrentCallstack
0x00007ff6c7505b39 (Unity) StackWalker::ShowCallstack
0x00007ff6c84a8df3 (Unity) GetStacktrace
0x00007ff6c8b7165d (Unity) DebugStringToFile
0x00007ff6c887b618 (Unity) CreateSerializedAssetV2
0x00007ff6c8810f55 (Unity) CreateSerializedAssetV2
0x00007ff6c877f8ee (Unity) AssetDatabase::CreateSerializedAsset
0x00007ff6c6cea4ff (Unity) AssetDatabaseBindings::CreateAsset
0x00007ff6c6b0b819 (Unity) AssetDatabase_CUSTOM_CreateAsset
0x000001cc53edcdb5 (Mono JIT Code) (wrapper managed-to-native) UnityEditor.AssetDatabase:CreateAsset (UnityEngine.Object,string)
0x000001cc53edc753 (Mono JIT Code) [PXR_ProjectSetting.cs:121] Unity.XR.PXR.PXR_ProjectSetting:GetProjectConfig () 
0x000001cc53edc383 (Mono JIT Code) [PXR_SDKSettingEditor.cs:111] Unity.XR.PXR.Editor.PXR_SDKSettingEditor:InitializeOnLoad () 
0x000001cc53d775c3 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
0x00007ffb3c53697e (mono-2.0-bdwgc) [mini-runtime.c:3445] mono_jit_runtime_invoke 
0x00007ffb3c46cdd4 (mono-2.0-bdwgc) [object.c:3068] do_runtime_invoke 
0x00007ffb3c4746df (mono-2.0-bdwgc) [object.c:5770] mono_runtime_try_invoke_array 
0x00007ffb3c3a4eb2 (mono-2.0-bdwgc) [icall.c:4041] ves_icall_InternalInvoke 
0x00007ffb3c3cb091 (mono-2.0-bdwgc) [icall-def.h:692] ves_icall_InternalInvoke_raw 
0x000001cc53d5ae20 (Mono JIT Code) (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo:InternalInvoke (System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
0x000001cc53d5a5bb (Mono JIT Code) System.Reflection.RuntimeMethodInfo:Invoke (object,System.Reflection.BindingFlags,System.Reflection.Binder,object[],System.Globalization.CultureInfo)
0x000001cc53d5a47e (Mono JIT Code) System.Reflection.MethodBase:Invoke (object,object[])
0x000001cc53ec8c7b (Mono JIT Code) UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes ()
0x000001cc530a6b35 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
0x00007ffb3c53697e (mono-2.0-bdwgc) [mini-runtime.c:3445] mono_jit_runtime_invoke 
0x00007ffb3c46cdd4 (mono-2.0-bdwgc) [object.c:3068] do_runtime_invoke 
0x00007ffb3c46cf4c (mono-2.0-bdwgc) [object.c:3115] mono_runtime_invoke 
0x00007ff6c7421934 (Unity) scripting_method_invoke
0x00007ff6c7400b84 (Unity) ScriptingInvocation::Invoke
0x00007ff6c73fb6e5 (Unity) ScriptingInvocation::Invoke<void>
0x00007ff6c753f53b (Unity) Scripting::UnityEditor::EditorAssembliesProxy::ProcessInitializeOnLoadMethodAttributes
0x00007ff6c73f5733 (Unity) MonoManager::SetupLoadedEditorAssemblies
0x00007ff6c73eb1ed (Unity) MonoManager::EndReloadAssembly
0x00007ff6c73f2d88 (Unity) MonoManager::ReloadAssembly
0x00007ff6c835fa12 (Unity) LoadDomainAndUserAssemblies
0x00007ff6c8360144 (Unity) LoadUserAssemblies
0x00007ff6c885c091 (Unity) <lambda_f8411f227bdd47da33e064bfa1662688>::operator()
0x00007ff6c889ce92 (Unity) asio::detail::completion_handler<core::mutable_function<void __cdecl(void)>,asio::io_context::basic_executor_type<std::allocator<void>,0> >::do_complete
0x00007ff6c8888d7e (Unity) asio::detail::win_iocp_io_context::do_one
0x00007ff6c888a9e4 (Unity) asio::detail::win_iocp_io_context::run
0x00007ff6c889b27e (Unity) IOService::Run
0x00007ff6c886dbcf (Unity) AssetImportWorkerClient::Run
0x00007ff6c8835d13 (Unity) RunAssetImportWorkerClientV2
0x00007ff6c8835d9b (Unity) RunAssetImporterV2
0x00007ff6c8060c48 (Unity) Application::InitializeProject
0x00007ff6c84b20a0 (Unity) WinMain
0x00007ff6c987fbae (Unity) __scrt_common_main_seh
0x00007ffbd486259d (KERNEL32) BaseThreadInitThunk
0x00007ffbd4e8af78 (ntdll) RtlUserThreadStart

System.Reflection.TargetInvocationException: Exception has been thrown by the target of an invocation. ---> UnityEngine.UnityException: Creating asset at path Assets/Resources/PXR_ProjectSetting.asset failed.
  at (wrapper managed-to-native) UnityEditor.AssetDatabase.CreateAsset(UnityEngine.Object,string)
  at Unity.XR.PXR.PXR_ProjectSetting.GetProjectConfig () [0x0015a] in D:\PICO Unity Integration SDK-3.2.0-20250529\Runtime\Scripts\PXR_ProjectSetting.cs:117 
  at Unity.XR.PXR.Editor.PXR_SDKSettingEditor.InitializeOnLoad () [0x00000] in D:\PICO Unity Integration SDK-3.2.0-20250529\Editor\PXR_SDKSettingEditor.cs:111 
  at (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo.InternalInvoke(System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x0006a] in <688f272cc27c4df98c503f9d1f1267d0>:0 
   --- End of inner exception stack trace ---
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x00083] in <688f272cc27c4df98c503f9d1f1267d0>:0 
  at System.Reflection.MethodBase.Invoke (System.Object obj, System.Object[] parameters) [0x00000] in <688f272cc27c4df98c503f9d1f1267d0>:0 
  at UnityEditor.EditorAssemblies.ProcessInitializeOnLoadMethodAttributes () [0x000a5] in <1518627dacca44efa32edd45bc720c48>:0 
0x00007ff6c74fefad (Unity) StackWalker::GetCurrentCallstack
0x00007ff6c7505b39 (Unity) StackWalker::ShowCallstack
0x00007ff6c84a8df3 (Unity) GetStacktrace
0x00007ff6c8b7165d (Unity) DebugStringToFile
0x00007ff6c664ea32 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x000001cc53d6c083 (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
0x000001cc53d6bd5b (Mono JIT Code) UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
0x000001cc53d6bae0 (Mono JIT Code) UnityEngine.Logger:Log (UnityEngine.LogType,object)
0x000001cc53edd045 (Mono JIT Code) UnityEngine.Debug:LogError (object)
0x000001cc53ec8c9b (Mono JIT Code) UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes ()
0x000001cc530a6b35 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
0x00007ffb3c53697e (mono-2.0-bdwgc) [mini-runtime.c:3445] mono_jit_runtime_invoke 
0x00007ffb3c46cdd4 (mono-2.0-bdwgc) [object.c:3068] do_runtime_invoke 
0x00007ffb3c46cf4c (mono-2.0-bdwgc) [object.c:3115] mono_runtime_invoke 
0x00007ff6c7421934 (Unity) scripting_method_invoke
0x00007ff6c7400b84 (Unity) ScriptingInvocation::Invoke
0x00007ff6c73fb6e5 (Unity) ScriptingInvocation::Invoke<void>
0x00007ff6c753f53b (Unity) Scripting::UnityEditor::EditorAssembliesProxy::ProcessInitializeOnLoadMethodAttributes
0x00007ff6c73f5733 (Unity) MonoManager::SetupLoadedEditorAssemblies
0x00007ff6c73eb1ed (Unity) MonoManager::EndReloadAssembly
0x00007ff6c73f2d88 (Unity) MonoManager::ReloadAssembly
0x00007ff6c835fa12 (Unity) LoadDomainAndUserAssemblies
0x00007ff6c8360144 (Unity) LoadUserAssemblies
0x00007ff6c885c091 (Unity) <lambda_f8411f227bdd47da33e064bfa1662688>::operator()
0x00007ff6c889ce92 (Unity) asio::detail::completion_handler<core::mutable_function<void __cdecl(void)>,asio::io_context::basic_executor_type<std::allocator<void>,0> >::do_complete
0x00007ff6c8888d7e (Unity) asio::detail::win_iocp_io_context::do_one
0x00007ff6c888a9e4 (Unity) asio::detail::win_iocp_io_context::run
0x00007ff6c889b27e (Unity) IOService::Run
0x00007ff6c886dbcf (Unity) AssetImportWorkerClient::Run
0x00007ff6c8835d13 (Unity) RunAssetImportWorkerClientV2
0x00007ff6c8835d9b (Unity) RunAssetImporterV2
0x00007ff6c8060c48 (Unity) Application::InitializeProject
0x00007ff6c84b20a0 (Unity) WinMain
0x00007ff6c987fbae (Unity) __scrt_common_main_seh
0x00007ffbd486259d (KERNEL32) BaseThreadInitThunk
0x00007ffbd4e8af78 (ntdll) RtlUserThreadStart

Mono: successfully reloaded assembly
- Completed reload, in  1.262 seconds
Domain Reload Profiling:
	ReloadAssembly (1263ms)
		BeginReloadAssembly (160ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (2ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (25ms)
		EndReloadAssembly (998ms)
			LoadAssemblies (135ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (176ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (26ms)
			SetupLoadedEditorAssemblies (669ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (15ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (60ms)
				ProcessInitializeOnLoadAttributes (544ms)
				ProcessInitializeOnLoadMethodAttributes (22ms)
				AfterProcessingInitializeOnLoad (26ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (5ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 0.71 ms, found 13 plugins.
Preloading 2 native plugins for Editor in 0.28 ms.
Unloading 4753 Unused Serialized files (Serialized files now loaded: 0)
Unloading 50 unused Assets / (95.8 KB). Loaded Objects now: 5340.
Memory consumption went from 192.2 MB to 192.1 MB.
Total: 2.079500 ms (FindLiveObjects: 0.199100 ms CreateObjectMapping: 0.059800 ms MarkObjects: 1.759800 ms  DeleteObjects: 0.059700 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.014186 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 0.63 ms, found 13 plugins.
Preloading 2 native plugins for Editor in 0.33 ms.
PXRLog [Build Check]RegisterBuildPlayerHandler,Already Do not show: False
0x00007ff6c74fefad (Unity) StackWalker::GetCurrentCallstack
0x00007ff6c7505b39 (Unity) StackWalker::ShowCallstack
0x00007ff6c84a8df3 (Unity) GetStacktrace
0x00007ff6c8b7165d (Unity) DebugStringToFile
0x00007ff6c664ea32 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x000001cc53e8c083 (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
0x000001cc53e8bd5b (Mono JIT Code) UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
0x000001cc53e8bae0 (Mono JIT Code) UnityEngine.Logger:Log (UnityEngine.LogType,object)
0x000001cc53e8b9a8 (Mono JIT Code) UnityEngine.Debug:Log (object)
0x000001cc53e88a53 (Mono JIT Code) [PXR_SDKBuildCheck.cs:32] Unity.XR.PXR.Editor.PXR_SDKBuildCheck:.cctor () 
0x000001cc53e97575 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
0x00007ffb3c53697e (mono-2.0-bdwgc) [mini-runtime.c:3445] mono_jit_runtime_invoke 
0x00007ffb3c46cdd4 (mono-2.0-bdwgc) [object.c:3068] do_runtime_invoke 
0x00007ffb3c467b79 (mono-2.0-bdwgc) [object.c:563] mono_runtime_class_init_full 
0x00007ffb3c39ddd7 (mono-2.0-bdwgc) [icall.c:1292] ves_icall_System_Runtime_CompilerServices_RuntimeHelpers_RunClassConstructor 
0x00007ffb3c3cfebc (mono-2.0-bdwgc) [icall-def.h:756] ves_icall_System_Runtime_CompilerServices_RuntimeHelpers_RunClassConstructor_raw 
0x000001cc53e96c1a (Mono JIT Code) (wrapper managed-to-native) System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (intptr)
0x000001cc53e96b2b (Mono JIT Code) System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
0x000001cc53e96273 (Mono JIT Code) UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])
0x000001cc53e36298 (Mono JIT Code) (wrapper runtime-invoke) <Module>:runtime_invoke_void_object (object,intptr,intptr,intptr)
0x00007ffb3c53697e (mono-2.0-bdwgc) [mini-runtime.c:3445] mono_jit_runtime_invoke 
0x00007ffb3c46cdd4 (mono-2.0-bdwgc) [object.c:3068] do_runtime_invoke 
0x00007ffb3c46cf4c (mono-2.0-bdwgc) [object.c:3115] mono_runtime_invoke 
0x00007ff6c7421934 (Unity) scripting_method_invoke
0x00007ff6c7400b84 (Unity) ScriptingInvocation::Invoke
0x00007ff6c73fb6e5 (Unity) ScriptingInvocation::Invoke<void>
0x00007ff6c753f4c3 (Unity) Scripting::UnityEditor::EditorAssembliesProxy::ProcessInitializeOnLoadAttributes
0x00007ff6c73f570b (Unity) MonoManager::SetupLoadedEditorAssemblies
0x00007ff6c73eb1ed (Unity) MonoManager::EndReloadAssembly
0x00007ff6c73f2d88 (Unity) MonoManager::ReloadAssembly
0x00007ff6c835fa12 (Unity) LoadDomainAndUserAssemblies
0x00007ff6c8360144 (Unity) LoadUserAssemblies
0x00007ff6c885c091 (Unity) <lambda_f8411f227bdd47da33e064bfa1662688>::operator()
0x00007ff6c889ce92 (Unity) asio::detail::completion_handler<core::mutable_function<void __cdecl(void)>,asio::io_context::basic_executor_type<std::allocator<void>,0> >::do_complete
0x00007ff6c8888d7e (Unity) asio::detail::win_iocp_io_context::do_one
0x00007ff6c888a9e4 (Unity) asio::detail::win_iocp_io_context::run
0x00007ff6c889b27e (Unity) IOService::Run
0x00007ff6c886dbcf (Unity) AssetImportWorkerClient::Run
0x00007ff6c8835d13 (Unity) RunAssetImportWorkerClientV2
0x00007ff6c8835d9b (Unity) RunAssetImporterV2
0x00007ff6c8060c48 (Unity) Application::InitializeProject
0x00007ff6c84b20a0 (Unity) WinMain
0x00007ff6c987fbae (Unity) __scrt_common_main_seh
0x00007ffbd486259d (KERNEL32) BaseThreadInitThunk
0x00007ffbd4e8af78 (ntdll) RtlUserThreadStart

[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
CreateSerializedAsset is not supported while importing out-of-process
0x00007ff6c74fefad (Unity) StackWalker::GetCurrentCallstack
0x00007ff6c7505b39 (Unity) StackWalker::ShowCallstack
0x00007ff6c84a8df3 (Unity) GetStacktrace
0x00007ff6c8b7165d (Unity) DebugStringToFile
0x00007ff6c887b618 (Unity) CreateSerializedAssetV2
0x00007ff6c8810f55 (Unity) CreateSerializedAssetV2
0x00007ff6c877f8ee (Unity) AssetDatabase::CreateSerializedAsset
0x00007ff6c6cea4ff (Unity) AssetDatabaseBindings::CreateAsset
0x00007ff6c6b0b819 (Unity) AssetDatabase_CUSTOM_CreateAsset
0x000001cc53d6cdb5 (Mono JIT Code) (wrapper managed-to-native) UnityEditor.AssetDatabase:CreateAsset (UnityEngine.Object,string)
0x000001cc53d6c753 (Mono JIT Code) [PXR_ProjectSetting.cs:121] Unity.XR.PXR.PXR_ProjectSetting:GetProjectConfig () 
0x000001cc53d6c383 (Mono JIT Code) [PXR_SDKSettingEditor.cs:111] Unity.XR.PXR.Editor.PXR_SDKSettingEditor:InitializeOnLoad () 
0x000001cc53e975c3 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
0x00007ffb3c53697e (mono-2.0-bdwgc) [mini-runtime.c:3445] mono_jit_runtime_invoke 
0x00007ffb3c46cdd4 (mono-2.0-bdwgc) [object.c:3068] do_runtime_invoke 
0x00007ffb3c4746df (mono-2.0-bdwgc) [object.c:5770] mono_runtime_try_invoke_array 
0x00007ffb3c3a4eb2 (mono-2.0-bdwgc) [icall.c:4041] ves_icall_InternalInvoke 
0x00007ffb3c3cb091 (mono-2.0-bdwgc) [icall-def.h:692] ves_icall_InternalInvoke_raw 
0x000001cc53edae20 (Mono JIT Code) (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo:InternalInvoke (System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
0x000001cc53eda5bb (Mono JIT Code) System.Reflection.RuntimeMethodInfo:Invoke (object,System.Reflection.BindingFlags,System.Reflection.Binder,object[],System.Globalization.CultureInfo)
0x000001cc53eda47e (Mono JIT Code) System.Reflection.MethodBase:Invoke (object,object[])
0x000001cc53d4905b (Mono JIT Code) UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes ()
0x000001cc53e36b35 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
0x00007ffb3c53697e (mono-2.0-bdwgc) [mini-runtime.c:3445] mono_jit_runtime_invoke 
0x00007ffb3c46cdd4 (mono-2.0-bdwgc) [object.c:3068] do_runtime_invoke 
0x00007ffb3c46cf4c (mono-2.0-bdwgc) [object.c:3115] mono_runtime_invoke 
0x00007ff6c7421934 (Unity) scripting_method_invoke
0x00007ff6c7400b84 (Unity) ScriptingInvocation::Invoke
0x00007ff6c73fb6e5 (Unity) ScriptingInvocation::Invoke<void>
0x00007ff6c753f53b (Unity) Scripting::UnityEditor::EditorAssembliesProxy::ProcessInitializeOnLoadMethodAttributes
0x00007ff6c73f5733 (Unity) MonoManager::SetupLoadedEditorAssemblies
0x00007ff6c73eb1ed (Unity) MonoManager::EndReloadAssembly
0x00007ff6c73f2d88 (Unity) MonoManager::ReloadAssembly
0x00007ff6c835fa12 (Unity) LoadDomainAndUserAssemblies
0x00007ff6c8360144 (Unity) LoadUserAssemblies
0x00007ff6c885c091 (Unity) <lambda_f8411f227bdd47da33e064bfa1662688>::operator()
0x00007ff6c889ce92 (Unity) asio::detail::completion_handler<core::mutable_function<void __cdecl(void)>,asio::io_context::basic_executor_type<std::allocator<void>,0> >::do_complete
0x00007ff6c8888d7e (Unity) asio::detail::win_iocp_io_context::do_one
0x00007ff6c888a9e4 (Unity) asio::detail::win_iocp_io_context::run
0x00007ff6c889b27e (Unity) IOService::Run
0x00007ff6c886dbcf (Unity) AssetImportWorkerClient::Run
0x00007ff6c8835d13 (Unity) RunAssetImportWorkerClientV2
0x00007ff6c8835d9b (Unity) RunAssetImporterV2
0x00007ff6c8060c48 (Unity) Application::InitializeProject
0x00007ff6c84b20a0 (Unity) WinMain
0x00007ff6c987fbae (Unity) __scrt_common_main_seh
0x00007ffbd486259d (KERNEL32) BaseThreadInitThunk
0x00007ffbd4e8af78 (ntdll) RtlUserThreadStart

System.Reflection.TargetInvocationException: Exception has been thrown by the target of an invocation. ---> UnityEngine.UnityException: Creating asset at path Assets/Resources/PXR_ProjectSetting.asset failed.
  at (wrapper managed-to-native) UnityEditor.AssetDatabase.CreateAsset(UnityEngine.Object,string)
  at Unity.XR.PXR.PXR_ProjectSetting.GetProjectConfig () [0x0015a] in D:\PICO Unity Integration SDK-3.2.0-20250529\Runtime\Scripts\PXR_ProjectSetting.cs:117 
  at Unity.XR.PXR.Editor.PXR_SDKSettingEditor.InitializeOnLoad () [0x00000] in D:\PICO Unity Integration SDK-3.2.0-20250529\Editor\PXR_SDKSettingEditor.cs:111 
  at (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo.InternalInvoke(System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x0006a] in <688f272cc27c4df98c503f9d1f1267d0>:0 
   --- End of inner exception stack trace ---
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x00083] in <688f272cc27c4df98c503f9d1f1267d0>:0 
  at System.Reflection.MethodBase.Invoke (System.Object obj, System.Object[] parameters) [0x00000] in <688f272cc27c4df98c503f9d1f1267d0>:0 
  at UnityEditor.EditorAssemblies.ProcessInitializeOnLoadMethodAttributes () [0x000a5] in <1518627dacca44efa32edd45bc720c48>:0 
0x00007ff6c74fefad (Unity) StackWalker::GetCurrentCallstack
0x00007ff6c7505b39 (Unity) StackWalker::ShowCallstack
0x00007ff6c84a8df3 (Unity) GetStacktrace
0x00007ff6c8b7165d (Unity) DebugStringToFile
0x00007ff6c664ea32 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x000001cc53e8c083 (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
0x000001cc53e8bd5b (Mono JIT Code) UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
0x000001cc53e8bae0 (Mono JIT Code) UnityEngine.Logger:Log (UnityEngine.LogType,object)
0x000001cc53d6d045 (Mono JIT Code) UnityEngine.Debug:LogError (object)
0x000001cc53d4907b (Mono JIT Code) UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes ()
0x000001cc53e36b35 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
0x00007ffb3c53697e (mono-2.0-bdwgc) [mini-runtime.c:3445] mono_jit_runtime_invoke 
0x00007ffb3c46cdd4 (mono-2.0-bdwgc) [object.c:3068] do_runtime_invoke 
0x00007ffb3c46cf4c (mono-2.0-bdwgc) [object.c:3115] mono_runtime_invoke 
0x00007ff6c7421934 (Unity) scripting_method_invoke
0x00007ff6c7400b84 (Unity) ScriptingInvocation::Invoke
0x00007ff6c73fb6e5 (Unity) ScriptingInvocation::Invoke<void>
0x00007ff6c753f53b (Unity) Scripting::UnityEditor::EditorAssembliesProxy::ProcessInitializeOnLoadMethodAttributes
0x00007ff6c73f5733 (Unity) MonoManager::SetupLoadedEditorAssemblies
0x00007ff6c73eb1ed (Unity) MonoManager::EndReloadAssembly
0x00007ff6c73f2d88 (Unity) MonoManager::ReloadAssembly
0x00007ff6c835fa12 (Unity) LoadDomainAndUserAssemblies
0x00007ff6c8360144 (Unity) LoadUserAssemblies
0x00007ff6c885c091 (Unity) <lambda_f8411f227bdd47da33e064bfa1662688>::operator()
0x00007ff6c889ce92 (Unity) asio::detail::completion_handler<core::mutable_function<void __cdecl(void)>,asio::io_context::basic_executor_type<std::allocator<void>,0> >::do_complete
0x00007ff6c8888d7e (Unity) asio::detail::win_iocp_io_context::do_one
0x00007ff6c888a9e4 (Unity) asio::detail::win_iocp_io_context::run
0x00007ff6c889b27e (Unity) IOService::Run
0x00007ff6c886dbcf (Unity) AssetImportWorkerClient::Run
0x00007ff6c8835d13 (Unity) RunAssetImportWorkerClientV2
0x00007ff6c8835d9b (Unity) RunAssetImporterV2
0x00007ff6c8060c48 (Unity) Application::InitializeProject
0x00007ff6c84b20a0 (Unity) WinMain
0x00007ff6c987fbae (Unity) __scrt_common_main_seh
0x00007ffbd486259d (KERNEL32) BaseThreadInitThunk
0x00007ffbd4e8af78 (ntdll) RtlUserThreadStart

Mono: successfully reloaded assembly
- Completed reload, in  1.384 seconds
Domain Reload Profiling:
	ReloadAssembly (1384ms)
		BeginReloadAssembly (173ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (2ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (26ms)
		EndReloadAssembly (1101ms)
			LoadAssemblies (143ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (187ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (28ms)
			SetupLoadedEditorAssemblies (736ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (19ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (69ms)
				ProcessInitializeOnLoadAttributes (586ms)
				ProcessInitializeOnLoadMethodAttributes (25ms)
				AfterProcessingInitializeOnLoad (36ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (7ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 0.76 ms, found 13 plugins.
Preloading 2 native plugins for Editor in 0.18 ms.
Unloading 4753 Unused Serialized files (Serialized files now loaded: 0)
Unloading 50 unused Assets / (95.8 KB). Loaded Objects now: 5357.
Memory consumption went from 192.4 MB to 192.3 MB.
Total: 2.090900 ms (FindLiveObjects: 0.194500 ms CreateObjectMapping: 0.061900 ms MarkObjects: 1.797300 ms  DeleteObjects: 0.036500 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 15.47 ms, found 13 plugins.
Preloading 2 native plugins for Editor in 0.19 ms.
Unloading 45 Unused Serialized files (Serialized files now loaded: 0)
Unloading 45 unused Assets / (95.6 KB). Loaded Objects now: 5357.
Memory consumption went from 98.2 MB to 98.1 MB.
Total: 2.627700 ms (FindLiveObjects: 0.201100 ms CreateObjectMapping: 0.077100 ms MarkObjects: 2.316100 ms  DeleteObjects: 0.032800 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.045492 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 0.62 ms, found 13 plugins.
Preloading 2 native plugins for Editor in 0.21 ms.
PXRLog [Build Check]RegisterBuildPlayerHandler,Already Do not show: False
0x00007ff6c74fefad (Unity) StackWalker::GetCurrentCallstack
0x00007ff6c7505b39 (Unity) StackWalker::ShowCallstack
0x00007ff6c84a8df3 (Unity) GetStacktrace
0x00007ff6c8b7165d (Unity) DebugStringToFile
0x00007ff6c664ea32 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x000001cc53d2c083 (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
0x000001cc53d2bd5b (Mono JIT Code) UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
0x000001cc53d2bae0 (Mono JIT Code) UnityEngine.Logger:Log (UnityEngine.LogType,object)
0x000001cc53d2b9a8 (Mono JIT Code) UnityEngine.Debug:Log (object)
0x000001cc53d28a53 (Mono JIT Code) [PXR_SDKBuildCheck.cs:32] Unity.XR.PXR.Editor.PXR_SDKBuildCheck:.cctor () 
0x000001cc53d37575 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
0x00007ffb3c53697e (mono-2.0-bdwgc) [mini-runtime.c:3445] mono_jit_runtime_invoke 
0x00007ffb3c46cdd4 (mono-2.0-bdwgc) [object.c:3068] do_runtime_invoke 
0x00007ffb3c467b79 (mono-2.0-bdwgc) [object.c:563] mono_runtime_class_init_full 
0x00007ffb3c39ddd7 (mono-2.0-bdwgc) [icall.c:1292] ves_icall_System_Runtime_CompilerServices_RuntimeHelpers_RunClassConstructor 
0x00007ffb3c3cfebc (mono-2.0-bdwgc) [icall-def.h:756] ves_icall_System_Runtime_CompilerServices_RuntimeHelpers_RunClassConstructor_raw 
0x000001cc53d36c1a (Mono JIT Code) (wrapper managed-to-native) System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (intptr)
0x000001cc53d36b2b (Mono JIT Code) System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
0x000001cc53d36273 (Mono JIT Code) UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])
0x000001cc53ec6298 (Mono JIT Code) (wrapper runtime-invoke) <Module>:runtime_invoke_void_object (object,intptr,intptr,intptr)
0x00007ffb3c53697e (mono-2.0-bdwgc) [mini-runtime.c:3445] mono_jit_runtime_invoke 
0x00007ffb3c46cdd4 (mono-2.0-bdwgc) [object.c:3068] do_runtime_invoke 
0x00007ffb3c46cf4c (mono-2.0-bdwgc) [object.c:3115] mono_runtime_invoke 
0x00007ff6c7421934 (Unity) scripting_method_invoke
0x00007ff6c7400b84 (Unity) ScriptingInvocation::Invoke
0x00007ff6c73fb6e5 (Unity) ScriptingInvocation::Invoke<void>
0x00007ff6c753f4c3 (Unity) Scripting::UnityEditor::EditorAssembliesProxy::ProcessInitializeOnLoadAttributes
0x00007ff6c73f570b (Unity) MonoManager::SetupLoadedEditorAssemblies
0x00007ff6c73eb1ed (Unity) MonoManager::EndReloadAssembly
0x00007ff6c73f2d88 (Unity) MonoManager::ReloadAssembly
0x00007ff6c835fa12 (Unity) LoadDomainAndUserAssemblies
0x00007ff6c8360144 (Unity) LoadUserAssemblies
0x00007ff6c885c091 (Unity) <lambda_f8411f227bdd47da33e064bfa1662688>::operator()
0x00007ff6c889ce92 (Unity) asio::detail::completion_handler<core::mutable_function<void __cdecl(void)>,asio::io_context::basic_executor_type<std::allocator<void>,0> >::do_complete
0x00007ff6c8888d7e (Unity) asio::detail::win_iocp_io_context::do_one
0x00007ff6c888a9e4 (Unity) asio::detail::win_iocp_io_context::run
0x00007ff6c889b27e (Unity) IOService::Run
0x00007ff6c886dbcf (Unity) AssetImportWorkerClient::Run
0x00007ff6c8835d13 (Unity) RunAssetImportWorkerClientV2
0x00007ff6c8835d9b (Unity) RunAssetImporterV2
0x00007ff6c8060c48 (Unity) Application::InitializeProject
0x00007ff6c84b20a0 (Unity) WinMain
0x00007ff6c987fbae (Unity) __scrt_common_main_seh
0x00007ffbd486259d (KERNEL32) BaseThreadInitThunk
0x00007ffbd4e8af78 (ntdll) RtlUserThreadStart

[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
CreateSerializedAsset is not supported while importing out-of-process
0x00007ff6c74fefad (Unity) StackWalker::GetCurrentCallstack
0x00007ff6c7505b39 (Unity) StackWalker::ShowCallstack
0x00007ff6c84a8df3 (Unity) GetStacktrace
0x00007ff6c8b7165d (Unity) DebugStringToFile
0x00007ff6c887b618 (Unity) CreateSerializedAssetV2
0x00007ff6c8810f55 (Unity) CreateSerializedAssetV2
0x00007ff6c877f8ee (Unity) AssetDatabase::CreateSerializedAsset
0x00007ff6c6cea4ff (Unity) AssetDatabaseBindings::CreateAsset
0x00007ff6c6b0b819 (Unity) AssetDatabase_CUSTOM_CreateAsset
0x000001cc53e3cdb5 (Mono JIT Code) (wrapper managed-to-native) UnityEditor.AssetDatabase:CreateAsset (UnityEngine.Object,string)
0x000001cc53e3c753 (Mono JIT Code) [PXR_ProjectSetting.cs:121] Unity.XR.PXR.PXR_ProjectSetting:GetProjectConfig () 
0x000001cc53e3c383 (Mono JIT Code) [PXR_SDKSettingEditor.cs:111] Unity.XR.PXR.Editor.PXR_SDKSettingEditor:InitializeOnLoad () 
0x000001cc53d375c3 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
0x00007ffb3c53697e (mono-2.0-bdwgc) [mini-runtime.c:3445] mono_jit_runtime_invoke 
0x00007ffb3c46cdd4 (mono-2.0-bdwgc) [object.c:3068] do_runtime_invoke 
0x00007ffb3c4746df (mono-2.0-bdwgc) [object.c:5770] mono_runtime_try_invoke_array 
0x00007ffb3c3a4eb2 (mono-2.0-bdwgc) [icall.c:4041] ves_icall_InternalInvoke 
0x00007ffb3c3cb091 (mono-2.0-bdwgc) [icall-def.h:692] ves_icall_InternalInvoke_raw 
0x000001cc53d6ae20 (Mono JIT Code) (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo:InternalInvoke (System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
0x000001cc53d6a5bb (Mono JIT Code) System.Reflection.RuntimeMethodInfo:Invoke (object,System.Reflection.BindingFlags,System.Reflection.Binder,object[],System.Globalization.CultureInfo)
0x000001cc53d6a47e (Mono JIT Code) System.Reflection.MethodBase:Invoke (object,object[])
0x000001cc53e2905b (Mono JIT Code) UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes ()
0x000001cc53ec6b35 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
0x00007ffb3c53697e (mono-2.0-bdwgc) [mini-runtime.c:3445] mono_jit_runtime_invoke 
0x00007ffb3c46cdd4 (mono-2.0-bdwgc) [object.c:3068] do_runtime_invoke 
0x00007ffb3c46cf4c (mono-2.0-bdwgc) [object.c:3115] mono_runtime_invoke 
0x00007ff6c7421934 (Unity) scripting_method_invoke
0x00007ff6c7400b84 (Unity) ScriptingInvocation::Invoke
0x00007ff6c73fb6e5 (Unity) ScriptingInvocation::Invoke<void>
0x00007ff6c753f53b (Unity) Scripting::UnityEditor::EditorAssembliesProxy::ProcessInitializeOnLoadMethodAttributes
0x00007ff6c73f5733 (Unity) MonoManager::SetupLoadedEditorAssemblies
0x00007ff6c73eb1ed (Unity) MonoManager::EndReloadAssembly
0x00007ff6c73f2d88 (Unity) MonoManager::ReloadAssembly
0x00007ff6c835fa12 (Unity) LoadDomainAndUserAssemblies
0x00007ff6c8360144 (Unity) LoadUserAssemblies
0x00007ff6c885c091 (Unity) <lambda_f8411f227bdd47da33e064bfa1662688>::operator()
0x00007ff6c889ce92 (Unity) asio::detail::completion_handler<core::mutable_function<void __cdecl(void)>,asio::io_context::basic_executor_type<std::allocator<void>,0> >::do_complete
0x00007ff6c8888d7e (Unity) asio::detail::win_iocp_io_context::do_one
0x00007ff6c888a9e4 (Unity) asio::detail::win_iocp_io_context::run
0x00007ff6c889b27e (Unity) IOService::Run
0x00007ff6c886dbcf (Unity) AssetImportWorkerClient::Run
0x00007ff6c8835d13 (Unity) RunAssetImportWorkerClientV2
0x00007ff6c8835d9b (Unity) RunAssetImporterV2
0x00007ff6c8060c48 (Unity) Application::InitializeProject
0x00007ff6c84b20a0 (Unity) WinMain
0x00007ff6c987fbae (Unity) __scrt_common_main_seh
0x00007ffbd486259d (KERNEL32) BaseThreadInitThunk
0x00007ffbd4e8af78 (ntdll) RtlUserThreadStart

System.Reflection.TargetInvocationException: Exception has been thrown by the target of an invocation. ---> UnityEngine.UnityException: Creating asset at path Assets/Resources/PXR_ProjectSetting.asset failed.
  at (wrapper managed-to-native) UnityEditor.AssetDatabase.CreateAsset(UnityEngine.Object,string)
  at Unity.XR.PXR.PXR_ProjectSetting.GetProjectConfig () [0x0015a] in D:\PICO Unity Integration SDK-3.2.0-20250529\Runtime\Scripts\PXR_ProjectSetting.cs:117 
  at Unity.XR.PXR.Editor.PXR_SDKSettingEditor.InitializeOnLoad () [0x00000] in D:\PICO Unity Integration SDK-3.2.0-20250529\Editor\PXR_SDKSettingEditor.cs:111 
  at (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo.InternalInvoke(System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x0006a] in <688f272cc27c4df98c503f9d1f1267d0>:0 
   --- End of inner exception stack trace ---
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x00083] in <688f272cc27c4df98c503f9d1f1267d0>:0 
  at System.Reflection.MethodBase.Invoke (System.Object obj, System.Object[] parameters) [0x00000] in <688f272cc27c4df98c503f9d1f1267d0>:0 
  at UnityEditor.EditorAssemblies.ProcessInitializeOnLoadMethodAttributes () [0x000a5] in <1518627dacca44efa32edd45bc720c48>:0 
0x00007ff6c74fefad (Unity) StackWalker::GetCurrentCallstack
0x00007ff6c7505b39 (Unity) StackWalker::ShowCallstack
0x00007ff6c84a8df3 (Unity) GetStacktrace
0x00007ff6c8b7165d (Unity) DebugStringToFile
0x00007ff6c664ea32 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x000001cc53d2c083 (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
0x000001cc53d2bd5b (Mono JIT Code) UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
0x000001cc53d2bae0 (Mono JIT Code) UnityEngine.Logger:Log (UnityEngine.LogType,object)
0x000001cc53e3d045 (Mono JIT Code) UnityEngine.Debug:LogError (object)
0x000001cc53e2907b (Mono JIT Code) UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes ()
0x000001cc53ec6b35 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
0x00007ffb3c53697e (mono-2.0-bdwgc) [mini-runtime.c:3445] mono_jit_runtime_invoke 
0x00007ffb3c46cdd4 (mono-2.0-bdwgc) [object.c:3068] do_runtime_invoke 
0x00007ffb3c46cf4c (mono-2.0-bdwgc) [object.c:3115] mono_runtime_invoke 
0x00007ff6c7421934 (Unity) scripting_method_invoke
0x00007ff6c7400b84 (Unity) ScriptingInvocation::Invoke
0x00007ff6c73fb6e5 (Unity) ScriptingInvocation::Invoke<void>
0x00007ff6c753f53b (Unity) Scripting::UnityEditor::EditorAssembliesProxy::ProcessInitializeOnLoadMethodAttributes
0x00007ff6c73f5733 (Unity) MonoManager::SetupLoadedEditorAssemblies
0x00007ff6c73eb1ed (Unity) MonoManager::EndReloadAssembly
0x00007ff6c73f2d88 (Unity) MonoManager::ReloadAssembly
0x00007ff6c835fa12 (Unity) LoadDomainAndUserAssemblies
0x00007ff6c8360144 (Unity) LoadUserAssemblies
0x00007ff6c885c091 (Unity) <lambda_f8411f227bdd47da33e064bfa1662688>::operator()
0x00007ff6c889ce92 (Unity) asio::detail::completion_handler<core::mutable_function<void __cdecl(void)>,asio::io_context::basic_executor_type<std::allocator<void>,0> >::do_complete
0x00007ff6c8888d7e (Unity) asio::detail::win_iocp_io_context::do_one
0x00007ff6c888a9e4 (Unity) asio::detail::win_iocp_io_context::run
0x00007ff6c889b27e (Unity) IOService::Run
0x00007ff6c886dbcf (Unity) AssetImportWorkerClient::Run
0x00007ff6c8835d13 (Unity) RunAssetImportWorkerClientV2
0x00007ff6c8835d9b (Unity) RunAssetImporterV2
0x00007ff6c8060c48 (Unity) Application::InitializeProject
0x00007ff6c84b20a0 (Unity) WinMain
0x00007ff6c987fbae (Unity) __scrt_common_main_seh
0x00007ffbd486259d (KERNEL32) BaseThreadInitThunk
0x00007ffbd4e8af78 (ntdll) RtlUserThreadStart

Mono: successfully reloaded assembly
- Completed reload, in  1.367 seconds
Domain Reload Profiling:
	ReloadAssembly (1367ms)
		BeginReloadAssembly (175ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (2ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (27ms)
		EndReloadAssembly (1079ms)
			LoadAssemblies (145ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (201ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (38ms)
			SetupLoadedEditorAssemblies (691ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (16ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (61ms)
				ProcessInitializeOnLoadAttributes (554ms)
				ProcessInitializeOnLoadMethodAttributes (22ms)
				AfterProcessingInitializeOnLoad (36ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (6ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 0.58 ms, found 13 plugins.
Preloading 2 native plugins for Editor in 0.16 ms.
Unloading 4754 Unused Serialized files (Serialized files now loaded: 0)
Unloading 50 unused Assets / (95.7 KB). Loaded Objects now: 5375.
Memory consumption went from 192.6 MB to 192.5 MB.
Total: 2.301100 ms (FindLiveObjects: 0.207800 ms CreateObjectMapping: 0.098200 ms MarkObjects: 1.961500 ms  DeleteObjects: 0.032900 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.015089 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 0.65 ms, found 13 plugins.
Preloading 2 native plugins for Editor in 0.20 ms.
PXRLog [Build Check]RegisterBuildPlayerHandler,Already Do not show: False
0x00007ff6c74fefad (Unity) StackWalker::GetCurrentCallstack
0x00007ff6c7505b39 (Unity) StackWalker::ShowCallstack
0x00007ff6c84a8df3 (Unity) GetStacktrace
0x00007ff6c8b7165d (Unity) DebugStringToFile
0x00007ff6c664ea32 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x000001cc53d9c083 (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
0x000001cc53d9bd5b (Mono JIT Code) UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
0x000001cc53d9bae0 (Mono JIT Code) UnityEngine.Logger:Log (UnityEngine.LogType,object)
0x000001cc53d9b9a8 (Mono JIT Code) UnityEngine.Debug:Log (object)
0x000001cc53d98a53 (Mono JIT Code) [PXR_SDKBuildCheck.cs:32] Unity.XR.PXR.Editor.PXR_SDKBuildCheck:.cctor () 
0x000001cc53da7575 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
0x00007ffb3c53697e (mono-2.0-bdwgc) [mini-runtime.c:3445] mono_jit_runtime_invoke 
0x00007ffb3c46cdd4 (mono-2.0-bdwgc) [object.c:3068] do_runtime_invoke 
0x00007ffb3c467b79 (mono-2.0-bdwgc) [object.c:563] mono_runtime_class_init_full 
0x00007ffb3c39ddd7 (mono-2.0-bdwgc) [icall.c:1292] ves_icall_System_Runtime_CompilerServices_RuntimeHelpers_RunClassConstructor 
0x00007ffb3c3cfebc (mono-2.0-bdwgc) [icall-def.h:756] ves_icall_System_Runtime_CompilerServices_RuntimeHelpers_RunClassConstructor_raw 
0x000001cc53da6c1a (Mono JIT Code) (wrapper managed-to-native) System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (intptr)
0x000001cc53da6b2b (Mono JIT Code) System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
0x000001cc53da6273 (Mono JIT Code) UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])
0x000001cc53d16298 (Mono JIT Code) (wrapper runtime-invoke) <Module>:runtime_invoke_void_object (object,intptr,intptr,intptr)
0x00007ffb3c53697e (mono-2.0-bdwgc) [mini-runtime.c:3445] mono_jit_runtime_invoke 
0x00007ffb3c46cdd4 (mono-2.0-bdwgc) [object.c:3068] do_runtime_invoke 
0x00007ffb3c46cf4c (mono-2.0-bdwgc) [object.c:3115] mono_runtime_invoke 
0x00007ff6c7421934 (Unity) scripting_method_invoke
0x00007ff6c7400b84 (Unity) ScriptingInvocation::Invoke
0x00007ff6c73fb6e5 (Unity) ScriptingInvocation::Invoke<void>
0x00007ff6c753f4c3 (Unity) Scripting::UnityEditor::EditorAssembliesProxy::ProcessInitializeOnLoadAttributes
0x00007ff6c73f570b (Unity) MonoManager::SetupLoadedEditorAssemblies
0x00007ff6c73eb1ed (Unity) MonoManager::EndReloadAssembly
0x00007ff6c73f2d88 (Unity) MonoManager::ReloadAssembly
0x00007ff6c835fa12 (Unity) LoadDomainAndUserAssemblies
0x00007ff6c8360144 (Unity) LoadUserAssemblies
0x00007ff6c885c091 (Unity) <lambda_f8411f227bdd47da33e064bfa1662688>::operator()
0x00007ff6c889ce92 (Unity) asio::detail::completion_handler<core::mutable_function<void __cdecl(void)>,asio::io_context::basic_executor_type<std::allocator<void>,0> >::do_complete
0x00007ff6c8888d7e (Unity) asio::detail::win_iocp_io_context::do_one
0x00007ff6c888a9e4 (Unity) asio::detail::win_iocp_io_context::run
0x00007ff6c889b27e (Unity) IOService::Run
0x00007ff6c886dbcf (Unity) AssetImportWorkerClient::Run
0x00007ff6c8835d13 (Unity) RunAssetImportWorkerClientV2
0x00007ff6c8835d9b (Unity) RunAssetImporterV2
0x00007ff6c8060c48 (Unity) Application::InitializeProject
0x00007ff6c84b20a0 (Unity) WinMain
0x00007ff6c987fbae (Unity) __scrt_common_main_seh
0x00007ffbd486259d (KERNEL32) BaseThreadInitThunk
0x00007ffbd4e8af78 (ntdll) RtlUserThreadStart

[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
CreateSerializedAsset is not supported while importing out-of-process
0x00007ff6c74fefad (Unity) StackWalker::GetCurrentCallstack
0x00007ff6c7505b39 (Unity) StackWalker::ShowCallstack
0x00007ff6c84a8df3 (Unity) GetStacktrace
0x00007ff6c8b7165d (Unity) DebugStringToFile
0x00007ff6c887b618 (Unity) CreateSerializedAssetV2
0x00007ff6c8810f55 (Unity) CreateSerializedAssetV2
0x00007ff6c877f8ee (Unity) AssetDatabase::CreateSerializedAsset
0x00007ff6c6cea4ff (Unity) AssetDatabaseBindings::CreateAsset
0x00007ff6c6b0b819 (Unity) AssetDatabase_CUSTOM_CreateAsset
0x000001cc53e9cdb5 (Mono JIT Code) (wrapper managed-to-native) UnityEditor.AssetDatabase:CreateAsset (UnityEngine.Object,string)
0x000001cc53e9c753 (Mono JIT Code) [PXR_ProjectSetting.cs:121] Unity.XR.PXR.PXR_ProjectSetting:GetProjectConfig () 
0x000001cc53e9c383 (Mono JIT Code) [PXR_SDKSettingEditor.cs:111] Unity.XR.PXR.Editor.PXR_SDKSettingEditor:InitializeOnLoad () 
0x000001cc53da75c3 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
0x00007ffb3c53697e (mono-2.0-bdwgc) [mini-runtime.c:3445] mono_jit_runtime_invoke 
0x00007ffb3c46cdd4 (mono-2.0-bdwgc) [object.c:3068] do_runtime_invoke 
0x00007ffb3c4746df (mono-2.0-bdwgc) [object.c:5770] mono_runtime_try_invoke_array 
0x00007ffb3c3a4eb2 (mono-2.0-bdwgc) [icall.c:4041] ves_icall_InternalInvoke 
0x00007ffb3c3cb091 (mono-2.0-bdwgc) [icall-def.h:692] ves_icall_InternalInvoke_raw 
0x000001cc53e3ae20 (Mono JIT Code) (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo:InternalInvoke (System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
0x000001cc53e3a5bb (Mono JIT Code) System.Reflection.RuntimeMethodInfo:Invoke (object,System.Reflection.BindingFlags,System.Reflection.Binder,object[],System.Globalization.CultureInfo)
0x000001cc53e3a47e (Mono JIT Code) System.Reflection.MethodBase:Invoke (object,object[])
0x000001cc53e5905b (Mono JIT Code) UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes ()
0x000001cc53d16b35 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
0x00007ffb3c53697e (mono-2.0-bdwgc) [mini-runtime.c:3445] mono_jit_runtime_invoke 
0x00007ffb3c46cdd4 (mono-2.0-bdwgc) [object.c:3068] do_runtime_invoke 
0x00007ffb3c46cf4c (mono-2.0-bdwgc) [object.c:3115] mono_runtime_invoke 
0x00007ff6c7421934 (Unity) scripting_method_invoke
0x00007ff6c7400b84 (Unity) ScriptingInvocation::Invoke
0x00007ff6c73fb6e5 (Unity) ScriptingInvocation::Invoke<void>
0x00007ff6c753f53b (Unity) Scripting::UnityEditor::EditorAssembliesProxy::ProcessInitializeOnLoadMethodAttributes
0x00007ff6c73f5733 (Unity) MonoManager::SetupLoadedEditorAssemblies
0x00007ff6c73eb1ed (Unity) MonoManager::EndReloadAssembly
0x00007ff6c73f2d88 (Unity) MonoManager::ReloadAssembly
0x00007ff6c835fa12 (Unity) LoadDomainAndUserAssemblies
0x00007ff6c8360144 (Unity) LoadUserAssemblies
0x00007ff6c885c091 (Unity) <lambda_f8411f227bdd47da33e064bfa1662688>::operator()
0x00007ff6c889ce92 (Unity) asio::detail::completion_handler<core::mutable_function<void __cdecl(void)>,asio::io_context::basic_executor_type<std::allocator<void>,0> >::do_complete
0x00007ff6c8888d7e (Unity) asio::detail::win_iocp_io_context::do_one
0x00007ff6c888a9e4 (Unity) asio::detail::win_iocp_io_context::run
0x00007ff6c889b27e (Unity) IOService::Run
0x00007ff6c886dbcf (Unity) AssetImportWorkerClient::Run
0x00007ff6c8835d13 (Unity) RunAssetImportWorkerClientV2
0x00007ff6c8835d9b (Unity) RunAssetImporterV2
0x00007ff6c8060c48 (Unity) Application::InitializeProject
0x00007ff6c84b20a0 (Unity) WinMain
0x00007ff6c987fbae (Unity) __scrt_common_main_seh
0x00007ffbd486259d (KERNEL32) BaseThreadInitThunk
0x00007ffbd4e8af78 (ntdll) RtlUserThreadStart

System.Reflection.TargetInvocationException: Exception has been thrown by the target of an invocation. ---> UnityEngine.UnityException: Creating asset at path Assets/Resources/PXR_ProjectSetting.asset failed.
  at (wrapper managed-to-native) UnityEditor.AssetDatabase.CreateAsset(UnityEngine.Object,string)
  at Unity.XR.PXR.PXR_ProjectSetting.GetProjectConfig () [0x0015a] in D:\PICO Unity Integration SDK-3.2.0-20250529\Runtime\Scripts\PXR_ProjectSetting.cs:117 
  at Unity.XR.PXR.Editor.PXR_SDKSettingEditor.InitializeOnLoad () [0x00000] in D:\PICO Unity Integration SDK-3.2.0-20250529\Editor\PXR_SDKSettingEditor.cs:111 
  at (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo.InternalInvoke(System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x0006a] in <688f272cc27c4df98c503f9d1f1267d0>:0 
   --- End of inner exception stack trace ---
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x00083] in <688f272cc27c4df98c503f9d1f1267d0>:0 
  at System.Reflection.MethodBase.Invoke (System.Object obj, System.Object[] parameters) [0x00000] in <688f272cc27c4df98c503f9d1f1267d0>:0 
  at UnityEditor.EditorAssemblies.ProcessInitializeOnLoadMethodAttributes () [0x000a5] in <1518627dacca44efa32edd45bc720c48>:0 
0x00007ff6c74fefad (Unity) StackWalker::GetCurrentCallstack
0x00007ff6c7505b39 (Unity) StackWalker::ShowCallstack
0x00007ff6c84a8df3 (Unity) GetStacktrace
0x00007ff6c8b7165d (Unity) DebugStringToFile
0x00007ff6c664ea32 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x000001cc53d9c083 (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
0x000001cc53d9bd5b (Mono JIT Code) UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
0x000001cc53d9bae0 (Mono JIT Code) UnityEngine.Logger:Log (UnityEngine.LogType,object)
0x000001cc53e9d045 (Mono JIT Code) UnityEngine.Debug:LogError (object)
0x000001cc53e5907b (Mono JIT Code) UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes ()
0x000001cc53d16b35 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
0x00007ffb3c53697e (mono-2.0-bdwgc) [mini-runtime.c:3445] mono_jit_runtime_invoke 
0x00007ffb3c46cdd4 (mono-2.0-bdwgc) [object.c:3068] do_runtime_invoke 
0x00007ffb3c46cf4c (mono-2.0-bdwgc) [object.c:3115] mono_runtime_invoke 
0x00007ff6c7421934 (Unity) scripting_method_invoke
0x00007ff6c7400b84 (Unity) ScriptingInvocation::Invoke
0x00007ff6c73fb6e5 (Unity) ScriptingInvocation::Invoke<void>
0x00007ff6c753f53b (Unity) Scripting::UnityEditor::EditorAssembliesProxy::ProcessInitializeOnLoadMethodAttributes
0x00007ff6c73f5733 (Unity) MonoManager::SetupLoadedEditorAssemblies
0x00007ff6c73eb1ed (Unity) MonoManager::EndReloadAssembly
0x00007ff6c73f2d88 (Unity) MonoManager::ReloadAssembly
0x00007ff6c835fa12 (Unity) LoadDomainAndUserAssemblies
0x00007ff6c8360144 (Unity) LoadUserAssemblies
0x00007ff6c885c091 (Unity) <lambda_f8411f227bdd47da33e064bfa1662688>::operator()
0x00007ff6c889ce92 (Unity) asio::detail::completion_handler<core::mutable_function<void __cdecl(void)>,asio::io_context::basic_executor_type<std::allocator<void>,0> >::do_complete
0x00007ff6c8888d7e (Unity) asio::detail::win_iocp_io_context::do_one
0x00007ff6c888a9e4 (Unity) asio::detail::win_iocp_io_context::run
0x00007ff6c889b27e (Unity) IOService::Run
0x00007ff6c886dbcf (Unity) AssetImportWorkerClient::Run
0x00007ff6c8835d13 (Unity) RunAssetImportWorkerClientV2
0x00007ff6c8835d9b (Unity) RunAssetImporterV2
0x00007ff6c8060c48 (Unity) Application::InitializeProject
0x00007ff6c84b20a0 (Unity) WinMain
0x00007ff6c987fbae (Unity) __scrt_common_main_seh
0x00007ffbd486259d (KERNEL32) BaseThreadInitThunk
0x00007ffbd4e8af78 (ntdll) RtlUserThreadStart

Mono: successfully reloaded assembly
- Completed reload, in  1.332 seconds
Domain Reload Profiling:
	ReloadAssembly (1332ms)
		BeginReloadAssembly (172ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (2ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (25ms)
		EndReloadAssembly (1054ms)
			LoadAssemblies (137ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (184ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (28ms)
			SetupLoadedEditorAssemblies (711ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (16ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (65ms)
				ProcessInitializeOnLoadAttributes (571ms)
				ProcessInitializeOnLoadMethodAttributes (24ms)
				AfterProcessingInitializeOnLoad (35ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (6ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 0.62 ms, found 13 plugins.
Preloading 2 native plugins for Editor in 0.20 ms.
Unloading 4754 Unused Serialized files (Serialized files now loaded: 0)
Unloading 50 unused Assets / (95.7 KB). Loaded Objects now: 5392.
Memory consumption went from 192.6 MB to 192.5 MB.
Total: 2.123300 ms (FindLiveObjects: 0.206100 ms CreateObjectMapping: 0.109600 ms MarkObjects: 1.772100 ms  DeleteObjects: 0.034800 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.015383 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 0.68 ms, found 13 plugins.
Preloading 2 native plugins for Editor in 0.19 ms.
PXRLog [Build Check]RegisterBuildPlayerHandler,Already Do not show: False
0x00007ff6c74fefad (Unity) StackWalker::GetCurrentCallstack
0x00007ff6c7505b39 (Unity) StackWalker::ShowCallstack
0x00007ff6c84a8df3 (Unity) GetStacktrace
0x00007ff6c8b7165d (Unity) DebugStringToFile
0x00007ff6c664ea32 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x000001cc53e2c083 (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
0x000001cc53e2bd5b (Mono JIT Code) UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
0x000001cc53e2bae0 (Mono JIT Code) UnityEngine.Logger:Log (UnityEngine.LogType,object)
0x000001cc53e2b9a8 (Mono JIT Code) UnityEngine.Debug:Log (object)
0x000001cc53e28a53 (Mono JIT Code) [PXR_SDKBuildCheck.cs:32] Unity.XR.PXR.Editor.PXR_SDKBuildCheck:.cctor () 
0x000001cc53e47575 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
0x00007ffb3c53697e (mono-2.0-bdwgc) [mini-runtime.c:3445] mono_jit_runtime_invoke 
0x00007ffb3c46cdd4 (mono-2.0-bdwgc) [object.c:3068] do_runtime_invoke 
0x00007ffb3c467b79 (mono-2.0-bdwgc) [object.c:563] mono_runtime_class_init_full 
0x00007ffb3c39ddd7 (mono-2.0-bdwgc) [icall.c:1292] ves_icall_System_Runtime_CompilerServices_RuntimeHelpers_RunClassConstructor 
0x00007ffb3c3cfebc (mono-2.0-bdwgc) [icall-def.h:756] ves_icall_System_Runtime_CompilerServices_RuntimeHelpers_RunClassConstructor_raw 
0x000001cc53e46c1a (Mono JIT Code) (wrapper managed-to-native) System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (intptr)
0x000001cc53e46b2b (Mono JIT Code) System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
0x000001cc53e46273 (Mono JIT Code) UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])
0x000001cc53d86298 (Mono JIT Code) (wrapper runtime-invoke) <Module>:runtime_invoke_void_object (object,intptr,intptr,intptr)
0x00007ffb3c53697e (mono-2.0-bdwgc) [mini-runtime.c:3445] mono_jit_runtime_invoke 
0x00007ffb3c46cdd4 (mono-2.0-bdwgc) [object.c:3068] do_runtime_invoke 
0x00007ffb3c46cf4c (mono-2.0-bdwgc) [object.c:3115] mono_runtime_invoke 
0x00007ff6c7421934 (Unity) scripting_method_invoke
0x00007ff6c7400b84 (Unity) ScriptingInvocation::Invoke
0x00007ff6c73fb6e5 (Unity) ScriptingInvocation::Invoke<void>
0x00007ff6c753f4c3 (Unity) Scripting::UnityEditor::EditorAssembliesProxy::ProcessInitializeOnLoadAttributes
0x00007ff6c73f570b (Unity) MonoManager::SetupLoadedEditorAssemblies
0x00007ff6c73eb1ed (Unity) MonoManager::EndReloadAssembly
0x00007ff6c73f2d88 (Unity) MonoManager::ReloadAssembly
0x00007ff6c835fa12 (Unity) LoadDomainAndUserAssemblies
0x00007ff6c8360144 (Unity) LoadUserAssemblies
0x00007ff6c885c091 (Unity) <lambda_f8411f227bdd47da33e064bfa1662688>::operator()
0x00007ff6c889ce92 (Unity) asio::detail::completion_handler<core::mutable_function<void __cdecl(void)>,asio::io_context::basic_executor_type<std::allocator<void>,0> >::do_complete
0x00007ff6c8888d7e (Unity) asio::detail::win_iocp_io_context::do_one
0x00007ff6c888a9e4 (Unity) asio::detail::win_iocp_io_context::run
0x00007ff6c889b27e (Unity) IOService::Run
0x00007ff6c886dbcf (Unity) AssetImportWorkerClient::Run
0x00007ff6c8835d13 (Unity) RunAssetImportWorkerClientV2
0x00007ff6c8835d9b (Unity) RunAssetImporterV2
0x00007ff6c8060c48 (Unity) Application::InitializeProject
0x00007ff6c84b20a0 (Unity) WinMain
0x00007ff6c987fbae (Unity) __scrt_common_main_seh
0x00007ffbd486259d (KERNEL32) BaseThreadInitThunk
0x00007ffbd4e8af78 (ntdll) RtlUserThreadStart

[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
CreateSerializedAsset is not supported while importing out-of-process
0x00007ff6c74fefad (Unity) StackWalker::GetCurrentCallstack
0x00007ff6c7505b39 (Unity) StackWalker::ShowCallstack
0x00007ff6c84a8df3 (Unity) GetStacktrace
0x00007ff6c8b7165d (Unity) DebugStringToFile
0x00007ff6c887b618 (Unity) CreateSerializedAssetV2
0x00007ff6c8810f55 (Unity) CreateSerializedAssetV2
0x00007ff6c877f8ee (Unity) AssetDatabase::CreateSerializedAsset
0x00007ff6c6cea4ff (Unity) AssetDatabaseBindings::CreateAsset
0x00007ff6c6b0b819 (Unity) AssetDatabase_CUSTOM_CreateAsset
0x000001cc53cdcdb5 (Mono JIT Code) (wrapper managed-to-native) UnityEditor.AssetDatabase:CreateAsset (UnityEngine.Object,string)
0x000001cc53cdc753 (Mono JIT Code) [PXR_ProjectSetting.cs:121] Unity.XR.PXR.PXR_ProjectSetting:GetProjectConfig () 
0x000001cc53cdc383 (Mono JIT Code) [PXR_SDKSettingEditor.cs:111] Unity.XR.PXR.Editor.PXR_SDKSettingEditor:InitializeOnLoad () 
0x000001cc53e475c3 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
0x00007ffb3c53697e (mono-2.0-bdwgc) [mini-runtime.c:3445] mono_jit_runtime_invoke 
0x00007ffb3c46cdd4 (mono-2.0-bdwgc) [object.c:3068] do_runtime_invoke 
0x00007ffb3c4746df (mono-2.0-bdwgc) [object.c:5770] mono_runtime_try_invoke_array 
0x00007ffb3c3a4eb2 (mono-2.0-bdwgc) [icall.c:4041] ves_icall_InternalInvoke 
0x00007ffb3c3cb091 (mono-2.0-bdwgc) [icall-def.h:692] ves_icall_InternalInvoke_raw 
0x000001cc53e9ae20 (Mono JIT Code) (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo:InternalInvoke (System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
0x000001cc53e9a5bb (Mono JIT Code) System.Reflection.RuntimeMethodInfo:Invoke (object,System.Reflection.BindingFlags,System.Reflection.Binder,object[],System.Globalization.CultureInfo)
0x000001cc53e9a47e (Mono JIT Code) System.Reflection.MethodBase:Invoke (object,object[])
0x000001cc53cc905b (Mono JIT Code) UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes ()
0x000001cc53d86b35 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
0x00007ffb3c53697e (mono-2.0-bdwgc) [mini-runtime.c:3445] mono_jit_runtime_invoke 
0x00007ffb3c46cdd4 (mono-2.0-bdwgc) [object.c:3068] do_runtime_invoke 
0x00007ffb3c46cf4c (mono-2.0-bdwgc) [object.c:3115] mono_runtime_invoke 
0x00007ff6c7421934 (Unity) scripting_method_invoke
0x00007ff6c7400b84 (Unity) ScriptingInvocation::Invoke
0x00007ff6c73fb6e5 (Unity) ScriptingInvocation::Invoke<void>
0x00007ff6c753f53b (Unity) Scripting::UnityEditor::EditorAssembliesProxy::ProcessInitializeOnLoadMethodAttributes
0x00007ff6c73f5733 (Unity) MonoManager::SetupLoadedEditorAssemblies
0x00007ff6c73eb1ed (Unity) MonoManager::EndReloadAssembly
0x00007ff6c73f2d88 (Unity) MonoManager::ReloadAssembly
0x00007ff6c835fa12 (Unity) LoadDomainAndUserAssemblies
0x00007ff6c8360144 (Unity) LoadUserAssemblies
0x00007ff6c885c091 (Unity) <lambda_f8411f227bdd47da33e064bfa1662688>::operator()
0x00007ff6c889ce92 (Unity) asio::detail::completion_handler<core::mutable_function<void __cdecl(void)>,asio::io_context::basic_executor_type<std::allocator<void>,0> >::do_complete
0x00007ff6c8888d7e (Unity) asio::detail::win_iocp_io_context::do_one
0x00007ff6c888a9e4 (Unity) asio::detail::win_iocp_io_context::run
0x00007ff6c889b27e (Unity) IOService::Run
0x00007ff6c886dbcf (Unity) AssetImportWorkerClient::Run
0x00007ff6c8835d13 (Unity) RunAssetImportWorkerClientV2
0x00007ff6c8835d9b (Unity) RunAssetImporterV2
0x00007ff6c8060c48 (Unity) Application::InitializeProject
0x00007ff6c84b20a0 (Unity) WinMain
0x00007ff6c987fbae (Unity) __scrt_common_main_seh
0x00007ffbd486259d (KERNEL32) BaseThreadInitThunk
0x00007ffbd4e8af78 (ntdll) RtlUserThreadStart

System.Reflection.TargetInvocationException: Exception has been thrown by the target of an invocation. ---> UnityEngine.UnityException: Creating asset at path Assets/Resources/PXR_ProjectSetting.asset failed.
  at (wrapper managed-to-native) UnityEditor.AssetDatabase.CreateAsset(UnityEngine.Object,string)
  at Unity.XR.PXR.PXR_ProjectSetting.GetProjectConfig () [0x0015a] in D:\PICO Unity Integration SDK-3.2.0-20250529\Runtime\Scripts\PXR_ProjectSetting.cs:117 
  at Unity.XR.PXR.Editor.PXR_SDKSettingEditor.InitializeOnLoad () [0x00000] in D:\PICO Unity Integration SDK-3.2.0-20250529\Editor\PXR_SDKSettingEditor.cs:111 
  at (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo.InternalInvoke(System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x0006a] in <688f272cc27c4df98c503f9d1f1267d0>:0 
   --- End of inner exception stack trace ---
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x00083] in <688f272cc27c4df98c503f9d1f1267d0>:0 
  at System.Reflection.MethodBase.Invoke (System.Object obj, System.Object[] parameters) [0x00000] in <688f272cc27c4df98c503f9d1f1267d0>:0 
  at UnityEditor.EditorAssemblies.ProcessInitializeOnLoadMethodAttributes () [0x000a5] in <1518627dacca44efa32edd45bc720c48>:0 
0x00007ff6c74fefad (Unity) StackWalker::GetCurrentCallstack
0x00007ff6c7505b39 (Unity) StackWalker::ShowCallstack
0x00007ff6c84a8df3 (Unity) GetStacktrace
0x00007ff6c8b7165d (Unity) DebugStringToFile
0x00007ff6c664ea32 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x000001cc53e2c083 (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
0x000001cc53e2bd5b (Mono JIT Code) UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
0x000001cc53e2bae0 (Mono JIT Code) UnityEngine.Logger:Log (UnityEngine.LogType,object)
0x000001cc53cdd045 (Mono JIT Code) UnityEngine.Debug:LogError (object)
0x000001cc53cc907b (Mono JIT Code) UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes ()
0x000001cc53d86b35 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
0x00007ffb3c53697e (mono-2.0-bdwgc) [mini-runtime.c:3445] mono_jit_runtime_invoke 
0x00007ffb3c46cdd4 (mono-2.0-bdwgc) [object.c:3068] do_runtime_invoke 
0x00007ffb3c46cf4c (mono-2.0-bdwgc) [object.c:3115] mono_runtime_invoke 
0x00007ff6c7421934 (Unity) scripting_method_invoke
0x00007ff6c7400b84 (Unity) ScriptingInvocation::Invoke
0x00007ff6c73fb6e5 (Unity) ScriptingInvocation::Invoke<void>
0x00007ff6c753f53b (Unity) Scripting::UnityEditor::EditorAssembliesProxy::ProcessInitializeOnLoadMethodAttributes
0x00007ff6c73f5733 (Unity) MonoManager::SetupLoadedEditorAssemblies
0x00007ff6c73eb1ed (Unity) MonoManager::EndReloadAssembly
0x00007ff6c73f2d88 (Unity) MonoManager::ReloadAssembly
0x00007ff6c835fa12 (Unity) LoadDomainAndUserAssemblies
0x00007ff6c8360144 (Unity) LoadUserAssemblies
0x00007ff6c885c091 (Unity) <lambda_f8411f227bdd47da33e064bfa1662688>::operator()
0x00007ff6c889ce92 (Unity) asio::detail::completion_handler<core::mutable_function<void __cdecl(void)>,asio::io_context::basic_executor_type<std::allocator<void>,0> >::do_complete
0x00007ff6c8888d7e (Unity) asio::detail::win_iocp_io_context::do_one
0x00007ff6c888a9e4 (Unity) asio::detail::win_iocp_io_context::run
0x00007ff6c889b27e (Unity) IOService::Run
0x00007ff6c886dbcf (Unity) AssetImportWorkerClient::Run
0x00007ff6c8835d13 (Unity) RunAssetImportWorkerClientV2
0x00007ff6c8835d9b (Unity) RunAssetImporterV2
0x00007ff6c8060c48 (Unity) Application::InitializeProject
0x00007ff6c84b20a0 (Unity) WinMain
0x00007ff6c987fbae (Unity) __scrt_common_main_seh
0x00007ffbd486259d (KERNEL32) BaseThreadInitThunk
0x00007ffbd4e8af78 (ntdll) RtlUserThreadStart

Mono: successfully reloaded assembly
- Completed reload, in  1.371 seconds
Domain Reload Profiling:
	ReloadAssembly (1372ms)
		BeginReloadAssembly (167ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (2ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (27ms)
		EndReloadAssembly (1098ms)
			LoadAssemblies (134ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (193ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (29ms)
			SetupLoadedEditorAssemblies (733ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (17ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (69ms)
				ProcessInitializeOnLoadAttributes (585ms)
				ProcessInitializeOnLoadMethodAttributes (24ms)
				AfterProcessingInitializeOnLoad (37ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (7ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 0.71 ms, found 13 plugins.
Preloading 2 native plugins for Editor in 0.23 ms.
Unloading 4754 Unused Serialized files (Serialized files now loaded: 0)
Unloading 50 unused Assets / (95.7 KB). Loaded Objects now: 5409.
Memory consumption went from 192.9 MB to 192.8 MB.
Total: 2.213000 ms (FindLiveObjects: 0.247400 ms CreateObjectMapping: 0.067200 ms MarkObjects: 1.862900 ms  DeleteObjects: 0.034000 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.014761 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 0.67 ms, found 13 plugins.
Preloading 2 native plugins for Editor in 0.18 ms.
PXRLog [Build Check]RegisterBuildPlayerHandler,Already Do not show: False
0x00007ff6c74fefad (Unity) StackWalker::GetCurrentCallstack
0x00007ff6c7505b39 (Unity) StackWalker::ShowCallstack
0x00007ff6c84a8df3 (Unity) GetStacktrace
0x00007ff6c8b7165d (Unity) DebugStringToFile
0x00007ff6c664ea32 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x000001cc532ac083 (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
0x000001cc532abd5b (Mono JIT Code) UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
0x000001cc532abae0 (Mono JIT Code) UnityEngine.Logger:Log (UnityEngine.LogType,object)
0x000001cc532ab9a8 (Mono JIT Code) UnityEngine.Debug:Log (object)
0x000001cc532a8a53 (Mono JIT Code) [PXR_SDKBuildCheck.cs:32] Unity.XR.PXR.Editor.PXR_SDKBuildCheck:.cctor () 
0x000001cc532b7575 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
0x00007ffb3c53697e (mono-2.0-bdwgc) [mini-runtime.c:3445] mono_jit_runtime_invoke 
0x00007ffb3c46cdd4 (mono-2.0-bdwgc) [object.c:3068] do_runtime_invoke 
0x00007ffb3c467b79 (mono-2.0-bdwgc) [object.c:563] mono_runtime_class_init_full 
0x00007ffb3c39ddd7 (mono-2.0-bdwgc) [icall.c:1292] ves_icall_System_Runtime_CompilerServices_RuntimeHelpers_RunClassConstructor 
0x00007ffb3c3cfebc (mono-2.0-bdwgc) [icall-def.h:756] ves_icall_System_Runtime_CompilerServices_RuntimeHelpers_RunClassConstructor_raw 
0x000001cc532b6c1a (Mono JIT Code) (wrapper managed-to-native) System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (intptr)
0x000001cc532b6b2b (Mono JIT Code) System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
0x000001cc532b6273 (Mono JIT Code) UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])
0x000001cc53d76298 (Mono JIT Code) (wrapper runtime-invoke) <Module>:runtime_invoke_void_object (object,intptr,intptr,intptr)
0x00007ffb3c53697e (mono-2.0-bdwgc) [mini-runtime.c:3445] mono_jit_runtime_invoke 
0x00007ffb3c46cdd4 (mono-2.0-bdwgc) [object.c:3068] do_runtime_invoke 
0x00007ffb3c46cf4c (mono-2.0-bdwgc) [object.c:3115] mono_runtime_invoke 
0x00007ff6c7421934 (Unity) scripting_method_invoke
0x00007ff6c7400b84 (Unity) ScriptingInvocation::Invoke
0x00007ff6c73fb6e5 (Unity) ScriptingInvocation::Invoke<void>
0x00007ff6c753f4c3 (Unity) Scripting::UnityEditor::EditorAssembliesProxy::ProcessInitializeOnLoadAttributes
0x00007ff6c73f570b (Unity) MonoManager::SetupLoadedEditorAssemblies
0x00007ff6c73eb1ed (Unity) MonoManager::EndReloadAssembly
0x00007ff6c73f2d88 (Unity) MonoManager::ReloadAssembly
0x00007ff6c835fa12 (Unity) LoadDomainAndUserAssemblies
0x00007ff6c8360144 (Unity) LoadUserAssemblies
0x00007ff6c885c091 (Unity) <lambda_f8411f227bdd47da33e064bfa1662688>::operator()
0x00007ff6c889ce92 (Unity) asio::detail::completion_handler<core::mutable_function<void __cdecl(void)>,asio::io_context::basic_executor_type<std::allocator<void>,0> >::do_complete
0x00007ff6c8888d7e (Unity) asio::detail::win_iocp_io_context::do_one
0x00007ff6c888a9e4 (Unity) asio::detail::win_iocp_io_context::run
0x00007ff6c889b27e (Unity) IOService::Run
0x00007ff6c886dbcf (Unity) AssetImportWorkerClient::Run
0x00007ff6c8835d13 (Unity) RunAssetImportWorkerClientV2
0x00007ff6c8835d9b (Unity) RunAssetImporterV2
0x00007ff6c8060c48 (Unity) Application::InitializeProject
0x00007ff6c84b20a0 (Unity) WinMain
0x00007ff6c987fbae (Unity) __scrt_common_main_seh
0x00007ffbd486259d (KERNEL32) BaseThreadInitThunk
0x00007ffbd4e8af78 (ntdll) RtlUserThreadStart

[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
CreateSerializedAsset is not supported while importing out-of-process
0x00007ff6c74fefad (Unity) StackWalker::GetCurrentCallstack
0x00007ff6c7505b39 (Unity) StackWalker::ShowCallstack
0x00007ff6c84a8df3 (Unity) GetStacktrace
0x00007ff6c8b7165d (Unity) DebugStringToFile
0x00007ff6c887b618 (Unity) CreateSerializedAssetV2
0x00007ff6c8810f55 (Unity) CreateSerializedAssetV2
0x00007ff6c877f8ee (Unity) AssetDatabase::CreateSerializedAsset
0x00007ff6c6cea4ff (Unity) AssetDatabaseBindings::CreateAsset
0x00007ff6c6b0b819 (Unity) AssetDatabase_CUSTOM_CreateAsset
0x000001cc53dbcdb5 (Mono JIT Code) (wrapper managed-to-native) UnityEditor.AssetDatabase:CreateAsset (UnityEngine.Object,string)
0x000001cc53dbc753 (Mono JIT Code) [PXR_ProjectSetting.cs:121] Unity.XR.PXR.PXR_ProjectSetting:GetProjectConfig () 
0x000001cc53dbc383 (Mono JIT Code) [PXR_SDKSettingEditor.cs:111] Unity.XR.PXR.Editor.PXR_SDKSettingEditor:InitializeOnLoad () 
0x000001cc532b75c3 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
0x00007ffb3c53697e (mono-2.0-bdwgc) [mini-runtime.c:3445] mono_jit_runtime_invoke 
0x00007ffb3c46cdd4 (mono-2.0-bdwgc) [object.c:3068] do_runtime_invoke 
0x00007ffb3c4746df (mono-2.0-bdwgc) [object.c:5770] mono_runtime_try_invoke_array 
0x00007ffb3c3a4eb2 (mono-2.0-bdwgc) [icall.c:4041] ves_icall_InternalInvoke 
0x00007ffb3c3cb091 (mono-2.0-bdwgc) [icall-def.h:692] ves_icall_InternalInvoke_raw 
0x000001cc53cdae20 (Mono JIT Code) (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo:InternalInvoke (System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
0x000001cc53cda5bb (Mono JIT Code) System.Reflection.RuntimeMethodInfo:Invoke (object,System.Reflection.BindingFlags,System.Reflection.Binder,object[],System.Globalization.CultureInfo)
0x000001cc53cda47e (Mono JIT Code) System.Reflection.MethodBase:Invoke (object,object[])
0x000001cc53da905b (Mono JIT Code) UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes ()
0x000001cc53d76b35 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
0x00007ffb3c53697e (mono-2.0-bdwgc) [mini-runtime.c:3445] mono_jit_runtime_invoke 
0x00007ffb3c46cdd4 (mono-2.0-bdwgc) [object.c:3068] do_runtime_invoke 
0x00007ffb3c46cf4c (mono-2.0-bdwgc) [object.c:3115] mono_runtime_invoke 
0x00007ff6c7421934 (Unity) scripting_method_invoke
0x00007ff6c7400b84 (Unity) ScriptingInvocation::Invoke
0x00007ff6c73fb6e5 (Unity) ScriptingInvocation::Invoke<void>
0x00007ff6c753f53b (Unity) Scripting::UnityEditor::EditorAssembliesProxy::ProcessInitializeOnLoadMethodAttributes
0x00007ff6c73f5733 (Unity) MonoManager::SetupLoadedEditorAssemblies
0x00007ff6c73eb1ed (Unity) MonoManager::EndReloadAssembly
0x00007ff6c73f2d88 (Unity) MonoManager::ReloadAssembly
0x00007ff6c835fa12 (Unity) LoadDomainAndUserAssemblies
0x00007ff6c8360144 (Unity) LoadUserAssemblies
0x00007ff6c885c091 (Unity) <lambda_f8411f227bdd47da33e064bfa1662688>::operator()
0x00007ff6c889ce92 (Unity) asio::detail::completion_handler<core::mutable_function<void __cdecl(void)>,asio::io_context::basic_executor_type<std::allocator<void>,0> >::do_complete
0x00007ff6c8888d7e (Unity) asio::detail::win_iocp_io_context::do_one
0x00007ff6c888a9e4 (Unity) asio::detail::win_iocp_io_context::run
0x00007ff6c889b27e (Unity) IOService::Run
0x00007ff6c886dbcf (Unity) AssetImportWorkerClient::Run
0x00007ff6c8835d13 (Unity) RunAssetImportWorkerClientV2
0x00007ff6c8835d9b (Unity) RunAssetImporterV2
0x00007ff6c8060c48 (Unity) Application::InitializeProject
0x00007ff6c84b20a0 (Unity) WinMain
0x00007ff6c987fbae (Unity) __scrt_common_main_seh
0x00007ffbd486259d (KERNEL32) BaseThreadInitThunk
0x00007ffbd4e8af78 (ntdll) RtlUserThreadStart

System.Reflection.TargetInvocationException: Exception has been thrown by the target of an invocation. ---> UnityEngine.UnityException: Creating asset at path Assets/Resources/PXR_ProjectSetting.asset failed.
  at (wrapper managed-to-native) UnityEditor.AssetDatabase.CreateAsset(UnityEngine.Object,string)
  at Unity.XR.PXR.PXR_ProjectSetting.GetProjectConfig () [0x0015a] in D:\PICO Unity Integration SDK-3.2.0-20250529\Runtime\Scripts\PXR_ProjectSetting.cs:117 
  at Unity.XR.PXR.Editor.PXR_SDKSettingEditor.InitializeOnLoad () [0x00000] in D:\PICO Unity Integration SDK-3.2.0-20250529\Editor\PXR_SDKSettingEditor.cs:111 
  at (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo.InternalInvoke(System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x0006a] in <688f272cc27c4df98c503f9d1f1267d0>:0 
   --- End of inner exception stack trace ---
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x00083] in <688f272cc27c4df98c503f9d1f1267d0>:0 
  at System.Reflection.MethodBase.Invoke (System.Object obj, System.Object[] parameters) [0x00000] in <688f272cc27c4df98c503f9d1f1267d0>:0 
  at UnityEditor.EditorAssemblies.ProcessInitializeOnLoadMethodAttributes () [0x000a5] in <1518627dacca44efa32edd45bc720c48>:0 
0x00007ff6c74fefad (Unity) StackWalker::GetCurrentCallstack
0x00007ff6c7505b39 (Unity) StackWalker::ShowCallstack
0x00007ff6c84a8df3 (Unity) GetStacktrace
0x00007ff6c8b7165d (Unity) DebugStringToFile
0x00007ff6c664ea32 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x000001cc532ac083 (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
0x000001cc532abd5b (Mono JIT Code) UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
0x000001cc532abae0 (Mono JIT Code) UnityEngine.Logger:Log (UnityEngine.LogType,object)
0x000001cc53dbd045 (Mono JIT Code) UnityEngine.Debug:LogError (object)
0x000001cc53da907b (Mono JIT Code) UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes ()
0x000001cc53d76b35 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
0x00007ffb3c53697e (mono-2.0-bdwgc) [mini-runtime.c:3445] mono_jit_runtime_invoke 
0x00007ffb3c46cdd4 (mono-2.0-bdwgc) [object.c:3068] do_runtime_invoke 
0x00007ffb3c46cf4c (mono-2.0-bdwgc) [object.c:3115] mono_runtime_invoke 
0x00007ff6c7421934 (Unity) scripting_method_invoke
0x00007ff6c7400b84 (Unity) ScriptingInvocation::Invoke
0x00007ff6c73fb6e5 (Unity) ScriptingInvocation::Invoke<void>
0x00007ff6c753f53b (Unity) Scripting::UnityEditor::EditorAssembliesProxy::ProcessInitializeOnLoadMethodAttributes
0x00007ff6c73f5733 (Unity) MonoManager::SetupLoadedEditorAssemblies
0x00007ff6c73eb1ed (Unity) MonoManager::EndReloadAssembly
0x00007ff6c73f2d88 (Unity) MonoManager::ReloadAssembly
0x00007ff6c835fa12 (Unity) LoadDomainAndUserAssemblies
0x00007ff6c8360144 (Unity) LoadUserAssemblies
0x00007ff6c885c091 (Unity) <lambda_f8411f227bdd47da33e064bfa1662688>::operator()
0x00007ff6c889ce92 (Unity) asio::detail::completion_handler<core::mutable_function<void __cdecl(void)>,asio::io_context::basic_executor_type<std::allocator<void>,0> >::do_complete
0x00007ff6c8888d7e (Unity) asio::detail::win_iocp_io_context::do_one
0x00007ff6c888a9e4 (Unity) asio::detail::win_iocp_io_context::run
0x00007ff6c889b27e (Unity) IOService::Run
0x00007ff6c886dbcf (Unity) AssetImportWorkerClient::Run
0x00007ff6c8835d13 (Unity) RunAssetImportWorkerClientV2
0x00007ff6c8835d9b (Unity) RunAssetImporterV2
0x00007ff6c8060c48 (Unity) Application::InitializeProject
0x00007ff6c84b20a0 (Unity) WinMain
0x00007ff6c987fbae (Unity) __scrt_common_main_seh
0x00007ffbd486259d (KERNEL32) BaseThreadInitThunk
0x00007ffbd4e8af78 (ntdll) RtlUserThreadStart

Mono: successfully reloaded assembly
- Completed reload, in  1.328 seconds
Domain Reload Profiling:
	ReloadAssembly (1328ms)
		BeginReloadAssembly (165ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (2ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (26ms)
		EndReloadAssembly (1054ms)
			LoadAssemblies (135ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (187ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (29ms)
			SetupLoadedEditorAssemblies (699ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (16ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (62ms)
				ProcessInitializeOnLoadAttributes (559ms)
				ProcessInitializeOnLoadMethodAttributes (24ms)
				AfterProcessingInitializeOnLoad (36ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (7ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 0.64 ms, found 13 plugins.
Preloading 2 native plugins for Editor in 0.17 ms.
Unloading 4754 Unused Serialized files (Serialized files now loaded: 0)
Unloading 50 unused Assets / (95.6 KB). Loaded Objects now: 5426.
Memory consumption went from 192.8 MB to 192.7 MB.
Total: 2.253800 ms (FindLiveObjects: 0.249000 ms CreateObjectMapping: 0.105500 ms MarkObjects: 1.861400 ms  DeleteObjects: 0.037100 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.013976 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 0.63 ms, found 13 plugins.
Preloading 2 native plugins for Editor in 0.24 ms.
PXRLog [Build Check]RegisterBuildPlayerHandler,Already Do not show: False
0x00007ff6c74fefad (Unity) StackWalker::GetCurrentCallstack
0x00007ff6c7505b39 (Unity) StackWalker::ShowCallstack
0x00007ff6c84a8df3 (Unity) GetStacktrace
0x00007ff6c8b7165d (Unity) DebugStringToFile
0x00007ff6c664ea32 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x000001cc53d6c083 (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
0x000001cc53d6bd5b (Mono JIT Code) UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
0x000001cc53d6bae0 (Mono JIT Code) UnityEngine.Logger:Log (UnityEngine.LogType,object)
0x000001cc53d6b9a8 (Mono JIT Code) UnityEngine.Debug:Log (object)
0x000001cc53d68a53 (Mono JIT Code) [PXR_SDKBuildCheck.cs:32] Unity.XR.PXR.Editor.PXR_SDKBuildCheck:.cctor () 
0x000001cc53d87575 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
0x00007ffb3c53697e (mono-2.0-bdwgc) [mini-runtime.c:3445] mono_jit_runtime_invoke 
0x00007ffb3c46cdd4 (mono-2.0-bdwgc) [object.c:3068] do_runtime_invoke 
0x00007ffb3c467b79 (mono-2.0-bdwgc) [object.c:563] mono_runtime_class_init_full 
0x00007ffb3c39ddd7 (mono-2.0-bdwgc) [icall.c:1292] ves_icall_System_Runtime_CompilerServices_RuntimeHelpers_RunClassConstructor 
0x00007ffb3c3cfebc (mono-2.0-bdwgc) [icall-def.h:756] ves_icall_System_Runtime_CompilerServices_RuntimeHelpers_RunClassConstructor_raw 
0x000001cc53d86c1a (Mono JIT Code) (wrapper managed-to-native) System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (intptr)
0x000001cc53d86b2b (Mono JIT Code) System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
0x000001cc53d86273 (Mono JIT Code) UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])
0x000001cc53196298 (Mono JIT Code) (wrapper runtime-invoke) <Module>:runtime_invoke_void_object (object,intptr,intptr,intptr)
0x00007ffb3c53697e (mono-2.0-bdwgc) [mini-runtime.c:3445] mono_jit_runtime_invoke 
0x00007ffb3c46cdd4 (mono-2.0-bdwgc) [object.c:3068] do_runtime_invoke 
0x00007ffb3c46cf4c (mono-2.0-bdwgc) [object.c:3115] mono_runtime_invoke 
0x00007ff6c7421934 (Unity) scripting_method_invoke
0x00007ff6c7400b84 (Unity) ScriptingInvocation::Invoke
0x00007ff6c73fb6e5 (Unity) ScriptingInvocation::Invoke<void>
0x00007ff6c753f4c3 (Unity) Scripting::UnityEditor::EditorAssembliesProxy::ProcessInitializeOnLoadAttributes
0x00007ff6c73f570b (Unity) MonoManager::SetupLoadedEditorAssemblies
0x00007ff6c73eb1ed (Unity) MonoManager::EndReloadAssembly
0x00007ff6c73f2d88 (Unity) MonoManager::ReloadAssembly
0x00007ff6c835fa12 (Unity) LoadDomainAndUserAssemblies
0x00007ff6c8360144 (Unity) LoadUserAssemblies
0x00007ff6c885c091 (Unity) <lambda_f8411f227bdd47da33e064bfa1662688>::operator()
0x00007ff6c889ce92 (Unity) asio::detail::completion_handler<core::mutable_function<void __cdecl(void)>,asio::io_context::basic_executor_type<std::allocator<void>,0> >::do_complete
0x00007ff6c8888d7e (Unity) asio::detail::win_iocp_io_context::do_one
0x00007ff6c888a9e4 (Unity) asio::detail::win_iocp_io_context::run
0x00007ff6c889b27e (Unity) IOService::Run
0x00007ff6c886dbcf (Unity) AssetImportWorkerClient::Run
0x00007ff6c8835d13 (Unity) RunAssetImportWorkerClientV2
0x00007ff6c8835d9b (Unity) RunAssetImporterV2
0x00007ff6c8060c48 (Unity) Application::InitializeProject
0x00007ff6c84b20a0 (Unity) WinMain
0x00007ff6c987fbae (Unity) __scrt_common_main_seh
0x00007ffbd486259d (KERNEL32) BaseThreadInitThunk
0x00007ffbd4e8af78 (ntdll) RtlUserThreadStart

[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
CreateSerializedAsset is not supported while importing out-of-process
0x00007ff6c74fefad (Unity) StackWalker::GetCurrentCallstack
0x00007ff6c7505b39 (Unity) StackWalker::ShowCallstack
0x00007ff6c84a8df3 (Unity) GetStacktrace
0x00007ff6c8b7165d (Unity) DebugStringToFile
0x00007ff6c887b618 (Unity) CreateSerializedAssetV2
0x00007ff6c8810f55 (Unity) CreateSerializedAssetV2
0x00007ff6c877f8ee (Unity) AssetDatabase::CreateSerializedAsset
0x00007ff6c6cea4ff (Unity) AssetDatabaseBindings::CreateAsset
0x00007ff6c6b0b819 (Unity) AssetDatabase_CUSTOM_CreateAsset
0x000001cc5318cdb5 (Mono JIT Code) (wrapper managed-to-native) UnityEditor.AssetDatabase:CreateAsset (UnityEngine.Object,string)
0x000001cc5318c753 (Mono JIT Code) [PXR_ProjectSetting.cs:121] Unity.XR.PXR.PXR_ProjectSetting:GetProjectConfig () 
0x000001cc5318c383 (Mono JIT Code) [PXR_SDKSettingEditor.cs:111] Unity.XR.PXR.Editor.PXR_SDKSettingEditor:InitializeOnLoad () 
0x000001cc53d875c3 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
0x00007ffb3c53697e (mono-2.0-bdwgc) [mini-runtime.c:3445] mono_jit_runtime_invoke 
0x00007ffb3c46cdd4 (mono-2.0-bdwgc) [object.c:3068] do_runtime_invoke 
0x00007ffb3c4746df (mono-2.0-bdwgc) [object.c:5770] mono_runtime_try_invoke_array 
0x00007ffb3c3a4eb2 (mono-2.0-bdwgc) [icall.c:4041] ves_icall_InternalInvoke 
0x00007ffb3c3cb091 (mono-2.0-bdwgc) [icall-def.h:692] ves_icall_InternalInvoke_raw 
0x000001cc53dbae20 (Mono JIT Code) (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo:InternalInvoke (System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
0x000001cc53dba5bb (Mono JIT Code) System.Reflection.RuntimeMethodInfo:Invoke (object,System.Reflection.BindingFlags,System.Reflection.Binder,object[],System.Globalization.CultureInfo)
0x000001cc53dba47e (Mono JIT Code) System.Reflection.MethodBase:Invoke (object,object[])
0x000001cc530a8c7b (Mono JIT Code) UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes ()
0x000001cc53196b35 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
0x00007ffb3c53697e (mono-2.0-bdwgc) [mini-runtime.c:3445] mono_jit_runtime_invoke 
0x00007ffb3c46cdd4 (mono-2.0-bdwgc) [object.c:3068] do_runtime_invoke 
0x00007ffb3c46cf4c (mono-2.0-bdwgc) [object.c:3115] mono_runtime_invoke 
0x00007ff6c7421934 (Unity) scripting_method_invoke
0x00007ff6c7400b84 (Unity) ScriptingInvocation::Invoke
0x00007ff6c73fb6e5 (Unity) ScriptingInvocation::Invoke<void>
0x00007ff6c753f53b (Unity) Scripting::UnityEditor::EditorAssembliesProxy::ProcessInitializeOnLoadMethodAttributes
0x00007ff6c73f5733 (Unity) MonoManager::SetupLoadedEditorAssemblies
0x00007ff6c73eb1ed (Unity) MonoManager::EndReloadAssembly
0x00007ff6c73f2d88 (Unity) MonoManager::ReloadAssembly
0x00007ff6c835fa12 (Unity) LoadDomainAndUserAssemblies
0x00007ff6c8360144 (Unity) LoadUserAssemblies
0x00007ff6c885c091 (Unity) <lambda_f8411f227bdd47da33e064bfa1662688>::operator()
0x00007ff6c889ce92 (Unity) asio::detail::completion_handler<core::mutable_function<void __cdecl(void)>,asio::io_context::basic_executor_type<std::allocator<void>,0> >::do_complete
0x00007ff6c8888d7e (Unity) asio::detail::win_iocp_io_context::do_one
0x00007ff6c888a9e4 (Unity) asio::detail::win_iocp_io_context::run
0x00007ff6c889b27e (Unity) IOService::Run
0x00007ff6c886dbcf (Unity) AssetImportWorkerClient::Run
0x00007ff6c8835d13 (Unity) RunAssetImportWorkerClientV2
0x00007ff6c8835d9b (Unity) RunAssetImporterV2
0x00007ff6c8060c48 (Unity) Application::InitializeProject
0x00007ff6c84b20a0 (Unity) WinMain
0x00007ff6c987fbae (Unity) __scrt_common_main_seh
0x00007ffbd486259d (KERNEL32) BaseThreadInitThunk
0x00007ffbd4e8af78 (ntdll) RtlUserThreadStart

System.Reflection.TargetInvocationException: Exception has been thrown by the target of an invocation. ---> UnityEngine.UnityException: Creating asset at path Assets/Resources/PXR_ProjectSetting.asset failed.
  at (wrapper managed-to-native) UnityEditor.AssetDatabase.CreateAsset(UnityEngine.Object,string)
  at Unity.XR.PXR.PXR_ProjectSetting.GetProjectConfig () [0x0015a] in D:\PICO Unity Integration SDK-3.2.0-20250529\Runtime\Scripts\PXR_ProjectSetting.cs:117 
  at Unity.XR.PXR.Editor.PXR_SDKSettingEditor.InitializeOnLoad () [0x00000] in D:\PICO Unity Integration SDK-3.2.0-20250529\Editor\PXR_SDKSettingEditor.cs:111 
  at (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo.InternalInvoke(System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x0006a] in <688f272cc27c4df98c503f9d1f1267d0>:0 
   --- End of inner exception stack trace ---
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x00083] in <688f272cc27c4df98c503f9d1f1267d0>:0 
  at System.Reflection.MethodBase.Invoke (System.Object obj, System.Object[] parameters) [0x00000] in <688f272cc27c4df98c503f9d1f1267d0>:0 
  at UnityEditor.EditorAssemblies.ProcessInitializeOnLoadMethodAttributes () [0x000a5] in <1518627dacca44efa32edd45bc720c48>:0 
0x00007ff6c74fefad (Unity) StackWalker::GetCurrentCallstack
0x00007ff6c7505b39 (Unity) StackWalker::ShowCallstack
0x00007ff6c84a8df3 (Unity) GetStacktrace
0x00007ff6c8b7165d (Unity) DebugStringToFile
0x00007ff6c664ea32 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x000001cc53d6c083 (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
0x000001cc53d6bd5b (Mono JIT Code) UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
0x000001cc53d6bae0 (Mono JIT Code) UnityEngine.Logger:Log (UnityEngine.LogType,object)
0x000001cc5318d045 (Mono JIT Code) UnityEngine.Debug:LogError (object)
0x000001cc530a8c9b (Mono JIT Code) UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes ()
0x000001cc53196b35 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
0x00007ffb3c53697e (mono-2.0-bdwgc) [mini-runtime.c:3445] mono_jit_runtime_invoke 
0x00007ffb3c46cdd4 (mono-2.0-bdwgc) [object.c:3068] do_runtime_invoke 
0x00007ffb3c46cf4c (mono-2.0-bdwgc) [object.c:3115] mono_runtime_invoke 
0x00007ff6c7421934 (Unity) scripting_method_invoke
0x00007ff6c7400b84 (Unity) ScriptingInvocation::Invoke
0x00007ff6c73fb6e5 (Unity) ScriptingInvocation::Invoke<void>
0x00007ff6c753f53b (Unity) Scripting::UnityEditor::EditorAssembliesProxy::ProcessInitializeOnLoadMethodAttributes
0x00007ff6c73f5733 (Unity) MonoManager::SetupLoadedEditorAssemblies
0x00007ff6c73eb1ed (Unity) MonoManager::EndReloadAssembly
0x00007ff6c73f2d88 (Unity) MonoManager::ReloadAssembly
0x00007ff6c835fa12 (Unity) LoadDomainAndUserAssemblies
0x00007ff6c8360144 (Unity) LoadUserAssemblies
0x00007ff6c885c091 (Unity) <lambda_f8411f227bdd47da33e064bfa1662688>::operator()
0x00007ff6c889ce92 (Unity) asio::detail::completion_handler<core::mutable_function<void __cdecl(void)>,asio::io_context::basic_executor_type<std::allocator<void>,0> >::do_complete
0x00007ff6c8888d7e (Unity) asio::detail::win_iocp_io_context::do_one
0x00007ff6c888a9e4 (Unity) asio::detail::win_iocp_io_context::run
0x00007ff6c889b27e (Unity) IOService::Run
0x00007ff6c886dbcf (Unity) AssetImportWorkerClient::Run
0x00007ff6c8835d13 (Unity) RunAssetImportWorkerClientV2
0x00007ff6c8835d9b (Unity) RunAssetImporterV2
0x00007ff6c8060c48 (Unity) Application::InitializeProject
0x00007ff6c84b20a0 (Unity) WinMain
0x00007ff6c987fbae (Unity) __scrt_common_main_seh
0x00007ffbd486259d (KERNEL32) BaseThreadInitThunk
0x00007ffbd4e8af78 (ntdll) RtlUserThreadStart

Mono: successfully reloaded assembly
- Completed reload, in  1.398 seconds
Domain Reload Profiling:
	ReloadAssembly (1399ms)
		BeginReloadAssembly (168ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (2ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (24ms)
		EndReloadAssembly (1120ms)
			LoadAssemblies (139ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (200ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (29ms)
			SetupLoadedEditorAssemblies (744ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (19ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (70ms)
				ProcessInitializeOnLoadAttributes (591ms)
				ProcessInitializeOnLoadMethodAttributes (25ms)
				AfterProcessingInitializeOnLoad (37ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (6ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 0.72 ms, found 13 plugins.
Preloading 2 native plugins for Editor in 0.19 ms.
Unloading 4754 Unused Serialized files (Serialized files now loaded: 0)
Unloading 50 unused Assets / (95.7 KB). Loaded Objects now: 5443.
Memory consumption went from 193.1 MB to 193.0 MB.
Total: 2.134000 ms (FindLiveObjects: 0.219300 ms CreateObjectMapping: 0.073300 ms MarkObjects: 1.803200 ms  DeleteObjects: 0.037300 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 15.46 ms, found 13 plugins.
Preloading 2 native plugins for Editor in 0.18 ms.
Unloading 45 Unused Serialized files (Serialized files now loaded: 0)
Unloading 45 unused Assets / (95.7 KB). Loaded Objects now: 5443.
Memory consumption went from 98.9 MB to 98.8 MB.
Total: 2.556600 ms (FindLiveObjects: 0.250800 ms CreateObjectMapping: 0.091900 ms MarkObjects: 2.181300 ms  DeleteObjects: 0.032100 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 18.16 ms, found 13 plugins.
Preloading 2 native plugins for Editor in 0.16 ms.
Unloading 45 Unused Serialized files (Serialized files now loaded: 0)
Unloading 45 unused Assets / (95.6 KB). Loaded Objects now: 5443.
Memory consumption went from 98.5 MB to 98.5 MB.
Total: 2.844300 ms (FindLiveObjects: 0.250000 ms CreateObjectMapping: 0.073200 ms MarkObjects: 2.491300 ms  DeleteObjects: 0.028300 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.018027 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 0.60 ms, found 13 plugins.
Preloading 2 native plugins for Editor in 0.20 ms.
PXRLog [Build Check]RegisterBuildPlayerHandler,Already Do not show: False
0x00007ff6c74fefad (Unity) StackWalker::GetCurrentCallstack
0x00007ff6c7505b39 (Unity) StackWalker::ShowCallstack
0x00007ff6c84a8df3 (Unity) GetStacktrace
0x00007ff6c8b7165d (Unity) DebugStringToFile
0x00007ff6c664ea32 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x000001cc53f0c083 (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
0x000001cc53f0bd5b (Mono JIT Code) UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
0x000001cc53f0bae0 (Mono JIT Code) UnityEngine.Logger:Log (UnityEngine.LogType,object)
0x000001cc53f0b9a8 (Mono JIT Code) UnityEngine.Debug:Log (object)
0x000001cc53f08a53 (Mono JIT Code) [PXR_SDKBuildCheck.cs:32] Unity.XR.PXR.Editor.PXR_SDKBuildCheck:.cctor () 
0x000001cc53f17575 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
0x00007ffb3c53697e (mono-2.0-bdwgc) [mini-runtime.c:3445] mono_jit_runtime_invoke 
0x00007ffb3c46cdd4 (mono-2.0-bdwgc) [object.c:3068] do_runtime_invoke 
0x00007ffb3c467b79 (mono-2.0-bdwgc) [object.c:563] mono_runtime_class_init_full 
0x00007ffb3c39ddd7 (mono-2.0-bdwgc) [icall.c:1292] ves_icall_System_Runtime_CompilerServices_RuntimeHelpers_RunClassConstructor 
0x00007ffb3c3cfebc (mono-2.0-bdwgc) [icall-def.h:756] ves_icall_System_Runtime_CompilerServices_RuntimeHelpers_RunClassConstructor_raw 
0x000001cc53f16c1a (Mono JIT Code) (wrapper managed-to-native) System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (intptr)
0x000001cc53f16b2b (Mono JIT Code) System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
0x000001cc53f16273 (Mono JIT Code) UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])
0x000001cc53eb6298 (Mono JIT Code) (wrapper runtime-invoke) <Module>:runtime_invoke_void_object (object,intptr,intptr,intptr)
0x00007ffb3c53697e (mono-2.0-bdwgc) [mini-runtime.c:3445] mono_jit_runtime_invoke 
0x00007ffb3c46cdd4 (mono-2.0-bdwgc) [object.c:3068] do_runtime_invoke 
0x00007ffb3c46cf4c (mono-2.0-bdwgc) [object.c:3115] mono_runtime_invoke 
0x00007ff6c7421934 (Unity) scripting_method_invoke
0x00007ff6c7400b84 (Unity) ScriptingInvocation::Invoke
0x00007ff6c73fb6e5 (Unity) ScriptingInvocation::Invoke<void>
0x00007ff6c753f4c3 (Unity) Scripting::UnityEditor::EditorAssembliesProxy::ProcessInitializeOnLoadAttributes
0x00007ff6c73f570b (Unity) MonoManager::SetupLoadedEditorAssemblies
0x00007ff6c73eb1ed (Unity) MonoManager::EndReloadAssembly
0x00007ff6c73f2d88 (Unity) MonoManager::ReloadAssembly
0x00007ff6c835fa12 (Unity) LoadDomainAndUserAssemblies
0x00007ff6c8360144 (Unity) LoadUserAssemblies
0x00007ff6c885c091 (Unity) <lambda_f8411f227bdd47da33e064bfa1662688>::operator()
0x00007ff6c889ce92 (Unity) asio::detail::completion_handler<core::mutable_function<void __cdecl(void)>,asio::io_context::basic_executor_type<std::allocator<void>,0> >::do_complete
0x00007ff6c8888d7e (Unity) asio::detail::win_iocp_io_context::do_one
0x00007ff6c888a9e4 (Unity) asio::detail::win_iocp_io_context::run
0x00007ff6c889b27e (Unity) IOService::Run
0x00007ff6c886dbcf (Unity) AssetImportWorkerClient::Run
0x00007ff6c8835d13 (Unity) RunAssetImportWorkerClientV2
0x00007ff6c8835d9b (Unity) RunAssetImporterV2
0x00007ff6c8060c48 (Unity) Application::InitializeProject
0x00007ff6c84b20a0 (Unity) WinMain
0x00007ff6c987fbae (Unity) __scrt_common_main_seh
0x00007ffbd486259d (KERNEL32) BaseThreadInitThunk
0x00007ffbd4e8af78 (ntdll) RtlUserThreadStart

[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
CreateSerializedAsset is not supported while importing out-of-process
0x00007ff6c74fefad (Unity) StackWalker::GetCurrentCallstack
0x00007ff6c7505b39 (Unity) StackWalker::ShowCallstack
0x00007ff6c84a8df3 (Unity) GetStacktrace
0x00007ff6c8b7165d (Unity) DebugStringToFile
0x00007ff6c887b618 (Unity) CreateSerializedAssetV2
0x00007ff6c8810f55 (Unity) CreateSerializedAssetV2
0x00007ff6c877f8ee (Unity) AssetDatabase::CreateSerializedAsset
0x00007ff6c6cea4ff (Unity) AssetDatabaseBindings::CreateAsset
0x00007ff6c6b0b819 (Unity) AssetDatabase_CUSTOM_CreateAsset
0x000001cc53ddcdb5 (Mono JIT Code) (wrapper managed-to-native) UnityEditor.AssetDatabase:CreateAsset (UnityEngine.Object,string)
0x000001cc53ddc753 (Mono JIT Code) [PXR_ProjectSetting.cs:121] Unity.XR.PXR.PXR_ProjectSetting:GetProjectConfig () 
0x000001cc53ddc383 (Mono JIT Code) [PXR_SDKSettingEditor.cs:111] Unity.XR.PXR.Editor.PXR_SDKSettingEditor:InitializeOnLoad () 
0x000001cc53f175c3 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
0x00007ffb3c53697e (mono-2.0-bdwgc) [mini-runtime.c:3445] mono_jit_runtime_invoke 
0x00007ffb3c46cdd4 (mono-2.0-bdwgc) [object.c:3068] do_runtime_invoke 
0x00007ffb3c4746df (mono-2.0-bdwgc) [object.c:5770] mono_runtime_try_invoke_array 
0x00007ffb3c3a4eb2 (mono-2.0-bdwgc) [icall.c:4041] ves_icall_InternalInvoke 
0x00007ffb3c3cb091 (mono-2.0-bdwgc) [icall-def.h:692] ves_icall_InternalInvoke_raw 
0x000001cc5318ae20 (Mono JIT Code) (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo:InternalInvoke (System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
0x000001cc5318a5bb (Mono JIT Code) System.Reflection.RuntimeMethodInfo:Invoke (object,System.Reflection.BindingFlags,System.Reflection.Binder,object[],System.Globalization.CultureInfo)
0x000001cc5318a47e (Mono JIT Code) System.Reflection.MethodBase:Invoke (object,object[])
0x000001cc53dc905b (Mono JIT Code) UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes ()
0x000001cc53eb6b35 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
0x00007ffb3c53697e (mono-2.0-bdwgc) [mini-runtime.c:3445] mono_jit_runtime_invoke 
0x00007ffb3c46cdd4 (mono-2.0-bdwgc) [object.c:3068] do_runtime_invoke 
0x00007ffb3c46cf4c (mono-2.0-bdwgc) [object.c:3115] mono_runtime_invoke 
0x00007ff6c7421934 (Unity) scripting_method_invoke
0x00007ff6c7400b84 (Unity) ScriptingInvocation::Invoke
0x00007ff6c73fb6e5 (Unity) ScriptingInvocation::Invoke<void>
0x00007ff6c753f53b (Unity) Scripting::UnityEditor::EditorAssembliesProxy::ProcessInitializeOnLoadMethodAttributes
0x00007ff6c73f5733 (Unity) MonoManager::SetupLoadedEditorAssemblies
0x00007ff6c73eb1ed (Unity) MonoManager::EndReloadAssembly
0x00007ff6c73f2d88 (Unity) MonoManager::ReloadAssembly
0x00007ff6c835fa12 (Unity) LoadDomainAndUserAssemblies
0x00007ff6c8360144 (Unity) LoadUserAssemblies
0x00007ff6c885c091 (Unity) <lambda_f8411f227bdd47da33e064bfa1662688>::operator()
0x00007ff6c889ce92 (Unity) asio::detail::completion_handler<core::mutable_function<void __cdecl(void)>,asio::io_context::basic_executor_type<std::allocator<void>,0> >::do_complete
0x00007ff6c8888d7e (Unity) asio::detail::win_iocp_io_context::do_one
0x00007ff6c888a9e4 (Unity) asio::detail::win_iocp_io_context::run
0x00007ff6c889b27e (Unity) IOService::Run
0x00007ff6c886dbcf (Unity) AssetImportWorkerClient::Run
0x00007ff6c8835d13 (Unity) RunAssetImportWorkerClientV2
0x00007ff6c8835d9b (Unity) RunAssetImporterV2
0x00007ff6c8060c48 (Unity) Application::InitializeProject
0x00007ff6c84b20a0 (Unity) WinMain
0x00007ff6c987fbae (Unity) __scrt_common_main_seh
0x00007ffbd486259d (KERNEL32) BaseThreadInitThunk
0x00007ffbd4e8af78 (ntdll) RtlUserThreadStart

System.Reflection.TargetInvocationException: Exception has been thrown by the target of an invocation. ---> UnityEngine.UnityException: Creating asset at path Assets/Resources/PXR_ProjectSetting.asset failed.
  at (wrapper managed-to-native) UnityEditor.AssetDatabase.CreateAsset(UnityEngine.Object,string)
  at Unity.XR.PXR.PXR_ProjectSetting.GetProjectConfig () [0x0015a] in D:\PICO Unity Integration SDK-3.2.0-20250529\Runtime\Scripts\PXR_ProjectSetting.cs:117 
  at Unity.XR.PXR.Editor.PXR_SDKSettingEditor.InitializeOnLoad () [0x00000] in D:\PICO Unity Integration SDK-3.2.0-20250529\Editor\PXR_SDKSettingEditor.cs:111 
  at (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo.InternalInvoke(System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x0006a] in <688f272cc27c4df98c503f9d1f1267d0>:0 
   --- End of inner exception stack trace ---
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x00083] in <688f272cc27c4df98c503f9d1f1267d0>:0 
  at System.Reflection.MethodBase.Invoke (System.Object obj, System.Object[] parameters) [0x00000] in <688f272cc27c4df98c503f9d1f1267d0>:0 
  at UnityEditor.EditorAssemblies.ProcessInitializeOnLoadMethodAttributes () [0x000a5] in <1518627dacca44efa32edd45bc720c48>:0 
0x00007ff6c74fefad (Unity) StackWalker::GetCurrentCallstack
0x00007ff6c7505b39 (Unity) StackWalker::ShowCallstack
0x00007ff6c84a8df3 (Unity) GetStacktrace
0x00007ff6c8b7165d (Unity) DebugStringToFile
0x00007ff6c664ea32 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x000001cc53f0c083 (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
0x000001cc53f0bd5b (Mono JIT Code) UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
0x000001cc53f0bae0 (Mono JIT Code) UnityEngine.Logger:Log (UnityEngine.LogType,object)
0x000001cc53ddd045 (Mono JIT Code) UnityEngine.Debug:LogError (object)
0x000001cc53dc907b (Mono JIT Code) UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes ()
0x000001cc53eb6b35 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
0x00007ffb3c53697e (mono-2.0-bdwgc) [mini-runtime.c:3445] mono_jit_runtime_invoke 
0x00007ffb3c46cdd4 (mono-2.0-bdwgc) [object.c:3068] do_runtime_invoke 
0x00007ffb3c46cf4c (mono-2.0-bdwgc) [object.c:3115] mono_runtime_invoke 
0x00007ff6c7421934 (Unity) scripting_method_invoke
0x00007ff6c7400b84 (Unity) ScriptingInvocation::Invoke
0x00007ff6c73fb6e5 (Unity) ScriptingInvocation::Invoke<void>
0x00007ff6c753f53b (Unity) Scripting::UnityEditor::EditorAssembliesProxy::ProcessInitializeOnLoadMethodAttributes
0x00007ff6c73f5733 (Unity) MonoManager::SetupLoadedEditorAssemblies
0x00007ff6c73eb1ed (Unity) MonoManager::EndReloadAssembly
0x00007ff6c73f2d88 (Unity) MonoManager::ReloadAssembly
0x00007ff6c835fa12 (Unity) LoadDomainAndUserAssemblies
0x00007ff6c8360144 (Unity) LoadUserAssemblies
0x00007ff6c885c091 (Unity) <lambda_f8411f227bdd47da33e064bfa1662688>::operator()
0x00007ff6c889ce92 (Unity) asio::detail::completion_handler<core::mutable_function<void __cdecl(void)>,asio::io_context::basic_executor_type<std::allocator<void>,0> >::do_complete
0x00007ff6c8888d7e (Unity) asio::detail::win_iocp_io_context::do_one
0x00007ff6c888a9e4 (Unity) asio::detail::win_iocp_io_context::run
0x00007ff6c889b27e (Unity) IOService::Run
0x00007ff6c886dbcf (Unity) AssetImportWorkerClient::Run
0x00007ff6c8835d13 (Unity) RunAssetImportWorkerClientV2
0x00007ff6c8835d9b (Unity) RunAssetImporterV2
0x00007ff6c8060c48 (Unity) Application::InitializeProject
0x00007ff6c84b20a0 (Unity) WinMain
0x00007ff6c987fbae (Unity) __scrt_common_main_seh
0x00007ffbd486259d (KERNEL32) BaseThreadInitThunk
0x00007ffbd4e8af78 (ntdll) RtlUserThreadStart

Mono: successfully reloaded assembly
- Completed reload, in  1.331 seconds
Domain Reload Profiling:
	ReloadAssembly (1331ms)
		BeginReloadAssembly (172ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (3ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (27ms)
		EndReloadAssembly (1051ms)
			LoadAssemblies (138ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (198ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (33ms)
			SetupLoadedEditorAssemblies (677ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (16ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (57ms)
				ProcessInitializeOnLoadAttributes (549ms)
				ProcessInitializeOnLoadMethodAttributes (23ms)
				AfterProcessingInitializeOnLoad (31ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (5ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 0.64 ms, found 13 plugins.
Preloading 2 native plugins for Editor in 0.19 ms.
Unloading 4753 Unused Serialized files (Serialized files now loaded: 0)
Unloading 51 unused Assets / (95.8 KB). Loaded Objects now: 5459.
Memory consumption went from 193.1 MB to 193.0 MB.
Total: 2.488200 ms (FindLiveObjects: 0.290700 ms CreateObjectMapping: 0.096500 ms MarkObjects: 2.057300 ms  DeleteObjects: 0.043000 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 17.04 ms, found 13 plugins.
Preloading 2 native plugins for Editor in 0.20 ms.
Unloading 45 Unused Serialized files (Serialized files now loaded: 0)
Unloading 45 unused Assets / (95.6 KB). Loaded Objects now: 5459.
Memory consumption went from 98.9 MB to 98.8 MB.
Total: 3.326400 ms (FindLiveObjects: 0.297900 ms CreateObjectMapping: 0.151300 ms MarkObjects: 2.844200 ms  DeleteObjects: 0.032300 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 17.35 ms, found 13 plugins.
Preloading 2 native plugins for Editor in 0.14 ms.
Unloading 45 Unused Serialized files (Serialized files now loaded: 0)
Unloading 45 unused Assets / (95.6 KB). Loaded Objects now: 5459.
Memory consumption went from 98.6 MB to 98.5 MB.
Total: 2.330100 ms (FindLiveObjects: 0.231000 ms CreateObjectMapping: 0.071300 ms MarkObjects: 1.985600 ms  DeleteObjects: 0.041100 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 418007.190360 seconds.
  path: Packages/com.unity.inputsystem/InputSystem/Editor/AssetEditor/Resources/pro/actionTreeBackground.png
  artifactKey: Guid(c4b66e31395c54087a2fbc13ae7e0f3c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Packages/com.unity.inputsystem/InputSystem/Editor/AssetEditor/Resources/pro/actionTreeBackground.png using Guid(c4b66e31395c54087a2fbc13ae7e0f3c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'bf2d09005277ec9f60d2978dc7f3b219') in 0.136317 seconds 
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.000020 seconds.
  path: Packages/com.unity.inputsystem/InputSystem/Plugins/PlayerInput/DefaultInputActions.inputactions
  artifactKey: Guid(ca9f5fa95ffab41fb9a615ab714db018) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Packages/com.unity.inputsystem/InputSystem/Plugins/PlayerInput/DefaultInputActions.inputactions using Guid(ca9f5fa95ffab41fb9a615ab714db018) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'e6bf3699cb97e1354a384b7ee8cff312') in 0.029059 seconds 
Number of asset objects unloaded after import = 14
========================================================================
Received Import Request.
  Time since last request: 0.000018 seconds.
  path: Packages/com.unity.inputsystem/InputSystem/Editor/AssetEditor/Resources/pro/actionTreeBackgroundWithoutBorder.png
  artifactKey: Guid(943deaaee9c9347e0a455aa208526c42) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Packages/com.unity.inputsystem/InputSystem/Editor/AssetEditor/Resources/pro/actionTreeBackgroundWithoutBorder.png using Guid(943deaaee9c9347e0a455aa208526c42) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'dfec94aac35cb07f42de9fa15dff36c4') in 0.018919 seconds 
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.961540 seconds.
  path: Assets/Samples/XR Interaction Toolkit/2.5.2/Starter Assets/Presets/XRI Default Right ActionBasedControllerManager.preset
  artifactKey: Guid(55f7614a1d331d14bb631965514937d8) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Samples/XR Interaction Toolkit/2.5.2/Starter Assets/Presets/XRI Default Right ActionBasedControllerManager.preset using Guid(55f7614a1d331d14bb631965514937d8) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'bd2718358377431626c559aa8c74d96d') in 0.007653 seconds 
Number of asset objects unloaded after import = 8
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.021109 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 0.60 ms, found 13 plugins.
Preloading 2 native plugins for Editor in 0.20 ms.
PXRLog [Build Check]RegisterBuildPlayerHandler,Already Do not show: False
0x00007ff6c74fefad (Unity) StackWalker::GetCurrentCallstack
0x00007ff6c7505b39 (Unity) StackWalker::ShowCallstack
0x00007ff6c84a8df3 (Unity) GetStacktrace
0x00007ff6c8b7165d (Unity) DebugStringToFile
0x00007ff6c664ea32 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x000001cc53d6c083 (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
0x000001cc53d6bd5b (Mono JIT Code) UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
0x000001cc53d6bae0 (Mono JIT Code) UnityEngine.Logger:Log (UnityEngine.LogType,object)
0x000001cc53d6b9a8 (Mono JIT Code) UnityEngine.Debug:Log (object)
0x000001cc53d68a53 (Mono JIT Code) [PXR_SDKBuildCheck.cs:32] Unity.XR.PXR.Editor.PXR_SDKBuildCheck:.cctor () 
0x000001cc53dd75e5 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
0x00007ffb3c53697e (mono-2.0-bdwgc) [mini-runtime.c:3445] mono_jit_runtime_invoke 
0x00007ffb3c46cdd4 (mono-2.0-bdwgc) [object.c:3068] do_runtime_invoke 
0x00007ffb3c467b79 (mono-2.0-bdwgc) [object.c:563] mono_runtime_class_init_full 
0x00007ffb3c39ddd7 (mono-2.0-bdwgc) [icall.c:1292] ves_icall_System_Runtime_CompilerServices_RuntimeHelpers_RunClassConstructor 
0x00007ffb3c3cfebc (mono-2.0-bdwgc) [icall-def.h:756] ves_icall_System_Runtime_CompilerServices_RuntimeHelpers_RunClassConstructor_raw 
0x000001cc53dd6c8a (Mono JIT Code) (wrapper managed-to-native) System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (intptr)
0x000001cc53dd6b9b (Mono JIT Code) System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
0x000001cc53dd62e3 (Mono JIT Code) UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])
0x000001cc52e86298 (Mono JIT Code) (wrapper runtime-invoke) <Module>:runtime_invoke_void_object (object,intptr,intptr,intptr)
0x00007ffb3c53697e (mono-2.0-bdwgc) [mini-runtime.c:3445] mono_jit_runtime_invoke 
0x00007ffb3c46cdd4 (mono-2.0-bdwgc) [object.c:3068] do_runtime_invoke 
0x00007ffb3c46cf4c (mono-2.0-bdwgc) [object.c:3115] mono_runtime_invoke 
0x00007ff6c7421934 (Unity) scripting_method_invoke
0x00007ff6c7400b84 (Unity) ScriptingInvocation::Invoke
0x00007ff6c73fb6e5 (Unity) ScriptingInvocation::Invoke<void>
0x00007ff6c753f4c3 (Unity) Scripting::UnityEditor::EditorAssembliesProxy::ProcessInitializeOnLoadAttributes
0x00007ff6c73f570b (Unity) MonoManager::SetupLoadedEditorAssemblies
0x00007ff6c73eb1ed (Unity) MonoManager::EndReloadAssembly
0x00007ff6c73f2d88 (Unity) MonoManager::ReloadAssembly
0x00007ff6c835fa12 (Unity) LoadDomainAndUserAssemblies
0x00007ff6c8360144 (Unity) LoadUserAssemblies
0x00007ff6c885c091 (Unity) <lambda_f8411f227bdd47da33e064bfa1662688>::operator()
0x00007ff6c889ce92 (Unity) asio::detail::completion_handler<core::mutable_function<void __cdecl(void)>,asio::io_context::basic_executor_type<std::allocator<void>,0> >::do_complete
0x00007ff6c8888d7e (Unity) asio::detail::win_iocp_io_context::do_one
0x00007ff6c888a9e4 (Unity) asio::detail::win_iocp_io_context::run
0x00007ff6c889b27e (Unity) IOService::Run
0x00007ff6c886dbcf (Unity) AssetImportWorkerClient::Run
0x00007ff6c8835d13 (Unity) RunAssetImportWorkerClientV2
0x00007ff6c8835d9b (Unity) RunAssetImporterV2
0x00007ff6c8060c48 (Unity) Application::InitializeProject
0x00007ff6c84b20a0 (Unity) WinMain
0x00007ff6c987fbae (Unity) __scrt_common_main_seh
0x00007ffbd486259d (KERNEL32) BaseThreadInitThunk
0x00007ffbd4e8af78 (ntdll) RtlUserThreadStart

[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
CreateSerializedAsset is not supported while importing out-of-process
0x00007ff6c74fefad (Unity) StackWalker::GetCurrentCallstack
0x00007ff6c7505b39 (Unity) StackWalker::ShowCallstack
0x00007ff6c84a8df3 (Unity) GetStacktrace
0x00007ff6c8b7165d (Unity) DebugStringToFile
0x00007ff6c887b618 (Unity) CreateSerializedAssetV2
0x00007ff6c8810f55 (Unity) CreateSerializedAssetV2
0x00007ff6c877f8ee (Unity) AssetDatabase::CreateSerializedAsset
0x00007ff6c6cea4ff (Unity) AssetDatabaseBindings::CreateAsset
0x00007ff6c6b0b819 (Unity) AssetDatabase_CUSTOM_CreateAsset
0x000001cc53e9cdb5 (Mono JIT Code) (wrapper managed-to-native) UnityEditor.AssetDatabase:CreateAsset (UnityEngine.Object,string)
0x000001cc53e9c753 (Mono JIT Code) [PXR_ProjectSetting.cs:121] Unity.XR.PXR.PXR_ProjectSetting:GetProjectConfig () 
0x000001cc53e9c383 (Mono JIT Code) [PXR_SDKSettingEditor.cs:111] Unity.XR.PXR.Editor.PXR_SDKSettingEditor:InitializeOnLoad () 
0x000001cc53dd7633 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
0x00007ffb3c53697e (mono-2.0-bdwgc) [mini-runtime.c:3445] mono_jit_runtime_invoke 
0x00007ffb3c46cdd4 (mono-2.0-bdwgc) [object.c:3068] do_runtime_invoke 
0x00007ffb3c4746df (mono-2.0-bdwgc) [object.c:5770] mono_runtime_try_invoke_array 
0x00007ffb3c3a4eb2 (mono-2.0-bdwgc) [icall.c:4041] ves_icall_InternalInvoke 
0x00007ffb3c3cb091 (mono-2.0-bdwgc) [icall-def.h:692] ves_icall_InternalInvoke_raw 
0x000001cc53d5ae20 (Mono JIT Code) (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo:InternalInvoke (System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
0x000001cc53d5a5bb (Mono JIT Code) System.Reflection.RuntimeMethodInfo:Invoke (object,System.Reflection.BindingFlags,System.Reflection.Binder,object[],System.Globalization.CultureInfo)
0x000001cc53d5a47e (Mono JIT Code) System.Reflection.MethodBase:Invoke (object,object[])
0x000001cc53e8905b (Mono JIT Code) UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes ()
0x000001cc52e86b35 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
0x00007ffb3c53697e (mono-2.0-bdwgc) [mini-runtime.c:3445] mono_jit_runtime_invoke 
0x00007ffb3c46cdd4 (mono-2.0-bdwgc) [object.c:3068] do_runtime_invoke 
0x00007ffb3c46cf4c (mono-2.0-bdwgc) [object.c:3115] mono_runtime_invoke 
0x00007ff6c7421934 (Unity) scripting_method_invoke
0x00007ff6c7400b84 (Unity) ScriptingInvocation::Invoke
0x00007ff6c73fb6e5 (Unity) ScriptingInvocation::Invoke<void>
0x00007ff6c753f53b (Unity) Scripting::UnityEditor::EditorAssembliesProxy::ProcessInitializeOnLoadMethodAttributes
0x00007ff6c73f5733 (Unity) MonoManager::SetupLoadedEditorAssemblies
0x00007ff6c73eb1ed (Unity) MonoManager::EndReloadAssembly
0x00007ff6c73f2d88 (Unity) MonoManager::ReloadAssembly
0x00007ff6c835fa12 (Unity) LoadDomainAndUserAssemblies
0x00007ff6c8360144 (Unity) LoadUserAssemblies
0x00007ff6c885c091 (Unity) <lambda_f8411f227bdd47da33e064bfa1662688>::operator()
0x00007ff6c889ce92 (Unity) asio::detail::completion_handler<core::mutable_function<void __cdecl(void)>,asio::io_context::basic_executor_type<std::allocator<void>,0> >::do_complete
0x00007ff6c8888d7e (Unity) asio::detail::win_iocp_io_context::do_one
0x00007ff6c888a9e4 (Unity) asio::detail::win_iocp_io_context::run
0x00007ff6c889b27e (Unity) IOService::Run
0x00007ff6c886dbcf (Unity) AssetImportWorkerClient::Run
0x00007ff6c8835d13 (Unity) RunAssetImportWorkerClientV2
0x00007ff6c8835d9b (Unity) RunAssetImporterV2
0x00007ff6c8060c48 (Unity) Application::InitializeProject
0x00007ff6c84b20a0 (Unity) WinMain
0x00007ff6c987fbae (Unity) __scrt_common_main_seh
0x00007ffbd486259d (KERNEL32) BaseThreadInitThunk
0x00007ffbd4e8af78 (ntdll) RtlUserThreadStart

System.Reflection.TargetInvocationException: Exception has been thrown by the target of an invocation. ---> UnityEngine.UnityException: Creating asset at path Assets/Resources/PXR_ProjectSetting.asset failed.
  at (wrapper managed-to-native) UnityEditor.AssetDatabase.CreateAsset(UnityEngine.Object,string)
  at Unity.XR.PXR.PXR_ProjectSetting.GetProjectConfig () [0x0015a] in D:\PICO Unity Integration SDK-3.2.0-20250529\Runtime\Scripts\PXR_ProjectSetting.cs:117 
  at Unity.XR.PXR.Editor.PXR_SDKSettingEditor.InitializeOnLoad () [0x00000] in D:\PICO Unity Integration SDK-3.2.0-20250529\Editor\PXR_SDKSettingEditor.cs:111 
  at (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo.InternalInvoke(System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x0006a] in <688f272cc27c4df98c503f9d1f1267d0>:0 
   --- End of inner exception stack trace ---
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x00083] in <688f272cc27c4df98c503f9d1f1267d0>:0 
  at System.Reflection.MethodBase.Invoke (System.Object obj, System.Object[] parameters) [0x00000] in <688f272cc27c4df98c503f9d1f1267d0>:0 
  at UnityEditor.EditorAssemblies.ProcessInitializeOnLoadMethodAttributes () [0x000a5] in <1518627dacca44efa32edd45bc720c48>:0 
0x00007ff6c74fefad (Unity) StackWalker::GetCurrentCallstack
0x00007ff6c7505b39 (Unity) StackWalker::ShowCallstack
0x00007ff6c84a8df3 (Unity) GetStacktrace
0x00007ff6c8b7165d (Unity) DebugStringToFile
0x00007ff6c664ea32 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x000001cc53d6c083 (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
0x000001cc53d6bd5b (Mono JIT Code) UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
0x000001cc53d6bae0 (Mono JIT Code) UnityEngine.Logger:Log (UnityEngine.LogType,object)
0x000001cc53e9d045 (Mono JIT Code) UnityEngine.Debug:LogError (object)
0x000001cc53e8907b (Mono JIT Code) UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes ()
0x000001cc52e86b35 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
0x00007ffb3c53697e (mono-2.0-bdwgc) [mini-runtime.c:3445] mono_jit_runtime_invoke 
0x00007ffb3c46cdd4 (mono-2.0-bdwgc) [object.c:3068] do_runtime_invoke 
0x00007ffb3c46cf4c (mono-2.0-bdwgc) [object.c:3115] mono_runtime_invoke 
0x00007ff6c7421934 (Unity) scripting_method_invoke
0x00007ff6c7400b84 (Unity) ScriptingInvocation::Invoke
0x00007ff6c73fb6e5 (Unity) ScriptingInvocation::Invoke<void>
0x00007ff6c753f53b (Unity) Scripting::UnityEditor::EditorAssembliesProxy::ProcessInitializeOnLoadMethodAttributes
0x00007ff6c73f5733 (Unity) MonoManager::SetupLoadedEditorAssemblies
0x00007ff6c73eb1ed (Unity) MonoManager::EndReloadAssembly
0x00007ff6c73f2d88 (Unity) MonoManager::ReloadAssembly
0x00007ff6c835fa12 (Unity) LoadDomainAndUserAssemblies
0x00007ff6c8360144 (Unity) LoadUserAssemblies
0x00007ff6c885c091 (Unity) <lambda_f8411f227bdd47da33e064bfa1662688>::operator()
0x00007ff6c889ce92 (Unity) asio::detail::completion_handler<core::mutable_function<void __cdecl(void)>,asio::io_context::basic_executor_type<std::allocator<void>,0> >::do_complete
0x00007ff6c8888d7e (Unity) asio::detail::win_iocp_io_context::do_one
0x00007ff6c888a9e4 (Unity) asio::detail::win_iocp_io_context::run
0x00007ff6c889b27e (Unity) IOService::Run
0x00007ff6c886dbcf (Unity) AssetImportWorkerClient::Run
0x00007ff6c8835d13 (Unity) RunAssetImportWorkerClientV2
0x00007ff6c8835d9b (Unity) RunAssetImporterV2
0x00007ff6c8060c48 (Unity) Application::InitializeProject
0x00007ff6c84b20a0 (Unity) WinMain
0x00007ff6c987fbae (Unity) __scrt_common_main_seh
0x00007ffbd486259d (KERNEL32) BaseThreadInitThunk
0x00007ffbd4e8af78 (ntdll) RtlUserThreadStart

Mono: successfully reloaded assembly
- Completed reload, in  1.401 seconds
Domain Reload Profiling:
	ReloadAssembly (1402ms)
		BeginReloadAssembly (216ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (2ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (29ms)
		EndReloadAssembly (1054ms)
			LoadAssemblies (176ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (183ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (26ms)
			SetupLoadedEditorAssemblies (698ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (15ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (71ms)
				ProcessInitializeOnLoadAttributes (556ms)
				ProcessInitializeOnLoadMethodAttributes (22ms)
				AfterProcessingInitializeOnLoad (33ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (5ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 0.58 ms, found 13 plugins.
Preloading 2 native plugins for Editor in 0.18 ms.
Unloading 4752 Unused Serialized files (Serialized files now loaded: 0)
Unloading 52 unused Assets / (95.7 KB). Loaded Objects now: 5479.
Memory consumption went from 193.2 MB to 193.1 MB.
Total: 2.306100 ms (FindLiveObjects: 0.255400 ms CreateObjectMapping: 0.104500 ms MarkObjects: 1.909800 ms  DeleteObjects: 0.034500 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 16.39 ms, found 13 plugins.
Preloading 2 native plugins for Editor in 0.17 ms.
Unloading 45 Unused Serialized files (Serialized files now loaded: 0)
Unloading 45 unused Assets / (95.7 KB). Loaded Objects now: 5479.
Memory consumption went from 99.0 MB to 98.9 MB.
Total: 2.121600 ms (FindLiveObjects: 0.222300 ms CreateObjectMapping: 0.077200 ms MarkObjects: 1.790500 ms  DeleteObjects: 0.031100 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
Editor requested this worker to shutdown with reason: Scaling down because of idle timeout
AssetImportWorker is now disconnected from the server
Process exiting
Exiting without the bug reporter. Application will terminate with return code 0