using UnityEngine;
using UnityEngine.UI;
using UnityEngine.SceneManagement;
using TMPro;
using System.Collections;

/// <summary>
/// VR Start Menu Manager
///
/// Start menu management system designed specifically for PICO VR environment
/// Provides username input, scene switching and VR interaction functions
/// </summary>
public class VRStartMenuManager : MonoBehaviour
{
    [Header("UI Components")]
    [SerializeField] private Canvas menuCanvas;
    [SerializeField] private TextMeshProUGUI titleText;
    [SerializeField] private TMP_InputField usernameInput;
    [SerializeField] private Button startButton;
    [SerializeField] private TextMeshProUGUI welcomeText;

    [Header("VR Settings")]
    [SerializeField] private bool enableVRMode = true;
    [SerializeField] private Camera vrCamera;
    [SerializeField] private float uiDistance = 2.5f;
    [SerializeField] private float uiScale = 0.01f;
    [SerializeField] private Vector3 uiOffset = new Vector3(0, 0.2f, 0);

    [Header("Scene Settings")]
    [SerializeField] private string nextSceneName = "Animation";
    [SerializeField] private bool validateUsername = true;

    [Header("Audio Settings")]
    [SerializeField] private AudioSource audioSource;
    [SerializeField] private AudioClip buttonClickSound;
    [SerializeField] private AudioClip errorSound;

    [Header("VR UI Components")]
    private VRUIManager vrUIManager;
    private VRUIInteractor vrUIInteractor;
    
    // Private variables
    private bool isInitialized = false;
    private string currentUsername = "";
    private bool isTransitioning = false;

    // Events
    public System.Action<string> OnUsernameChanged;
    public System.Action OnStartButtonClicked;
    public System.Action<string> OnSceneTransition;
    
    void Start()
    {
        StartCoroutine(InitializeStartMenu());
    }
    
    /// <summary>
    /// Initialize start menu
    /// </summary>
    private IEnumerator InitializeStartMenu()
    {
        Debug.Log("[VRStartMenuManager] Starting VR start menu initialization");

        // Wait one frame to ensure all components are loaded
        yield return null;

        // Find or create necessary components
        SetupComponents();

        // Ensure UI component references are correct
        EnsureUIReferences();

        // Configure VR UI system
        if (enableVRMode)
        {
            SetupVRUI();
        }

        // Configure UI elements
        SetupUIElements();

        // Setup event listeners
        SetupEventListeners();

        // Setup Canvas interaction fix (will be added manually)
        // SetupCanvasInteractionFix();

        isInitialized = true;
        Debug.Log("[VRStartMenuManager] VR start menu initialization completed");
    }
    
    /// <summary>
    /// 设置组件
    /// </summary>
    private void SetupComponents()
    {
        // 查找摄像机
        if (vrCamera == null)
        {
            vrCamera = Camera.main;
            if (vrCamera == null)
            {
                vrCamera = FindObjectOfType<Camera>();
            }
        }
        
        // 查找Canvas
        if (menuCanvas == null)
        {
            menuCanvas = FindObjectOfType<Canvas>();
        }
        
        // 查找音频源
        if (audioSource == null)
        {
            audioSource = GetComponent<AudioSource>();
            if (audioSource == null)
            {
                audioSource = gameObject.AddComponent<AudioSource>();
            }
        }
    }

    /// <summary>
    /// 确保UI组件引用正确
    /// </summary>
    private void EnsureUIReferences()
    {
        // 如果组件引用为空，尝试自动查找
        if (menuCanvas == null)
        {
            menuCanvas = FindObjectOfType<Canvas>();
            Debug.Log($"[VRStartMenuManager] 自动查找Canvas: {(menuCanvas != null ? "成功" : "失败")}");
        }

        if (usernameInput == null)
        {
            usernameInput = FindObjectOfType<TMP_InputField>();
            Debug.Log($"[VRStartMenuManager] 自动查找输入框: {(usernameInput != null ? "成功" : "失败")}");
        }

        if (startButton == null)
        {
            startButton = FindObjectOfType<Button>();
            Debug.Log($"[VRStartMenuManager] 自动查找开始按钮: {(startButton != null ? "成功" : "失败")}");
        }

        if (titleText == null)
        {
            // 查找标题文本（通常是第一个TextMeshProUGUI）
            var allTexts = FindObjectsOfType<TextMeshProUGUI>();
            foreach (var text in allTexts)
            {
                if (text.name.Contains("Title") || text.name.Contains("标题"))
                {
                    titleText = text;
                    break;
                }
            }
            Debug.Log($"[VRStartMenuManager] 自动查找标题文本: {(titleText != null ? "成功" : "失败")}");
        }

        if (welcomeText == null)
        {
            // 查找欢迎文本
            var allTexts = FindObjectsOfType<TextMeshProUGUI>();
            foreach (var text in allTexts)
            {
                if (text.name.Contains("Welcome") || text.name.Contains("欢迎") ||
                    (text != titleText && text.transform.parent == menuCanvas?.transform))
                {
                    welcomeText = text;
                    break;
                }
            }
            Debug.Log($"[VRStartMenuManager] 自动查找欢迎文本: {(welcomeText != null ? "成功" : "失败")}");
        }

        // 输出最终的组件引用状态
        Debug.Log("=== UI组件引用状态 ===");
        Debug.Log($"Canvas: {(menuCanvas != null ? menuCanvas.name : "未找到")}");
        Debug.Log($"输入框: {(usernameInput != null ? usernameInput.name : "未找到")}");
        Debug.Log($"开始按钮: {(startButton != null ? startButton.name : "未找到")}");
        Debug.Log($"标题文本: {(titleText != null ? titleText.name : "未找到")}");
        Debug.Log($"欢迎文本: {(welcomeText != null ? welcomeText.name : "未找到")}");
        Debug.Log("=== 引用状态结束 ===");
    }

    /// <summary>
    /// 设置VR UI系统
    /// </summary>
    private void SetupVRUI()
    {
        if (menuCanvas == null)
        {
            Debug.LogError("[VRStartMenuManager] 找不到Canvas，无法设置VR UI");
            return;
        }
        
        // 配置Canvas为World Space
        menuCanvas.renderMode = RenderMode.WorldSpace;
        menuCanvas.worldCamera = vrCamera;
        
        // 添加VR UI管理器
        vrUIManager = menuCanvas.GetComponent<VRUIManager>();
        if (vrUIManager == null)
        {
            vrUIManager = menuCanvas.gameObject.AddComponent<VRUIManager>();
        }
        
        // 添加VR UI交互器
        vrUIInteractor = menuCanvas.GetComponent<VRUIInteractor>();
        if (vrUIInteractor == null)
        {
            vrUIInteractor = menuCanvas.gameObject.AddComponent<VRUIInteractor>();
        }
        
        // 添加GraphicRaycaster用于VR交互
        if (!menuCanvas.TryGetComponent<GraphicRaycaster>(out _))
        {
            menuCanvas.gameObject.AddComponent<GraphicRaycaster>();
        }
        
        // 设置UI位置
        PositionUIForVR();
        
        Debug.Log("[VRStartMenuManager] VR UI系统设置完成");
    }


    
    /// <summary>
    /// 为VR定位UI
    /// </summary>
    private void PositionUIForVR()
    {
        if (menuCanvas == null || vrCamera == null) return;
        
        Vector3 cameraPosition = vrCamera.transform.position;
        Vector3 cameraForward = vrCamera.transform.forward;
        
        // 计算UI位置
        Vector3 targetPosition = cameraPosition + cameraForward * uiDistance + uiOffset;
        menuCanvas.transform.position = targetPosition;
        
        // 设置缩放
        menuCanvas.transform.localScale = Vector3.one * uiScale;
        
        // 面向用户
        menuCanvas.transform.LookAt(vrCamera.transform);
        menuCanvas.transform.Rotate(0, 180, 0); // 翻转让UI正面朝向用户
    }
    
    /// <summary>
    /// 设置UI元素
    /// </summary>
    private void SetupUIElements()
    {
        // 只设置必要的初始状态，不修改文本内容和大小

        // 设置开始按钮初始状态（始终可用，点击时检查用户名）
        if (startButton != null)
        {
            startButton.interactable = true;
        }

        Debug.Log("[VRStartMenuManager] UI元素设置完成，保持场景中的预设内容和大小");
    }
    
    /// <summary>
    /// 设置事件监听
    /// </summary>
    private void SetupEventListeners()
    {
        // 用户名输入事件
        if (usernameInput != null)
        {
            usernameInput.onValueChanged.AddListener(OnUsernameInputChanged);
            Debug.Log("[VRStartMenuManager] 用户名输入事件监听已设置");
        }
        else
        {
            Debug.LogError("[VRStartMenuManager] 用户名输入框引用为空，无法设置事件监听");
        }

        // 开始按钮点击事件
        if (startButton != null)
        {
            startButton.onClick.AddListener(OnStartButtonPressed);
            Debug.Log("[VRStartMenuManager] 开始按钮点击事件监听已设置");
        }
        else
        {
            Debug.LogError("[VRStartMenuManager] 开始按钮引用为空，无法设置事件监听");
        }
    }
    
    /// <summary>
    /// 用户名输入改变事件
    /// </summary>
    private void OnUsernameInputChanged(string username)
    {
        currentUsername = username.Trim();

        // 触发事件
        OnUsernameChanged?.Invoke(currentUsername);

        Debug.Log($"[VRStartMenuManager] 用户名输入: {currentUsername}");
    }
    
    /// <summary>
    /// 验证用户名
    /// </summary>
    private bool ValidateUsername(string username)
    {
        if (!validateUsername) return true;

        // 只检查是否为空或只包含空白字符
        return !string.IsNullOrWhiteSpace(username);
    }
    
    /// <summary>
    /// 开始按钮按下事件
    /// </summary>
    private void OnStartButtonPressed()
    {
        if (isTransitioning) return;

        Debug.Log($"[VRStartMenuManager] 开始按钮被点击，用户名: {currentUsername}");

        // 检查用户名是否为空
        if (string.IsNullOrWhiteSpace(currentUsername))
        {
            Debug.LogWarning("[VRStartMenuManager] 用户名为空，请输入用户名");

            // 播放错误音效
            PlaySound(errorSound);

            // 可以在这里添加UI提示，比如显示错误消息
            ShowUsernameEmptyWarning();

            return;
        }

        // 播放点击音效
        PlaySound(buttonClickSound);

        // 触发事件
        OnStartButtonClicked?.Invoke();

        // 开始场景切换
        StartCoroutine(TransitionToNextScene());
    }
    
    /// <summary>
    /// 切换到下一个场景
    /// </summary>
    private IEnumerator TransitionToNextScene()
    {
        isTransitioning = true;

        // 保存用户名到场景数据
        if (!string.IsNullOrEmpty(currentUsername))
        {
            VRSceneManager.Instance.SetSceneData("Username", currentUsername);
            VRSceneManager.Instance.SetSceneData("StartTime", System.DateTime.Now.ToString());
        }

        // 触发场景切换事件
        OnSceneTransition?.Invoke(nextSceneName);

        Debug.Log($"[VRStartMenuManager] 开始切换到场景: {nextSceneName}");

        // 可以在这里添加过渡动画
        yield return new WaitForSeconds(0.5f);

        // 使用VRSceneManager加载下一个场景
        VRSceneManager.Instance.LoadScene(nextSceneName);
    }
    
    /// <summary>
    /// 显示用户名为空的警告
    /// </summary>
    private void ShowUsernameEmptyWarning()
    {
        // 在控制台显示警告
        Debug.LogWarning("请输入用户名后再开始！");

        // 如果有欢迎文本，临时显示警告信息
        if (welcomeText != null)
        {
            StartCoroutine(ShowTemporaryWarning());
        }
    }

    /// <summary>
    /// 显示临时警告信息
    /// </summary>
    private System.Collections.IEnumerator ShowTemporaryWarning()
    {
        // 保存原始文本
        string originalText = welcomeText.text;
        Color originalColor = welcomeText.color;

        // 显示警告文本
        welcomeText.text = "请输入用户名后再开始！";
        welcomeText.color = Color.red;

        // 等待2秒
        yield return new WaitForSeconds(2f);

        // 恢复原始文本
        welcomeText.text = originalText;
        welcomeText.color = originalColor;
    }

    /// <summary>
    /// 播放音效
    /// </summary>
    private void PlaySound(AudioClip clip)
    {
        if (audioSource != null && clip != null)
        {
            audioSource.PlayOneShot(clip);
        }
    }
    
    /// <summary>
    /// 获取当前用户名
    /// </summary>
    public string GetCurrentUsername()
    {
        return currentUsername;
    }
    
    /// <summary>
    /// 设置下一个场景名称
    /// </summary>
    public void SetNextSceneName(string sceneName)
    {
        nextSceneName = sceneName;
    }
    
    /// <summary>
    /// 重新定位UI到用户前方
    /// </summary>
    public void RepositionUI()
    {
        if (enableVRMode)
        {
            PositionUIForVR();
        }
    }
    
    void OnDestroy()
    {
        // 清理事件监听
        if (usernameInput != null)
        {
            usernameInput.onValueChanged.RemoveListener(OnUsernameInputChanged);
        }
        
        if (startButton != null)
        {
            startButton.onClick.RemoveListener(OnStartButtonPressed);
        }
    }
}
