using UnityEngine;
using UnityEngine.UI;
using TMPro;

/// <summary>
/// VR设置UI管理器
/// 
/// 管理VR环境中的设置按钮和设置菜单
/// 设置按钮固定在用户视角左上角，类似游戏中的设置按钮
/// </summary>
public class VRSettingsUI : MonoBehaviour
{
    [Header("UI组件引用")]
    [SerializeField] private Canvas settingsCanvas;
    [SerializeField] private Button settingsButton; // 齿轮设置按钮
    [SerializeField] private GameObject settingsPanel; // 设置菜单面板
    [SerializeField] private Button closeButton; // 关闭按钮
    
    [Header("设置按钮配置")]
    [SerializeField] private Vector3 buttonOffset = new Vector3(-0.8f, 0.4f, 1.5f); // 相对于摄像机的偏移
    [SerializeField] private Vector3 buttonScale = Vector3.one * 0.5f; // 按钮缩放
    [SerializeField] private bool followCameraRotation = false; // 是否跟随摄像机旋转
    
    [Header("设置面板配置")]
    [SerializeField] private Vector3 panelOffset = new Vector3(0f, 0f, 2f); // 面板相对于摄像机的偏移
    [SerializeField] private Vector3 panelScale = Vector3.one; // 面板缩放
    [SerializeField] private bool panelFaceCamera = true; // 面板是否始终面向摄像机
    
    [Header("动画设置")]
    [SerializeField] private bool enableAnimations = true;
    [SerializeField] private float animationDuration = 0.3f;
    [SerializeField] private AnimationCurve animationCurve = AnimationCurve.EaseInOut(0, 0, 1, 1);
    
    [Header("音效设置")]
    [SerializeField] private AudioSource audioSource;
    [SerializeField] private AudioClip buttonClickSound;
    [SerializeField] private AudioClip panelOpenSound;
    [SerializeField] private AudioClip panelCloseSound;
    
    // 私有变量
    private Camera vrCamera;
    private bool isSettingsPanelOpen = false;
    private Coroutine animationCoroutine;
    
    // 事件
    public System.Action OnSettingsOpened;
    public System.Action OnSettingsClosed;
    
    void Start()
    {
        InitializeVRSettingsUI();
    }
    
    void Update()
    {
        UpdateUIPositions();
    }
    
    /// <summary>
    /// 初始化VR设置UI
    /// </summary>
    private void InitializeVRSettingsUI()
    {
        // 查找VR摄像机
        FindVRCamera();
        
        // 设置UI组件
        SetupUIComponents();
        
        // 设置事件监听
        SetupEventListeners();
        
        // 初始化UI位置
        UpdateUIPositions();
        
        Debug.Log("[VRSettingsUI] VR设置UI初始化完成");
    }
    
    /// <summary>
    /// 查找VR摄像机
    /// </summary>
    private void FindVRCamera()
    {
        // 优先查找主摄像机
        vrCamera = Camera.main;
        
        if (vrCamera == null)
        {
            // 查找第一个摄像机
            vrCamera = FindObjectOfType<Camera>();
        }
        
        if (vrCamera == null)
        {
            Debug.LogError("[VRSettingsUI] 未找到VR摄像机！");
        }
        else
        {
            Debug.Log($"[VRSettingsUI] 找到VR摄像机: {vrCamera.name}");
        }
    }
    
    /// <summary>
    /// 设置UI组件
    /// </summary>
    private void SetupUIComponents()
    {
        // 确保设置面板初始状态为关闭
        if (settingsPanel != null)
        {
            settingsPanel.SetActive(false);
        }
        
        // 设置Canvas为World Space模式
        if (settingsCanvas != null)
        {
            settingsCanvas.renderMode = RenderMode.WorldSpace;
            settingsCanvas.worldCamera = vrCamera;
        }
        
        // 设置按钮缩放
        if (settingsButton != null)
        {
            settingsButton.transform.localScale = buttonScale;
        }
        
        // 设置面板缩放
        if (settingsPanel != null)
        {
            settingsPanel.transform.localScale = panelScale;
        }
    }
    
    /// <summary>
    /// 设置事件监听
    /// </summary>
    private void SetupEventListeners()
    {
        if (settingsButton != null)
        {
            settingsButton.onClick.AddListener(OnSettingsButtonClicked);
        }
        
        if (closeButton != null)
        {
            closeButton.onClick.AddListener(OnCloseButtonClicked);
        }
    }
    
    /// <summary>
    /// 更新UI位置
    /// </summary>
    private void UpdateUIPositions()
    {
        if (vrCamera == null) return;
        
        // 更新设置按钮位置
        UpdateSettingsButtonPosition();
        
        // 更新设置面板位置
        if (isSettingsPanelOpen)
        {
            UpdateSettingsPanelPosition();
        }
    }
    
    /// <summary>
    /// 更新设置按钮位置
    /// </summary>
    private void UpdateSettingsButtonPosition()
    {
        if (settingsButton == null) return;
        
        Transform cameraTransform = vrCamera.transform;
        
        // 计算按钮位置（相对于摄像机）
        Vector3 buttonPosition = cameraTransform.position + 
                                cameraTransform.TransformDirection(buttonOffset);
        
        settingsButton.transform.position = buttonPosition;
        
        // 设置按钮旋转
        if (followCameraRotation)
        {
            settingsButton.transform.rotation = cameraTransform.rotation;
        }
        else
        {
            // 始终面向摄像机
            settingsButton.transform.LookAt(cameraTransform.position);
            settingsButton.transform.Rotate(0, 180, 0); // 翻转以正确面向
        }
    }
    
    /// <summary>
    /// 更新设置面板位置
    /// </summary>
    private void UpdateSettingsPanelPosition()
    {
        if (settingsPanel == null) return;
        
        Transform cameraTransform = vrCamera.transform;
        
        // 计算面板位置（相对于摄像机）
        Vector3 panelPosition = cameraTransform.position + 
                               cameraTransform.TransformDirection(panelOffset);
        
        settingsPanel.transform.position = panelPosition;
        
        // 设置面板旋转
        if (panelFaceCamera)
        {
            settingsPanel.transform.LookAt(cameraTransform.position);
            settingsPanel.transform.Rotate(0, 180, 0); // 翻转以正确面向
        }
    }
    
    /// <summary>
    /// 设置按钮点击事件
    /// </summary>
    private void OnSettingsButtonClicked()
    {
        Debug.Log("[VRSettingsUI] 设置按钮被点击");
        
        PlaySound(buttonClickSound);
        
        if (isSettingsPanelOpen)
        {
            CloseSettingsPanel();
        }
        else
        {
            OpenSettingsPanel();
        }
    }
    
    /// <summary>
    /// 关闭按钮点击事件
    /// </summary>
    private void OnCloseButtonClicked()
    {
        Debug.Log("[VRSettingsUI] 关闭按钮被点击");
        
        PlaySound(buttonClickSound);
        CloseSettingsPanel();
    }
    
    /// <summary>
    /// 打开设置面板
    /// </summary>
    public void OpenSettingsPanel()
    {
        if (isSettingsPanelOpen || settingsPanel == null) return;
        
        isSettingsPanelOpen = true;
        
        // 更新面板位置
        UpdateSettingsPanelPosition();
        
        // 激活面板
        settingsPanel.SetActive(true);
        
        // 播放音效
        PlaySound(panelOpenSound);
        
        // 播放动画
        if (enableAnimations)
        {
            if (animationCoroutine != null)
            {
                StopCoroutine(animationCoroutine);
            }
            animationCoroutine = StartCoroutine(AnimatePanel(true));
        }
        
        // 触发事件
        OnSettingsOpened?.Invoke();
        
        Debug.Log("[VRSettingsUI] 设置面板已打开");
    }
    
    /// <summary>
    /// 关闭设置面板
    /// </summary>
    public void CloseSettingsPanel()
    {
        if (!isSettingsPanelOpen || settingsPanel == null) return;
        
        isSettingsPanelOpen = false;
        
        // 播放音效
        PlaySound(panelCloseSound);
        
        // 播放动画
        if (enableAnimations)
        {
            if (animationCoroutine != null)
            {
                StopCoroutine(animationCoroutine);
            }
            animationCoroutine = StartCoroutine(AnimatePanel(false));
        }
        else
        {
            // 直接关闭
            settingsPanel.SetActive(false);
        }
        
        // 触发事件
        OnSettingsClosed?.Invoke();
        
        Debug.Log("[VRSettingsUI] 设置面板已关闭");
    }
    
    /// <summary>
    /// 面板动画
    /// </summary>
    private System.Collections.IEnumerator AnimatePanel(bool opening)
    {
        Vector3 startScale = opening ? Vector3.zero : panelScale;
        Vector3 endScale = opening ? panelScale : Vector3.zero;
        
        float elapsedTime = 0f;
        
        while (elapsedTime < animationDuration)
        {
            elapsedTime += Time.deltaTime;
            float progress = elapsedTime / animationDuration;
            float curveValue = animationCurve.Evaluate(progress);
            
            Vector3 currentScale = Vector3.Lerp(startScale, endScale, curveValue);
            settingsPanel.transform.localScale = currentScale;
            
            yield return null;
        }
        
        settingsPanel.transform.localScale = endScale;
        
        // 如果是关闭动画，在动画结束后禁用面板
        if (!opening)
        {
            settingsPanel.SetActive(false);
        }
        
        animationCoroutine = null;
    }
    
    /// <summary>
    /// 播放音效
    /// </summary>
    private void PlaySound(AudioClip clip)
    {
        if (audioSource != null && clip != null)
        {
            audioSource.PlayOneShot(clip);
        }
    }
    
    /// <summary>
    /// 切换设置面板状态
    /// </summary>
    public void ToggleSettingsPanel()
    {
        if (isSettingsPanelOpen)
        {
            CloseSettingsPanel();
        }
        else
        {
            OpenSettingsPanel();
        }
    }
    
    /// <summary>
    /// 获取设置面板状态
    /// </summary>
    public bool IsSettingsPanelOpen()
    {
        return isSettingsPanelOpen;
    }
    
    void OnDestroy()
    {
        // 清理事件监听
        if (settingsButton != null)
        {
            settingsButton.onClick.RemoveListener(OnSettingsButtonClicked);
        }
        
        if (closeButton != null)
        {
            closeButton.onClick.RemoveListener(OnCloseButtonClicked);
        }
    }
}
