# VR开始菜单使用指南

## 🎯 概述

本指南介绍如何在StartMenu场景中设置和使用VR开始界面系统。该系统专门为PICO VR环境设计，提供完整的用户登录、输入和场景切换功能。

## 📦 系统组件

### 核心脚本

1. **VRStartMenuManager** - 主要的菜单管理器
2. **VRStartMenuSetupHelper** - UI创建和配置助手
3. **VRStartMenuInputHandler** - VR输入处理器
4. **VRSceneManager** - 场景切换管理器
5. **VRStartMenuDemo** - 演示和测试脚本

### 依赖组件

- **VRUIManager** - VR UI管理器（已存在）
- **VRUIInteractor** - VR UI交互器（已存在）
- **VRUILayoutOptimizer** - VR UI布局优化器（已存在）

## 🚀 快速开始

### 方法1：自动设置（推荐）

1. **添加演示脚本到场景**
   ```
   1. 在StartMenu场景中创建空GameObject，命名为"VRStartMenuDemo"
   2. 添加VRStartMenuDemo组件
   3. 运行场景，系统会自动创建VR开始界面
   ```

2. **使用编辑器菜单**
   ```
   1. 在Unity编辑器中选择菜单：VR Assembly > Create VR Start Menu
   2. 系统会自动创建完整的VR开始界面
   ```

### 方法2：手动设置

1. **创建设置助手**
   ```csharp
   GameObject helperGO = new GameObject("VRStartMenuSetupHelper");
   VRStartMenuSetupHelper helper = helperGO.AddComponent<VRStartMenuSetupHelper>();
   helper.CreateVRStartMenu();
   ```

2. **手动配置组件**
   ```csharp
   // 获取Canvas并添加组件
   Canvas canvas = FindObjectOfType<Canvas>();
   canvas.gameObject.AddComponent<VRStartMenuManager>();
   canvas.gameObject.AddComponent<VRStartMenuInputHandler>();
   ```

## 🎮 VR交互说明

### PICO控制器操作

| 操作 | 功能 | 说明 |
|------|------|------|
| **触发器** | 点击UI元素 | 主要的选择和确认操作 |
| **菜单按钮** | 显示/隐藏虚拟键盘 | 用于文本输入 |
| **主按钮(A)** | 确认输入 | 完成文本输入并触发开始按钮 |
| **副按钮(B)** | 删除字符 | 删除输入框中的最后一个字符 |

### 交互流程

1. **启动应用** → 看到VR开始界面
2. **瞄准输入框** → 使用触发器点击激活
3. **输入用户名** → 使用虚拟键盘或控制器按钮
4. **确认输入** → 按主按钮或点击开始按钮
5. **场景切换** → 自动跳转到主装配场景

## ⚙️ 配置选项

### VRStartMenuManager 配置

```csharp
[Header("VR设置")]
public bool enableVRMode = true;           // 启用VR模式
public float uiDistance = 2.5f;            // UI距离用户的距离
public float uiScale = 0.01f;              // UI缩放比例
public Vector3 uiOffset = Vector3.zero;    // UI偏移位置

[Header("场景设置")]
public string nextSceneName = "Animation"; // 下一个场景名称
public bool validateUsername = true;       // 验证用户名
public int minUsernameLength = 2;          // 最小用户名长度
```

### VRStartMenuInputHandler 配置

```csharp
[Header("VR控制器设置")]
public bool enableControllerInput = true;    // 启用控制器输入
public bool enableVirtualKeyboard = true;    // 启用虚拟键盘
public bool enableHapticFeedback = true;     // 启用触觉反馈

[Header("输入验证")]
public int maxInputLength = 20;              // 最大输入长度
public bool allowSpecialCharacters = false;  // 允许特殊字符
```

## 🎨 UI自定义

### 修改UI样式

```csharp
[Header("UI样式设置")]
public Color backgroundColor = new Color(0.1f, 0.1f, 0.2f, 0.8f);
public Color primaryColor = new Color(0.2f, 0.6f, 1f, 1f);
public Color textColor = Color.white;
```

### 调整UI布局

```csharp
// 在VRStartMenuSetupHelper中修改UI元素位置
private void CreateTitleText(Transform parent)
{
    // 修改标题位置和大小
    rectTransform.anchorMin = new Vector2(0, 0.8f);
    rectTransform.anchorMax = new Vector2(1, 1f);
}
```

## 🔧 高级功能

### 用户数据管理

```csharp
// 获取用户输入的用户名
string username = VRSceneManager.Instance.GetSceneData<string>("Username");

// 设置自定义数据
VRSceneManager.Instance.SetSceneData("CustomData", "value");
```

### 场景切换事件

```csharp
// 监听场景切换事件
VRStartMenuManager manager = FindObjectOfType<VRStartMenuManager>();
manager.OnSceneTransition += (sceneName) => {
    Debug.Log($"切换到场景: {sceneName}");
};
```

### 自定义输入验证

```csharp
// 在VRStartMenuManager中重写验证逻辑
private bool ValidateUsername(string username)
{
    // 自定义验证逻辑
    return !string.IsNullOrEmpty(username) && 
           username.Length >= minUsernameLength &&
           !username.Contains("@"); // 例如：不允许包含@符号
}
```

## 🐛 故障排除

### 常见问题

1. **UI不显示**
   - 检查Canvas是否设置为World Space模式
   - 确认VR摄像机引用正确
   - 验证UI距离和缩放设置

2. **控制器无法交互**
   - 确认XR Interaction Toolkit已安装
   - 检查TrackedDeviceGraphicRaycaster组件
   - 验证控制器射线配置

3. **场景切换失败**
   - 确认目标场景已添加到Build Settings
   - 检查场景名称是否正确
   - 验证VRSceneManager是否正常工作

4. **虚拟键盘不显示**
   - 检查enableVirtualKeyboard设置
   - 确认输入框已正确配置
   - 验证菜单按钮输入

### 调试工具

```csharp
// 启用调试模式
VRStartMenuDemo demo = FindObjectOfType<VRStartMenuDemo>();
demo.enableDebugMode = true;

// 使用快捷键
// F1: 显示说明
// F2: 设置菜单
// F3: 测试功能
```

## 📱 PICO部署注意事项

### Build Settings配置

1. **添加场景到Build Settings**
   ```
   - StartMenu.unity (Index: 0)
   - Animation.unity (Index: 1)
   ```

2. **XR设置**
   ```
   - 启用XR Plugin Management
   - 选择PICO XR Plugin
   - 配置XR Interaction Toolkit
   ```

### 性能优化

```csharp
[Header("性能设置")]
public bool optimizeForVR = true;        // VR性能优化
public bool useAsyncLoading = true;      // 异步场景加载
public bool showLoadingProgress = true;  // 显示加载进度
```

## 📋 检查清单

### 部署前检查

- [ ] StartMenu和Animation场景已添加到Build Settings
- [ ] PICO XR Plugin已正确配置
- [ ] XR Interaction Toolkit已安装
- [ ] VR UI组件已正确设置
- [ ] 场景切换逻辑已测试
- [ ] 用户输入验证已配置
- [ ] 音效和触觉反馈已设置

### 测试清单

- [ ] VR界面正确显示
- [ ] 控制器射线交互正常
- [ ] 文本输入功能正常
- [ ] 虚拟键盘显示/隐藏正常
- [ ] 开始按钮响应正常
- [ ] 场景切换成功
- [ ] 用户数据正确保存
- [ ] 触觉反馈正常工作

## 🔄 更新和维护

### 版本兼容性

- Unity 2022.3 LTS或更高版本
- XR Interaction Toolkit 2.6.4或更高版本
- PICO Unity Integration SDK最新版本

### 扩展开发

```csharp
// 添加自定义功能
public class CustomVRStartMenu : VRStartMenuManager
{
    // 重写或扩展现有功能
    protected override void SetupUIElements()
    {
        base.SetupUIElements();
        // 添加自定义UI元素
    }
}
```

## 📞 技术支持

如果遇到问题，请检查：

1. **Unity Console** - 查看错误日志
2. **VR设备日志** - 检查PICO设备日志
3. **组件配置** - 验证所有组件设置
4. **场景设置** - 确认场景配置正确

---

**注意**: 本系统与现有的VR装配动画系统完全兼容，不会影响现有功能。
