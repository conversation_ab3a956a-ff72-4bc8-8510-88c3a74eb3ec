﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





// 0x00000001 System.String UnityEngine.XR.Management.XRConfigurationDataAttribute::get_displayName()
extern void XRConfigurationDataAttribute_get_displayName_mE0BD51CF7824CBB1318708EAD9717F604BE14057 (void);
// 0x00000002 System.Void UnityEngine.XR.Management.XRConfigurationDataAttribute::set_displayName(System.String)
extern void XRConfigurationDataAttribute_set_displayName_m3AFFE133640E562156E3D20A81206FE9F62D12BB (void);
// 0x00000003 System.String UnityEngine.XR.Management.XRConfigurationDataAttribute::get_buildSettingsKey()
extern void XRConfigurationDataAttribute_get_buildSettingsKey_m5458381526D6DEDA8A3DDC7375F24EE3A481CE37 (void);
// 0x00000004 System.Void UnityEngine.XR.Management.XRConfigurationDataAttribute::set_buildSettingsKey(System.String)
extern void XRConfigurationDataAttribute_set_buildSettingsKey_m6AFAEBF0A402551EC9A80324E5B64403065E75A0 (void);
// 0x00000005 System.Void UnityEngine.XR.Management.XRConfigurationDataAttribute::.ctor()
extern void XRConfigurationDataAttribute__ctor_mC4325AA1EFFCBB1D2CC10B1F0BFDACCDE147A9C8 (void);
// 0x00000006 System.Void UnityEngine.XR.Management.XRConfigurationDataAttribute::.ctor(System.String,System.String)
extern void XRConfigurationDataAttribute__ctor_m644D2F677622C19F3AF0AF72BC355A73A274597D (void);
// 0x00000007 UnityEngine.XR.Management.XRManagerSettings UnityEngine.XR.Management.XRGeneralSettings::get_Manager()
extern void XRGeneralSettings_get_Manager_m112FEB4E6DFB7B5F5C4A2DEC4E975CF2EBD51B42 (void);
// 0x00000008 System.Void UnityEngine.XR.Management.XRGeneralSettings::set_Manager(UnityEngine.XR.Management.XRManagerSettings)
extern void XRGeneralSettings_set_Manager_m8BF44008AF9941A38529BF4E9DD291B519CD3310 (void);
// 0x00000009 UnityEngine.XR.Management.XRGeneralSettings UnityEngine.XR.Management.XRGeneralSettings::get_Instance()
extern void XRGeneralSettings_get_Instance_m9F222F982E62E066E119754858D8E73CFE42048C (void);
// 0x0000000A UnityEngine.XR.Management.XRManagerSettings UnityEngine.XR.Management.XRGeneralSettings::get_AssignedSettings()
extern void XRGeneralSettings_get_AssignedSettings_mBC55A93C750C07D07C3588D7238FAE6C0D8E005F (void);
// 0x0000000B System.Boolean UnityEngine.XR.Management.XRGeneralSettings::get_InitManagerOnStart()
extern void XRGeneralSettings_get_InitManagerOnStart_mA117DE9A4754CC3BA0E7F99FB7A593FF5B5B482A (void);
// 0x0000000C System.Void UnityEngine.XR.Management.XRGeneralSettings::Awake()
extern void XRGeneralSettings_Awake_mDB501D0A232DAB9D5E56F232CBB9FE5BFA8F5AE2 (void);
// 0x0000000D System.Void UnityEngine.XR.Management.XRGeneralSettings::Quit()
extern void XRGeneralSettings_Quit_m1AAD46F067D518F864C13ABD5832559C39540A54 (void);
// 0x0000000E System.Void UnityEngine.XR.Management.XRGeneralSettings::Start()
extern void XRGeneralSettings_Start_m191BC7C4C042DB4599D74254F6E62F346F20BDAB (void);
// 0x0000000F System.Void UnityEngine.XR.Management.XRGeneralSettings::OnDestroy()
extern void XRGeneralSettings_OnDestroy_mFEE1954662ED788B1A09309573EFA9DE210BEF96 (void);
// 0x00000010 System.Void UnityEngine.XR.Management.XRGeneralSettings::AttemptInitializeXRSDKOnLoad()
extern void XRGeneralSettings_AttemptInitializeXRSDKOnLoad_m1905ABBBDC0982AD1AF76D157881215BB66002B7 (void);
// 0x00000011 System.Void UnityEngine.XR.Management.XRGeneralSettings::AttemptStartXRSDKOnBeforeSplashScreen()
extern void XRGeneralSettings_AttemptStartXRSDKOnBeforeSplashScreen_m186BDD078384E5494797E213BA9BEE337747DD3A (void);
// 0x00000012 System.Void UnityEngine.XR.Management.XRGeneralSettings::InitXRSDK()
extern void XRGeneralSettings_InitXRSDK_m29F5580C758C0F617E9A2588AA861DE49E4EC779 (void);
// 0x00000013 System.Void UnityEngine.XR.Management.XRGeneralSettings::StartXRSDK()
extern void XRGeneralSettings_StartXRSDK_mC2BAC4CF6D76CC4704B9DAC2601FCF8CDB9A9F12 (void);
// 0x00000014 System.Void UnityEngine.XR.Management.XRGeneralSettings::StopXRSDK()
extern void XRGeneralSettings_StopXRSDK_m1ABC0D7F3AB9422CAE5993A1641836CE0DC394A2 (void);
// 0x00000015 System.Void UnityEngine.XR.Management.XRGeneralSettings::DeInitXRSDK()
extern void XRGeneralSettings_DeInitXRSDK_mF101D7A880E1DC4958ADA2388E7140F4DF7B7EDC (void);
// 0x00000016 System.Void UnityEngine.XR.Management.XRGeneralSettings::.ctor()
extern void XRGeneralSettings__ctor_m17F368F5DF52B3CF764C69C6A551DC983874CB59 (void);
// 0x00000017 System.Void UnityEngine.XR.Management.XRGeneralSettings::.cctor()
extern void XRGeneralSettings__cctor_mA1B586D9E9534249DACCFD9CEA23C69A433F1A44 (void);
// 0x00000018 System.Boolean UnityEngine.XR.Management.XRLoader::Initialize()
extern void XRLoader_Initialize_mA506BDCDF97BA180CDC5BF1945E29CF3766CA859 (void);
// 0x00000019 System.Boolean UnityEngine.XR.Management.XRLoader::Start()
extern void XRLoader_Start_m8E73CE045F2B7D7BE46C5670C936880DDF380FD1 (void);
// 0x0000001A System.Boolean UnityEngine.XR.Management.XRLoader::Stop()
extern void XRLoader_Stop_m398D1FFBE01D294AD5DFB6DB8D807C1DDEA91C97 (void);
// 0x0000001B System.Boolean UnityEngine.XR.Management.XRLoader::Deinitialize()
extern void XRLoader_Deinitialize_m001821A3F803AC067C6884A6BBD01DDE378DFAB9 (void);
// 0x0000001C T UnityEngine.XR.Management.XRLoader::GetLoadedSubsystem()
// 0x0000001D System.Collections.Generic.List`1<UnityEngine.Rendering.GraphicsDeviceType> UnityEngine.XR.Management.XRLoader::GetSupportedGraphicsDeviceTypes(System.Boolean)
extern void XRLoader_GetSupportedGraphicsDeviceTypes_m92C8ECDE9FEF211C2B9EB9574B563EE805859753 (void);
// 0x0000001E System.Void UnityEngine.XR.Management.XRLoader::.ctor()
extern void XRLoader__ctor_m466A0E280C64F30806342A9EEF55801FA88682CE (void);
// 0x0000001F T UnityEngine.XR.Management.XRLoaderHelper::GetLoadedSubsystem()
// 0x00000020 System.Void UnityEngine.XR.Management.XRLoaderHelper::StartSubsystem()
// 0x00000021 System.Void UnityEngine.XR.Management.XRLoaderHelper::StopSubsystem()
// 0x00000022 System.Void UnityEngine.XR.Management.XRLoaderHelper::DestroySubsystem()
// 0x00000023 System.Void UnityEngine.XR.Management.XRLoaderHelper::CreateSubsystem(System.Collections.Generic.List`1<TDescriptor>,System.String)
// 0x00000024 System.Void UnityEngine.XR.Management.XRLoaderHelper::CreateIntegratedSubsystem(System.Collections.Generic.List`1<TDescriptor>,System.String)
// 0x00000025 System.Void UnityEngine.XR.Management.XRLoaderHelper::CreateStandaloneSubsystem(System.Collections.Generic.List`1<TDescriptor>,System.String)
// 0x00000026 System.Boolean UnityEngine.XR.Management.XRLoaderHelper::Deinitialize()
extern void XRLoaderHelper_Deinitialize_m879052C82795CBA2443070F8206798BED6C0C5E7 (void);
// 0x00000027 System.Void UnityEngine.XR.Management.XRLoaderHelper::.ctor()
extern void XRLoaderHelper__ctor_mEAD9691DBE10C223AB11AB7056ED0B3BA59D7699 (void);
// 0x00000028 System.Boolean UnityEngine.XR.Management.XRManagementAnalytics::Initialize()
extern void XRManagementAnalytics_Initialize_mA0727A2B768833A169E4FBC45B7F6F2A09FAEC78 (void);
// 0x00000029 System.Boolean UnityEngine.XR.Management.XRManagerSettings::get_automaticLoading()
extern void XRManagerSettings_get_automaticLoading_mF28DC51D46409F4D75DCF53E38C8E6B28444B01A (void);
// 0x0000002A System.Void UnityEngine.XR.Management.XRManagerSettings::set_automaticLoading(System.Boolean)
extern void XRManagerSettings_set_automaticLoading_m8F328FA730A8DD8C62D9A8FA8569A2084D530E0E (void);
// 0x0000002B System.Boolean UnityEngine.XR.Management.XRManagerSettings::get_automaticRunning()
extern void XRManagerSettings_get_automaticRunning_mE6F4B5875CF9B098B0B20388E77B651EB633D166 (void);
// 0x0000002C System.Void UnityEngine.XR.Management.XRManagerSettings::set_automaticRunning(System.Boolean)
extern void XRManagerSettings_set_automaticRunning_mAC78998A385194EBEBF00C7337FC018148A40729 (void);
// 0x0000002D System.Collections.Generic.List`1<UnityEngine.XR.Management.XRLoader> UnityEngine.XR.Management.XRManagerSettings::get_loaders()
extern void XRManagerSettings_get_loaders_m485A1CD420B07D57B9E2856795BDB933389322D0 (void);
// 0x0000002E System.Collections.Generic.IReadOnlyList`1<UnityEngine.XR.Management.XRLoader> UnityEngine.XR.Management.XRManagerSettings::get_activeLoaders()
extern void XRManagerSettings_get_activeLoaders_m47D0FF16B7012A87F56D93124DC5D8DA0ACA591F (void);
// 0x0000002F System.Boolean UnityEngine.XR.Management.XRManagerSettings::get_isInitializationComplete()
extern void XRManagerSettings_get_isInitializationComplete_m2F7E30B51DB12D34535BE7805A3CD490FFE12F68 (void);
// 0x00000030 UnityEngine.XR.Management.XRLoader UnityEngine.XR.Management.XRManagerSettings::get_activeLoader()
extern void XRManagerSettings_get_activeLoader_mFB3B679005792D3DF871EAA7120DD86DCA1D5DEA (void);
// 0x00000031 System.Void UnityEngine.XR.Management.XRManagerSettings::set_activeLoader(UnityEngine.XR.Management.XRLoader)
extern void XRManagerSettings_set_activeLoader_m85857974C574787278D7B4E861AD235F2D49726A (void);
// 0x00000032 T UnityEngine.XR.Management.XRManagerSettings::ActiveLoaderAs()
// 0x00000033 System.Void UnityEngine.XR.Management.XRManagerSettings::InitializeLoaderSync()
extern void XRManagerSettings_InitializeLoaderSync_m5CE4139417252856F67537554BAD16798E5A8D6D (void);
// 0x00000034 System.Collections.IEnumerator UnityEngine.XR.Management.XRManagerSettings::InitializeLoader()
extern void XRManagerSettings_InitializeLoader_m563761BAF04C04AC931D738AD9E4EBF7FD87E62B (void);
// 0x00000035 System.Boolean UnityEngine.XR.Management.XRManagerSettings::TryAddLoader(UnityEngine.XR.Management.XRLoader,System.Int32)
extern void XRManagerSettings_TryAddLoader_m1CF7DEB07D6E67F3D788B41FE0404DAE19F38031 (void);
// 0x00000036 System.Boolean UnityEngine.XR.Management.XRManagerSettings::TryRemoveLoader(UnityEngine.XR.Management.XRLoader)
extern void XRManagerSettings_TryRemoveLoader_m29479875EF3C4773D5D74661131A06C2E8736BB0 (void);
// 0x00000037 System.Boolean UnityEngine.XR.Management.XRManagerSettings::TrySetLoaders(System.Collections.Generic.List`1<UnityEngine.XR.Management.XRLoader>)
extern void XRManagerSettings_TrySetLoaders_mD2EAE278D90F867E6EA02A75D60F09FD1EE13C30 (void);
// 0x00000038 System.Void UnityEngine.XR.Management.XRManagerSettings::Awake()
extern void XRManagerSettings_Awake_mAD790D96C19843B09AA0D7B8F6819757E0D62C89 (void);
// 0x00000039 System.Boolean UnityEngine.XR.Management.XRManagerSettings::CheckGraphicsAPICompatibility(UnityEngine.XR.Management.XRLoader)
extern void XRManagerSettings_CheckGraphicsAPICompatibility_m63AE4A622C2B137618B747F25A7591CBD0B9496B (void);
// 0x0000003A System.Void UnityEngine.XR.Management.XRManagerSettings::StartSubsystems()
extern void XRManagerSettings_StartSubsystems_m94D89460222C083D6E25881C6825E44FEC98DDCC (void);
// 0x0000003B System.Void UnityEngine.XR.Management.XRManagerSettings::StopSubsystems()
extern void XRManagerSettings_StopSubsystems_mB59730C9283178A5E9E6D6E9127FE3DB25CC82D4 (void);
// 0x0000003C System.Void UnityEngine.XR.Management.XRManagerSettings::DeinitializeLoader()
extern void XRManagerSettings_DeinitializeLoader_m81038A43AD1AD84045B90396D1E2E4F2D8029BAB (void);
// 0x0000003D System.Void UnityEngine.XR.Management.XRManagerSettings::Start()
extern void XRManagerSettings_Start_mBF2ADCCC6377C8A7DDA7C462313DAD00DC00382C (void);
// 0x0000003E System.Void UnityEngine.XR.Management.XRManagerSettings::OnDisable()
extern void XRManagerSettings_OnDisable_m31F1208BDDBDC01D75AAC4063C0F8765269CA408 (void);
// 0x0000003F System.Void UnityEngine.XR.Management.XRManagerSettings::OnDestroy()
extern void XRManagerSettings_OnDestroy_m88AE1F5A6B819CF07D249F6A6D3A7809C4DBF9E2 (void);
// 0x00000040 System.Collections.Generic.List`1<UnityEngine.XR.Management.XRLoader> UnityEngine.XR.Management.XRManagerSettings::get_currentLoaders()
extern void XRManagerSettings_get_currentLoaders_m3A4FF4CA5168E58C4DAB89B33972C1EE83FF2237 (void);
// 0x00000041 System.Void UnityEngine.XR.Management.XRManagerSettings::set_currentLoaders(System.Collections.Generic.List`1<UnityEngine.XR.Management.XRLoader>)
extern void XRManagerSettings_set_currentLoaders_mBF7D7DE30A657C0B8A145FBE84104797D668454A (void);
// 0x00000042 System.Collections.Generic.HashSet`1<UnityEngine.XR.Management.XRLoader> UnityEngine.XR.Management.XRManagerSettings::get_registeredLoaders()
extern void XRManagerSettings_get_registeredLoaders_m9FDA9ABA6EE526EC99C892488869777BF13E4D1B (void);
// 0x00000043 System.Void UnityEngine.XR.Management.XRManagerSettings::.ctor()
extern void XRManagerSettings__ctor_m516BC0C114BD8652EAE41D77BF961C5521520317 (void);
// 0x00000044 System.Void UnityEngine.XR.Management.XRManagerSettings/<InitializeLoader>d__24::.ctor(System.Int32)
extern void U3CInitializeLoaderU3Ed__24__ctor_m55EFC72939B5E8ABAF9E70DD0878A80B0280C97A (void);
// 0x00000045 System.Void UnityEngine.XR.Management.XRManagerSettings/<InitializeLoader>d__24::System.IDisposable.Dispose()
extern void U3CInitializeLoaderU3Ed__24_System_IDisposable_Dispose_mB850632340F899B06CCC956A3557DECF8BCC7AE8 (void);
// 0x00000046 System.Boolean UnityEngine.XR.Management.XRManagerSettings/<InitializeLoader>d__24::MoveNext()
extern void U3CInitializeLoaderU3Ed__24_MoveNext_m0D3BCBEFAE33727F17874481BF551C2741132003 (void);
// 0x00000047 System.Void UnityEngine.XR.Management.XRManagerSettings/<InitializeLoader>d__24::<>m__Finally1()
extern void U3CInitializeLoaderU3Ed__24_U3CU3Em__Finally1_m6CCA9E68B1BF2344768A755069F9B9CD799E277C (void);
// 0x00000048 System.Object UnityEngine.XR.Management.XRManagerSettings/<InitializeLoader>d__24::System.Collections.Generic.IEnumerator<System.Object>.get_Current()
extern void U3CInitializeLoaderU3Ed__24_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m4F6952CC23A00893DC2B18A638480696CEE3370C (void);
// 0x00000049 System.Void UnityEngine.XR.Management.XRManagerSettings/<InitializeLoader>d__24::System.Collections.IEnumerator.Reset()
extern void U3CInitializeLoaderU3Ed__24_System_Collections_IEnumerator_Reset_m803D94C93BE413DFBA21C0FEFABA00DA3E3414C4 (void);
// 0x0000004A System.Object UnityEngine.XR.Management.XRManagerSettings/<InitializeLoader>d__24::System.Collections.IEnumerator.get_Current()
extern void U3CInitializeLoaderU3Ed__24_System_Collections_IEnumerator_get_Current_m92156199FCA3AAD568CA668A7A6CF7FB31B8A23C (void);
static Il2CppMethodPointer s_methodPointers[74] = 
{
	XRConfigurationDataAttribute_get_displayName_mE0BD51CF7824CBB1318708EAD9717F604BE14057,
	XRConfigurationDataAttribute_set_displayName_m3AFFE133640E562156E3D20A81206FE9F62D12BB,
	XRConfigurationDataAttribute_get_buildSettingsKey_m5458381526D6DEDA8A3DDC7375F24EE3A481CE37,
	XRConfigurationDataAttribute_set_buildSettingsKey_m6AFAEBF0A402551EC9A80324E5B64403065E75A0,
	XRConfigurationDataAttribute__ctor_mC4325AA1EFFCBB1D2CC10B1F0BFDACCDE147A9C8,
	XRConfigurationDataAttribute__ctor_m644D2F677622C19F3AF0AF72BC355A73A274597D,
	XRGeneralSettings_get_Manager_m112FEB4E6DFB7B5F5C4A2DEC4E975CF2EBD51B42,
	XRGeneralSettings_set_Manager_m8BF44008AF9941A38529BF4E9DD291B519CD3310,
	XRGeneralSettings_get_Instance_m9F222F982E62E066E119754858D8E73CFE42048C,
	XRGeneralSettings_get_AssignedSettings_mBC55A93C750C07D07C3588D7238FAE6C0D8E005F,
	XRGeneralSettings_get_InitManagerOnStart_mA117DE9A4754CC3BA0E7F99FB7A593FF5B5B482A,
	XRGeneralSettings_Awake_mDB501D0A232DAB9D5E56F232CBB9FE5BFA8F5AE2,
	XRGeneralSettings_Quit_m1AAD46F067D518F864C13ABD5832559C39540A54,
	XRGeneralSettings_Start_m191BC7C4C042DB4599D74254F6E62F346F20BDAB,
	XRGeneralSettings_OnDestroy_mFEE1954662ED788B1A09309573EFA9DE210BEF96,
	XRGeneralSettings_AttemptInitializeXRSDKOnLoad_m1905ABBBDC0982AD1AF76D157881215BB66002B7,
	XRGeneralSettings_AttemptStartXRSDKOnBeforeSplashScreen_m186BDD078384E5494797E213BA9BEE337747DD3A,
	XRGeneralSettings_InitXRSDK_m29F5580C758C0F617E9A2588AA861DE49E4EC779,
	XRGeneralSettings_StartXRSDK_mC2BAC4CF6D76CC4704B9DAC2601FCF8CDB9A9F12,
	XRGeneralSettings_StopXRSDK_m1ABC0D7F3AB9422CAE5993A1641836CE0DC394A2,
	XRGeneralSettings_DeInitXRSDK_mF101D7A880E1DC4958ADA2388E7140F4DF7B7EDC,
	XRGeneralSettings__ctor_m17F368F5DF52B3CF764C69C6A551DC983874CB59,
	XRGeneralSettings__cctor_mA1B586D9E9534249DACCFD9CEA23C69A433F1A44,
	XRLoader_Initialize_mA506BDCDF97BA180CDC5BF1945E29CF3766CA859,
	XRLoader_Start_m8E73CE045F2B7D7BE46C5670C936880DDF380FD1,
	XRLoader_Stop_m398D1FFBE01D294AD5DFB6DB8D807C1DDEA91C97,
	XRLoader_Deinitialize_m001821A3F803AC067C6884A6BBD01DDE378DFAB9,
	NULL,
	XRLoader_GetSupportedGraphicsDeviceTypes_m92C8ECDE9FEF211C2B9EB9574B563EE805859753,
	XRLoader__ctor_m466A0E280C64F30806342A9EEF55801FA88682CE,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	XRLoaderHelper_Deinitialize_m879052C82795CBA2443070F8206798BED6C0C5E7,
	XRLoaderHelper__ctor_mEAD9691DBE10C223AB11AB7056ED0B3BA59D7699,
	XRManagementAnalytics_Initialize_mA0727A2B768833A169E4FBC45B7F6F2A09FAEC78,
	XRManagerSettings_get_automaticLoading_mF28DC51D46409F4D75DCF53E38C8E6B28444B01A,
	XRManagerSettings_set_automaticLoading_m8F328FA730A8DD8C62D9A8FA8569A2084D530E0E,
	XRManagerSettings_get_automaticRunning_mE6F4B5875CF9B098B0B20388E77B651EB633D166,
	XRManagerSettings_set_automaticRunning_mAC78998A385194EBEBF00C7337FC018148A40729,
	XRManagerSettings_get_loaders_m485A1CD420B07D57B9E2856795BDB933389322D0,
	XRManagerSettings_get_activeLoaders_m47D0FF16B7012A87F56D93124DC5D8DA0ACA591F,
	XRManagerSettings_get_isInitializationComplete_m2F7E30B51DB12D34535BE7805A3CD490FFE12F68,
	XRManagerSettings_get_activeLoader_mFB3B679005792D3DF871EAA7120DD86DCA1D5DEA,
	XRManagerSettings_set_activeLoader_m85857974C574787278D7B4E861AD235F2D49726A,
	NULL,
	XRManagerSettings_InitializeLoaderSync_m5CE4139417252856F67537554BAD16798E5A8D6D,
	XRManagerSettings_InitializeLoader_m563761BAF04C04AC931D738AD9E4EBF7FD87E62B,
	XRManagerSettings_TryAddLoader_m1CF7DEB07D6E67F3D788B41FE0404DAE19F38031,
	XRManagerSettings_TryRemoveLoader_m29479875EF3C4773D5D74661131A06C2E8736BB0,
	XRManagerSettings_TrySetLoaders_mD2EAE278D90F867E6EA02A75D60F09FD1EE13C30,
	XRManagerSettings_Awake_mAD790D96C19843B09AA0D7B8F6819757E0D62C89,
	XRManagerSettings_CheckGraphicsAPICompatibility_m63AE4A622C2B137618B747F25A7591CBD0B9496B,
	XRManagerSettings_StartSubsystems_m94D89460222C083D6E25881C6825E44FEC98DDCC,
	XRManagerSettings_StopSubsystems_mB59730C9283178A5E9E6D6E9127FE3DB25CC82D4,
	XRManagerSettings_DeinitializeLoader_m81038A43AD1AD84045B90396D1E2E4F2D8029BAB,
	XRManagerSettings_Start_mBF2ADCCC6377C8A7DDA7C462313DAD00DC00382C,
	XRManagerSettings_OnDisable_m31F1208BDDBDC01D75AAC4063C0F8765269CA408,
	XRManagerSettings_OnDestroy_m88AE1F5A6B819CF07D249F6A6D3A7809C4DBF9E2,
	XRManagerSettings_get_currentLoaders_m3A4FF4CA5168E58C4DAB89B33972C1EE83FF2237,
	XRManagerSettings_set_currentLoaders_mBF7D7DE30A657C0B8A145FBE84104797D668454A,
	XRManagerSettings_get_registeredLoaders_m9FDA9ABA6EE526EC99C892488869777BF13E4D1B,
	XRManagerSettings__ctor_m516BC0C114BD8652EAE41D77BF961C5521520317,
	U3CInitializeLoaderU3Ed__24__ctor_m55EFC72939B5E8ABAF9E70DD0878A80B0280C97A,
	U3CInitializeLoaderU3Ed__24_System_IDisposable_Dispose_mB850632340F899B06CCC956A3557DECF8BCC7AE8,
	U3CInitializeLoaderU3Ed__24_MoveNext_m0D3BCBEFAE33727F17874481BF551C2741132003,
	U3CInitializeLoaderU3Ed__24_U3CU3Em__Finally1_m6CCA9E68B1BF2344768A755069F9B9CD799E277C,
	U3CInitializeLoaderU3Ed__24_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m4F6952CC23A00893DC2B18A638480696CEE3370C,
	U3CInitializeLoaderU3Ed__24_System_Collections_IEnumerator_Reset_m803D94C93BE413DFBA21C0FEFABA00DA3E3414C4,
	U3CInitializeLoaderU3Ed__24_System_Collections_IEnumerator_get_Current_m92156199FCA3AAD568CA668A7A6CF7FB31B8A23C,
};
static const int32_t s_InvokerIndices[74] = 
{
	6133,
	4919,
	6133,
	4919,
	6260,
	2670,
	6133,
	4919,
	10933,
	6133,
	6039,
	6260,
	10975,
	6260,
	6260,
	10975,
	10975,
	6260,
	6260,
	6260,
	6260,
	6260,
	10975,
	6039,
	6039,
	6039,
	6039,
	0,
	4337,
	6260,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	6039,
	6260,
	10903,
	6039,
	4821,
	6039,
	4821,
	6133,
	6133,
	6039,
	6133,
	4919,
	0,
	6260,
	6133,
	1592,
	3449,
	3449,
	6260,
	3449,
	6260,
	6260,
	6260,
	6260,
	6260,
	6260,
	6133,
	4919,
	6133,
	6260,
	4892,
	6260,
	6039,
	6260,
	6133,
	6260,
	6133,
};
static const Il2CppTokenRangePair s_rgctxIndices[8] = 
{
	{ 0x0600001F, { 0, 2 } },
	{ 0x06000020, { 2, 2 } },
	{ 0x06000021, { 4, 2 } },
	{ 0x06000022, { 6, 3 } },
	{ 0x06000023, { 9, 13 } },
	{ 0x06000024, { 22, 1 } },
	{ 0x06000025, { 23, 1 } },
	{ 0x06000032, { 24, 1 } },
};
extern const uint32_t g_rgctx_T_tE83530F7E4A38B58BF27632C151E64FE9AB1BD8C;
extern const uint32_t g_rgctx_T_tE83530F7E4A38B58BF27632C151E64FE9AB1BD8C;
extern const uint32_t g_rgctx_XRLoaderHelper_GetLoadedSubsystem_TisT_tDCC1B517C2249B05E00CF18E5D227E2549F29CF6_m0F4A18CECB2B4405DADDD191E94A3139153E32D0;
extern const uint32_t g_rgctx_T_tDCC1B517C2249B05E00CF18E5D227E2549F29CF6;
extern const uint32_t g_rgctx_XRLoaderHelper_GetLoadedSubsystem_TisT_t33F97C7A0EC049E81B790F15B06A784991EF2E57_m4545BCACEE2518432742404A4ED60C8BD70ACFBB;
extern const uint32_t g_rgctx_T_t33F97C7A0EC049E81B790F15B06A784991EF2E57;
extern const uint32_t g_rgctx_XRLoaderHelper_GetLoadedSubsystem_TisT_t5C19882F056170FFBC337AB1996C22F4F3D0087A_m2D10291859C420E98DC9F67E7BC059D94EFB2995;
extern const uint32_t g_rgctx_T_t5C19882F056170FFBC337AB1996C22F4F3D0087A;
extern const uint32_t g_rgctx_T_t5C19882F056170FFBC337AB1996C22F4F3D0087A;
extern const uint32_t g_rgctx_SubsystemManager_GetSubsystemDescriptors_TisTDescriptor_t2B74798ADF57AC69447766D064ECD0EFD59B49C3_m6D2D3BAC3B2104373E3EF5A7C21C25923929902F;
extern const uint32_t g_rgctx_List_1_t8C622A547070267F95814ECC4460B2421F6CE5FA;
extern const uint32_t g_rgctx_List_1_get_Count_mA2FF9BD8129720B95F4DD92EBD1A322AB6ED8D61;
extern const uint32_t g_rgctx_List_1_GetEnumerator_m34B293E1376FAB46C6EDE95161ABD611CE4F6049;
extern const uint32_t g_rgctx_Enumerator_get_Current_mABF921F950F66BB6A8836662A851B9370AC5135A;
extern const uint32_t g_rgctx_Enumerator_t1346215E738D72F515555CF84BCEBC664757A9A9;
extern const uint32_t g_rgctx_TDescriptor_t2B74798ADF57AC69447766D064ECD0EFD59B49C3;
extern const Il2CppRGCTXConstrainedData g_rgctx_TDescriptor_t2B74798ADF57AC69447766D064ECD0EFD59B49C3_ISubsystemDescriptor_get_id_mB5D2D5D3B9E9FD3ABF5E064E1262DD4AAF09C6F2;
extern const Il2CppRGCTXConstrainedData g_rgctx_TDescriptor_t2B74798ADF57AC69447766D064ECD0EFD59B49C3_ISubsystemDescriptor_Create_m70CF3561B7EC144C6C34CDBF5CA4AFF82D576E9A;
extern const uint32_t g_rgctx_TSubsystem_t5328216FD45421FF07AFEE2EFA5CD3F90B7B23AA;
extern const uint32_t g_rgctx_Enumerator_MoveNext_m7AA605115D9FD24AC584238A4A05D09159F5B8A5;
extern const uint32_t g_rgctx_Enumerator_t1346215E738D72F515555CF84BCEBC664757A9A9;
extern const Il2CppRGCTXConstrainedData g_rgctx_Enumerator_t1346215E738D72F515555CF84BCEBC664757A9A9_IDisposable_Dispose_m3C902735BE731EE30AC1185E7AEF6ACE7A9D9CC7;
extern const uint32_t g_rgctx_XRLoaderHelper_CreateSubsystem_TisTDescriptor_t2F53B0027CD2CEE8DEEC547765AC6876B085CDD6_TisTSubsystem_t715FA6A4371BF289E7095C8CA0A1A0E8031BE68A_m5C03A52075DA69E79D3C7B6057D93920C0E99330;
extern const uint32_t g_rgctx_XRLoaderHelper_CreateSubsystem_TisTDescriptor_t44D625D0193FBFDD7CAD1706999009F067C07B90_TisTSubsystem_t967888085478054B52B58EEF14F8D4B660C57F64_mDD5FBE85951A428FCC852CA4C4FD40404830A88E;
extern const uint32_t g_rgctx_T_tE4B5A6FD716FACA7C7240732A44211D0F880A4BA;
static const Il2CppRGCTXDefinition s_rgctxValues[25] = 
{
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_T_tE83530F7E4A38B58BF27632C151E64FE9AB1BD8C },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tE83530F7E4A38B58BF27632C151E64FE9AB1BD8C },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_XRLoaderHelper_GetLoadedSubsystem_TisT_tDCC1B517C2249B05E00CF18E5D227E2549F29CF6_m0F4A18CECB2B4405DADDD191E94A3139153E32D0 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tDCC1B517C2249B05E00CF18E5D227E2549F29CF6 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_XRLoaderHelper_GetLoadedSubsystem_TisT_t33F97C7A0EC049E81B790F15B06A784991EF2E57_m4545BCACEE2518432742404A4ED60C8BD70ACFBB },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t33F97C7A0EC049E81B790F15B06A784991EF2E57 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_XRLoaderHelper_GetLoadedSubsystem_TisT_t5C19882F056170FFBC337AB1996C22F4F3D0087A_m2D10291859C420E98DC9F67E7BC059D94EFB2995 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t5C19882F056170FFBC337AB1996C22F4F3D0087A },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_T_t5C19882F056170FFBC337AB1996C22F4F3D0087A },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_SubsystemManager_GetSubsystemDescriptors_TisTDescriptor_t2B74798ADF57AC69447766D064ECD0EFD59B49C3_m6D2D3BAC3B2104373E3EF5A7C21C25923929902F },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_List_1_t8C622A547070267F95814ECC4460B2421F6CE5FA },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1_get_Count_mA2FF9BD8129720B95F4DD92EBD1A322AB6ED8D61 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1_GetEnumerator_m34B293E1376FAB46C6EDE95161ABD611CE4F6049 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Enumerator_get_Current_mABF921F950F66BB6A8836662A851B9370AC5135A },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Enumerator_t1346215E738D72F515555CF84BCEBC664757A9A9 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TDescriptor_t2B74798ADF57AC69447766D064ECD0EFD59B49C3 },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_TDescriptor_t2B74798ADF57AC69447766D064ECD0EFD59B49C3_ISubsystemDescriptor_get_id_mB5D2D5D3B9E9FD3ABF5E064E1262DD4AAF09C6F2 },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_TDescriptor_t2B74798ADF57AC69447766D064ECD0EFD59B49C3_ISubsystemDescriptor_Create_m70CF3561B7EC144C6C34CDBF5CA4AFF82D576E9A },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_TSubsystem_t5328216FD45421FF07AFEE2EFA5CD3F90B7B23AA },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Enumerator_MoveNext_m7AA605115D9FD24AC584238A4A05D09159F5B8A5 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Enumerator_t1346215E738D72F515555CF84BCEBC664757A9A9 },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_Enumerator_t1346215E738D72F515555CF84BCEBC664757A9A9_IDisposable_Dispose_m3C902735BE731EE30AC1185E7AEF6ACE7A9D9CC7 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_XRLoaderHelper_CreateSubsystem_TisTDescriptor_t2F53B0027CD2CEE8DEEC547765AC6876B085CDD6_TisTSubsystem_t715FA6A4371BF289E7095C8CA0A1A0E8031BE68A_m5C03A52075DA69E79D3C7B6057D93920C0E99330 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_XRLoaderHelper_CreateSubsystem_TisTDescriptor_t44D625D0193FBFDD7CAD1706999009F067C07B90_TisTSubsystem_t967888085478054B52B58EEF14F8D4B660C57F64_mDD5FBE85951A428FCC852CA4C4FD40404830A88E },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tE4B5A6FD716FACA7C7240732A44211D0F880A4BA },
};
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_Unity_XR_Management_CodeGenModule;
const Il2CppCodeGenModule g_Unity_XR_Management_CodeGenModule = 
{
	"Unity.XR.Management.dll",
	74,
	s_methodPointers,
	0,
	NULL,
	s_InvokerIndices,
	0,
	NULL,
	8,
	s_rgctxIndices,
	25,
	s_rgctxValues,
	NULL,
	NULL, // module initializer,
	NULL,
	NULL,
	NULL,
};
