using UnityEngine;
using UnityEngine.SceneManagement;
using System.Collections;
using System.Collections.Generic;

/// <summary>
/// VR场景管理器
/// 
/// 处理VR应用中的场景切换、加载和用户数据传递
/// 支持异步加载、过渡动画和错误处理
/// </summary>
public class VRSceneManager : MonoBehaviour
{
    [Header("场景配置")]
    [SerializeField] private List<SceneInfo> availableScenes = new List<SceneInfo>();
    [SerializeField] private string defaultNextScene = "Animation";
    [SerializeField] private bool useAsyncLoading = true;
    [SerializeField] private bool showLoadingProgress = true;
    
    [Header("过渡设置")]
    [SerializeField] private float transitionDelay = 0.5f;
    [SerializeField] private bool fadeTransition = true;
    [SerializeField] private Color fadeColor = Color.black;
    [SerializeField] private float fadeDuration = 1f;
    
    [Header("用户数据")]
    [SerializeField] private bool preserveUserData = true;
    [SerializeField] private string userDataKey = "UserData";
    
    [Header("音效")]
    [SerializeField] private AudioSource audioSource;
    [SerializeField] private AudioClip transitionSound;
    [SerializeField] private AudioClip errorSound;
    
    // 私有变量
    private bool isTransitioning = false;
    private AsyncOperation currentLoadOperation;
    private CanvasGroup fadeCanvasGroup;
    private Dictionary<string, object> sceneData = new Dictionary<string, object>();
    
    // 事件
    public System.Action<string> OnSceneLoadStarted;
    public System.Action<string, float> OnSceneLoadProgress;
    public System.Action<string> OnSceneLoadCompleted;
    public System.Action<string> OnSceneLoadFailed;
    
    // 单例模式
    private static VRSceneManager instance;
    public static VRSceneManager Instance
    {
        get
        {
            if (instance == null)
            {
                instance = FindObjectOfType<VRSceneManager>();
                if (instance == null)
                {
                    GameObject go = new GameObject("VRSceneManager");
                    instance = go.AddComponent<VRSceneManager>();
                    DontDestroyOnLoad(go);
                }
            }
            return instance;
        }
    }
    
    [System.Serializable]
    public class SceneInfo
    {
        public string sceneName;
        public string displayName;
        public string description;
        public bool isVRScene = true;
        public bool requiresUserData = false;
    }
    
    void Awake()
    {
        // 确保只有一个实例
        if (instance == null)
        {
            instance = this;
            DontDestroyOnLoad(gameObject);
            InitializeSceneManager();
        }
        else if (instance != this)
        {
            Destroy(gameObject);
        }
    }
    
    /// <summary>
    /// 初始化场景管理器
    /// </summary>
    private void InitializeSceneManager()
    {
        // 设置音频源
        if (audioSource == null)
        {
            audioSource = GetComponent<AudioSource>();
            if (audioSource == null)
            {
                audioSource = gameObject.AddComponent<AudioSource>();
            }
        }
        
        // 创建淡入淡出Canvas
        if (fadeTransition)
        {
            CreateFadeCanvas();
        }
        
        // 初始化可用场景列表
        InitializeSceneList();
        
        Debug.Log("[VRSceneManager] 场景管理器初始化完成");
    }
    
    /// <summary>
    /// 初始化场景列表
    /// </summary>
    private void InitializeSceneList()
    {
        if (availableScenes.Count == 0)
        {
            // 添加默认场景
            availableScenes.Add(new SceneInfo
            {
                sceneName = "StartMenu",
                displayName = "开始菜单",
                description = "VR应用开始界面",
                isVRScene = true,
                requiresUserData = false
            });
            
            availableScenes.Add(new SceneInfo
            {
                sceneName = "Animation",
                displayName = "装配动画",
                description = "主要的VR装配动画场景",
                isVRScene = true,
                requiresUserData = true
            });
        }
    }
    
    /// <summary>
    /// 创建淡入淡出Canvas
    /// </summary>
    private void CreateFadeCanvas()
    {
        GameObject fadeGO = new GameObject("Fade Canvas");
        fadeGO.transform.SetParent(transform);
        
        Canvas canvas = fadeGO.AddComponent<Canvas>();
        canvas.renderMode = RenderMode.ScreenSpaceOverlay;
        canvas.sortingOrder = 1000;
        
        fadeCanvasGroup = fadeGO.AddComponent<CanvasGroup>();
        fadeCanvasGroup.alpha = 0f;
        fadeCanvasGroup.blocksRaycasts = false;
        
        GameObject imageGO = new GameObject("Fade Image");
        imageGO.transform.SetParent(fadeGO.transform, false);
        
        UnityEngine.UI.Image fadeImage = imageGO.AddComponent<UnityEngine.UI.Image>();
        fadeImage.color = fadeColor;
        
        RectTransform rectTransform = imageGO.GetComponent<RectTransform>();
        rectTransform.anchorMin = Vector2.zero;
        rectTransform.anchorMax = Vector2.one;
        rectTransform.offsetMin = Vector2.zero;
        rectTransform.offsetMax = Vector2.zero;
        
        DontDestroyOnLoad(fadeGO);
    }
    
    /// <summary>
    /// 加载场景
    /// </summary>
    public void LoadScene(string sceneName)
    {
        if (isTransitioning)
        {
            Debug.LogWarning("[VRSceneManager] 场景正在切换中，忽略新的加载请求");
            return;
        }
        
        if (string.IsNullOrEmpty(sceneName))
        {
            sceneName = defaultNextScene;
        }
        
        StartCoroutine(LoadSceneCoroutine(sceneName));
    }
    
    /// <summary>
    /// 加载场景协程
    /// </summary>
    private IEnumerator LoadSceneCoroutine(string sceneName)
    {
        isTransitioning = true;
        
        Debug.Log($"[VRSceneManager] 开始加载场景: {sceneName}");
        
        // 触发开始事件
        OnSceneLoadStarted?.Invoke(sceneName);
        
        // 播放过渡音效
        PlaySound(transitionSound);
        
        // 淡出效果
        if (fadeTransition && fadeCanvasGroup != null)
        {
            yield return StartCoroutine(FadeOut());
        }
        
        // 等待过渡延迟
        yield return new WaitForSeconds(transitionDelay);
        
        // 验证场景是否存在
        if (!IsSceneValid(sceneName))
        {
            Debug.LogError($"[VRSceneManager] 场景不存在: {sceneName}");
            OnSceneLoadFailed?.Invoke(sceneName);
            PlaySound(errorSound);
            
            // 淡入回到当前场景
            if (fadeTransition && fadeCanvasGroup != null)
            {
                yield return StartCoroutine(FadeIn());
            }
            
            isTransitioning = false;
            yield break;
        }
        
        // 异步加载场景
        if (useAsyncLoading)
        {
            currentLoadOperation = SceneManager.LoadSceneAsync(sceneName);
            currentLoadOperation.allowSceneActivation = false;
            
            // 监控加载进度
            while (currentLoadOperation.progress < 0.9f)
            {
                float progress = currentLoadOperation.progress / 0.9f;
                OnSceneLoadProgress?.Invoke(sceneName, progress);
                yield return null;
            }
            
            // 完成加载
            currentLoadOperation.allowSceneActivation = true;
            yield return currentLoadOperation;
        }
        else
        {
            // 同步加载
            SceneManager.LoadScene(sceneName);
        }
        
        // 触发完成事件
        OnSceneLoadCompleted?.Invoke(sceneName);
        
        // 淡入新场景
        if (fadeTransition && fadeCanvasGroup != null)
        {
            yield return StartCoroutine(FadeIn());
        }
        
        isTransitioning = false;
        Debug.Log($"[VRSceneManager] 场景加载完成: {sceneName}");
    }
    
    /// <summary>
    /// 验证场景是否有效
    /// </summary>
    private bool IsSceneValid(string sceneName)
    {
        for (int i = 0; i < SceneManager.sceneCountInBuildSettings; i++)
        {
            string scenePath = SceneUtility.GetScenePathByBuildIndex(i);
            string sceneNameFromPath = System.IO.Path.GetFileNameWithoutExtension(scenePath);
            if (sceneNameFromPath == sceneName)
            {
                return true;
            }
        }
        return false;
    }
    
    /// <summary>
    /// 淡出效果
    /// </summary>
    private IEnumerator FadeOut()
    {
        if (fadeCanvasGroup == null) yield break;
        
        fadeCanvasGroup.blocksRaycasts = true;
        float elapsedTime = 0f;
        
        while (elapsedTime < fadeDuration)
        {
            elapsedTime += Time.deltaTime;
            fadeCanvasGroup.alpha = Mathf.Clamp01(elapsedTime / fadeDuration);
            yield return null;
        }
        
        fadeCanvasGroup.alpha = 1f;
    }
    
    /// <summary>
    /// 淡入效果
    /// </summary>
    private IEnumerator FadeIn()
    {
        if (fadeCanvasGroup == null) yield break;
        
        float elapsedTime = 0f;
        
        while (elapsedTime < fadeDuration)
        {
            elapsedTime += Time.deltaTime;
            fadeCanvasGroup.alpha = Mathf.Clamp01(1f - (elapsedTime / fadeDuration));
            yield return null;
        }
        
        fadeCanvasGroup.alpha = 0f;
        fadeCanvasGroup.blocksRaycasts = false;
    }
    
    /// <summary>
    /// 播放音效
    /// </summary>
    private void PlaySound(AudioClip clip)
    {
        if (audioSource != null && clip != null)
        {
            audioSource.PlayOneShot(clip);
        }
    }
    
    /// <summary>
    /// 设置场景数据
    /// </summary>
    public void SetSceneData(string key, object value)
    {
        sceneData[key] = value;
        
        if (preserveUserData)
        {
            // 保存到PlayerPrefs（仅支持基本类型）
            if (value is string stringValue)
            {
                PlayerPrefs.SetString(key, stringValue);
            }
            else if (value is int intValue)
            {
                PlayerPrefs.SetInt(key, intValue);
            }
            else if (value is float floatValue)
            {
                PlayerPrefs.SetFloat(key, floatValue);
            }
            
            PlayerPrefs.Save();
        }
    }
    
    /// <summary>
    /// 获取场景数据
    /// </summary>
    public T GetSceneData<T>(string key, T defaultValue = default(T))
    {
        if (sceneData.ContainsKey(key))
        {
            return (T)sceneData[key];
        }
        
        // 尝试从PlayerPrefs获取
        if (preserveUserData)
        {
            if (typeof(T) == typeof(string))
            {
                return (T)(object)PlayerPrefs.GetString(key, defaultValue?.ToString() ?? "");
            }
            else if (typeof(T) == typeof(int))
            {
                return (T)(object)PlayerPrefs.GetInt(key, (int)(object)defaultValue);
            }
            else if (typeof(T) == typeof(float))
            {
                return (T)(object)PlayerPrefs.GetFloat(key, (float)(object)defaultValue);
            }
        }
        
        return defaultValue;
    }
    
    /// <summary>
    /// 获取场景信息
    /// </summary>
    public SceneInfo GetSceneInfo(string sceneName)
    {
        return availableScenes.Find(scene => scene.sceneName == sceneName);
    }
    
    /// <summary>
    /// 获取所有可用场景
    /// </summary>
    public List<SceneInfo> GetAvailableScenes()
    {
        return new List<SceneInfo>(availableScenes);
    }
    
    /// <summary>
    /// 重新加载当前场景
    /// </summary>
    public void ReloadCurrentScene()
    {
        string currentSceneName = SceneManager.GetActiveScene().name;
        LoadScene(currentSceneName);
    }
    
    /// <summary>
    /// 退出应用
    /// </summary>
    public void QuitApplication()
    {
        Debug.Log("[VRSceneManager] 退出应用");
        
#if UNITY_EDITOR
        UnityEditor.EditorApplication.isPlaying = false;
#else
        Application.Quit();
#endif
    }
    
    /// <summary>
    /// 获取当前是否正在切换场景
    /// </summary>
    public bool IsTransitioning()
    {
        return isTransitioning;
    }
}
