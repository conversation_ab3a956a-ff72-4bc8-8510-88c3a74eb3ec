﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





// 0x00000001 System.Collections.Generic.HashSet`1<System.UInt32> UnityEngine.TextCore.Text.UnicodeLineBreakingRules::get_leadingCharactersLookup()
extern void UnicodeLineBreakingRules_get_leadingCharactersLookup_m1DAC015D7E37112EAE0437E6472AEA0719DFF3DC (void);
// 0x00000002 System.Collections.Generic.HashSet`1<System.UInt32> UnityEngine.TextCore.Text.UnicodeLineBreakingRules::get_followingCharactersLookup()
extern void UnicodeLineBreakingRules_get_followingCharactersLookup_m5510A21873DC5DA66F4A2DFA4C26A5EFAD494D8B (void);
// 0x00000003 System.Void UnityEngine.TextCore.Text.UnicodeLineBreakingRules::LoadLineBreakingRules()
extern void UnicodeLineBreakingRules_LoadLineBreakingRules_mD9380F38BF762469AE42BA1255526A610AC143AD (void);
// 0x00000004 System.Collections.Generic.HashSet`1<System.UInt32> UnityEngine.TextCore.Text.UnicodeLineBreakingRules::GetCharacters(UnityEngine.TextAsset)
extern void UnicodeLineBreakingRules_GetCharacters_m93663235F54D32D801E67994714B79759D2F4137 (void);
// 0x00000005 System.Void UnityEngine.TextCore.Text.UnicodeLineBreakingRules::.ctor()
extern void UnicodeLineBreakingRules__ctor_mD94779B4996B56EA84C847EC4DD287AB1A8ADE85 (void);
// 0x00000006 System.Void UnityEngine.TextCore.Text.UnicodeLineBreakingRules::.cctor()
extern void UnicodeLineBreakingRules__cctor_m401C164CCBE1ED299AACF899F8535BC6E50CFC03 (void);
// 0x00000007 System.Void UnityEngine.TextCore.Text.TextInfo::.ctor()
extern void TextInfo__ctor_m241E24715CC5F6293DC90A4D25884548BAD0D602 (void);
// 0x00000008 System.Void UnityEngine.TextCore.Text.TextInfo::Clear()
extern void TextInfo_Clear_m60412774208F9D920707448E71E89C99233D9128 (void);
// 0x00000009 System.Void UnityEngine.TextCore.Text.TextInfo::ClearMeshInfo(System.Boolean)
extern void TextInfo_ClearMeshInfo_mCA598F01C7F302CFCD0F508E2DBF072E66CA74F3 (void);
// 0x0000000A System.Void UnityEngine.TextCore.Text.TextInfo::ClearLineInfo()
extern void TextInfo_ClearLineInfo_m986C886D34A324C8C4D30F9D8EF24AC242A10AD7 (void);
// 0x0000000B System.Void UnityEngine.TextCore.Text.TextInfo::Resize(T[]&,System.Int32)
// 0x0000000C System.Void UnityEngine.TextCore.Text.TextInfo::Resize(T[]&,System.Int32,System.Boolean)
// 0x0000000D System.Void UnityEngine.TextCore.Text.TextInfo::.cctor()
extern void TextInfo__cctor_m0B33CCC02D4B32FB18864B6B7657695E9AEB3F0D (void);
// 0x0000000E System.Int32 UnityEngine.TextCore.Text.TextStyle::get_hashCode()
extern void TextStyle_get_hashCode_mA1F4D3630B6AE71C2A31F94B7054C28BDD96084F (void);
// 0x0000000F System.Int32[] UnityEngine.TextCore.Text.TextStyle::get_styleOpeningTagArray()
extern void TextStyle_get_styleOpeningTagArray_m32B909EFDDBCFFECC7D1F4D551DCB520A72240EA (void);
// 0x00000010 System.Int32[] UnityEngine.TextCore.Text.TextStyle::get_styleClosingTagArray()
extern void TextStyle_get_styleClosingTagArray_mB5F9A0BD01EF3E6ABFF44311ADF2A1511D838033 (void);
// 0x00000011 System.Void UnityEngine.TextCore.Text.TextStyle::RefreshStyle()
extern void TextStyle_RefreshStyle_m2D0771408F06C24EF303749ED8E656C800575BDB (void);
// 0x00000012 System.Void UnityEngine.TextCore.Text.Character::.ctor()
extern void Character__ctor_m5DCCE862D40487C733C29A233DB8513A9A6A02F6 (void);
// 0x00000013 System.Void UnityEngine.TextCore.Text.Character::.ctor(System.UInt32,UnityEngine.TextCore.Text.FontAsset,UnityEngine.TextCore.Glyph)
extern void Character__ctor_mEEAC42D4227E0053C8008C12B222CC208D781795 (void);
// 0x00000014 System.Void UnityEngine.TextCore.Text.Character::.ctor(System.UInt32,System.UInt32)
extern void Character__ctor_m21FBFAF1F6324565246096EFFB81C3F9E15D43CC (void);
// 0x00000015 System.Void UnityEngine.TextCore.Text.MaterialReference::.ctor(System.Int32,UnityEngine.TextCore.Text.FontAsset,UnityEngine.TextCore.Text.SpriteAsset,UnityEngine.Material,System.Single)
extern void MaterialReference__ctor_m044AAA2C1079EB25A5534A6E0FA2314F033DB15A (void);
// 0x00000016 System.Int32 UnityEngine.TextCore.Text.MaterialReference::AddMaterialReference(UnityEngine.Material,UnityEngine.TextCore.Text.FontAsset,UnityEngine.TextCore.Text.MaterialReference[]&,System.Collections.Generic.Dictionary`2<System.Int32,System.Int32>)
extern void MaterialReference_AddMaterialReference_m13CC47A7CA6C8781EA68A355B36FCD5AFF467A40 (void);
// 0x00000017 System.Int32 UnityEngine.TextCore.Text.MaterialReference::AddMaterialReference(UnityEngine.Material,UnityEngine.TextCore.Text.SpriteAsset,UnityEngine.TextCore.Text.MaterialReference[]&,System.Collections.Generic.Dictionary`2<System.Int32,System.Int32>)
extern void MaterialReference_AddMaterialReference_mEE57C2284E5BE17BDD80C69165FAECB3B4CC1BCD (void);
// 0x00000018 UnityEngine.Font UnityEngine.TextCore.Text.FontAsset::get_sourceFontFile()
extern void FontAsset_get_sourceFontFile_m6B0E805BD1B7712F0B5135D157E96F3F40314830 (void);
// 0x00000019 System.Void UnityEngine.TextCore.Text.FontAsset::set_sourceFontFile(UnityEngine.Font)
extern void FontAsset_set_sourceFontFile_m2E6D2AED5E5D2585A09E9BF830387DEB10A2F4E8 (void);
// 0x0000001A UnityEngine.TextCore.Text.AtlasPopulationMode UnityEngine.TextCore.Text.FontAsset::get_atlasPopulationMode()
extern void FontAsset_get_atlasPopulationMode_m5364C5A9E84969D8E4FF8436BD18A3F10BF90366 (void);
// 0x0000001B System.Void UnityEngine.TextCore.Text.FontAsset::set_atlasPopulationMode(UnityEngine.TextCore.Text.AtlasPopulationMode)
extern void FontAsset_set_atlasPopulationMode_m1A9DD5C702ED0924B9C208F4CE5ADEACF9188268 (void);
// 0x0000001C UnityEngine.TextCore.FaceInfo UnityEngine.TextCore.Text.FontAsset::get_faceInfo()
extern void FontAsset_get_faceInfo_mF020EC579E3C18A6279D55D86AF1C585031B49A9 (void);
// 0x0000001D System.Void UnityEngine.TextCore.Text.FontAsset::set_faceInfo(UnityEngine.TextCore.FaceInfo)
extern void FontAsset_set_faceInfo_mCCA87B67C4CA2C0A1F6D85FB1FAA09667996EDCD (void);
// 0x0000001E System.Int32 UnityEngine.TextCore.Text.FontAsset::get_familyNameHashCode()
extern void FontAsset_get_familyNameHashCode_mF2DB211A5712A291B2D28FCDB7F7C29057770330 (void);
// 0x0000001F System.Void UnityEngine.TextCore.Text.FontAsset::set_familyNameHashCode(System.Int32)
extern void FontAsset_set_familyNameHashCode_mE1495199BCE7B771CC920E2DBB86A8AF1518CB55 (void);
// 0x00000020 System.Int32 UnityEngine.TextCore.Text.FontAsset::get_styleNameHashCode()
extern void FontAsset_get_styleNameHashCode_m3CD3D77F64DAEB31D8F69E4D7CC1AD0AC784ABF5 (void);
// 0x00000021 System.Void UnityEngine.TextCore.Text.FontAsset::set_styleNameHashCode(System.Int32)
extern void FontAsset_set_styleNameHashCode_mE1BE5B75DE1E9EA0F76569609E6C4FFDC57558BA (void);
// 0x00000022 UnityEngine.TextCore.Text.FontWeightPair[] UnityEngine.TextCore.Text.FontAsset::get_fontWeightTable()
extern void FontAsset_get_fontWeightTable_m8DADE0BCE53D53EEECE27025F7E94FDD4BF00099 (void);
// 0x00000023 System.Void UnityEngine.TextCore.Text.FontAsset::set_fontWeightTable(UnityEngine.TextCore.Text.FontWeightPair[])
extern void FontAsset_set_fontWeightTable_m39D8B63BD3FCC773AAB5634C6D9314C713161814 (void);
// 0x00000024 System.Collections.Generic.List`1<UnityEngine.TextCore.Glyph> UnityEngine.TextCore.Text.FontAsset::get_glyphTable()
extern void FontAsset_get_glyphTable_m212E940F74AEE62ACBB3374486296CA518D934B5 (void);
// 0x00000025 System.Void UnityEngine.TextCore.Text.FontAsset::set_glyphTable(System.Collections.Generic.List`1<UnityEngine.TextCore.Glyph>)
extern void FontAsset_set_glyphTable_m2753BC6CEE011983C2B4B181867C3EB00CDE87D4 (void);
// 0x00000026 System.Collections.Generic.Dictionary`2<System.UInt32,UnityEngine.TextCore.Glyph> UnityEngine.TextCore.Text.FontAsset::get_glyphLookupTable()
extern void FontAsset_get_glyphLookupTable_mD04A90D8262F1963EDC472272B67BBFAF73DEEA5 (void);
// 0x00000027 System.Collections.Generic.List`1<UnityEngine.TextCore.Text.Character> UnityEngine.TextCore.Text.FontAsset::get_characterTable()
extern void FontAsset_get_characterTable_mC77FAE1355269834F7C8A2D46AFB4BFE7B7AD72D (void);
// 0x00000028 System.Void UnityEngine.TextCore.Text.FontAsset::set_characterTable(System.Collections.Generic.List`1<UnityEngine.TextCore.Text.Character>)
extern void FontAsset_set_characterTable_m44703292F669F2F6D4920EB9427077E24FB1512C (void);
// 0x00000029 System.Collections.Generic.Dictionary`2<System.UInt32,UnityEngine.TextCore.Text.Character> UnityEngine.TextCore.Text.FontAsset::get_characterLookupTable()
extern void FontAsset_get_characterLookupTable_m7E76D6C706C5CEB04A9541C68AE6D9E5C75F0FFC (void);
// 0x0000002A UnityEngine.Texture2D UnityEngine.TextCore.Text.FontAsset::get_atlasTexture()
extern void FontAsset_get_atlasTexture_mC49216F40093C7AC4FA5DA68F9F5C9FCC83B8F27 (void);
// 0x0000002B UnityEngine.Texture2D[] UnityEngine.TextCore.Text.FontAsset::get_atlasTextures()
extern void FontAsset_get_atlasTextures_mADD7A506F0444A1EE4F1D52536B0C5DA9BE35075 (void);
// 0x0000002C System.Void UnityEngine.TextCore.Text.FontAsset::set_atlasTextures(UnityEngine.Texture2D[])
extern void FontAsset_set_atlasTextures_m5315BA4903B77742EFA1E54CEA2AF12726B10A99 (void);
// 0x0000002D System.Int32 UnityEngine.TextCore.Text.FontAsset::get_atlasTextureCount()
extern void FontAsset_get_atlasTextureCount_mC20300C53E52D7A8351DE296BAD565268568119F (void);
// 0x0000002E System.Boolean UnityEngine.TextCore.Text.FontAsset::get_isMultiAtlasTexturesEnabled()
extern void FontAsset_get_isMultiAtlasTexturesEnabled_mF222228A76102BB0EA593A60439D22F912761F1E (void);
// 0x0000002F System.Void UnityEngine.TextCore.Text.FontAsset::set_isMultiAtlasTexturesEnabled(System.Boolean)
extern void FontAsset_set_isMultiAtlasTexturesEnabled_mA470AF6D312989E752C68DC0FD5700235877566B (void);
// 0x00000030 System.Boolean UnityEngine.TextCore.Text.FontAsset::get_clearDynamicDataOnBuild()
extern void FontAsset_get_clearDynamicDataOnBuild_mC1F714E56F087B29E0A3B43D820EDCEB78E9EE75 (void);
// 0x00000031 System.Void UnityEngine.TextCore.Text.FontAsset::set_clearDynamicDataOnBuild(System.Boolean)
extern void FontAsset_set_clearDynamicDataOnBuild_mA1C6F298742DF78EC0F81157F0E04246F8B82F7E (void);
// 0x00000032 System.Int32 UnityEngine.TextCore.Text.FontAsset::get_atlasWidth()
extern void FontAsset_get_atlasWidth_mE711550FDD4B5F988B77AB5D332A80A34B5CF364 (void);
// 0x00000033 System.Void UnityEngine.TextCore.Text.FontAsset::set_atlasWidth(System.Int32)
extern void FontAsset_set_atlasWidth_mFFB9D37EB1C648384ED1426B42E26A4104D329B1 (void);
// 0x00000034 System.Int32 UnityEngine.TextCore.Text.FontAsset::get_atlasHeight()
extern void FontAsset_get_atlasHeight_m306FBF7D35C39123A4770E147FAF95B1B8DE8086 (void);
// 0x00000035 System.Void UnityEngine.TextCore.Text.FontAsset::set_atlasHeight(System.Int32)
extern void FontAsset_set_atlasHeight_m7116DFD32F38971CE39D7F4BF84CB5217DCAA2B5 (void);
// 0x00000036 System.Int32 UnityEngine.TextCore.Text.FontAsset::get_atlasPadding()
extern void FontAsset_get_atlasPadding_m251A35FB5F499EE66CC2E2150CBEDB2C8C5D5581 (void);
// 0x00000037 System.Void UnityEngine.TextCore.Text.FontAsset::set_atlasPadding(System.Int32)
extern void FontAsset_set_atlasPadding_mB34AA836A3D02722ABED71B3583D767560CA956D (void);
// 0x00000038 UnityEngine.TextCore.LowLevel.GlyphRenderMode UnityEngine.TextCore.Text.FontAsset::get_atlasRenderMode()
extern void FontAsset_get_atlasRenderMode_m036D4BA220E5D4B0C407CA6BC1B09D8914B5058A (void);
// 0x00000039 System.Void UnityEngine.TextCore.Text.FontAsset::set_atlasRenderMode(UnityEngine.TextCore.LowLevel.GlyphRenderMode)
extern void FontAsset_set_atlasRenderMode_m993764193CE75D57DC4CC755336596681A7866D2 (void);
// 0x0000003A System.Collections.Generic.List`1<UnityEngine.TextCore.GlyphRect> UnityEngine.TextCore.Text.FontAsset::get_usedGlyphRects()
extern void FontAsset_get_usedGlyphRects_mE039AEF3AE45A15A86B2C0F774E6ED58AFA2F341 (void);
// 0x0000003B System.Void UnityEngine.TextCore.Text.FontAsset::set_usedGlyphRects(System.Collections.Generic.List`1<UnityEngine.TextCore.GlyphRect>)
extern void FontAsset_set_usedGlyphRects_mBF80C1063C0A274AD95F55C43DA734E126F6643F (void);
// 0x0000003C System.Collections.Generic.List`1<UnityEngine.TextCore.GlyphRect> UnityEngine.TextCore.Text.FontAsset::get_freeGlyphRects()
extern void FontAsset_get_freeGlyphRects_mCDAEF0519586C5248BBEDEAA85086CC117903E88 (void);
// 0x0000003D System.Void UnityEngine.TextCore.Text.FontAsset::set_freeGlyphRects(System.Collections.Generic.List`1<UnityEngine.TextCore.GlyphRect>)
extern void FontAsset_set_freeGlyphRects_mED3C0E01ABFC63CE700C701476BA2B66D112AA9B (void);
// 0x0000003E UnityEngine.TextCore.Text.FontFeatureTable UnityEngine.TextCore.Text.FontAsset::get_fontFeatureTable()
extern void FontAsset_get_fontFeatureTable_m7C4EB9A655B237CE02FAF7B8B16C2F2863FE5070 (void);
// 0x0000003F System.Void UnityEngine.TextCore.Text.FontAsset::set_fontFeatureTable(UnityEngine.TextCore.Text.FontFeatureTable)
extern void FontAsset_set_fontFeatureTable_m3FD11B99122416777808E1CE5414D7BA40920C3B (void);
// 0x00000040 System.Collections.Generic.List`1<UnityEngine.TextCore.Text.FontAsset> UnityEngine.TextCore.Text.FontAsset::get_fallbackFontAssetTable()
extern void FontAsset_get_fallbackFontAssetTable_m43303BFBE8A8C55D8CE8A67C47EFAFF5A712CB69 (void);
// 0x00000041 System.Void UnityEngine.TextCore.Text.FontAsset::set_fallbackFontAssetTable(System.Collections.Generic.List`1<UnityEngine.TextCore.Text.FontAsset>)
extern void FontAsset_set_fallbackFontAssetTable_mE22C3D2323111FD6A5DF9847FB812BD41E18832E (void);
// 0x00000042 UnityEngine.TextCore.Text.FontAssetCreationEditorSettings UnityEngine.TextCore.Text.FontAsset::get_fontAssetCreationEditorSettings()
extern void FontAsset_get_fontAssetCreationEditorSettings_m024033F91B976A8EAA5CBE67D3DB2A756B91CF01 (void);
// 0x00000043 System.Void UnityEngine.TextCore.Text.FontAsset::set_fontAssetCreationEditorSettings(UnityEngine.TextCore.Text.FontAssetCreationEditorSettings)
extern void FontAsset_set_fontAssetCreationEditorSettings_mF7EE6A46807D78A7E99872171D2AD774DE20C7EB (void);
// 0x00000044 System.Single UnityEngine.TextCore.Text.FontAsset::get_regularStyleWeight()
extern void FontAsset_get_regularStyleWeight_m6C4B4D4CAD36800E6E686A05A5DB8D4475F2707F (void);
// 0x00000045 System.Void UnityEngine.TextCore.Text.FontAsset::set_regularStyleWeight(System.Single)
extern void FontAsset_set_regularStyleWeight_m2D1E5440A5E1794A003FF087A87393DA9A114385 (void);
// 0x00000046 System.Single UnityEngine.TextCore.Text.FontAsset::get_regularStyleSpacing()
extern void FontAsset_get_regularStyleSpacing_mB7EEEA236312F5AC31FD3B787808279206F521B1 (void);
// 0x00000047 System.Void UnityEngine.TextCore.Text.FontAsset::set_regularStyleSpacing(System.Single)
extern void FontAsset_set_regularStyleSpacing_m7CCE54FB9163D65A6B40269B2BDD30199023E797 (void);
// 0x00000048 System.Single UnityEngine.TextCore.Text.FontAsset::get_boldStyleWeight()
extern void FontAsset_get_boldStyleWeight_m804ACC85DD80DC72DB4BCC83C3FB866411F8EFCA (void);
// 0x00000049 System.Void UnityEngine.TextCore.Text.FontAsset::set_boldStyleWeight(System.Single)
extern void FontAsset_set_boldStyleWeight_m204B04CF9E98AD8669025BFDC0EF3CE9AB5CBBA2 (void);
// 0x0000004A System.Single UnityEngine.TextCore.Text.FontAsset::get_boldStyleSpacing()
extern void FontAsset_get_boldStyleSpacing_mB8CF4F4880B110E41D566648FF1D995010CF1FF0 (void);
// 0x0000004B System.Void UnityEngine.TextCore.Text.FontAsset::set_boldStyleSpacing(System.Single)
extern void FontAsset_set_boldStyleSpacing_m62DAA35837E8563DD76E3D162B6DB59BE3804914 (void);
// 0x0000004C System.Byte UnityEngine.TextCore.Text.FontAsset::get_italicStyleSlant()
extern void FontAsset_get_italicStyleSlant_m69E70060C6E7940B4ACE61F2B7CB8965F86DA96B (void);
// 0x0000004D System.Void UnityEngine.TextCore.Text.FontAsset::set_italicStyleSlant(System.Byte)
extern void FontAsset_set_italicStyleSlant_m223875ED81B0397CA36E94A6F346AEE68510C0D2 (void);
// 0x0000004E System.Byte UnityEngine.TextCore.Text.FontAsset::get_tabMultiple()
extern void FontAsset_get_tabMultiple_m9C0422A00BFCF82091F14F4E303E2717247350AE (void);
// 0x0000004F System.Void UnityEngine.TextCore.Text.FontAsset::set_tabMultiple(System.Byte)
extern void FontAsset_set_tabMultiple_mC927B74D27FBB94245E09FE39D9F6749AF07017B (void);
// 0x00000050 UnityEngine.TextCore.Text.FontAsset UnityEngine.TextCore.Text.FontAsset::CreateFontAsset(System.String,System.String,System.Int32)
extern void FontAsset_CreateFontAsset_mDBAB2C51721B94702B8706450DAF74FD2E757712 (void);
// 0x00000051 UnityEngine.TextCore.Text.FontAsset UnityEngine.TextCore.Text.FontAsset::CreateFontAsset(System.String,System.Int32,System.Int32,System.Int32,UnityEngine.TextCore.LowLevel.GlyphRenderMode,System.Int32,System.Int32,UnityEngine.TextCore.Text.AtlasPopulationMode,System.Boolean)
extern void FontAsset_CreateFontAsset_m5C2993AF8A6DB979E34173276952C0DD70524777 (void);
// 0x00000052 UnityEngine.TextCore.Text.FontAsset UnityEngine.TextCore.Text.FontAsset::CreateFontAsset(UnityEngine.Font)
extern void FontAsset_CreateFontAsset_m627EE2CFBDCEADB906A5CD26FB474D3C1A37A166 (void);
// 0x00000053 UnityEngine.TextCore.Text.FontAsset UnityEngine.TextCore.Text.FontAsset::CreateFontAsset(UnityEngine.Font,System.Int32,System.Int32,UnityEngine.TextCore.LowLevel.GlyphRenderMode,System.Int32,System.Int32,UnityEngine.TextCore.Text.AtlasPopulationMode,System.Boolean)
extern void FontAsset_CreateFontAsset_mEFD731F35B521BB833F532C0EE7637F97647E650 (void);
// 0x00000054 UnityEngine.TextCore.Text.FontAsset UnityEngine.TextCore.Text.FontAsset::CreateFontAsset(UnityEngine.Font,System.Int32,System.Int32,System.Int32,UnityEngine.TextCore.LowLevel.GlyphRenderMode,System.Int32,System.Int32,UnityEngine.TextCore.Text.AtlasPopulationMode,System.Boolean)
extern void FontAsset_CreateFontAsset_mF5E82AB887021B02F7DA71E36328A9D1C943F1B5 (void);
// 0x00000055 UnityEngine.TextCore.Text.FontAsset UnityEngine.TextCore.Text.FontAsset::CreateFontAssetInstance(UnityEngine.Font,System.Int32,UnityEngine.TextCore.LowLevel.GlyphRenderMode,System.Int32,System.Int32,UnityEngine.TextCore.Text.AtlasPopulationMode,System.Boolean)
extern void FontAsset_CreateFontAssetInstance_m9114A363FC6DDEB899CC75C73A41CF903A59333E (void);
// 0x00000056 System.Void UnityEngine.TextCore.Text.FontAsset::Awake()
extern void FontAsset_Awake_mB7906A1E21F5FAB84A023B97435F75C09EAB92ED (void);
// 0x00000057 System.Void UnityEngine.TextCore.Text.FontAsset::OnDestroy()
extern void FontAsset_OnDestroy_m3587016A089072C5C03168AA4C6AA1956FE12785 (void);
// 0x00000058 System.Void UnityEngine.TextCore.Text.FontAsset::ReadFontAssetDefinition()
extern void FontAsset_ReadFontAssetDefinition_m6D84DBCB130D530B2F78A7E24232D8A6A81AEC48 (void);
// 0x00000059 System.Void UnityEngine.TextCore.Text.FontAsset::InitializeDictionaryLookupTables()
extern void FontAsset_InitializeDictionaryLookupTables_m29A4AEF49CF11A0E49C229EF13B2262AE66757FF (void);
// 0x0000005A System.Void UnityEngine.TextCore.Text.FontAsset::InitializeGlyphLookupDictionary()
extern void FontAsset_InitializeGlyphLookupDictionary_m82782B7B5C602AD5097A016BF668868C0892CCF6 (void);
// 0x0000005B System.Void UnityEngine.TextCore.Text.FontAsset::InitializeCharacterLookupDictionary()
extern void FontAsset_InitializeCharacterLookupDictionary_m8886A4CA911334BD319AB78F1CBBD68E13624BB6 (void);
// 0x0000005C System.Void UnityEngine.TextCore.Text.FontAsset::InitializeGlyphPaidAdjustmentRecordsLookupDictionary()
extern void FontAsset_InitializeGlyphPaidAdjustmentRecordsLookupDictionary_mDF1C20792344999B9CF500685E6256B0642CE7DD (void);
// 0x0000005D System.Void UnityEngine.TextCore.Text.FontAsset::AddSynthesizedCharactersAndFaceMetrics()
extern void FontAsset_AddSynthesizedCharactersAndFaceMetrics_m203BD62D0A537A6EA7CD7DBA1FF9A94301492933 (void);
// 0x0000005E System.Void UnityEngine.TextCore.Text.FontAsset::AddSynthesizedCharacter(System.UInt32,System.Boolean,System.Boolean)
extern void FontAsset_AddSynthesizedCharacter_m6ABFCE6454A09D5CF7914F318DDC79198C47F9EA (void);
// 0x0000005F System.Void UnityEngine.TextCore.Text.FontAsset::AddCharacterToLookupCache(System.UInt32,UnityEngine.TextCore.Text.Character)
extern void FontAsset_AddCharacterToLookupCache_mB90E06CE313CC0BB6F81415BF8FB4E043108EED8 (void);
// 0x00000060 UnityEngine.TextCore.LowLevel.FontEngineError UnityEngine.TextCore.Text.FontAsset::LoadFontFace()
extern void FontAsset_LoadFontFace_m64C78A2FE5DA2E7029E43B467A1B242827B45B4F (void);
// 0x00000061 System.Void UnityEngine.TextCore.Text.FontAsset::SortCharacterTable()
extern void FontAsset_SortCharacterTable_m9A551DF3B19E246E8C4BE86463E0ED1DEB27D321 (void);
// 0x00000062 System.Void UnityEngine.TextCore.Text.FontAsset::SortGlyphTable()
extern void FontAsset_SortGlyphTable_mC853714CB002D923A19C3A925BB24D6BF42A08CD (void);
// 0x00000063 System.Void UnityEngine.TextCore.Text.FontAsset::SortFontFeatureTable()
extern void FontAsset_SortFontFeatureTable_m072B32D6D8C562F60D3D6CBCC7DCB3282EDD587F (void);
// 0x00000064 System.Void UnityEngine.TextCore.Text.FontAsset::SortAllTables()
extern void FontAsset_SortAllTables_mACA7063865A460F5949E5B8A8D978D588124A094 (void);
// 0x00000065 System.Boolean UnityEngine.TextCore.Text.FontAsset::HasCharacter(System.Int32)
extern void FontAsset_HasCharacter_m6BAF48714E1BF5D8EE7ACF33F774C8C6EEE452F3 (void);
// 0x00000066 System.Boolean UnityEngine.TextCore.Text.FontAsset::HasCharacter(System.Char,System.Boolean,System.Boolean)
extern void FontAsset_HasCharacter_mE87EEF6CDA1F4E1D6928CC9A3C01A91922D4FB21 (void);
// 0x00000067 System.Boolean UnityEngine.TextCore.Text.FontAsset::HasCharacter_Internal(System.UInt32,System.Boolean,System.Boolean)
extern void FontAsset_HasCharacter_Internal_mDC0D2954E0975A7DBC8829E894CDBCABEA7D6A60 (void);
// 0x00000068 System.Boolean UnityEngine.TextCore.Text.FontAsset::HasCharacters(System.String,System.Collections.Generic.List`1<System.Char>&)
extern void FontAsset_HasCharacters_mD670CCEB48448CE5C1430B938F99D4FC659FB2F8 (void);
// 0x00000069 System.Boolean UnityEngine.TextCore.Text.FontAsset::HasCharacters(System.String,System.UInt32[]&,System.Boolean,System.Boolean)
extern void FontAsset_HasCharacters_m97A50BC627C163418CAE0B42A50893057B025E90 (void);
// 0x0000006A System.Boolean UnityEngine.TextCore.Text.FontAsset::HasCharacters(System.String)
extern void FontAsset_HasCharacters_m048839FDD1876CDCA3C5A744592545B46C75E15B (void);
// 0x0000006B System.String UnityEngine.TextCore.Text.FontAsset::GetCharacters(UnityEngine.TextCore.Text.FontAsset)
extern void FontAsset_GetCharacters_m5CCEC5A0F89B0BC29B1EFEE9E33923F6879409F4 (void);
// 0x0000006C System.Int32[] UnityEngine.TextCore.Text.FontAsset::GetCharactersArray(UnityEngine.TextCore.Text.FontAsset)
extern void FontAsset_GetCharactersArray_m4CE08FECFCF3F9913B5B29DF0F2272FA4B3F945F (void);
// 0x0000006D System.UInt32 UnityEngine.TextCore.Text.FontAsset::GetGlyphIndex(System.UInt32)
extern void FontAsset_GetGlyphIndex_mF20097CDB68A8CE866E61D4C237FBB95257A9745 (void);
// 0x0000006E System.Void UnityEngine.TextCore.Text.FontAsset::RegisterFontAssetForFontFeatureUpdate(UnityEngine.TextCore.Text.FontAsset)
extern void FontAsset_RegisterFontAssetForFontFeatureUpdate_m5D26FB74AB0C3AA8DC363DA517C1017641F85739 (void);
// 0x0000006F System.Void UnityEngine.TextCore.Text.FontAsset::UpdateFontFeaturesForFontAssetsInQueue()
extern void FontAsset_UpdateFontFeaturesForFontAssetsInQueue_m021524DE2B5F8ABE9DDA58447F896159EA15E0F3 (void);
// 0x00000070 System.Void UnityEngine.TextCore.Text.FontAsset::RegisterAtlasTextureForApply(UnityEngine.Texture2D)
extern void FontAsset_RegisterAtlasTextureForApply_mAB5DC9E4B77C8E0FCADF0AD98E8D63C5229FC383 (void);
// 0x00000071 System.Void UnityEngine.TextCore.Text.FontAsset::UpdateAtlasTexturesInQueue()
extern void FontAsset_UpdateAtlasTexturesInQueue_m359EFCCB6F692F5F8546D4205F366B079075792E (void);
// 0x00000072 System.Void UnityEngine.TextCore.Text.FontAsset::UpdateFontAssetInUpdateQueue()
extern void FontAsset_UpdateFontAssetInUpdateQueue_mCF68DCD3B3F71341198DD8F528CC9E0E27071471 (void);
// 0x00000073 System.Boolean UnityEngine.TextCore.Text.FontAsset::TryAddCharacters(System.UInt32[],System.Boolean)
extern void FontAsset_TryAddCharacters_m7F1D0CB7E4D9B8D3CE44D4D01F9CDCEFD4D1B46B (void);
// 0x00000074 System.Boolean UnityEngine.TextCore.Text.FontAsset::TryAddCharacters(System.UInt32[],System.UInt32[]&,System.Boolean)
extern void FontAsset_TryAddCharacters_m9618B4F12C004B8267E0D17ED81B94BE48D85119 (void);
// 0x00000075 System.Boolean UnityEngine.TextCore.Text.FontAsset::TryAddCharacters(System.String,System.Boolean)
extern void FontAsset_TryAddCharacters_m5E282618D9ED92AD0112BC7B6B2C3B1066DDFA63 (void);
// 0x00000076 System.Boolean UnityEngine.TextCore.Text.FontAsset::TryAddCharacters(System.String,System.String&,System.Boolean)
extern void FontAsset_TryAddCharacters_mDA1C3A68799C84A80C27CDB84482684F6822137F (void);
// 0x00000077 System.Boolean UnityEngine.TextCore.Text.FontAsset::TryAddCharacterInternal(System.UInt32,UnityEngine.TextCore.Text.Character&,System.Boolean)
extern void FontAsset_TryAddCharacterInternal_mCDC9AE2C61B9F73B8879C3F5AE00598A67B2BA7F (void);
// 0x00000078 System.Boolean UnityEngine.TextCore.Text.FontAsset::TryGetCharacter_and_QueueRenderToTexture(System.UInt32,UnityEngine.TextCore.Text.Character&,System.Boolean)
extern void FontAsset_TryGetCharacter_and_QueueRenderToTexture_mA76A244F58E0F2978178FBBEB18F2E0DCA568AEC (void);
// 0x00000079 System.Void UnityEngine.TextCore.Text.FontAsset::TryAddGlyphsToAtlasTextures()
extern void FontAsset_TryAddGlyphsToAtlasTextures_m83F7EDB3193B3C9A4FA86B89A51E9FA6A41F6834 (void);
// 0x0000007A System.Boolean UnityEngine.TextCore.Text.FontAsset::TryAddGlyphsToNewAtlasTexture()
extern void FontAsset_TryAddGlyphsToNewAtlasTexture_m8F98FBF7A0EC1B37C4DB43536DA42D3864F6F3AB (void);
// 0x0000007B System.Void UnityEngine.TextCore.Text.FontAsset::SetupNewAtlasTexture()
extern void FontAsset_SetupNewAtlasTexture_m38F81BE1582A15DDDB950E7AAC650CD9B7D14168 (void);
// 0x0000007C System.Void UnityEngine.TextCore.Text.FontAsset::UpdateAtlasTexture()
extern void FontAsset_UpdateAtlasTexture_m30D7C235A878B1A33F2A3891D8096F534C10F6C5 (void);
// 0x0000007D System.Void UnityEngine.TextCore.Text.FontAsset::UpdateGlyphAdjustmentRecords()
extern void FontAsset_UpdateGlyphAdjustmentRecords_mD1C9297EA75EA767A823709CC39B6E57905E22A3 (void);
// 0x0000007E System.Void UnityEngine.TextCore.Text.FontAsset::UpdateGlyphAdjustmentRecords(System.UInt32[])
extern void FontAsset_UpdateGlyphAdjustmentRecords_mC9882537E81709FE1EFA9B744D88C6C32ACEDF52 (void);
// 0x0000007F System.Void UnityEngine.TextCore.Text.FontAsset::UpdateGlyphAdjustmentRecords(System.Collections.Generic.List`1<System.UInt32>)
extern void FontAsset_UpdateGlyphAdjustmentRecords_m2D0444352012E8DFDD6036025886EC0CED0AD82A (void);
// 0x00000080 System.Void UnityEngine.TextCore.Text.FontAsset::UpdateGlyphAdjustmentRecords(System.Collections.Generic.List`1<System.UInt32>,System.Collections.Generic.List`1<System.UInt32>)
extern void FontAsset_UpdateGlyphAdjustmentRecords_m9410421BA99635607C50EED1C9C374570EFABC60 (void);
// 0x00000081 System.Void UnityEngine.TextCore.Text.FontAsset::CopyListDataToArray(System.Collections.Generic.List`1<T>,T[]&)
// 0x00000082 System.Void UnityEngine.TextCore.Text.FontAsset::ClearFontAssetData(System.Boolean)
extern void FontAsset_ClearFontAssetData_m225ADFCBB0CFD481E18637F3D3FDFFEAFC6FE9A1 (void);
// 0x00000083 System.Void UnityEngine.TextCore.Text.FontAsset::ClearFontAssetDataInternal()
extern void FontAsset_ClearFontAssetDataInternal_mD3DB3516BB6D61F0A9630D9F6958F474DFE044F7 (void);
// 0x00000084 System.Void UnityEngine.TextCore.Text.FontAsset::UpdateFontAssetData()
extern void FontAsset_UpdateFontAssetData_mAAC0ED05410942C08E8EFD4678F9565FD8C373D4 (void);
// 0x00000085 System.Void UnityEngine.TextCore.Text.FontAsset::ClearFontAssetTables()
extern void FontAsset_ClearFontAssetTables_m38470615509BEFAE39C90C234D8B460F05824C39 (void);
// 0x00000086 System.Void UnityEngine.TextCore.Text.FontAsset::ClearAtlasTextures(System.Boolean)
extern void FontAsset_ClearAtlasTextures_m5B320A65E1CD35F2C17E27F09158F8E9BDA9EA2B (void);
// 0x00000087 System.Void UnityEngine.TextCore.Text.FontAsset::DestroyAtlasTextures()
extern void FontAsset_DestroyAtlasTextures_mBE2810F8C55E286B5B7ABE24A6F9132F51CBE027 (void);
// 0x00000088 System.Void UnityEngine.TextCore.Text.FontAsset::.ctor()
extern void FontAsset__ctor_mD55676BD025F9D05DBC9A5B32480E092169B9D45 (void);
// 0x00000089 System.Void UnityEngine.TextCore.Text.FontAsset::.cctor()
extern void FontAsset__cctor_m0F11465E3C2F3D158DB9BF403CE86298432BD00E (void);
// 0x0000008A System.Void UnityEngine.TextCore.Text.FontAsset/<>c::.cctor()
extern void U3CU3Ec__cctor_mEB4ED309CE67EB2C5633590466BD1C4EE90E0D5C (void);
// 0x0000008B System.Void UnityEngine.TextCore.Text.FontAsset/<>c::.ctor()
extern void U3CU3Ec__ctor_m3CDF0903C024856278B5A7DF46C7EFCBA6F6B651 (void);
// 0x0000008C System.UInt32 UnityEngine.TextCore.Text.FontAsset/<>c::<SortCharacterTable>b__144_0(UnityEngine.TextCore.Text.Character)
extern void U3CU3Ec_U3CSortCharacterTableU3Eb__144_0_m5655A423C374C42F5761CFBFBEA41E6566421B0B (void);
// 0x0000008D System.UInt32 UnityEngine.TextCore.Text.FontAsset/<>c::<SortGlyphTable>b__145_0(UnityEngine.TextCore.Glyph)
extern void U3CU3Ec_U3CSortGlyphTableU3Eb__145_0_m2BB897F83BD1FE22634624544AFCD8B5988788A0 (void);
// 0x0000008E UnityEngine.TextCore.FaceInfo UnityEngine.TextCore.Text.SpriteAsset::get_faceInfo()
extern void SpriteAsset_get_faceInfo_m54EC5227F682ED6A24F5633283258E6641CDA4DC (void);
// 0x0000008F System.Void UnityEngine.TextCore.Text.SpriteAsset::set_faceInfo(UnityEngine.TextCore.FaceInfo)
extern void SpriteAsset_set_faceInfo_m060A5DBBD5941A53BFE9D45E2B637D88ED8223EA (void);
// 0x00000090 UnityEngine.Texture UnityEngine.TextCore.Text.SpriteAsset::get_spriteSheet()
extern void SpriteAsset_get_spriteSheet_mC53205114A12A79F7495FA5F5EFC9948F151256B (void);
// 0x00000091 System.Void UnityEngine.TextCore.Text.SpriteAsset::set_spriteSheet(UnityEngine.Texture)
extern void SpriteAsset_set_spriteSheet_m1DE591615ABCBB4B10118BF4C0E1B57F559C6469 (void);
// 0x00000092 System.Collections.Generic.List`1<UnityEngine.TextCore.Text.SpriteCharacter> UnityEngine.TextCore.Text.SpriteAsset::get_spriteCharacterTable()
extern void SpriteAsset_get_spriteCharacterTable_m8D0D65C430AD8BC8C2BC8151DC4672CC0F690E0A (void);
// 0x00000093 System.Void UnityEngine.TextCore.Text.SpriteAsset::set_spriteCharacterTable(System.Collections.Generic.List`1<UnityEngine.TextCore.Text.SpriteCharacter>)
extern void SpriteAsset_set_spriteCharacterTable_m38553B81E01B502CCD568A654E9EF3B0D0BCA92D (void);
// 0x00000094 System.Collections.Generic.Dictionary`2<System.UInt32,UnityEngine.TextCore.Text.SpriteCharacter> UnityEngine.TextCore.Text.SpriteAsset::get_spriteCharacterLookupTable()
extern void SpriteAsset_get_spriteCharacterLookupTable_mDB0BA7D703F58A05BB765AE44D6244D98C17DE03 (void);
// 0x00000095 System.Void UnityEngine.TextCore.Text.SpriteAsset::set_spriteCharacterLookupTable(System.Collections.Generic.Dictionary`2<System.UInt32,UnityEngine.TextCore.Text.SpriteCharacter>)
extern void SpriteAsset_set_spriteCharacterLookupTable_m38F3E7A0A52B82595C87E6A630B156E4D22F2E25 (void);
// 0x00000096 System.Collections.Generic.List`1<UnityEngine.TextCore.Text.SpriteGlyph> UnityEngine.TextCore.Text.SpriteAsset::get_spriteGlyphTable()
extern void SpriteAsset_get_spriteGlyphTable_m491B3F4A5350C38D8B5166A60B7C43ED3608C0BA (void);
// 0x00000097 System.Void UnityEngine.TextCore.Text.SpriteAsset::set_spriteGlyphTable(System.Collections.Generic.List`1<UnityEngine.TextCore.Text.SpriteGlyph>)
extern void SpriteAsset_set_spriteGlyphTable_m7402B7CF195E4B9F15DDA3273F49CA460A68DAD3 (void);
// 0x00000098 System.Void UnityEngine.TextCore.Text.SpriteAsset::Awake()
extern void SpriteAsset_Awake_m3A012935612A7EB924A77B85EDCF6C09257F60BE (void);
// 0x00000099 System.Void UnityEngine.TextCore.Text.SpriteAsset::UpdateLookupTables()
extern void SpriteAsset_UpdateLookupTables_mCC7A470A65A72908C9CDBDDFD17A056188A5C7CE (void);
// 0x0000009A System.Int32 UnityEngine.TextCore.Text.SpriteAsset::GetSpriteIndexFromHashcode(System.Int32)
extern void SpriteAsset_GetSpriteIndexFromHashcode_mE73615D1D9A8BB45C3426197EC54B1A002642DE0 (void);
// 0x0000009B System.Int32 UnityEngine.TextCore.Text.SpriteAsset::GetSpriteIndexFromUnicode(System.UInt32)
extern void SpriteAsset_GetSpriteIndexFromUnicode_m321E02B6000E5F6673F5724155C3EF1DE3F5A66B (void);
// 0x0000009C System.Int32 UnityEngine.TextCore.Text.SpriteAsset::GetSpriteIndexFromName(System.String)
extern void SpriteAsset_GetSpriteIndexFromName_mBCB684ED6E3DF5663A7FDA02CA69C99D9B17281B (void);
// 0x0000009D UnityEngine.TextCore.Text.SpriteAsset UnityEngine.TextCore.Text.SpriteAsset::SearchForSpriteByUnicode(UnityEngine.TextCore.Text.SpriteAsset,System.UInt32,System.Boolean,System.Int32&)
extern void SpriteAsset_SearchForSpriteByUnicode_mC50EFD6F2B78609891C357212CF65A8F18EB8A66 (void);
// 0x0000009E UnityEngine.TextCore.Text.SpriteAsset UnityEngine.TextCore.Text.SpriteAsset::SearchForSpriteByUnicodeInternal(System.Collections.Generic.List`1<UnityEngine.TextCore.Text.SpriteAsset>,System.UInt32,System.Boolean,System.Int32&)
extern void SpriteAsset_SearchForSpriteByUnicodeInternal_mAA47B4DB58070A7A3F5F97C597098A65E896B5A5 (void);
// 0x0000009F UnityEngine.TextCore.Text.SpriteAsset UnityEngine.TextCore.Text.SpriteAsset::SearchForSpriteByUnicodeInternal(UnityEngine.TextCore.Text.SpriteAsset,System.UInt32,System.Boolean,System.Int32&)
extern void SpriteAsset_SearchForSpriteByUnicodeInternal_mEB122A514DF6A0D063EF8BE18F31F278ED9C3518 (void);
// 0x000000A0 UnityEngine.TextCore.Text.SpriteAsset UnityEngine.TextCore.Text.SpriteAsset::SearchForSpriteByHashCode(UnityEngine.TextCore.Text.SpriteAsset,System.Int32,System.Boolean,System.Int32&,UnityEngine.TextCore.Text.TextSettings)
extern void SpriteAsset_SearchForSpriteByHashCode_m8B9CAB0028CC297570785693F2973918B31C9028 (void);
// 0x000000A1 UnityEngine.TextCore.Text.SpriteAsset UnityEngine.TextCore.Text.SpriteAsset::SearchForSpriteByHashCodeInternal(System.Collections.Generic.List`1<UnityEngine.TextCore.Text.SpriteAsset>,System.Int32,System.Boolean,System.Int32&)
extern void SpriteAsset_SearchForSpriteByHashCodeInternal_mAD106CFA37AACBD783D0A74817D55507013BBC14 (void);
// 0x000000A2 UnityEngine.TextCore.Text.SpriteAsset UnityEngine.TextCore.Text.SpriteAsset::SearchForSpriteByHashCodeInternal(UnityEngine.TextCore.Text.SpriteAsset,System.Int32,System.Boolean,System.Int32&)
extern void SpriteAsset_SearchForSpriteByHashCodeInternal_mD38A7595ACBC7773C8292B0FD7E5A170A4105208 (void);
// 0x000000A3 System.Void UnityEngine.TextCore.Text.SpriteAsset::SortGlyphTable()
extern void SpriteAsset_SortGlyphTable_mA700CE5246D5798FA65779BE53179FFF4FFED6E5 (void);
// 0x000000A4 System.Void UnityEngine.TextCore.Text.SpriteAsset::SortCharacterTable()
extern void SpriteAsset_SortCharacterTable_m5447649977AF2C9F62A14415B44CDDD897A53AE1 (void);
// 0x000000A5 System.Void UnityEngine.TextCore.Text.SpriteAsset::SortGlyphAndCharacterTables()
extern void SpriteAsset_SortGlyphAndCharacterTables_m0E2B691E7C1F284E12A88B47B705307E83C7D927 (void);
// 0x000000A6 System.Void UnityEngine.TextCore.Text.SpriteAsset::.ctor()
extern void SpriteAsset__ctor_mE03F69799389DE8D90E69CD70054955033C4ED3C (void);
// 0x000000A7 System.Void UnityEngine.TextCore.Text.SpriteAsset/<>c::.cctor()
extern void U3CU3Ec__cctor_m84A11BA2AE10FB375A960D167ABF5F559DACAE3A (void);
// 0x000000A8 System.Void UnityEngine.TextCore.Text.SpriteAsset/<>c::.ctor()
extern void U3CU3Ec__ctor_mEFC122BF1D0D0CA8F0EAE9CE353C37A8CFABB5F3 (void);
// 0x000000A9 System.UInt32 UnityEngine.TextCore.Text.SpriteAsset/<>c::<SortGlyphTable>b__37_0(UnityEngine.TextCore.Text.SpriteGlyph)
extern void U3CU3Ec_U3CSortGlyphTableU3Eb__37_0_mC479CF63F85C34FC407D92E67878B9B2AD99B739 (void);
// 0x000000AA System.UInt32 UnityEngine.TextCore.Text.SpriteAsset/<>c::<SortCharacterTable>b__38_0(UnityEngine.TextCore.Text.SpriteCharacter)
extern void U3CU3Ec_U3CSortCharacterTableU3Eb__38_0_m6A3F26D4286DF4F04DC23D23D04E12CA014D6E92 (void);
// 0x000000AB System.String UnityEngine.TextCore.Text.Extents::ToString()
extern void Extents_ToString_m8A1F748127EE9CCCD6FFAF4CE1F38E37C07831AC (void);
// 0x000000AC System.Collections.Generic.List`1<UnityEngine.TextCore.LowLevel.GlyphPairAdjustmentRecord> UnityEngine.TextCore.Text.FontFeatureTable::get_glyphPairAdjustmentRecords()
extern void FontFeatureTable_get_glyphPairAdjustmentRecords_mABE78F7C2EA171927CC33170617D72E6C976323E (void);
// 0x000000AD System.Void UnityEngine.TextCore.Text.FontFeatureTable::.ctor()
extern void FontFeatureTable__ctor_m5F00F284C63F1867F679A3250ABFC1393C27025C (void);
// 0x000000AE System.Void UnityEngine.TextCore.Text.FontFeatureTable::SortGlyphPairAdjustmentRecords()
extern void FontFeatureTable_SortGlyphPairAdjustmentRecords_m2F5E2ED405FCAEE946CE5CF81163DDCC1B02A905 (void);
// 0x000000AF System.Void UnityEngine.TextCore.Text.FontFeatureTable/<>c::.cctor()
extern void U3CU3Ec__cctor_m024C5AAB034EF6BBF574EA4EB3AE66D03E259CEA (void);
// 0x000000B0 System.Void UnityEngine.TextCore.Text.FontFeatureTable/<>c::.ctor()
extern void U3CU3Ec__ctor_m51815D1379A3BDB616D65C006DA7AB32406723F4 (void);
// 0x000000B1 System.UInt32 UnityEngine.TextCore.Text.FontFeatureTable/<>c::<SortGlyphPairAdjustmentRecords>b__6_0(UnityEngine.TextCore.LowLevel.GlyphPairAdjustmentRecord)
extern void U3CU3Ec_U3CSortGlyphPairAdjustmentRecordsU3Eb__6_0_m4A2B8B22BA5C619432654E7A0F5DEDAE949E12BA (void);
// 0x000000B2 System.UInt32 UnityEngine.TextCore.Text.FontFeatureTable/<>c::<SortGlyphPairAdjustmentRecords>b__6_1(UnityEngine.TextCore.LowLevel.GlyphPairAdjustmentRecord)
extern void U3CU3Ec_U3CSortGlyphPairAdjustmentRecordsU3Eb__6_1_m5BF29DF1AB0CBEC9E1D73F20A998CBEC05DC7572 (void);
// 0x000000B3 System.Boolean UnityEngine.TextCore.Text.TextGeneratorUtilities::Approximately(System.Single,System.Single)
extern void TextGeneratorUtilities_Approximately_m696ABB909732F536F1FF83EA8CE34CF53266794D (void);
// 0x000000B4 UnityEngine.Color32 UnityEngine.TextCore.Text.TextGeneratorUtilities::HexCharsToColor(System.Char[],System.Int32)
extern void TextGeneratorUtilities_HexCharsToColor_m2C739FBEC67C612B593FDF344E5875F0C0D8AC31 (void);
// 0x000000B5 UnityEngine.Color32 UnityEngine.TextCore.Text.TextGeneratorUtilities::HexCharsToColor(System.Char[],System.Int32,System.Int32)
extern void TextGeneratorUtilities_HexCharsToColor_m4D7AB2E490DA31C514A42A880AB35CE9CBAF77B4 (void);
// 0x000000B6 System.Int32 UnityEngine.TextCore.Text.TextGeneratorUtilities::HexToInt(System.Char)
extern void TextGeneratorUtilities_HexToInt_m3017B82A336E3DB4B577D95AF6ADD393000AEB63 (void);
// 0x000000B7 System.Single UnityEngine.TextCore.Text.TextGeneratorUtilities::ConvertToFloat(System.Char[],System.Int32,System.Int32)
extern void TextGeneratorUtilities_ConvertToFloat_m93B85749154AAFB614CD246876786B75C98AE11C (void);
// 0x000000B8 System.Single UnityEngine.TextCore.Text.TextGeneratorUtilities::ConvertToFloat(System.Char[],System.Int32,System.Int32,System.Int32&)
extern void TextGeneratorUtilities_ConvertToFloat_m37E1CF11E22ED6528B166907211426EEE6D0A4F8 (void);
// 0x000000B9 UnityEngine.Vector2 UnityEngine.TextCore.Text.TextGeneratorUtilities::PackUV(System.Single,System.Single,System.Single)
extern void TextGeneratorUtilities_PackUV_mE110A97960725C40D87FA903B63E0100AFCB06F5 (void);
// 0x000000BA System.Void UnityEngine.TextCore.Text.TextGeneratorUtilities::StringToCharArray(System.String,System.Int32[]&,UnityEngine.TextCore.Text.TextProcessingStack`1<System.Int32>&,UnityEngine.TextCore.Text.TextGenerationSettings)
extern void TextGeneratorUtilities_StringToCharArray_m54D1AB72DAC31ADF5CF5EFD69E8B3BA5E2DCD1F5 (void);
// 0x000000BB System.Void UnityEngine.TextCore.Text.TextGeneratorUtilities::ResizeInternalArray(T[]&)
// 0x000000BC System.Boolean UnityEngine.TextCore.Text.TextGeneratorUtilities::IsTagName(System.String&,System.String,System.Int32)
extern void TextGeneratorUtilities_IsTagName_m67178B96C0B3119FFEA83A8ACED7D7DAD0414208 (void);
// 0x000000BD System.Boolean UnityEngine.TextCore.Text.TextGeneratorUtilities::IsTagName(System.Int32[]&,System.String,System.Int32)
extern void TextGeneratorUtilities_IsTagName_mA8FA492104AD554B93FD6FDAC0D67416ED746A9D (void);
// 0x000000BE System.Boolean UnityEngine.TextCore.Text.TextGeneratorUtilities::ReplaceOpeningStyleTag(System.Int32[]&,System.Int32,System.Int32&,System.Int32[]&,System.Int32&,UnityEngine.TextCore.Text.TextProcessingStack`1<System.Int32>&,UnityEngine.TextCore.Text.TextGenerationSettings&)
extern void TextGeneratorUtilities_ReplaceOpeningStyleTag_m4559E128C7101E4C355B98D3E10A00E603FA9140 (void);
// 0x000000BF System.Boolean UnityEngine.TextCore.Text.TextGeneratorUtilities::ReplaceOpeningStyleTag(System.String&,System.Int32,System.Int32&,System.Int32[]&,System.Int32&,UnityEngine.TextCore.Text.TextProcessingStack`1<System.Int32>&,UnityEngine.TextCore.Text.TextGenerationSettings&)
extern void TextGeneratorUtilities_ReplaceOpeningStyleTag_mE66FFFF690CBFBFAAE2982103CF0D6211CFFA5A6 (void);
// 0x000000C0 System.Void UnityEngine.TextCore.Text.TextGeneratorUtilities::ReplaceClosingStyleTag(System.Int32[]&,System.Int32&,UnityEngine.TextCore.Text.TextProcessingStack`1<System.Int32>&,UnityEngine.TextCore.Text.TextGenerationSettings&)
extern void TextGeneratorUtilities_ReplaceClosingStyleTag_m3659E196D25EB735F5F7D53B7B5FED3DC211CC48 (void);
// 0x000000C1 UnityEngine.TextCore.Text.TextStyle UnityEngine.TextCore.Text.TextGeneratorUtilities::GetStyle(UnityEngine.TextCore.Text.TextGenerationSettings,System.Int32)
extern void TextGeneratorUtilities_GetStyle_m236E7C5EBFA951D1D4B2C3C7803BBEA5C2FD1812 (void);
// 0x000000C2 System.Int32 UnityEngine.TextCore.Text.TextGeneratorUtilities::GetUtf32(System.String,System.Int32)
extern void TextGeneratorUtilities_GetUtf32_mD4E8A09D5232957490A6B6D2001867D2814CFBDB (void);
// 0x000000C3 System.Int32 UnityEngine.TextCore.Text.TextGeneratorUtilities::GetUtf16(System.String,System.Int32)
extern void TextGeneratorUtilities_GetUtf16_mCCED0D4CAB31E7C9ADB22A3546D1C209FA2C3E13 (void);
// 0x000000C4 System.Int32 UnityEngine.TextCore.Text.TextGeneratorUtilities::GetTagHashCode(System.Int32[]&,System.Int32,System.Int32&)
extern void TextGeneratorUtilities_GetTagHashCode_m89243960CD468B3DA8EC0AC29829FD8125056D9F (void);
// 0x000000C5 System.Int32 UnityEngine.TextCore.Text.TextGeneratorUtilities::GetTagHashCode(System.String&,System.Int32,System.Int32&)
extern void TextGeneratorUtilities_GetTagHashCode_m52AED809412409E8D9D9AD9D4DDDF0990A0049CE (void);
// 0x000000C6 System.Void UnityEngine.TextCore.Text.TextGeneratorUtilities::FillCharacterVertexBuffers(System.Int32,UnityEngine.TextCore.Text.TextGenerationSettings,UnityEngine.TextCore.Text.TextInfo)
extern void TextGeneratorUtilities_FillCharacterVertexBuffers_m54CA97C6C26BA84BC949845B20E9DADF2F0C19CA (void);
// 0x000000C7 System.Void UnityEngine.TextCore.Text.TextGeneratorUtilities::FillSpriteVertexBuffers(System.Int32,UnityEngine.TextCore.Text.TextGenerationSettings,UnityEngine.TextCore.Text.TextInfo)
extern void TextGeneratorUtilities_FillSpriteVertexBuffers_m4305B80FA32FE21A59AF68A5501226E5A4203CC3 (void);
// 0x000000C8 System.Void UnityEngine.TextCore.Text.TextGeneratorUtilities::AdjustLineOffset(System.Int32,System.Int32,System.Single,UnityEngine.TextCore.Text.TextInfo)
extern void TextGeneratorUtilities_AdjustLineOffset_m811C187EA3E41781116F0C7A679B05BB27874123 (void);
// 0x000000C9 System.Void UnityEngine.TextCore.Text.TextGeneratorUtilities::ResizeLineExtents(System.Int32,UnityEngine.TextCore.Text.TextInfo)
extern void TextGeneratorUtilities_ResizeLineExtents_m2EA9BE32A38D5E075DEF8EDA9EC01766E45C0F85 (void);
// 0x000000CA UnityEngine.TextCore.Text.FontStyles UnityEngine.TextCore.Text.TextGeneratorUtilities::LegacyStyleToNewStyle(UnityEngine.FontStyle)
extern void TextGeneratorUtilities_LegacyStyleToNewStyle_m8E9C09A7BE7B27A4EC73ADD747BFD9A4DE7E656C (void);
// 0x000000CB UnityEngine.TextCore.Text.TextAlignment UnityEngine.TextCore.Text.TextGeneratorUtilities::LegacyAlignmentToNewAlignment(UnityEngine.TextAnchor)
extern void TextGeneratorUtilities_LegacyAlignmentToNewAlignment_mA16F5CB58D3C8F352CAACB8DACA5AFED59D3ED63 (void);
// 0x000000CC System.Void UnityEngine.TextCore.Text.TextGeneratorUtilities::.cctor()
extern void TextGeneratorUtilities__cctor_m01201F8A0A4161B232AB9D179AB74877D4E17D85 (void);
// 0x000000CD System.Void UnityEngine.TextCore.Text.FontStyleStack::Clear()
extern void FontStyleStack_Clear_m989659363648B27540168E46F23E1EF9877C06E0 (void);
// 0x000000CE System.Byte UnityEngine.TextCore.Text.FontStyleStack::Add(UnityEngine.TextCore.Text.FontStyles)
extern void FontStyleStack_Add_m26E701C9F052EEEBB213B9B8BC6CB8F1F8F6AFCB (void);
// 0x000000CF System.Byte UnityEngine.TextCore.Text.FontStyleStack::Remove(UnityEngine.TextCore.Text.FontStyles)
extern void FontStyleStack_Remove_mC2B4F44A6596E92D6992DBCA298648F8A7416CAB (void);
// 0x000000D0 System.Void UnityEngine.TextCore.Text.TextProcessingStack`1::.ctor(T[])
// 0x000000D1 System.Void UnityEngine.TextCore.Text.TextProcessingStack`1::.ctor(System.Int32)
// 0x000000D2 System.Void UnityEngine.TextCore.Text.TextProcessingStack`1::Clear()
// 0x000000D3 System.Void UnityEngine.TextCore.Text.TextProcessingStack`1::SetDefault(T)
// 0x000000D4 System.Void UnityEngine.TextCore.Text.TextProcessingStack`1::Add(T)
// 0x000000D5 T UnityEngine.TextCore.Text.TextProcessingStack`1::Remove()
// 0x000000D6 System.Void UnityEngine.TextCore.Text.TextProcessingStack`1::Push(T)
// 0x000000D7 T UnityEngine.TextCore.Text.TextProcessingStack`1::Pop()
// 0x000000D8 T UnityEngine.TextCore.Text.TextProcessingStack`1::Peek()
// 0x000000D9 T UnityEngine.TextCore.Text.TextProcessingStack`1::CurrentItem()
// 0x000000DA System.Boolean UnityEngine.TextCore.Text.ColorUtilities::CompareColors(UnityEngine.Color32,UnityEngine.Color32)
extern void ColorUtilities_CompareColors_m0F0F140129DEE889FB8AE3B2921C495E94B5E875 (void);
// 0x000000DB UnityEngine.Color32 UnityEngine.TextCore.Text.ColorUtilities::MultiplyColors(UnityEngine.Color32,UnityEngine.Color32)
extern void ColorUtilities_MultiplyColors_m81D3E41A86E195794977F31C5A944DB40FE734B4 (void);
// 0x000000DC System.String UnityEngine.TextCore.Text.SpriteCharacter::get_name()
extern void SpriteCharacter_get_name_mD5A9CC908308BB48D459973C8844FE1FD7C925B1 (void);
// 0x000000DD System.Void UnityEngine.TextCore.Text.SpriteCharacter::.ctor()
extern void SpriteCharacter__ctor_m0B3812DF9A667CA2C3AA321DF3403197EEBC83BA (void);
// 0x000000DE System.Char UnityEngine.TextCore.Text.TextUtilities::ToUpperFast(System.Char)
extern void TextUtilities_ToUpperFast_m7E98D5D17030A387012E262648346523DCC3AA0B (void);
// 0x000000DF System.UInt32 UnityEngine.TextCore.Text.TextUtilities::ToUpperASCIIFast(System.UInt32)
extern void TextUtilities_ToUpperASCIIFast_m48A8B61739F9D5E8CB46B108746277DAF38AB58C (void);
// 0x000000E0 System.Int32 UnityEngine.TextCore.Text.TextUtilities::GetHashCodeCaseInSensitive(System.String)
extern void TextUtilities_GetHashCodeCaseInSensitive_m3241BDD3A6F82A061C21BF5D347B690D732F5B59 (void);
// 0x000000E1 System.String UnityEngine.TextCore.Text.TextUtilities::UintToString(System.Collections.Generic.List`1<System.UInt32>)
extern void TextUtilities_UintToString_m7C0ECB6D2370EC4275FE0E70FB979CADA55A6216 (void);
// 0x000000E2 UnityEngine.TextCore.Text.TextElementType UnityEngine.TextCore.Text.TextElement::get_elementType()
extern void TextElement_get_elementType_m7BF97842479112227C1C3C83E0E94A176CD7D31A (void);
// 0x000000E3 System.UInt32 UnityEngine.TextCore.Text.TextElement::get_unicode()
extern void TextElement_get_unicode_m40C69806537940F7BA1D3969713DA10CCBE57BC7 (void);
// 0x000000E4 System.Void UnityEngine.TextCore.Text.TextElement::set_unicode(System.UInt32)
extern void TextElement_set_unicode_m99608D824B25E3529236C06BCC0983B5FC094F98 (void);
// 0x000000E5 UnityEngine.TextCore.Text.TextAsset UnityEngine.TextCore.Text.TextElement::get_textAsset()
extern void TextElement_get_textAsset_m52383A3758AABF5BEA013155765BD1141479685A (void);
// 0x000000E6 System.Void UnityEngine.TextCore.Text.TextElement::set_textAsset(UnityEngine.TextCore.Text.TextAsset)
extern void TextElement_set_textAsset_m3F65429660C011F6F25B65D6BA7C4B2CF05659FA (void);
// 0x000000E7 UnityEngine.TextCore.Glyph UnityEngine.TextCore.Text.TextElement::get_glyph()
extern void TextElement_get_glyph_m101DBCCA0CDE2461B504174272A2FFCD53EA59E2 (void);
// 0x000000E8 System.Void UnityEngine.TextCore.Text.TextElement::set_glyph(UnityEngine.TextCore.Glyph)
extern void TextElement_set_glyph_m6E8E2F1366089FA638680F1CF53F6F5027D022A5 (void);
// 0x000000E9 System.UInt32 UnityEngine.TextCore.Text.TextElement::get_glyphIndex()
extern void TextElement_get_glyphIndex_m43F82F2F998D640DEDBE6860EBE7B171DDF4FE56 (void);
// 0x000000EA System.Void UnityEngine.TextCore.Text.TextElement::set_glyphIndex(System.UInt32)
extern void TextElement_set_glyphIndex_mFD72B93816998BE291DA6379EAF5E4153BC64F6C (void);
// 0x000000EB System.Single UnityEngine.TextCore.Text.TextElement::get_scale()
extern void TextElement_get_scale_mD16946900449FEE9E2F86B2C4C71E26F4491A0E6 (void);
// 0x000000EC System.Void UnityEngine.TextCore.Text.TextElement::set_scale(System.Single)
extern void TextElement_set_scale_m83FC0850B2B0F31BDFC779954923F50FD06DC03F (void);
// 0x000000ED System.Void UnityEngine.TextCore.Text.TextElement::.ctor()
extern void TextElement__ctor_m8CB701D9A4C0444E834F178D97E9FC63E6D7E0B9 (void);
// 0x000000EE System.Collections.Generic.List`1<UnityEngine.TextCore.Text.TextStyle> UnityEngine.TextCore.Text.TextStyleSheet::get_styles()
extern void TextStyleSheet_get_styles_m7916C62D70AA430314D85EA5B5A778FFAE1544DB (void);
// 0x000000EF UnityEngine.TextCore.Text.TextStyle UnityEngine.TextCore.Text.TextStyleSheet::GetStyle(System.Int32)
extern void TextStyleSheet_GetStyle_m648B766D750E1B37DD126918BF7EB22DDFD21D29 (void);
// 0x000000F0 UnityEngine.TextCore.Text.TextStyle UnityEngine.TextCore.Text.TextStyleSheet::GetStyle(System.String)
extern void TextStyleSheet_GetStyle_mF87D8108EEF60C3FC32A2B01D5C1C23D3F22A4D1 (void);
// 0x000000F1 System.Void UnityEngine.TextCore.Text.TextStyleSheet::RefreshStyles()
extern void TextStyleSheet_RefreshStyles_m92525DE6F7951D03D906E18EC89349AA2794AEC5 (void);
// 0x000000F2 System.Void UnityEngine.TextCore.Text.TextStyleSheet::LoadStyleDictionaryInternal()
extern void TextStyleSheet_LoadStyleDictionaryInternal_m482E4CDF0CD2ED291F85869E141EEB5EC21F6D81 (void);
// 0x000000F3 System.Void UnityEngine.TextCore.Text.TextStyleSheet::.ctor()
extern void TextStyleSheet__ctor_m1A0B93B55B12CE15FFF1053443179BD010022B75 (void);
// 0x000000F4 UnityEngine.TextCore.Text.MaterialReferenceManager UnityEngine.TextCore.Text.MaterialReferenceManager::get_instance()
extern void MaterialReferenceManager_get_instance_m30BD1A367C3807D0B42CF7156CE699B15E51ED07 (void);
// 0x000000F5 System.Void UnityEngine.TextCore.Text.MaterialReferenceManager::AddFontAsset(UnityEngine.TextCore.Text.FontAsset)
extern void MaterialReferenceManager_AddFontAsset_mF950087FC7EAA324CC15D3E19D62138A96F6FDF7 (void);
// 0x000000F6 System.Void UnityEngine.TextCore.Text.MaterialReferenceManager::AddFontAssetInternal(UnityEngine.TextCore.Text.FontAsset)
extern void MaterialReferenceManager_AddFontAssetInternal_m6F5A4E5ED988BA6F482F015F051ACD19D7B0A005 (void);
// 0x000000F7 System.Void UnityEngine.TextCore.Text.MaterialReferenceManager::AddSpriteAsset(System.Int32,UnityEngine.TextCore.Text.SpriteAsset)
extern void MaterialReferenceManager_AddSpriteAsset_m7FBAAC41386CFA769FDDFF1630D041C4D008A322 (void);
// 0x000000F8 System.Void UnityEngine.TextCore.Text.MaterialReferenceManager::AddSpriteAssetInternal(System.Int32,UnityEngine.TextCore.Text.SpriteAsset)
extern void MaterialReferenceManager_AddSpriteAssetInternal_m788619DC6BAD5B77E9419ACBDECBCCFE1A6AC97C (void);
// 0x000000F9 System.Void UnityEngine.TextCore.Text.MaterialReferenceManager::AddFontMaterial(System.Int32,UnityEngine.Material)
extern void MaterialReferenceManager_AddFontMaterial_m76DC9390D0599D1AC898AE981C8756EA0E536AA6 (void);
// 0x000000FA System.Void UnityEngine.TextCore.Text.MaterialReferenceManager::AddFontMaterialInternal(System.Int32,UnityEngine.Material)
extern void MaterialReferenceManager_AddFontMaterialInternal_m732F46EF768A41B9519917F4FA1E746E056C745C (void);
// 0x000000FB System.Void UnityEngine.TextCore.Text.MaterialReferenceManager::AddColorGradientPreset(System.Int32,UnityEngine.TextCore.Text.TextColorGradient)
extern void MaterialReferenceManager_AddColorGradientPreset_m1496DD123DB526F2B3F49DD3BCAED78A6B1B83EB (void);
// 0x000000FC System.Void UnityEngine.TextCore.Text.MaterialReferenceManager::AddColorGradientPreset_Internal(System.Int32,UnityEngine.TextCore.Text.TextColorGradient)
extern void MaterialReferenceManager_AddColorGradientPreset_Internal_mF27270501EB3725B4CBE4C241B4A2FCD8D871BF1 (void);
// 0x000000FD System.Boolean UnityEngine.TextCore.Text.MaterialReferenceManager::TryGetFontAsset(System.Int32,UnityEngine.TextCore.Text.FontAsset&)
extern void MaterialReferenceManager_TryGetFontAsset_m499C7538909343667E9B837489662862CFF9FB56 (void);
// 0x000000FE System.Boolean UnityEngine.TextCore.Text.MaterialReferenceManager::TryGetFontAssetInternal(System.Int32,UnityEngine.TextCore.Text.FontAsset&)
extern void MaterialReferenceManager_TryGetFontAssetInternal_m2FECC618624B12D200EB311F59CBEECA7CDBB69D (void);
// 0x000000FF System.Boolean UnityEngine.TextCore.Text.MaterialReferenceManager::TryGetSpriteAsset(System.Int32,UnityEngine.TextCore.Text.SpriteAsset&)
extern void MaterialReferenceManager_TryGetSpriteAsset_m1909F75399C0A23DDE7DEDCA60476E1F1ED567EF (void);
// 0x00000100 System.Boolean UnityEngine.TextCore.Text.MaterialReferenceManager::TryGetSpriteAssetInternal(System.Int32,UnityEngine.TextCore.Text.SpriteAsset&)
extern void MaterialReferenceManager_TryGetSpriteAssetInternal_mC434A7C6DB005EDBBA52154E2AB0E36ED7083C84 (void);
// 0x00000101 System.Boolean UnityEngine.TextCore.Text.MaterialReferenceManager::TryGetColorGradientPreset(System.Int32,UnityEngine.TextCore.Text.TextColorGradient&)
extern void MaterialReferenceManager_TryGetColorGradientPreset_m7442603626A04928C6C042BDAC3D3957B8C0AA99 (void);
// 0x00000102 System.Boolean UnityEngine.TextCore.Text.MaterialReferenceManager::TryGetColorGradientPresetInternal(System.Int32,UnityEngine.TextCore.Text.TextColorGradient&)
extern void MaterialReferenceManager_TryGetColorGradientPresetInternal_mD8018B3225786E71F804D629F3107AB75EE5212B (void);
// 0x00000103 System.Boolean UnityEngine.TextCore.Text.MaterialReferenceManager::TryGetMaterial(System.Int32,UnityEngine.Material&)
extern void MaterialReferenceManager_TryGetMaterial_mDC2610737935CD2DE6B1F6302F63C2F3BF7E09CF (void);
// 0x00000104 System.Boolean UnityEngine.TextCore.Text.MaterialReferenceManager::TryGetMaterialInternal(System.Int32,UnityEngine.Material&)
extern void MaterialReferenceManager_TryGetMaterialInternal_mEBFC9CE0A6063B25FEA9070F22FD8AD27107ECE7 (void);
// 0x00000105 System.Void UnityEngine.TextCore.Text.MaterialReferenceManager::.ctor()
extern void MaterialReferenceManager__ctor_mC102EC445A27BE8E3968ADB80EF8FEF3BCFB7778 (void);
// 0x00000106 System.Void UnityEngine.TextCore.Text.TextColorGradient::.ctor()
extern void TextColorGradient__ctor_mAF03C3B68C29D94ED1F1517E65477CFC2B078FAC (void);
// 0x00000107 System.Void UnityEngine.TextCore.Text.TextColorGradient::.ctor(UnityEngine.Color)
extern void TextColorGradient__ctor_m0712D0C09E7BCBC00909CEB71B43F2276ADB8B55 (void);
// 0x00000108 System.Void UnityEngine.TextCore.Text.TextColorGradient::.ctor(UnityEngine.Color,UnityEngine.Color,UnityEngine.Color,UnityEngine.Color)
extern void TextColorGradient__ctor_m0A6B9264A8FBABEEC5BA84353D9B3DD7999DD2FF (void);
// 0x00000109 System.Void UnityEngine.TextCore.Text.TextColorGradient::.cctor()
extern void TextColorGradient__cctor_mC5AE96BE4461EF9AE84CE4E3765B712D6A64693E (void);
// 0x0000010A System.Boolean UnityEngine.TextCore.Text.TextGenerationSettings::Equals(UnityEngine.TextCore.Text.TextGenerationSettings)
extern void TextGenerationSettings_Equals_mA5EDDF0453F2A7314AF5E1FE29F4138CD97E52D5 (void);
// 0x0000010B System.Boolean UnityEngine.TextCore.Text.TextGenerationSettings::Equals(System.Object)
extern void TextGenerationSettings_Equals_mDCEEB056B70FC65ED6065E3BFE8D69D823DFEFD0 (void);
// 0x0000010C System.Int32 UnityEngine.TextCore.Text.TextGenerationSettings::GetHashCode()
extern void TextGenerationSettings_GetHashCode_m1F750434FCE1853C36A579827B064619E85453E0 (void);
// 0x0000010D System.String UnityEngine.TextCore.Text.TextGenerationSettings::ToString()
extern void TextGenerationSettings_ToString_mF8AB635EB369C7F66AC3B396934158CF3B6BA6CE (void);
// 0x0000010E System.Void UnityEngine.TextCore.Text.TextGenerationSettings::.ctor()
extern void TextGenerationSettings__ctor_mA20608A16443434DAE9FEF0BF8BD076270FA660E (void);
// 0x0000010F UnityEngine.TextCore.Text.TextGenerator UnityEngine.TextCore.Text.TextGenerator::GetTextGenerator()
extern void TextGenerator_GetTextGenerator_m5BDD6657637032A944115A1D6D52A6D511D43D46 (void);
// 0x00000110 System.Void UnityEngine.TextCore.Text.TextGenerator::GenerateText(UnityEngine.TextCore.Text.TextGenerationSettings,UnityEngine.TextCore.Text.TextInfo)
extern void TextGenerator_GenerateText_m28C6EED85E5BB42AA40812F475D533AAF6694757 (void);
// 0x00000111 UnityEngine.Vector2 UnityEngine.TextCore.Text.TextGenerator::GetCursorPosition(UnityEngine.TextCore.Text.TextInfo,UnityEngine.Rect,System.Int32,System.Boolean)
extern void TextGenerator_GetCursorPosition_m9F767EE74114971780EF08619DEE0F5223FC5095 (void);
// 0x00000112 UnityEngine.Vector2 UnityEngine.TextCore.Text.TextGenerator::GetPreferredValues(UnityEngine.TextCore.Text.TextGenerationSettings,UnityEngine.TextCore.Text.TextInfo)
extern void TextGenerator_GetPreferredValues_m17A1C8F1AA7D260AB9167985429D6819D0E8D9CA (void);
// 0x00000113 System.Void UnityEngine.TextCore.Text.TextGenerator::Prepare(UnityEngine.TextCore.Text.TextGenerationSettings,UnityEngine.TextCore.Text.TextInfo)
extern void TextGenerator_Prepare_mD0A24977334138340CA73FB9787627373C6AA255 (void);
// 0x00000114 System.Void UnityEngine.TextCore.Text.TextGenerator::GenerateTextMesh(UnityEngine.TextCore.Text.TextGenerationSettings,UnityEngine.TextCore.Text.TextInfo)
extern void TextGenerator_GenerateTextMesh_mAB70FC29A49A6C4F8211EA977E37C66BE67D1831 (void);
// 0x00000115 System.Void UnityEngine.TextCore.Text.TextGenerator::SaveWordWrappingState(UnityEngine.TextCore.Text.WordWrapState&,System.Int32,System.Int32,UnityEngine.TextCore.Text.TextInfo)
extern void TextGenerator_SaveWordWrappingState_mC07B2C5977EECE10216F8C6AC9CC4204F7EF1936 (void);
// 0x00000116 System.Int32 UnityEngine.TextCore.Text.TextGenerator::RestoreWordWrappingState(UnityEngine.TextCore.Text.WordWrapState&,UnityEngine.TextCore.Text.TextInfo)
extern void TextGenerator_RestoreWordWrappingState_mA63B3DD2C02E61CD8670A32A53163AF6BF765F61 (void);
// 0x00000117 System.Boolean UnityEngine.TextCore.Text.TextGenerator::ValidateHtmlTag(System.Int32[],System.Int32,System.Int32&,UnityEngine.TextCore.Text.TextGenerationSettings,UnityEngine.TextCore.Text.TextInfo)
extern void TextGenerator_ValidateHtmlTag_m9C85462F15A6165B10E4C4EE93620AC1021BE5CD (void);
// 0x00000118 System.Void UnityEngine.TextCore.Text.TextGenerator::SaveGlyphVertexInfo(System.Single,System.Single,UnityEngine.Color32,UnityEngine.TextCore.Text.TextGenerationSettings,UnityEngine.TextCore.Text.TextInfo)
extern void TextGenerator_SaveGlyphVertexInfo_m0CD6E1D45488FFC6675294AC64F40AC23C986A09 (void);
// 0x00000119 System.Void UnityEngine.TextCore.Text.TextGenerator::SaveSpriteVertexInfo(UnityEngine.Color32,UnityEngine.TextCore.Text.TextGenerationSettings,UnityEngine.TextCore.Text.TextInfo)
extern void TextGenerator_SaveSpriteVertexInfo_m4B47901F01927E7CC4E486A1C4354AFBF4D138A5 (void);
// 0x0000011A System.Void UnityEngine.TextCore.Text.TextGenerator::DrawUnderlineMesh(UnityEngine.Vector3,UnityEngine.Vector3,System.Int32&,System.Single,System.Single,System.Single,System.Single,UnityEngine.Color32,UnityEngine.TextCore.Text.TextGenerationSettings,UnityEngine.TextCore.Text.TextInfo)
extern void TextGenerator_DrawUnderlineMesh_m7BA49F01C2BC1BEF7845A3D8487B45F15A3BB20E (void);
// 0x0000011B System.Void UnityEngine.TextCore.Text.TextGenerator::DrawTextHighlight(UnityEngine.Vector3,UnityEngine.Vector3,System.Int32&,UnityEngine.Color32,UnityEngine.TextCore.Text.TextGenerationSettings,UnityEngine.TextCore.Text.TextInfo)
extern void TextGenerator_DrawTextHighlight_m3A8E9A72C0984B5DEEF9858060675F3B517F701B (void);
// 0x0000011C System.Void UnityEngine.TextCore.Text.TextGenerator::ClearMesh(System.Boolean,UnityEngine.TextCore.Text.TextInfo)
extern void TextGenerator_ClearMesh_m68BA46B0365FC730BA5D2E6BDF2528BD370B2D83 (void);
// 0x0000011D System.Void UnityEngine.TextCore.Text.TextGenerator::EnableMasking()
extern void TextGenerator_EnableMasking_mB38D92D32518523DE33A2FCD85A67DE481BB0991 (void);
// 0x0000011E System.Void UnityEngine.TextCore.Text.TextGenerator::DisableMasking()
extern void TextGenerator_DisableMasking_mBDE8E47000367F45FC907243C845A11DBDD89950 (void);
// 0x0000011F System.Void UnityEngine.TextCore.Text.TextGenerator::SetArraySizes(System.Int32[],UnityEngine.TextCore.Text.TextGenerationSettings,UnityEngine.TextCore.Text.TextInfo)
extern void TextGenerator_SetArraySizes_mF0041F3D79936C05EB87DEE399F1DC389CCD1BD5 (void);
// 0x00000120 UnityEngine.TextCore.Text.TextElement UnityEngine.TextCore.Text.TextGenerator::GetTextElement(UnityEngine.TextCore.Text.TextGenerationSettings,System.UInt32,UnityEngine.TextCore.Text.FontAsset,UnityEngine.TextCore.Text.FontStyles,UnityEngine.TextCore.Text.TextFontWeight,System.Boolean&)
extern void TextGenerator_GetTextElement_mC46F0E788A0F6EB5A62601BCE4F383C3143C78CB (void);
// 0x00000121 System.Void UnityEngine.TextCore.Text.TextGenerator::ComputeMarginSize(UnityEngine.Rect,UnityEngine.Vector4)
extern void TextGenerator_ComputeMarginSize_m485F8B01196058B15F597DE99D6F6A47FA539D3F (void);
// 0x00000122 System.Void UnityEngine.TextCore.Text.TextGenerator::GetSpecialCharacters(UnityEngine.TextCore.Text.TextGenerationSettings)
extern void TextGenerator_GetSpecialCharacters_mA82879FA537C58223BB660E797AC135A8E07B492 (void);
// 0x00000123 System.Void UnityEngine.TextCore.Text.TextGenerator::GetEllipsisSpecialCharacter(UnityEngine.TextCore.Text.TextGenerationSettings)
extern void TextGenerator_GetEllipsisSpecialCharacter_m5139CAE03CD2E25C9A528A6A6FC984A8515C2460 (void);
// 0x00000124 System.Void UnityEngine.TextCore.Text.TextGenerator::GetUnderlineSpecialCharacter(UnityEngine.TextCore.Text.TextGenerationSettings)
extern void TextGenerator_GetUnderlineSpecialCharacter_mE5E9D5DEB9A7758333CDDCAD05EF25F076EC1AD5 (void);
// 0x00000125 System.Single UnityEngine.TextCore.Text.TextGenerator::GetPaddingForMaterial(UnityEngine.Material,System.Boolean)
extern void TextGenerator_GetPaddingForMaterial_mE5A4DEF3F64851861C092F7A4FC58C902F775C74 (void);
// 0x00000126 UnityEngine.Vector2 UnityEngine.TextCore.Text.TextGenerator::GetPreferredValuesInternal(UnityEngine.TextCore.Text.TextGenerationSettings,UnityEngine.TextCore.Text.TextInfo)
extern void TextGenerator_GetPreferredValuesInternal_m125B070164DFEA503C67525D1F418DAF41300ABD (void);
// 0x00000127 UnityEngine.Vector2 UnityEngine.TextCore.Text.TextGenerator::CalculatePreferredValues(System.Single,UnityEngine.Vector2,System.Boolean,UnityEngine.TextCore.Text.TextGenerationSettings,UnityEngine.TextCore.Text.TextInfo)
extern void TextGenerator_CalculatePreferredValues_mBBE23FA780CF24415963F32F7C500F0394C2545E (void);
// 0x00000128 System.Void UnityEngine.TextCore.Text.TextGenerator::.ctor()
extern void TextGenerator__ctor_m52E4D01DC28BDF753BF52F6501E7FD2FB2B30D90 (void);
// 0x00000129 System.Void UnityEngine.TextCore.Text.TextGenerator/SpecialCharacter::.ctor(UnityEngine.TextCore.Text.Character,System.Int32)
extern void SpecialCharacter__ctor_m6697A8BF272F0144733EE12368C038F45E99F969 (void);
// 0x0000012A System.String UnityEngine.TextCore.Text.TextAsset::get_version()
extern void TextAsset_get_version_m2316C9F212FE4A13335032354A496CAEC86CB25B (void);
// 0x0000012B System.Void UnityEngine.TextCore.Text.TextAsset::set_version(System.String)
extern void TextAsset_set_version_m24718A4A6A86F4CE41D9ED1E0F796CCF367516CD (void);
// 0x0000012C System.Int32 UnityEngine.TextCore.Text.TextAsset::get_instanceID()
extern void TextAsset_get_instanceID_m843A6CAA7FE9322CD19546671D3F0E90A0E27AFB (void);
// 0x0000012D System.Int32 UnityEngine.TextCore.Text.TextAsset::get_hashCode()
extern void TextAsset_get_hashCode_m4D519E837097D8869A8D38EBD11611FADE411092 (void);
// 0x0000012E System.Void UnityEngine.TextCore.Text.TextAsset::set_hashCode(System.Int32)
extern void TextAsset_set_hashCode_mC6ED3271A5EFC05562FD1083BE1C872CB69CFF74 (void);
// 0x0000012F UnityEngine.Material UnityEngine.TextCore.Text.TextAsset::get_material()
extern void TextAsset_get_material_m4B9C02D34426436FDB01F1963A9FDC11D75604EF (void);
// 0x00000130 System.Void UnityEngine.TextCore.Text.TextAsset::set_material(UnityEngine.Material)
extern void TextAsset_set_material_mD9988165763E0E72C7FB7537760899EC1841C829 (void);
// 0x00000131 System.Int32 UnityEngine.TextCore.Text.TextAsset::get_materialHashCode()
extern void TextAsset_get_materialHashCode_m9BAA469D5760D87EEC6E09B9ED3C5902B75017E6 (void);
// 0x00000132 System.Void UnityEngine.TextCore.Text.TextAsset::set_materialHashCode(System.Int32)
extern void TextAsset_set_materialHashCode_m15EE7CCAF81DBAA326049F00788BCC918FDB2631 (void);
// 0x00000133 System.Void UnityEngine.TextCore.Text.TextAsset::.ctor()
extern void TextAsset__ctor_m2C43EAC2D75854E6724A235794DEAF8A1AF9E786 (void);
// 0x00000134 System.Void UnityEngine.TextCore.Text.SpriteGlyph::.ctor()
extern void SpriteGlyph__ctor_mFFE2D9AA5A28EA0C20C10694569A1E23A664BB4D (void);
// 0x00000135 System.String UnityEngine.TextCore.Text.TextSettings::get_version()
extern void TextSettings_get_version_mB5B7C3F04FFC8DCDFCFF63CFFF7612B768E267CB (void);
// 0x00000136 System.Void UnityEngine.TextCore.Text.TextSettings::set_version(System.String)
extern void TextSettings_set_version_m7BEE644EEDD77892C295170F9B2B68BC45C7C6CF (void);
// 0x00000137 UnityEngine.TextCore.Text.FontAsset UnityEngine.TextCore.Text.TextSettings::get_defaultFontAsset()
extern void TextSettings_get_defaultFontAsset_mC6280464BFEE081DB23243BB94E49C72A0885A1F (void);
// 0x00000138 System.Void UnityEngine.TextCore.Text.TextSettings::set_defaultFontAsset(UnityEngine.TextCore.Text.FontAsset)
extern void TextSettings_set_defaultFontAsset_mC0137D9AEB9EE7A9E1B3D7C499414EDD217AF3BA (void);
// 0x00000139 System.String UnityEngine.TextCore.Text.TextSettings::get_defaultFontAssetPath()
extern void TextSettings_get_defaultFontAssetPath_mF7B1713753CFAE048C745C3572332CE18CD51D3B (void);
// 0x0000013A System.Void UnityEngine.TextCore.Text.TextSettings::set_defaultFontAssetPath(System.String)
extern void TextSettings_set_defaultFontAssetPath_m35111382F9C2E3C76FA6B77BABEE8149BDDCC7ED (void);
// 0x0000013B System.Collections.Generic.List`1<UnityEngine.TextCore.Text.FontAsset> UnityEngine.TextCore.Text.TextSettings::get_fallbackFontAssets()
extern void TextSettings_get_fallbackFontAssets_m332526E834C994425141A58C968FD40320573F98 (void);
// 0x0000013C System.Void UnityEngine.TextCore.Text.TextSettings::set_fallbackFontAssets(System.Collections.Generic.List`1<UnityEngine.TextCore.Text.FontAsset>)
extern void TextSettings_set_fallbackFontAssets_mEBEC769B2FF339BDB44D72CB48F0010577889772 (void);
// 0x0000013D System.Boolean UnityEngine.TextCore.Text.TextSettings::get_matchMaterialPreset()
extern void TextSettings_get_matchMaterialPreset_m4675979547AE4C83E680260EAE5ACBC4FAC53B87 (void);
// 0x0000013E System.Void UnityEngine.TextCore.Text.TextSettings::set_matchMaterialPreset(System.Boolean)
extern void TextSettings_set_matchMaterialPreset_mD5A3DDE2D93FE2872C869A2AE0B0C984CF5BC12C (void);
// 0x0000013F System.Int32 UnityEngine.TextCore.Text.TextSettings::get_missingCharacterUnicode()
extern void TextSettings_get_missingCharacterUnicode_mA707E5E6633633BBB3BAFB96B97A5A995100F3F3 (void);
// 0x00000140 System.Void UnityEngine.TextCore.Text.TextSettings::set_missingCharacterUnicode(System.Int32)
extern void TextSettings_set_missingCharacterUnicode_mE3E3E2F902BE89B9FE78F03D3B8C508583D1CDA3 (void);
// 0x00000141 System.Boolean UnityEngine.TextCore.Text.TextSettings::get_clearDynamicDataOnBuild()
extern void TextSettings_get_clearDynamicDataOnBuild_m0C130F53F106D77250F38239FB73DCB97299FB79 (void);
// 0x00000142 System.Void UnityEngine.TextCore.Text.TextSettings::set_clearDynamicDataOnBuild(System.Boolean)
extern void TextSettings_set_clearDynamicDataOnBuild_m86B73E68953D42C6AE4210BEA5D678768155A1AD (void);
// 0x00000143 UnityEngine.TextCore.Text.SpriteAsset UnityEngine.TextCore.Text.TextSettings::get_defaultSpriteAsset()
extern void TextSettings_get_defaultSpriteAsset_m8FA900F9747B7ADBCD2A2F612E7D977DB58D6445 (void);
// 0x00000144 System.Void UnityEngine.TextCore.Text.TextSettings::set_defaultSpriteAsset(UnityEngine.TextCore.Text.SpriteAsset)
extern void TextSettings_set_defaultSpriteAsset_m6083C46D0B3AFB6816363CF2564D2DB2D1ECFEB9 (void);
// 0x00000145 System.String UnityEngine.TextCore.Text.TextSettings::get_defaultSpriteAssetPath()
extern void TextSettings_get_defaultSpriteAssetPath_mF57875222B7FACC9B69369305EEEB53FFE7986E0 (void);
// 0x00000146 System.Void UnityEngine.TextCore.Text.TextSettings::set_defaultSpriteAssetPath(System.String)
extern void TextSettings_set_defaultSpriteAssetPath_m9C49C00A96230DAF828E629C106B229A4552F753 (void);
// 0x00000147 System.Collections.Generic.List`1<UnityEngine.TextCore.Text.SpriteAsset> UnityEngine.TextCore.Text.TextSettings::get_fallbackSpriteAssets()
extern void TextSettings_get_fallbackSpriteAssets_mC6D74088EBDFDFBACD581FE4AB4EAA447ED242AD (void);
// 0x00000148 System.Void UnityEngine.TextCore.Text.TextSettings::set_fallbackSpriteAssets(System.Collections.Generic.List`1<UnityEngine.TextCore.Text.SpriteAsset>)
extern void TextSettings_set_fallbackSpriteAssets_mB28744780B58AE04BA7F1275508FF7DEE4E485E5 (void);
// 0x00000149 System.UInt32 UnityEngine.TextCore.Text.TextSettings::get_missingSpriteCharacterUnicode()
extern void TextSettings_get_missingSpriteCharacterUnicode_m496D85B0D88C91F2A5CC9929C233EAA84831523E (void);
// 0x0000014A System.Void UnityEngine.TextCore.Text.TextSettings::set_missingSpriteCharacterUnicode(System.UInt32)
extern void TextSettings_set_missingSpriteCharacterUnicode_mFE1AFD04C4AA793559E0A157C9E5472E3C3F6701 (void);
// 0x0000014B UnityEngine.TextCore.Text.TextStyleSheet UnityEngine.TextCore.Text.TextSettings::get_defaultStyleSheet()
extern void TextSettings_get_defaultStyleSheet_mDA420960556C00405FA66CBD2DA36807F8F4B4F2 (void);
// 0x0000014C System.Void UnityEngine.TextCore.Text.TextSettings::set_defaultStyleSheet(UnityEngine.TextCore.Text.TextStyleSheet)
extern void TextSettings_set_defaultStyleSheet_m2867A0B047E623E4485F10EE717FF0B46BAAECEE (void);
// 0x0000014D System.String UnityEngine.TextCore.Text.TextSettings::get_styleSheetsResourcePath()
extern void TextSettings_get_styleSheetsResourcePath_mE65A055D1C99CA2048BE6F246258EF262E43C4E6 (void);
// 0x0000014E System.Void UnityEngine.TextCore.Text.TextSettings::set_styleSheetsResourcePath(System.String)
extern void TextSettings_set_styleSheetsResourcePath_m19A5C893047D78B43AC5EB43FB9EA79792464C05 (void);
// 0x0000014F System.String UnityEngine.TextCore.Text.TextSettings::get_defaultColorGradientPresetsPath()
extern void TextSettings_get_defaultColorGradientPresetsPath_m1571454580E1F0DC859D3F201BB1F6355A5DBB8E (void);
// 0x00000150 System.Void UnityEngine.TextCore.Text.TextSettings::set_defaultColorGradientPresetsPath(System.String)
extern void TextSettings_set_defaultColorGradientPresetsPath_m951D5A7AE71C87BFA5B9B8CE8EA2F78A474FE01D (void);
// 0x00000151 UnityEngine.TextCore.Text.UnicodeLineBreakingRules UnityEngine.TextCore.Text.TextSettings::get_lineBreakingRules()
extern void TextSettings_get_lineBreakingRules_m96E2C32D4F08309D904B0BCD83CEBE8CD6716A04 (void);
// 0x00000152 System.Void UnityEngine.TextCore.Text.TextSettings::set_lineBreakingRules(UnityEngine.TextCore.Text.UnicodeLineBreakingRules)
extern void TextSettings_set_lineBreakingRules_mB1174FE0F923443130A3D4686C119EDB0430992D (void);
// 0x00000153 System.Boolean UnityEngine.TextCore.Text.TextSettings::get_displayWarnings()
extern void TextSettings_get_displayWarnings_m3CA9FCB44B30CC06F54CD3716D68285FF844DF83 (void);
// 0x00000154 System.Void UnityEngine.TextCore.Text.TextSettings::set_displayWarnings(System.Boolean)
extern void TextSettings_set_displayWarnings_m08373EE3900E911B4021CBF8AF915DA69856CC20 (void);
// 0x00000155 System.Void UnityEngine.TextCore.Text.TextSettings::InitializeFontReferenceLookup()
extern void TextSettings_InitializeFontReferenceLookup_m34B5FB3D61296A7620196398D267812EEFCE0B04 (void);
// 0x00000156 UnityEngine.TextCore.Text.FontAsset UnityEngine.TextCore.Text.TextSettings::GetCachedFontAssetInternal(UnityEngine.Font)
extern void TextSettings_GetCachedFontAssetInternal_m9308E0DDEF8F6D8ACA17DAADB43745C860788202 (void);
// 0x00000157 System.Void UnityEngine.TextCore.Text.TextSettings::.ctor()
extern void TextSettings__ctor_m860D28B10258792A195E1C6391479E16D04CA8BF (void);
// 0x00000158 System.Void UnityEngine.TextCore.Text.TextSettings/FontReferenceMap::.ctor(UnityEngine.Font,UnityEngine.TextCore.Text.FontAsset)
extern void FontReferenceMap__ctor_mA94127060F9F3FDC3093D9356832503BFBCE44F5 (void);
// 0x00000159 System.Void UnityEngine.TextCore.Text.TextResourceManager::AddFontAsset(UnityEngine.TextCore.Text.FontAsset)
extern void TextResourceManager_AddFontAsset_mAAD97609F00F02146B283982E5DE667EB7A692EA (void);
// 0x0000015A System.Void UnityEngine.TextCore.Text.TextResourceManager::.cctor()
extern void TextResourceManager__cctor_m54E40FCFB355A0C90B56366B057F307F75832868 (void);
// 0x0000015B System.Void UnityEngine.TextCore.Text.TextResourceManager/FontAssetRef::.ctor(System.Int32,System.Int32,System.Int32,UnityEngine.TextCore.Text.FontAsset)
extern void FontAssetRef__ctor_m5553FC4D7E51DE97D70CE09E1C99B002A61FFDB5 (void);
// 0x0000015C System.Void UnityEngine.TextCore.Text.MeshInfo::.ctor(System.Int32)
extern void MeshInfo__ctor_mCC2410C5590BEA974468F4CECFA874BE966CDE61 (void);
// 0x0000015D System.Void UnityEngine.TextCore.Text.MeshInfo::ResizeMeshInfo(System.Int32)
extern void MeshInfo_ResizeMeshInfo_mE411FE40935FB9CFB7C334B3A1F216A98B96F5FC (void);
// 0x0000015E System.Void UnityEngine.TextCore.Text.MeshInfo::Clear(System.Boolean)
extern void MeshInfo_Clear_m06992FEB7AC9B2AE1728BEDFC8D8A39DE1AAD475 (void);
// 0x0000015F System.Void UnityEngine.TextCore.Text.MeshInfo::ClearUnusedVertices()
extern void MeshInfo_ClearUnusedVertices_m7B6003EF4CA72C0ABBA4D25DEA8B0BF3934B2830 (void);
// 0x00000160 System.Void UnityEngine.TextCore.Text.MeshInfo::SortGeometry(UnityEngine.TextCore.Text.VertexSortingOrder)
extern void MeshInfo_SortGeometry_m92046C53AA6AE75EE3627CE73846296AB3E99DD1 (void);
// 0x00000161 System.Void UnityEngine.TextCore.Text.MeshInfo::SwapVertexData(System.Int32,System.Int32)
extern void MeshInfo_SwapVertexData_mD145A4C2B45422DFCB2D9697956E16395E717C7F (void);
// 0x00000162 System.Void UnityEngine.TextCore.Text.MeshInfo::.cctor()
extern void MeshInfo__cctor_m667601EA787620405D7DB5F0B37E4D489DE067D9 (void);
// 0x00000163 UnityEngine.TextCore.Text.Character UnityEngine.TextCore.Text.FontAssetUtilities::GetCharacterFromFontAsset(System.UInt32,UnityEngine.TextCore.Text.FontAsset,System.Boolean,UnityEngine.TextCore.Text.FontStyles,UnityEngine.TextCore.Text.TextFontWeight,System.Boolean&)
extern void FontAssetUtilities_GetCharacterFromFontAsset_m0F073D15EC39A1D4F302F02A5E2F583F28889332 (void);
// 0x00000164 UnityEngine.TextCore.Text.Character UnityEngine.TextCore.Text.FontAssetUtilities::GetCharacterFromFontAsset_Internal(System.UInt32,UnityEngine.TextCore.Text.FontAsset,System.Boolean,UnityEngine.TextCore.Text.FontStyles,UnityEngine.TextCore.Text.TextFontWeight,System.Boolean&)
extern void FontAssetUtilities_GetCharacterFromFontAsset_Internal_m3D8D41600A318422D89103D3839B5CFCF15A956E (void);
// 0x00000165 UnityEngine.TextCore.Text.Character UnityEngine.TextCore.Text.FontAssetUtilities::GetCharacterFromFontAssets(System.UInt32,UnityEngine.TextCore.Text.FontAsset,System.Collections.Generic.List`1<UnityEngine.TextCore.Text.FontAsset>,System.Boolean,UnityEngine.TextCore.Text.FontStyles,UnityEngine.TextCore.Text.TextFontWeight,System.Boolean&)
extern void FontAssetUtilities_GetCharacterFromFontAssets_mB26999A2C8D9AD3D35857403DD59BEED6D008BA0 (void);
// 0x00000166 UnityEngine.TextCore.Text.SpriteCharacter UnityEngine.TextCore.Text.FontAssetUtilities::GetSpriteCharacterFromSpriteAsset(System.UInt32,UnityEngine.TextCore.Text.SpriteAsset,System.Boolean)
extern void FontAssetUtilities_GetSpriteCharacterFromSpriteAsset_mB9147480CA9223089D54748F37F227BC180D78E1 (void);
// 0x00000167 UnityEngine.TextCore.Text.SpriteCharacter UnityEngine.TextCore.Text.FontAssetUtilities::GetSpriteCharacterFromSpriteAsset_Internal(System.UInt32,UnityEngine.TextCore.Text.SpriteAsset,System.Boolean)
extern void FontAssetUtilities_GetSpriteCharacterFromSpriteAsset_Internal_m5A04BE23599904FF0B23061BC9F7EDBF6AC7DB88 (void);
// 0x00000168 UnityEngine.Shader UnityEngine.TextCore.Text.TextShaderUtilities::get_ShaderRef_MobileSDF()
extern void TextShaderUtilities_get_ShaderRef_MobileSDF_m9014F695545914ADF74FFD8A461B1DD2590A163B (void);
// 0x00000169 UnityEngine.Shader UnityEngine.TextCore.Text.TextShaderUtilities::get_ShaderRef_MobileBitmap()
extern void TextShaderUtilities_get_ShaderRef_MobileBitmap_m54CAA31B3B374C0517BF356C80B34F169C89F649 (void);
// 0x0000016A System.Void UnityEngine.TextCore.Text.TextShaderUtilities::.cctor()
extern void TextShaderUtilities__cctor_m5E3AAC923C6500CFCB7F775610B085AD43CDE4EE (void);
// 0x0000016B System.Void UnityEngine.TextCore.Text.TextShaderUtilities::GetShaderPropertyIDs()
extern void TextShaderUtilities_GetShaderPropertyIDs_mBA841B8CACE147593703FF6E7E2804FF2017DE52 (void);
// 0x0000016C System.Void UnityEngine.TextCore.Text.TextShaderUtilities::UpdateShaderRatios(UnityEngine.Material)
extern void TextShaderUtilities_UpdateShaderRatios_m5DFBADF0AABA261FB14F1431E82340B13D38F110 (void);
// 0x0000016D System.Boolean UnityEngine.TextCore.Text.TextShaderUtilities::IsMaskingEnabled(UnityEngine.Material)
extern void TextShaderUtilities_IsMaskingEnabled_m5613AC55B82463CB01B7983743EF5A4518490331 (void);
// 0x0000016E System.Single UnityEngine.TextCore.Text.TextShaderUtilities::GetPadding(UnityEngine.Material,System.Boolean,System.Boolean)
extern void TextShaderUtilities_GetPadding_mB8AB51D48DC021C3446D30408B4515B16E3BFA5F (void);
// 0x0000016F System.Void UnityEngine.TextCore.Text.LinkInfo::SetLinkId(System.Char[],System.Int32,System.Int32)
extern void LinkInfo_SetLinkId_mB4145264190D5C857705261CB27F87C6E10C3F3F (void);
// 0x00000170 UnityEngine.Material UnityEngine.TextCore.Text.MaterialManager::GetFallbackMaterial(UnityEngine.Material,UnityEngine.Material)
extern void MaterialManager_GetFallbackMaterial_m10F67CE1AE1E0B9D8BA8AFC06110FADA2404B5A1 (void);
// 0x00000171 UnityEngine.Material UnityEngine.TextCore.Text.MaterialManager::GetFallbackMaterial(UnityEngine.TextCore.Text.FontAsset,UnityEngine.Material,System.Int32)
extern void MaterialManager_GetFallbackMaterial_mB65C8D7625B0D0A0D623FC6AC545469AF6B7724D (void);
// 0x00000172 System.Void UnityEngine.TextCore.Text.MaterialManager::.cctor()
extern void MaterialManager__cctor_mB6337D5678E1F45035C1F8EE33D389AB4CBA6527 (void);
static Il2CppMethodPointer s_methodPointers[370] = 
{
	UnicodeLineBreakingRules_get_leadingCharactersLookup_m1DAC015D7E37112EAE0437E6472AEA0719DFF3DC,
	UnicodeLineBreakingRules_get_followingCharactersLookup_m5510A21873DC5DA66F4A2DFA4C26A5EFAD494D8B,
	UnicodeLineBreakingRules_LoadLineBreakingRules_mD9380F38BF762469AE42BA1255526A610AC143AD,
	UnicodeLineBreakingRules_GetCharacters_m93663235F54D32D801E67994714B79759D2F4137,
	UnicodeLineBreakingRules__ctor_mD94779B4996B56EA84C847EC4DD287AB1A8ADE85,
	UnicodeLineBreakingRules__cctor_m401C164CCBE1ED299AACF899F8535BC6E50CFC03,
	TextInfo__ctor_m241E24715CC5F6293DC90A4D25884548BAD0D602,
	TextInfo_Clear_m60412774208F9D920707448E71E89C99233D9128,
	TextInfo_ClearMeshInfo_mCA598F01C7F302CFCD0F508E2DBF072E66CA74F3,
	TextInfo_ClearLineInfo_m986C886D34A324C8C4D30F9D8EF24AC242A10AD7,
	NULL,
	NULL,
	TextInfo__cctor_m0B33CCC02D4B32FB18864B6B7657695E9AEB3F0D,
	TextStyle_get_hashCode_mA1F4D3630B6AE71C2A31F94B7054C28BDD96084F,
	TextStyle_get_styleOpeningTagArray_m32B909EFDDBCFFECC7D1F4D551DCB520A72240EA,
	TextStyle_get_styleClosingTagArray_mB5F9A0BD01EF3E6ABFF44311ADF2A1511D838033,
	TextStyle_RefreshStyle_m2D0771408F06C24EF303749ED8E656C800575BDB,
	Character__ctor_m5DCCE862D40487C733C29A233DB8513A9A6A02F6,
	Character__ctor_mEEAC42D4227E0053C8008C12B222CC208D781795,
	Character__ctor_m21FBFAF1F6324565246096EFFB81C3F9E15D43CC,
	MaterialReference__ctor_m044AAA2C1079EB25A5534A6E0FA2314F033DB15A,
	MaterialReference_AddMaterialReference_m13CC47A7CA6C8781EA68A355B36FCD5AFF467A40,
	MaterialReference_AddMaterialReference_mEE57C2284E5BE17BDD80C69165FAECB3B4CC1BCD,
	FontAsset_get_sourceFontFile_m6B0E805BD1B7712F0B5135D157E96F3F40314830,
	FontAsset_set_sourceFontFile_m2E6D2AED5E5D2585A09E9BF830387DEB10A2F4E8,
	FontAsset_get_atlasPopulationMode_m5364C5A9E84969D8E4FF8436BD18A3F10BF90366,
	FontAsset_set_atlasPopulationMode_m1A9DD5C702ED0924B9C208F4CE5ADEACF9188268,
	FontAsset_get_faceInfo_mF020EC579E3C18A6279D55D86AF1C585031B49A9,
	FontAsset_set_faceInfo_mCCA87B67C4CA2C0A1F6D85FB1FAA09667996EDCD,
	FontAsset_get_familyNameHashCode_mF2DB211A5712A291B2D28FCDB7F7C29057770330,
	FontAsset_set_familyNameHashCode_mE1495199BCE7B771CC920E2DBB86A8AF1518CB55,
	FontAsset_get_styleNameHashCode_m3CD3D77F64DAEB31D8F69E4D7CC1AD0AC784ABF5,
	FontAsset_set_styleNameHashCode_mE1BE5B75DE1E9EA0F76569609E6C4FFDC57558BA,
	FontAsset_get_fontWeightTable_m8DADE0BCE53D53EEECE27025F7E94FDD4BF00099,
	FontAsset_set_fontWeightTable_m39D8B63BD3FCC773AAB5634C6D9314C713161814,
	FontAsset_get_glyphTable_m212E940F74AEE62ACBB3374486296CA518D934B5,
	FontAsset_set_glyphTable_m2753BC6CEE011983C2B4B181867C3EB00CDE87D4,
	FontAsset_get_glyphLookupTable_mD04A90D8262F1963EDC472272B67BBFAF73DEEA5,
	FontAsset_get_characterTable_mC77FAE1355269834F7C8A2D46AFB4BFE7B7AD72D,
	FontAsset_set_characterTable_m44703292F669F2F6D4920EB9427077E24FB1512C,
	FontAsset_get_characterLookupTable_m7E76D6C706C5CEB04A9541C68AE6D9E5C75F0FFC,
	FontAsset_get_atlasTexture_mC49216F40093C7AC4FA5DA68F9F5C9FCC83B8F27,
	FontAsset_get_atlasTextures_mADD7A506F0444A1EE4F1D52536B0C5DA9BE35075,
	FontAsset_set_atlasTextures_m5315BA4903B77742EFA1E54CEA2AF12726B10A99,
	FontAsset_get_atlasTextureCount_mC20300C53E52D7A8351DE296BAD565268568119F,
	FontAsset_get_isMultiAtlasTexturesEnabled_mF222228A76102BB0EA593A60439D22F912761F1E,
	FontAsset_set_isMultiAtlasTexturesEnabled_mA470AF6D312989E752C68DC0FD5700235877566B,
	FontAsset_get_clearDynamicDataOnBuild_mC1F714E56F087B29E0A3B43D820EDCEB78E9EE75,
	FontAsset_set_clearDynamicDataOnBuild_mA1C6F298742DF78EC0F81157F0E04246F8B82F7E,
	FontAsset_get_atlasWidth_mE711550FDD4B5F988B77AB5D332A80A34B5CF364,
	FontAsset_set_atlasWidth_mFFB9D37EB1C648384ED1426B42E26A4104D329B1,
	FontAsset_get_atlasHeight_m306FBF7D35C39123A4770E147FAF95B1B8DE8086,
	FontAsset_set_atlasHeight_m7116DFD32F38971CE39D7F4BF84CB5217DCAA2B5,
	FontAsset_get_atlasPadding_m251A35FB5F499EE66CC2E2150CBEDB2C8C5D5581,
	FontAsset_set_atlasPadding_mB34AA836A3D02722ABED71B3583D767560CA956D,
	FontAsset_get_atlasRenderMode_m036D4BA220E5D4B0C407CA6BC1B09D8914B5058A,
	FontAsset_set_atlasRenderMode_m993764193CE75D57DC4CC755336596681A7866D2,
	FontAsset_get_usedGlyphRects_mE039AEF3AE45A15A86B2C0F774E6ED58AFA2F341,
	FontAsset_set_usedGlyphRects_mBF80C1063C0A274AD95F55C43DA734E126F6643F,
	FontAsset_get_freeGlyphRects_mCDAEF0519586C5248BBEDEAA85086CC117903E88,
	FontAsset_set_freeGlyphRects_mED3C0E01ABFC63CE700C701476BA2B66D112AA9B,
	FontAsset_get_fontFeatureTable_m7C4EB9A655B237CE02FAF7B8B16C2F2863FE5070,
	FontAsset_set_fontFeatureTable_m3FD11B99122416777808E1CE5414D7BA40920C3B,
	FontAsset_get_fallbackFontAssetTable_m43303BFBE8A8C55D8CE8A67C47EFAFF5A712CB69,
	FontAsset_set_fallbackFontAssetTable_mE22C3D2323111FD6A5DF9847FB812BD41E18832E,
	FontAsset_get_fontAssetCreationEditorSettings_m024033F91B976A8EAA5CBE67D3DB2A756B91CF01,
	FontAsset_set_fontAssetCreationEditorSettings_mF7EE6A46807D78A7E99872171D2AD774DE20C7EB,
	FontAsset_get_regularStyleWeight_m6C4B4D4CAD36800E6E686A05A5DB8D4475F2707F,
	FontAsset_set_regularStyleWeight_m2D1E5440A5E1794A003FF087A87393DA9A114385,
	FontAsset_get_regularStyleSpacing_mB7EEEA236312F5AC31FD3B787808279206F521B1,
	FontAsset_set_regularStyleSpacing_m7CCE54FB9163D65A6B40269B2BDD30199023E797,
	FontAsset_get_boldStyleWeight_m804ACC85DD80DC72DB4BCC83C3FB866411F8EFCA,
	FontAsset_set_boldStyleWeight_m204B04CF9E98AD8669025BFDC0EF3CE9AB5CBBA2,
	FontAsset_get_boldStyleSpacing_mB8CF4F4880B110E41D566648FF1D995010CF1FF0,
	FontAsset_set_boldStyleSpacing_m62DAA35837E8563DD76E3D162B6DB59BE3804914,
	FontAsset_get_italicStyleSlant_m69E70060C6E7940B4ACE61F2B7CB8965F86DA96B,
	FontAsset_set_italicStyleSlant_m223875ED81B0397CA36E94A6F346AEE68510C0D2,
	FontAsset_get_tabMultiple_m9C0422A00BFCF82091F14F4E303E2717247350AE,
	FontAsset_set_tabMultiple_mC927B74D27FBB94245E09FE39D9F6749AF07017B,
	FontAsset_CreateFontAsset_mDBAB2C51721B94702B8706450DAF74FD2E757712,
	FontAsset_CreateFontAsset_m5C2993AF8A6DB979E34173276952C0DD70524777,
	FontAsset_CreateFontAsset_m627EE2CFBDCEADB906A5CD26FB474D3C1A37A166,
	FontAsset_CreateFontAsset_mEFD731F35B521BB833F532C0EE7637F97647E650,
	FontAsset_CreateFontAsset_mF5E82AB887021B02F7DA71E36328A9D1C943F1B5,
	FontAsset_CreateFontAssetInstance_m9114A363FC6DDEB899CC75C73A41CF903A59333E,
	FontAsset_Awake_mB7906A1E21F5FAB84A023B97435F75C09EAB92ED,
	FontAsset_OnDestroy_m3587016A089072C5C03168AA4C6AA1956FE12785,
	FontAsset_ReadFontAssetDefinition_m6D84DBCB130D530B2F78A7E24232D8A6A81AEC48,
	FontAsset_InitializeDictionaryLookupTables_m29A4AEF49CF11A0E49C229EF13B2262AE66757FF,
	FontAsset_InitializeGlyphLookupDictionary_m82782B7B5C602AD5097A016BF668868C0892CCF6,
	FontAsset_InitializeCharacterLookupDictionary_m8886A4CA911334BD319AB78F1CBBD68E13624BB6,
	FontAsset_InitializeGlyphPaidAdjustmentRecordsLookupDictionary_mDF1C20792344999B9CF500685E6256B0642CE7DD,
	FontAsset_AddSynthesizedCharactersAndFaceMetrics_m203BD62D0A537A6EA7CD7DBA1FF9A94301492933,
	FontAsset_AddSynthesizedCharacter_m6ABFCE6454A09D5CF7914F318DDC79198C47F9EA,
	FontAsset_AddCharacterToLookupCache_mB90E06CE313CC0BB6F81415BF8FB4E043108EED8,
	FontAsset_LoadFontFace_m64C78A2FE5DA2E7029E43B467A1B242827B45B4F,
	FontAsset_SortCharacterTable_m9A551DF3B19E246E8C4BE86463E0ED1DEB27D321,
	FontAsset_SortGlyphTable_mC853714CB002D923A19C3A925BB24D6BF42A08CD,
	FontAsset_SortFontFeatureTable_m072B32D6D8C562F60D3D6CBCC7DCB3282EDD587F,
	FontAsset_SortAllTables_mACA7063865A460F5949E5B8A8D978D588124A094,
	FontAsset_HasCharacter_m6BAF48714E1BF5D8EE7ACF33F774C8C6EEE452F3,
	FontAsset_HasCharacter_mE87EEF6CDA1F4E1D6928CC9A3C01A91922D4FB21,
	FontAsset_HasCharacter_Internal_mDC0D2954E0975A7DBC8829E894CDBCABEA7D6A60,
	FontAsset_HasCharacters_mD670CCEB48448CE5C1430B938F99D4FC659FB2F8,
	FontAsset_HasCharacters_m97A50BC627C163418CAE0B42A50893057B025E90,
	FontAsset_HasCharacters_m048839FDD1876CDCA3C5A744592545B46C75E15B,
	FontAsset_GetCharacters_m5CCEC5A0F89B0BC29B1EFEE9E33923F6879409F4,
	FontAsset_GetCharactersArray_m4CE08FECFCF3F9913B5B29DF0F2272FA4B3F945F,
	FontAsset_GetGlyphIndex_mF20097CDB68A8CE866E61D4C237FBB95257A9745,
	FontAsset_RegisterFontAssetForFontFeatureUpdate_m5D26FB74AB0C3AA8DC363DA517C1017641F85739,
	FontAsset_UpdateFontFeaturesForFontAssetsInQueue_m021524DE2B5F8ABE9DDA58447F896159EA15E0F3,
	FontAsset_RegisterAtlasTextureForApply_mAB5DC9E4B77C8E0FCADF0AD98E8D63C5229FC383,
	FontAsset_UpdateAtlasTexturesInQueue_m359EFCCB6F692F5F8546D4205F366B079075792E,
	FontAsset_UpdateFontAssetInUpdateQueue_mCF68DCD3B3F71341198DD8F528CC9E0E27071471,
	FontAsset_TryAddCharacters_m7F1D0CB7E4D9B8D3CE44D4D01F9CDCEFD4D1B46B,
	FontAsset_TryAddCharacters_m9618B4F12C004B8267E0D17ED81B94BE48D85119,
	FontAsset_TryAddCharacters_m5E282618D9ED92AD0112BC7B6B2C3B1066DDFA63,
	FontAsset_TryAddCharacters_mDA1C3A68799C84A80C27CDB84482684F6822137F,
	FontAsset_TryAddCharacterInternal_mCDC9AE2C61B9F73B8879C3F5AE00598A67B2BA7F,
	FontAsset_TryGetCharacter_and_QueueRenderToTexture_mA76A244F58E0F2978178FBBEB18F2E0DCA568AEC,
	FontAsset_TryAddGlyphsToAtlasTextures_m83F7EDB3193B3C9A4FA86B89A51E9FA6A41F6834,
	FontAsset_TryAddGlyphsToNewAtlasTexture_m8F98FBF7A0EC1B37C4DB43536DA42D3864F6F3AB,
	FontAsset_SetupNewAtlasTexture_m38F81BE1582A15DDDB950E7AAC650CD9B7D14168,
	FontAsset_UpdateAtlasTexture_m30D7C235A878B1A33F2A3891D8096F534C10F6C5,
	FontAsset_UpdateGlyphAdjustmentRecords_mD1C9297EA75EA767A823709CC39B6E57905E22A3,
	FontAsset_UpdateGlyphAdjustmentRecords_mC9882537E81709FE1EFA9B744D88C6C32ACEDF52,
	FontAsset_UpdateGlyphAdjustmentRecords_m2D0444352012E8DFDD6036025886EC0CED0AD82A,
	FontAsset_UpdateGlyphAdjustmentRecords_m9410421BA99635607C50EED1C9C374570EFABC60,
	NULL,
	FontAsset_ClearFontAssetData_m225ADFCBB0CFD481E18637F3D3FDFFEAFC6FE9A1,
	FontAsset_ClearFontAssetDataInternal_mD3DB3516BB6D61F0A9630D9F6958F474DFE044F7,
	FontAsset_UpdateFontAssetData_mAAC0ED05410942C08E8EFD4678F9565FD8C373D4,
	FontAsset_ClearFontAssetTables_m38470615509BEFAE39C90C234D8B460F05824C39,
	FontAsset_ClearAtlasTextures_m5B320A65E1CD35F2C17E27F09158F8E9BDA9EA2B,
	FontAsset_DestroyAtlasTextures_mBE2810F8C55E286B5B7ABE24A6F9132F51CBE027,
	FontAsset__ctor_mD55676BD025F9D05DBC9A5B32480E092169B9D45,
	FontAsset__cctor_m0F11465E3C2F3D158DB9BF403CE86298432BD00E,
	U3CU3Ec__cctor_mEB4ED309CE67EB2C5633590466BD1C4EE90E0D5C,
	U3CU3Ec__ctor_m3CDF0903C024856278B5A7DF46C7EFCBA6F6B651,
	U3CU3Ec_U3CSortCharacterTableU3Eb__144_0_m5655A423C374C42F5761CFBFBEA41E6566421B0B,
	U3CU3Ec_U3CSortGlyphTableU3Eb__145_0_m2BB897F83BD1FE22634624544AFCD8B5988788A0,
	SpriteAsset_get_faceInfo_m54EC5227F682ED6A24F5633283258E6641CDA4DC,
	SpriteAsset_set_faceInfo_m060A5DBBD5941A53BFE9D45E2B637D88ED8223EA,
	SpriteAsset_get_spriteSheet_mC53205114A12A79F7495FA5F5EFC9948F151256B,
	SpriteAsset_set_spriteSheet_m1DE591615ABCBB4B10118BF4C0E1B57F559C6469,
	SpriteAsset_get_spriteCharacterTable_m8D0D65C430AD8BC8C2BC8151DC4672CC0F690E0A,
	SpriteAsset_set_spriteCharacterTable_m38553B81E01B502CCD568A654E9EF3B0D0BCA92D,
	SpriteAsset_get_spriteCharacterLookupTable_mDB0BA7D703F58A05BB765AE44D6244D98C17DE03,
	SpriteAsset_set_spriteCharacterLookupTable_m38F3E7A0A52B82595C87E6A630B156E4D22F2E25,
	SpriteAsset_get_spriteGlyphTable_m491B3F4A5350C38D8B5166A60B7C43ED3608C0BA,
	SpriteAsset_set_spriteGlyphTable_m7402B7CF195E4B9F15DDA3273F49CA460A68DAD3,
	SpriteAsset_Awake_m3A012935612A7EB924A77B85EDCF6C09257F60BE,
	SpriteAsset_UpdateLookupTables_mCC7A470A65A72908C9CDBDDFD17A056188A5C7CE,
	SpriteAsset_GetSpriteIndexFromHashcode_mE73615D1D9A8BB45C3426197EC54B1A002642DE0,
	SpriteAsset_GetSpriteIndexFromUnicode_m321E02B6000E5F6673F5724155C3EF1DE3F5A66B,
	SpriteAsset_GetSpriteIndexFromName_mBCB684ED6E3DF5663A7FDA02CA69C99D9B17281B,
	SpriteAsset_SearchForSpriteByUnicode_mC50EFD6F2B78609891C357212CF65A8F18EB8A66,
	SpriteAsset_SearchForSpriteByUnicodeInternal_mAA47B4DB58070A7A3F5F97C597098A65E896B5A5,
	SpriteAsset_SearchForSpriteByUnicodeInternal_mEB122A514DF6A0D063EF8BE18F31F278ED9C3518,
	SpriteAsset_SearchForSpriteByHashCode_m8B9CAB0028CC297570785693F2973918B31C9028,
	SpriteAsset_SearchForSpriteByHashCodeInternal_mAD106CFA37AACBD783D0A74817D55507013BBC14,
	SpriteAsset_SearchForSpriteByHashCodeInternal_mD38A7595ACBC7773C8292B0FD7E5A170A4105208,
	SpriteAsset_SortGlyphTable_mA700CE5246D5798FA65779BE53179FFF4FFED6E5,
	SpriteAsset_SortCharacterTable_m5447649977AF2C9F62A14415B44CDDD897A53AE1,
	SpriteAsset_SortGlyphAndCharacterTables_m0E2B691E7C1F284E12A88B47B705307E83C7D927,
	SpriteAsset__ctor_mE03F69799389DE8D90E69CD70054955033C4ED3C,
	U3CU3Ec__cctor_m84A11BA2AE10FB375A960D167ABF5F559DACAE3A,
	U3CU3Ec__ctor_mEFC122BF1D0D0CA8F0EAE9CE353C37A8CFABB5F3,
	U3CU3Ec_U3CSortGlyphTableU3Eb__37_0_mC479CF63F85C34FC407D92E67878B9B2AD99B739,
	U3CU3Ec_U3CSortCharacterTableU3Eb__38_0_m6A3F26D4286DF4F04DC23D23D04E12CA014D6E92,
	Extents_ToString_m8A1F748127EE9CCCD6FFAF4CE1F38E37C07831AC,
	FontFeatureTable_get_glyphPairAdjustmentRecords_mABE78F7C2EA171927CC33170617D72E6C976323E,
	FontFeatureTable__ctor_m5F00F284C63F1867F679A3250ABFC1393C27025C,
	FontFeatureTable_SortGlyphPairAdjustmentRecords_m2F5E2ED405FCAEE946CE5CF81163DDCC1B02A905,
	U3CU3Ec__cctor_m024C5AAB034EF6BBF574EA4EB3AE66D03E259CEA,
	U3CU3Ec__ctor_m51815D1379A3BDB616D65C006DA7AB32406723F4,
	U3CU3Ec_U3CSortGlyphPairAdjustmentRecordsU3Eb__6_0_m4A2B8B22BA5C619432654E7A0F5DEDAE949E12BA,
	U3CU3Ec_U3CSortGlyphPairAdjustmentRecordsU3Eb__6_1_m5BF29DF1AB0CBEC9E1D73F20A998CBEC05DC7572,
	TextGeneratorUtilities_Approximately_m696ABB909732F536F1FF83EA8CE34CF53266794D,
	TextGeneratorUtilities_HexCharsToColor_m2C739FBEC67C612B593FDF344E5875F0C0D8AC31,
	TextGeneratorUtilities_HexCharsToColor_m4D7AB2E490DA31C514A42A880AB35CE9CBAF77B4,
	TextGeneratorUtilities_HexToInt_m3017B82A336E3DB4B577D95AF6ADD393000AEB63,
	TextGeneratorUtilities_ConvertToFloat_m93B85749154AAFB614CD246876786B75C98AE11C,
	TextGeneratorUtilities_ConvertToFloat_m37E1CF11E22ED6528B166907211426EEE6D0A4F8,
	TextGeneratorUtilities_PackUV_mE110A97960725C40D87FA903B63E0100AFCB06F5,
	TextGeneratorUtilities_StringToCharArray_m54D1AB72DAC31ADF5CF5EFD69E8B3BA5E2DCD1F5,
	NULL,
	TextGeneratorUtilities_IsTagName_m67178B96C0B3119FFEA83A8ACED7D7DAD0414208,
	TextGeneratorUtilities_IsTagName_mA8FA492104AD554B93FD6FDAC0D67416ED746A9D,
	TextGeneratorUtilities_ReplaceOpeningStyleTag_m4559E128C7101E4C355B98D3E10A00E603FA9140,
	TextGeneratorUtilities_ReplaceOpeningStyleTag_mE66FFFF690CBFBFAAE2982103CF0D6211CFFA5A6,
	TextGeneratorUtilities_ReplaceClosingStyleTag_m3659E196D25EB735F5F7D53B7B5FED3DC211CC48,
	TextGeneratorUtilities_GetStyle_m236E7C5EBFA951D1D4B2C3C7803BBEA5C2FD1812,
	TextGeneratorUtilities_GetUtf32_mD4E8A09D5232957490A6B6D2001867D2814CFBDB,
	TextGeneratorUtilities_GetUtf16_mCCED0D4CAB31E7C9ADB22A3546D1C209FA2C3E13,
	TextGeneratorUtilities_GetTagHashCode_m89243960CD468B3DA8EC0AC29829FD8125056D9F,
	TextGeneratorUtilities_GetTagHashCode_m52AED809412409E8D9D9AD9D4DDDF0990A0049CE,
	TextGeneratorUtilities_FillCharacterVertexBuffers_m54CA97C6C26BA84BC949845B20E9DADF2F0C19CA,
	TextGeneratorUtilities_FillSpriteVertexBuffers_m4305B80FA32FE21A59AF68A5501226E5A4203CC3,
	TextGeneratorUtilities_AdjustLineOffset_m811C187EA3E41781116F0C7A679B05BB27874123,
	TextGeneratorUtilities_ResizeLineExtents_m2EA9BE32A38D5E075DEF8EDA9EC01766E45C0F85,
	TextGeneratorUtilities_LegacyStyleToNewStyle_m8E9C09A7BE7B27A4EC73ADD747BFD9A4DE7E656C,
	TextGeneratorUtilities_LegacyAlignmentToNewAlignment_mA16F5CB58D3C8F352CAACB8DACA5AFED59D3ED63,
	TextGeneratorUtilities__cctor_m01201F8A0A4161B232AB9D179AB74877D4E17D85,
	FontStyleStack_Clear_m989659363648B27540168E46F23E1EF9877C06E0,
	FontStyleStack_Add_m26E701C9F052EEEBB213B9B8BC6CB8F1F8F6AFCB,
	FontStyleStack_Remove_mC2B4F44A6596E92D6992DBCA298648F8A7416CAB,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	ColorUtilities_CompareColors_m0F0F140129DEE889FB8AE3B2921C495E94B5E875,
	ColorUtilities_MultiplyColors_m81D3E41A86E195794977F31C5A944DB40FE734B4,
	SpriteCharacter_get_name_mD5A9CC908308BB48D459973C8844FE1FD7C925B1,
	SpriteCharacter__ctor_m0B3812DF9A667CA2C3AA321DF3403197EEBC83BA,
	TextUtilities_ToUpperFast_m7E98D5D17030A387012E262648346523DCC3AA0B,
	TextUtilities_ToUpperASCIIFast_m48A8B61739F9D5E8CB46B108746277DAF38AB58C,
	TextUtilities_GetHashCodeCaseInSensitive_m3241BDD3A6F82A061C21BF5D347B690D732F5B59,
	TextUtilities_UintToString_m7C0ECB6D2370EC4275FE0E70FB979CADA55A6216,
	TextElement_get_elementType_m7BF97842479112227C1C3C83E0E94A176CD7D31A,
	TextElement_get_unicode_m40C69806537940F7BA1D3969713DA10CCBE57BC7,
	TextElement_set_unicode_m99608D824B25E3529236C06BCC0983B5FC094F98,
	TextElement_get_textAsset_m52383A3758AABF5BEA013155765BD1141479685A,
	TextElement_set_textAsset_m3F65429660C011F6F25B65D6BA7C4B2CF05659FA,
	TextElement_get_glyph_m101DBCCA0CDE2461B504174272A2FFCD53EA59E2,
	TextElement_set_glyph_m6E8E2F1366089FA638680F1CF53F6F5027D022A5,
	TextElement_get_glyphIndex_m43F82F2F998D640DEDBE6860EBE7B171DDF4FE56,
	TextElement_set_glyphIndex_mFD72B93816998BE291DA6379EAF5E4153BC64F6C,
	TextElement_get_scale_mD16946900449FEE9E2F86B2C4C71E26F4491A0E6,
	TextElement_set_scale_m83FC0850B2B0F31BDFC779954923F50FD06DC03F,
	TextElement__ctor_m8CB701D9A4C0444E834F178D97E9FC63E6D7E0B9,
	TextStyleSheet_get_styles_m7916C62D70AA430314D85EA5B5A778FFAE1544DB,
	TextStyleSheet_GetStyle_m648B766D750E1B37DD126918BF7EB22DDFD21D29,
	TextStyleSheet_GetStyle_mF87D8108EEF60C3FC32A2B01D5C1C23D3F22A4D1,
	TextStyleSheet_RefreshStyles_m92525DE6F7951D03D906E18EC89349AA2794AEC5,
	TextStyleSheet_LoadStyleDictionaryInternal_m482E4CDF0CD2ED291F85869E141EEB5EC21F6D81,
	TextStyleSheet__ctor_m1A0B93B55B12CE15FFF1053443179BD010022B75,
	MaterialReferenceManager_get_instance_m30BD1A367C3807D0B42CF7156CE699B15E51ED07,
	MaterialReferenceManager_AddFontAsset_mF950087FC7EAA324CC15D3E19D62138A96F6FDF7,
	MaterialReferenceManager_AddFontAssetInternal_m6F5A4E5ED988BA6F482F015F051ACD19D7B0A005,
	MaterialReferenceManager_AddSpriteAsset_m7FBAAC41386CFA769FDDFF1630D041C4D008A322,
	MaterialReferenceManager_AddSpriteAssetInternal_m788619DC6BAD5B77E9419ACBDECBCCFE1A6AC97C,
	MaterialReferenceManager_AddFontMaterial_m76DC9390D0599D1AC898AE981C8756EA0E536AA6,
	MaterialReferenceManager_AddFontMaterialInternal_m732F46EF768A41B9519917F4FA1E746E056C745C,
	MaterialReferenceManager_AddColorGradientPreset_m1496DD123DB526F2B3F49DD3BCAED78A6B1B83EB,
	MaterialReferenceManager_AddColorGradientPreset_Internal_mF27270501EB3725B4CBE4C241B4A2FCD8D871BF1,
	MaterialReferenceManager_TryGetFontAsset_m499C7538909343667E9B837489662862CFF9FB56,
	MaterialReferenceManager_TryGetFontAssetInternal_m2FECC618624B12D200EB311F59CBEECA7CDBB69D,
	MaterialReferenceManager_TryGetSpriteAsset_m1909F75399C0A23DDE7DEDCA60476E1F1ED567EF,
	MaterialReferenceManager_TryGetSpriteAssetInternal_mC434A7C6DB005EDBBA52154E2AB0E36ED7083C84,
	MaterialReferenceManager_TryGetColorGradientPreset_m7442603626A04928C6C042BDAC3D3957B8C0AA99,
	MaterialReferenceManager_TryGetColorGradientPresetInternal_mD8018B3225786E71F804D629F3107AB75EE5212B,
	MaterialReferenceManager_TryGetMaterial_mDC2610737935CD2DE6B1F6302F63C2F3BF7E09CF,
	MaterialReferenceManager_TryGetMaterialInternal_mEBFC9CE0A6063B25FEA9070F22FD8AD27107ECE7,
	MaterialReferenceManager__ctor_mC102EC445A27BE8E3968ADB80EF8FEF3BCFB7778,
	TextColorGradient__ctor_mAF03C3B68C29D94ED1F1517E65477CFC2B078FAC,
	TextColorGradient__ctor_m0712D0C09E7BCBC00909CEB71B43F2276ADB8B55,
	TextColorGradient__ctor_m0A6B9264A8FBABEEC5BA84353D9B3DD7999DD2FF,
	TextColorGradient__cctor_mC5AE96BE4461EF9AE84CE4E3765B712D6A64693E,
	TextGenerationSettings_Equals_mA5EDDF0453F2A7314AF5E1FE29F4138CD97E52D5,
	TextGenerationSettings_Equals_mDCEEB056B70FC65ED6065E3BFE8D69D823DFEFD0,
	TextGenerationSettings_GetHashCode_m1F750434FCE1853C36A579827B064619E85453E0,
	TextGenerationSettings_ToString_mF8AB635EB369C7F66AC3B396934158CF3B6BA6CE,
	TextGenerationSettings__ctor_mA20608A16443434DAE9FEF0BF8BD076270FA660E,
	TextGenerator_GetTextGenerator_m5BDD6657637032A944115A1D6D52A6D511D43D46,
	TextGenerator_GenerateText_m28C6EED85E5BB42AA40812F475D533AAF6694757,
	TextGenerator_GetCursorPosition_m9F767EE74114971780EF08619DEE0F5223FC5095,
	TextGenerator_GetPreferredValues_m17A1C8F1AA7D260AB9167985429D6819D0E8D9CA,
	TextGenerator_Prepare_mD0A24977334138340CA73FB9787627373C6AA255,
	TextGenerator_GenerateTextMesh_mAB70FC29A49A6C4F8211EA977E37C66BE67D1831,
	TextGenerator_SaveWordWrappingState_mC07B2C5977EECE10216F8C6AC9CC4204F7EF1936,
	TextGenerator_RestoreWordWrappingState_mA63B3DD2C02E61CD8670A32A53163AF6BF765F61,
	TextGenerator_ValidateHtmlTag_m9C85462F15A6165B10E4C4EE93620AC1021BE5CD,
	TextGenerator_SaveGlyphVertexInfo_m0CD6E1D45488FFC6675294AC64F40AC23C986A09,
	TextGenerator_SaveSpriteVertexInfo_m4B47901F01927E7CC4E486A1C4354AFBF4D138A5,
	TextGenerator_DrawUnderlineMesh_m7BA49F01C2BC1BEF7845A3D8487B45F15A3BB20E,
	TextGenerator_DrawTextHighlight_m3A8E9A72C0984B5DEEF9858060675F3B517F701B,
	TextGenerator_ClearMesh_m68BA46B0365FC730BA5D2E6BDF2528BD370B2D83,
	TextGenerator_EnableMasking_mB38D92D32518523DE33A2FCD85A67DE481BB0991,
	TextGenerator_DisableMasking_mBDE8E47000367F45FC907243C845A11DBDD89950,
	TextGenerator_SetArraySizes_mF0041F3D79936C05EB87DEE399F1DC389CCD1BD5,
	TextGenerator_GetTextElement_mC46F0E788A0F6EB5A62601BCE4F383C3143C78CB,
	TextGenerator_ComputeMarginSize_m485F8B01196058B15F597DE99D6F6A47FA539D3F,
	TextGenerator_GetSpecialCharacters_mA82879FA537C58223BB660E797AC135A8E07B492,
	TextGenerator_GetEllipsisSpecialCharacter_m5139CAE03CD2E25C9A528A6A6FC984A8515C2460,
	TextGenerator_GetUnderlineSpecialCharacter_mE5E9D5DEB9A7758333CDDCAD05EF25F076EC1AD5,
	TextGenerator_GetPaddingForMaterial_mE5A4DEF3F64851861C092F7A4FC58C902F775C74,
	TextGenerator_GetPreferredValuesInternal_m125B070164DFEA503C67525D1F418DAF41300ABD,
	TextGenerator_CalculatePreferredValues_mBBE23FA780CF24415963F32F7C500F0394C2545E,
	TextGenerator__ctor_m52E4D01DC28BDF753BF52F6501E7FD2FB2B30D90,
	SpecialCharacter__ctor_m6697A8BF272F0144733EE12368C038F45E99F969,
	TextAsset_get_version_m2316C9F212FE4A13335032354A496CAEC86CB25B,
	TextAsset_set_version_m24718A4A6A86F4CE41D9ED1E0F796CCF367516CD,
	TextAsset_get_instanceID_m843A6CAA7FE9322CD19546671D3F0E90A0E27AFB,
	TextAsset_get_hashCode_m4D519E837097D8869A8D38EBD11611FADE411092,
	TextAsset_set_hashCode_mC6ED3271A5EFC05562FD1083BE1C872CB69CFF74,
	TextAsset_get_material_m4B9C02D34426436FDB01F1963A9FDC11D75604EF,
	TextAsset_set_material_mD9988165763E0E72C7FB7537760899EC1841C829,
	TextAsset_get_materialHashCode_m9BAA469D5760D87EEC6E09B9ED3C5902B75017E6,
	TextAsset_set_materialHashCode_m15EE7CCAF81DBAA326049F00788BCC918FDB2631,
	TextAsset__ctor_m2C43EAC2D75854E6724A235794DEAF8A1AF9E786,
	SpriteGlyph__ctor_mFFE2D9AA5A28EA0C20C10694569A1E23A664BB4D,
	TextSettings_get_version_mB5B7C3F04FFC8DCDFCFF63CFFF7612B768E267CB,
	TextSettings_set_version_m7BEE644EEDD77892C295170F9B2B68BC45C7C6CF,
	TextSettings_get_defaultFontAsset_mC6280464BFEE081DB23243BB94E49C72A0885A1F,
	TextSettings_set_defaultFontAsset_mC0137D9AEB9EE7A9E1B3D7C499414EDD217AF3BA,
	TextSettings_get_defaultFontAssetPath_mF7B1713753CFAE048C745C3572332CE18CD51D3B,
	TextSettings_set_defaultFontAssetPath_m35111382F9C2E3C76FA6B77BABEE8149BDDCC7ED,
	TextSettings_get_fallbackFontAssets_m332526E834C994425141A58C968FD40320573F98,
	TextSettings_set_fallbackFontAssets_mEBEC769B2FF339BDB44D72CB48F0010577889772,
	TextSettings_get_matchMaterialPreset_m4675979547AE4C83E680260EAE5ACBC4FAC53B87,
	TextSettings_set_matchMaterialPreset_mD5A3DDE2D93FE2872C869A2AE0B0C984CF5BC12C,
	TextSettings_get_missingCharacterUnicode_mA707E5E6633633BBB3BAFB96B97A5A995100F3F3,
	TextSettings_set_missingCharacterUnicode_mE3E3E2F902BE89B9FE78F03D3B8C508583D1CDA3,
	TextSettings_get_clearDynamicDataOnBuild_m0C130F53F106D77250F38239FB73DCB97299FB79,
	TextSettings_set_clearDynamicDataOnBuild_m86B73E68953D42C6AE4210BEA5D678768155A1AD,
	TextSettings_get_defaultSpriteAsset_m8FA900F9747B7ADBCD2A2F612E7D977DB58D6445,
	TextSettings_set_defaultSpriteAsset_m6083C46D0B3AFB6816363CF2564D2DB2D1ECFEB9,
	TextSettings_get_defaultSpriteAssetPath_mF57875222B7FACC9B69369305EEEB53FFE7986E0,
	TextSettings_set_defaultSpriteAssetPath_m9C49C00A96230DAF828E629C106B229A4552F753,
	TextSettings_get_fallbackSpriteAssets_mC6D74088EBDFDFBACD581FE4AB4EAA447ED242AD,
	TextSettings_set_fallbackSpriteAssets_mB28744780B58AE04BA7F1275508FF7DEE4E485E5,
	TextSettings_get_missingSpriteCharacterUnicode_m496D85B0D88C91F2A5CC9929C233EAA84831523E,
	TextSettings_set_missingSpriteCharacterUnicode_mFE1AFD04C4AA793559E0A157C9E5472E3C3F6701,
	TextSettings_get_defaultStyleSheet_mDA420960556C00405FA66CBD2DA36807F8F4B4F2,
	TextSettings_set_defaultStyleSheet_m2867A0B047E623E4485F10EE717FF0B46BAAECEE,
	TextSettings_get_styleSheetsResourcePath_mE65A055D1C99CA2048BE6F246258EF262E43C4E6,
	TextSettings_set_styleSheetsResourcePath_m19A5C893047D78B43AC5EB43FB9EA79792464C05,
	TextSettings_get_defaultColorGradientPresetsPath_m1571454580E1F0DC859D3F201BB1F6355A5DBB8E,
	TextSettings_set_defaultColorGradientPresetsPath_m951D5A7AE71C87BFA5B9B8CE8EA2F78A474FE01D,
	TextSettings_get_lineBreakingRules_m96E2C32D4F08309D904B0BCD83CEBE8CD6716A04,
	TextSettings_set_lineBreakingRules_mB1174FE0F923443130A3D4686C119EDB0430992D,
	TextSettings_get_displayWarnings_m3CA9FCB44B30CC06F54CD3716D68285FF844DF83,
	TextSettings_set_displayWarnings_m08373EE3900E911B4021CBF8AF915DA69856CC20,
	TextSettings_InitializeFontReferenceLookup_m34B5FB3D61296A7620196398D267812EEFCE0B04,
	TextSettings_GetCachedFontAssetInternal_m9308E0DDEF8F6D8ACA17DAADB43745C860788202,
	TextSettings__ctor_m860D28B10258792A195E1C6391479E16D04CA8BF,
	FontReferenceMap__ctor_mA94127060F9F3FDC3093D9356832503BFBCE44F5,
	TextResourceManager_AddFontAsset_mAAD97609F00F02146B283982E5DE667EB7A692EA,
	TextResourceManager__cctor_m54E40FCFB355A0C90B56366B057F307F75832868,
	FontAssetRef__ctor_m5553FC4D7E51DE97D70CE09E1C99B002A61FFDB5,
	MeshInfo__ctor_mCC2410C5590BEA974468F4CECFA874BE966CDE61,
	MeshInfo_ResizeMeshInfo_mE411FE40935FB9CFB7C334B3A1F216A98B96F5FC,
	MeshInfo_Clear_m06992FEB7AC9B2AE1728BEDFC8D8A39DE1AAD475,
	MeshInfo_ClearUnusedVertices_m7B6003EF4CA72C0ABBA4D25DEA8B0BF3934B2830,
	MeshInfo_SortGeometry_m92046C53AA6AE75EE3627CE73846296AB3E99DD1,
	MeshInfo_SwapVertexData_mD145A4C2B45422DFCB2D9697956E16395E717C7F,
	MeshInfo__cctor_m667601EA787620405D7DB5F0B37E4D489DE067D9,
	FontAssetUtilities_GetCharacterFromFontAsset_m0F073D15EC39A1D4F302F02A5E2F583F28889332,
	FontAssetUtilities_GetCharacterFromFontAsset_Internal_m3D8D41600A318422D89103D3839B5CFCF15A956E,
	FontAssetUtilities_GetCharacterFromFontAssets_mB26999A2C8D9AD3D35857403DD59BEED6D008BA0,
	FontAssetUtilities_GetSpriteCharacterFromSpriteAsset_mB9147480CA9223089D54748F37F227BC180D78E1,
	FontAssetUtilities_GetSpriteCharacterFromSpriteAsset_Internal_m5A04BE23599904FF0B23061BC9F7EDBF6AC7DB88,
	TextShaderUtilities_get_ShaderRef_MobileSDF_m9014F695545914ADF74FFD8A461B1DD2590A163B,
	TextShaderUtilities_get_ShaderRef_MobileBitmap_m54CAA31B3B374C0517BF356C80B34F169C89F649,
	TextShaderUtilities__cctor_m5E3AAC923C6500CFCB7F775610B085AD43CDE4EE,
	TextShaderUtilities_GetShaderPropertyIDs_mBA841B8CACE147593703FF6E7E2804FF2017DE52,
	TextShaderUtilities_UpdateShaderRatios_m5DFBADF0AABA261FB14F1431E82340B13D38F110,
	TextShaderUtilities_IsMaskingEnabled_m5613AC55B82463CB01B7983743EF5A4518490331,
	TextShaderUtilities_GetPadding_mB8AB51D48DC021C3446D30408B4515B16E3BFA5F,
	LinkInfo_SetLinkId_mB4145264190D5C857705261CB27F87C6E10C3F3F,
	MaterialManager_GetFallbackMaterial_m10F67CE1AE1E0B9D8BA8AFC06110FADA2404B5A1,
	MaterialManager_GetFallbackMaterial_mB65C8D7625B0D0A0D623FC6AC545469AF6B7724D,
	MaterialManager__cctor_mB6337D5678E1F45035C1F8EE33D389AB4CBA6527,
};
extern void MaterialReference__ctor_m044AAA2C1079EB25A5534A6E0FA2314F033DB15A_AdjustorThunk (void);
extern void Extents_ToString_m8A1F748127EE9CCCD6FFAF4CE1F38E37C07831AC_AdjustorThunk (void);
extern void FontStyleStack_Clear_m989659363648B27540168E46F23E1EF9877C06E0_AdjustorThunk (void);
extern void FontStyleStack_Add_m26E701C9F052EEEBB213B9B8BC6CB8F1F8F6AFCB_AdjustorThunk (void);
extern void FontStyleStack_Remove_mC2B4F44A6596E92D6992DBCA298648F8A7416CAB_AdjustorThunk (void);
extern void SpecialCharacter__ctor_m6697A8BF272F0144733EE12368C038F45E99F969_AdjustorThunk (void);
extern void FontReferenceMap__ctor_mA94127060F9F3FDC3093D9356832503BFBCE44F5_AdjustorThunk (void);
extern void FontAssetRef__ctor_m5553FC4D7E51DE97D70CE09E1C99B002A61FFDB5_AdjustorThunk (void);
extern void MeshInfo__ctor_mCC2410C5590BEA974468F4CECFA874BE966CDE61_AdjustorThunk (void);
extern void MeshInfo_ResizeMeshInfo_mE411FE40935FB9CFB7C334B3A1F216A98B96F5FC_AdjustorThunk (void);
extern void MeshInfo_Clear_m06992FEB7AC9B2AE1728BEDFC8D8A39DE1AAD475_AdjustorThunk (void);
extern void MeshInfo_ClearUnusedVertices_m7B6003EF4CA72C0ABBA4D25DEA8B0BF3934B2830_AdjustorThunk (void);
extern void MeshInfo_SortGeometry_m92046C53AA6AE75EE3627CE73846296AB3E99DD1_AdjustorThunk (void);
extern void MeshInfo_SwapVertexData_mD145A4C2B45422DFCB2D9697956E16395E717C7F_AdjustorThunk (void);
extern void LinkInfo_SetLinkId_mB4145264190D5C857705261CB27F87C6E10C3F3F_AdjustorThunk (void);
static Il2CppTokenAdjustorThunkPair s_adjustorThunks[15] = 
{
	{ 0x06000015, MaterialReference__ctor_m044AAA2C1079EB25A5534A6E0FA2314F033DB15A_AdjustorThunk },
	{ 0x060000AB, Extents_ToString_m8A1F748127EE9CCCD6FFAF4CE1F38E37C07831AC_AdjustorThunk },
	{ 0x060000CD, FontStyleStack_Clear_m989659363648B27540168E46F23E1EF9877C06E0_AdjustorThunk },
	{ 0x060000CE, FontStyleStack_Add_m26E701C9F052EEEBB213B9B8BC6CB8F1F8F6AFCB_AdjustorThunk },
	{ 0x060000CF, FontStyleStack_Remove_mC2B4F44A6596E92D6992DBCA298648F8A7416CAB_AdjustorThunk },
	{ 0x06000129, SpecialCharacter__ctor_m6697A8BF272F0144733EE12368C038F45E99F969_AdjustorThunk },
	{ 0x06000158, FontReferenceMap__ctor_mA94127060F9F3FDC3093D9356832503BFBCE44F5_AdjustorThunk },
	{ 0x0600015B, FontAssetRef__ctor_m5553FC4D7E51DE97D70CE09E1C99B002A61FFDB5_AdjustorThunk },
	{ 0x0600015C, MeshInfo__ctor_mCC2410C5590BEA974468F4CECFA874BE966CDE61_AdjustorThunk },
	{ 0x0600015D, MeshInfo_ResizeMeshInfo_mE411FE40935FB9CFB7C334B3A1F216A98B96F5FC_AdjustorThunk },
	{ 0x0600015E, MeshInfo_Clear_m06992FEB7AC9B2AE1728BEDFC8D8A39DE1AAD475_AdjustorThunk },
	{ 0x0600015F, MeshInfo_ClearUnusedVertices_m7B6003EF4CA72C0ABBA4D25DEA8B0BF3934B2830_AdjustorThunk },
	{ 0x06000160, MeshInfo_SortGeometry_m92046C53AA6AE75EE3627CE73846296AB3E99DD1_AdjustorThunk },
	{ 0x06000161, MeshInfo_SwapVertexData_mD145A4C2B45422DFCB2D9697956E16395E717C7F_AdjustorThunk },
	{ 0x0600016F, LinkInfo_SetLinkId_mB4145264190D5C857705261CB27F87C6E10C3F3F_AdjustorThunk },
};
static const int32_t s_InvokerIndices[370] = 
{
	6136,
	6136,
	10978,
	9860,
	6263,
	10978,
	6263,
	6263,
	4824,
	6263,
	0,
	0,
	10978,
	6107,
	6136,
	6136,
	6263,
	6263,
	1406,
	2744,
	421,
	7210,
	7210,
	6136,
	4922,
	6107,
	4895,
	6069,
	4857,
	6107,
	4895,
	6107,
	4895,
	6136,
	4922,
	6136,
	4922,
	6136,
	6136,
	4922,
	6136,
	6136,
	6136,
	4922,
	6107,
	6042,
	4824,
	6042,
	4824,
	6107,
	4895,
	6107,
	4895,
	6107,
	4895,
	6107,
	4895,
	6136,
	4922,
	6136,
	4922,
	6136,
	4922,
	6136,
	4922,
	6070,
	4860,
	6195,
	4977,
	6195,
	4977,
	6195,
	4977,
	6195,
	4977,
	6042,
	4824,
	6042,
	4824,
	7825,
	6463,
	9860,
	6499,
	6463,
	6547,
	6263,
	6263,
	6263,
	6263,
	6263,
	6263,
	6263,
	6263,
	1405,
	2742,
	6107,
	6263,
	6263,
	6263,
	6263,
	3425,
	1100,
	1103,
	1588,
	478,
	3452,
	9860,
	9860,
	4531,
	10146,
	10978,
	10146,
	10978,
	10978,
	1590,
	1063,
	1590,
	1063,
	1102,
	1102,
	6263,
	6042,
	6263,
	6263,
	6263,
	4922,
	4922,
	2673,
	0,
	4824,
	6263,
	6263,
	6263,
	4824,
	6263,
	6263,
	10978,
	10978,
	6263,
	4530,
	4530,
	6069,
	4857,
	6136,
	4922,
	6136,
	4922,
	6136,
	4922,
	6136,
	4922,
	6263,
	6263,
	4076,
	4169,
	4097,
	7361,
	7361,
	7361,
	6886,
	7344,
	7344,
	6263,
	6263,
	6263,
	6263,
	10978,
	6263,
	4530,
	4530,
	6136,
	6136,
	6263,
	6263,
	10978,
	6263,
	4528,
	4528,
	8350,
	8406,
	7656,
	9758,
	7870,
	7375,
	7923,
	7454,
	0,
	7601,
	7601,
	6527,
	6527,
	7400,
	8574,
	8488,
	8488,
	7694,
	7694,
	8010,
	8010,
	7441,
	8806,
	9743,
	9743,
	10978,
	6263,
	3425,
	3425,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	8284,
	8404,
	6136,
	6263,
	10007,
	10023,
	9748,
	9860,
	6042,
	6250,
	5024,
	6136,
	4922,
	6136,
	4922,
	6250,
	5024,
	6195,
	4977,
	6263,
	6136,
	4345,
	4351,
	6263,
	6263,
	6263,
	10936,
	10146,
	4922,
	8806,
	2444,
	8806,
	2444,
	8806,
	2444,
	8309,
	1555,
	8309,
	1555,
	8309,
	1555,
	8309,
	1555,
	6263,
	6263,
	4827,
	861,
	10978,
	3452,
	3452,
	6107,
	6136,
	6263,
	10936,
	8860,
	7389,
	8713,
	2673,
	2673,
	828,
	1804,
	238,
	454,
	1263,
	25,
	228,
	8795,
	6263,
	6263,
	1372,
	177,
	2710,
	4922,
	4922,
	4922,
	2103,
	2128,
	395,
	6263,
	2668,
	6136,
	4922,
	6107,
	6107,
	4895,
	6136,
	4922,
	6107,
	4895,
	6263,
	6263,
	6136,
	4922,
	6136,
	4922,
	6136,
	4922,
	6136,
	4922,
	6042,
	4824,
	6107,
	4895,
	6042,
	4824,
	6136,
	4922,
	6136,
	4922,
	6136,
	4922,
	6250,
	5024,
	6136,
	4922,
	6136,
	4922,
	6136,
	4922,
	6136,
	4922,
	6042,
	4824,
	6263,
	4351,
	6263,
	2673,
	10146,
	10978,
	878,
	4895,
	4895,
	4824,
	6263,
	4895,
	2423,
	10978,
	6628,
	6628,
	6554,
	7841,
	7841,
	10936,
	10936,
	10978,
	10978,
	10146,
	9625,
	7869,
	1350,
	8579,
	7825,
	10978,
};
static const Il2CppTokenRangePair s_rgctxIndices[5] = 
{
	{ 0x0200001A, { 8, 2 } },
	{ 0x0600000B, { 0, 1 } },
	{ 0x0600000C, { 1, 1 } },
	{ 0x06000081, { 2, 5 } },
	{ 0x060000BB, { 7, 1 } },
};
extern const uint32_t g_rgctx_Array_Resize_TisT_t17405AE98647F674620A8588A2A38EC17385AEF3_m819D8D61F3D2D33140D1CBF31E6D9A4540919072;
extern const uint32_t g_rgctx_Array_Resize_TisT_tA5CF78E77248D29C8555253A5B104C010B149A3D_m66CF7004011C393E0AFA739A42295579B02C917F;
extern const uint32_t g_rgctx_List_1_tA286387994A1339C2CF844BE5728698C1E998AFA;
extern const uint32_t g_rgctx_List_1_get_Count_m94023DC2882A00FF3E2A87B830A03C35E07FAED7;
extern const uint32_t g_rgctx_TU5BU5D_tDFCEC986C8C5C9107923D8936ADFFA31D6C82631;
extern const uint32_t g_rgctx_Array_Resize_TisT_tC526A9C1F5DF2DFE47235494265CDBAC2F3C7084_mE23A43EFBF8FEFA791C6C9975CB651227B8576A3;
extern const uint32_t g_rgctx_List_1_get_Item_mE8A0A11819707997B5CBE996B1D1A5498DF9FF44;
extern const uint32_t g_rgctx_Array_Resize_TisT_tB4C007A2682984D0FC6913C0705D947ACCDD2686_mB7195CE4A4808328845CB836896D64A709E7B865;
extern const uint32_t g_rgctx_TU5BU5D_tF671EEC26D8310D901066C4094937EF74C4BDB43;
extern const uint32_t g_rgctx_Array_Resize_TisT_t221CFDD3BE5C61800E826B7B924C88413E6CFAD5_m6B12D3B6815FB2F9D9DF2EA23C4DFCA6BF547697;
static const Il2CppRGCTXDefinition s_rgctxValues[10] = 
{
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Array_Resize_TisT_t17405AE98647F674620A8588A2A38EC17385AEF3_m819D8D61F3D2D33140D1CBF31E6D9A4540919072 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Array_Resize_TisT_tA5CF78E77248D29C8555253A5B104C010B149A3D_m66CF7004011C393E0AFA739A42295579B02C917F },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_List_1_tA286387994A1339C2CF844BE5728698C1E998AFA },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1_get_Count_m94023DC2882A00FF3E2A87B830A03C35E07FAED7 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5D_tDFCEC986C8C5C9107923D8936ADFFA31D6C82631 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Array_Resize_TisT_tC526A9C1F5DF2DFE47235494265CDBAC2F3C7084_mE23A43EFBF8FEFA791C6C9975CB651227B8576A3 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1_get_Item_mE8A0A11819707997B5CBE996B1D1A5498DF9FF44 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Array_Resize_TisT_tB4C007A2682984D0FC6913C0705D947ACCDD2686_mB7195CE4A4808328845CB836896D64A709E7B865 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5D_tF671EEC26D8310D901066C4094937EF74C4BDB43 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Array_Resize_TisT_t221CFDD3BE5C61800E826B7B924C88413E6CFAD5_m6B12D3B6815FB2F9D9DF2EA23C4DFCA6BF547697 },
};
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_UnityEngine_TextCoreTextEngineModule_CodeGenModule;
const Il2CppCodeGenModule g_UnityEngine_TextCoreTextEngineModule_CodeGenModule = 
{
	"UnityEngine.TextCoreTextEngineModule.dll",
	370,
	s_methodPointers,
	15,
	s_adjustorThunks,
	s_InvokerIndices,
	0,
	NULL,
	5,
	s_rgctxIndices,
	10,
	s_rgctxValues,
	NULL,
	NULL, // module initializer,
	NULL,
	NULL,
	NULL,
};
