using UnityEngine;

/// <summary>
/// PICO直接输入处理器
/// 不依赖XR Interaction Toolkit，直接使用Unity的Input系统和PICO SDK
/// </summary>
public class PICODirectInputHandler : MonoBehaviour
{
    [Header("组件引用")]
    [SerializeField] private VRAssemblyDebugger debugger;

    [Header("调试设置")]
    [SerializeField] private bool enableDebugLogs = true;
    [SerializeField] private bool enableKeyboardFallback = true;
    [SerializeField] private float inputCooldown = 0.3f;

    [Header("输入映射")]
    [SerializeField] private bool triggerForFunction1 = false;   // 扳机 → 禁用（避免与UI点击冲突）
    [SerializeField] private bool gripForFunction1 = true;       // 握把 → 装配区域定位
    [SerializeField] private bool primaryForFunction2 = true;    // A按钮 → 装配面朝向

    // 输入状态跟踪
    private float lastInputTime = 0f;

    void Start()
    {
        Debug.Log("=== PICO直接输入处理器启动 ===");
        
        if (debugger == null)
        {
            debugger = FindObjectOfType<VRAssemblyDebugger>();
        }

        Debug.Log($"VRAssemblyDebugger: {(debugger != null ? "✅ 已找到" : "❌ 未找到")}");
        Debug.Log("输入映射:");
        Debug.Log("  键盘T/右手扳机 → 装配区域定位");
        Debug.Log("  键盘P/右手握把 → 装配面朝向");
        Debug.Log("  键盘G → 重置位置");
        Debug.Log("================================");
    }

    void Update()
    {
        HandleInput();
    }

    /// <summary>
    /// 处理输入
    /// </summary>
    private void HandleInput()
    {
        // 键盘备用输入
        if (enableKeyboardFallback)
        {
            if (Input.GetKeyDown(KeyCode.T))
            {
                OnTriggerPressed("键盘");
            }
            if (Input.GetKeyDown(KeyCode.P))
            {
                OnGripPressed("键盘");
            }
            if (Input.GetKeyDown(KeyCode.G))
            {
                OnResetPressed("键盘");
            }
        }

        // VR控制器输入 - 使用Unity的通用输入系统
        HandleVRInput();
    }

    /// <summary>
    /// 处理VR输入
    /// </summary>
    private void HandleVRInput()
    {
        // 检查输入冷却时间
        if (Time.time - lastInputTime < inputCooldown)
        {
            return;
        }

        // 方法1：尝试使用Unity的Input系统
        TryUnityInputSystem();

        // 方法2：尝试使用PICO SDK（如果可用）
        TryPICOSDKInput();
    }

    /// <summary>
    /// 尝试使用Unity的Input系统
    /// </summary>
    private void TryUnityInputSystem()
    {
        // Unity的通用VR输入
        try
        {
            // 检查右手控制器的扳机
            if (Input.GetAxis("XRI_Right_Trigger") > 0.8f)
            {
                OnTriggerPressed("Unity输入系统");
                return;
            }

            // 检查右手控制器的握把
            if (Input.GetAxis("XRI_Right_Grip") > 0.8f)
            {
                OnGripPressed("Unity输入系统");
                return;
            }

            // 尝试其他可能的输入轴
            if (Input.GetButtonDown("Fire1"))
            {
                OnTriggerPressed("Fire1按钮");
                return;
            }

            if (Input.GetButtonDown("Fire2"))
            {
                OnGripPressed("Fire2按钮");
                return;
            }
        }
        catch (System.Exception e)
        {
            if (enableDebugLogs)
            {
                Debug.LogWarning($"Unity输入系统检查失败: {e.Message}");
            }
        }
    }

    /// <summary>
    /// 尝试使用PICO SDK输入
    /// </summary>
    private void TryPICOSDKInput()
    {
        // 这里可以添加PICO SDK的直接调用
        // 由于我们看到日志中有PXR_Loader相关信息，说明PICO SDK是可用的
        
        try
        {
            // 尝试使用反射调用PICO SDK
            // 这是一个通用的方法，不需要直接引用PICO SDK
            CheckPICOControllerInput();
        }
        catch (System.Exception e)
        {
            if (enableDebugLogs)
            {
                Debug.LogWarning($"PICO SDK输入检查失败: {e.Message}");
            }
        }
    }

    /// <summary>
    /// 检查PICO控制器输入
    /// </summary>
    private void CheckPICOControllerInput()
    {
        // 尝试通过反射使用PICO SDK
        // 这种方法可以在没有直接引用PICO SDK的情况下工作
        
        var picoControllerType = System.Type.GetType("Unity.XR.PXR.PXR_Input");
        if (picoControllerType != null)
        {
            // 尝试获取PICO控制器状态
            var getTriggerMethod = picoControllerType.GetMethod("GetControllerTriggerValue");
            var getGripMethod = picoControllerType.GetMethod("GetControllerGripValue");

            if (getTriggerMethod != null)
            {
                try
                {
                    // 检查右手控制器扳机 (controller 1 = 右手)
                    float triggerValue = (float)getTriggerMethod.Invoke(null, new object[] { 1 });
                    if (triggerValue > 0.8f)
                    {
                        OnTriggerPressed("PICO SDK");
                        return;
                    }
                }
                catch { }
            }

            if (getGripMethod != null)
            {
                try
                {
                    // 检查右手控制器握把
                    float gripValue = (float)getGripMethod.Invoke(null, new object[] { 1 });
                    if (gripValue > 0.8f)
                    {
                        OnGripPressed("PICO SDK");
                        return;
                    }
                }
                catch { }
            }
        }

        // 如果PICO SDK方法不可用，尝试其他通用方法
        CheckGenericVRInput();
    }

    /// <summary>
    /// 检查通用VR输入
    /// </summary>
    private void CheckGenericVRInput()
    {
        // 尝试使用Unity的通用VR输入
        try
        {
            // 检查鼠标按钮作为备用（在某些VR系统中，控制器按钮会映射到鼠标按钮）
            if (Input.GetMouseButtonDown(0))
            {
                OnTriggerPressed("鼠标左键");
                return;
            }

            if (Input.GetMouseButtonDown(1))
            {
                OnGripPressed("鼠标右键");
                return;
            }

            // 检查游戏手柄输入（某些VR系统会将控制器映射为游戏手柄）
            if (Input.GetButtonDown("joystick button 0"))
            {
                OnTriggerPressed("手柄按钮0");
                return;
            }

            if (Input.GetButtonDown("joystick button 1"))
            {
                OnGripPressed("手柄按钮1");
                return;
            }
        }
        catch (System.Exception e)
        {
            if (enableDebugLogs)
            {
                Debug.LogWarning($"通用VR输入检查失败: {e.Message}");
            }
        }
    }

    /// <summary>
    /// 扳机按下事件
    /// </summary>
    private void OnTriggerPressed(string source)
    {
        if (!triggerForFunction1 || debugger == null) return;

        lastInputTime = Time.time;

        if (enableDebugLogs)
        {
            Debug.Log($"[PICO直接输入] {source}扳机 → 装配区域定位");
        }

        try
        {
            debugger.TestCameraBasedPositioning();
            Debug.Log("[PICO直接输入] 功能1执行成功");
        }
        catch (System.Exception e)
        {
            Debug.LogError($"[PICO直接输入] 功能1执行失败: {e.Message}");
        }
    }

    /// <summary>
    /// 握把按下事件
    /// </summary>
    private void OnGripPressed(string source)
    {
        if (!gripForFunction2 || debugger == null) return;

        lastInputTime = Time.time;

        if (enableDebugLogs)
        {
            Debug.Log($"[PICO直接输入] {source}握把 → 装配面朝向");
        }

        try
        {
            debugger.TestOrientation();
            Debug.Log("[PICO直接输入] 功能2执行成功");
        }
        catch (System.Exception e)
        {
            Debug.LogError($"[PICO直接输入] 功能2执行失败: {e.Message}");
        }
    }

    /// <summary>
    /// 重置按下事件
    /// </summary>
    private void OnResetPressed(string source)
    {
        if (debugger == null) return;

        lastInputTime = Time.time;

        if (enableDebugLogs)
        {
            Debug.Log($"[PICO直接输入] {source} → 重置位置");
        }

        try
        {
            debugger.ResetPosition();
            Debug.Log("[PICO直接输入] 重置功能执行成功");
        }
        catch (System.Exception e)
        {
            Debug.LogError($"[PICO直接输入] 重置功能执行失败: {e.Message}");
        }
    }

    /// <summary>
    /// 测试所有输入方法
    /// </summary>
    [ContextMenu("测试所有输入方法")]
    public void TestAllInputMethods()
    {
        Debug.Log("=== 测试所有输入方法 ===");
        
        Debug.Log("测试键盘输入...");
        OnTriggerPressed("手动测试");
        
        StartCoroutine(DelayedTest());
    }

    /// <summary>
    /// 延迟测试
    /// </summary>
    private System.Collections.IEnumerator DelayedTest()
    {
        yield return new WaitForSeconds(2f);
        
        Debug.Log("测试第二个功能...");
        OnGripPressed("手动测试");
        
        yield return new WaitForSeconds(2f);
        
        Debug.Log("测试完成！");
    }

    /// <summary>
    /// 显示输入状态
    /// </summary>
    [ContextMenu("显示输入状态")]
    public void ShowInputStatus()
    {
        Debug.Log("=== 输入状态报告 ===");
        Debug.Log($"VRAssemblyDebugger: {(debugger != null ? "可用" : "不可用")}");
        Debug.Log($"键盘备用输入: {(enableKeyboardFallback ? "启用" : "禁用")}");
        Debug.Log($"输入冷却时间: {inputCooldown}秒");
        Debug.Log($"扳机映射功能1: {triggerForFunction1}");
        Debug.Log($"握把映射功能2: {gripForFunction2}");
        Debug.Log("==================");
    }
}
