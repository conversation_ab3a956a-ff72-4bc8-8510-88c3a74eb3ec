using UnityEngine;
using UnityEngine.UI;
using TMPro;
using UnityEngine.EventSystems;

#if UNITY_EDITOR
using UnityEditor;
#endif

/// <summary>
/// VR开始菜单设置助手
/// 
/// 用于在场景中快速创建和配置VR开始菜单UI
/// </summary>
public class VRStartMenuSetupHelper : MonoBehaviour
{
    [Header("UI创建设置")]
    [SerializeField] private bool createCanvas = true;
    [SerializeField] private bool createEventSystem = true;
    [SerializeField] private bool setupVRComponents = true;
    [SerializeField] private bool optimizeForVR = true;
    
    [Header("UI样式设置")]
    [SerializeField] private Color backgroundColor = new Color(0.1f, 0.1f, 0.2f, 0.8f);
    [SerializeField] private Color primaryColor = new Color(0.2f, 0.6f, 1f, 1f);
    [SerializeField] private Color textColor = Color.white;
    [SerializeField] private Font defaultFont;
    
    [Header("VR设置")]
    [SerializeField] private float uiDistance = 2.5f;
    [SerializeField] private float uiScale = 0.01f;
    [SerializeField] private Vector3 uiOffset = new Vector3(0, 0.2f, 0);
    
    /// <summary>
    /// 创建VR开始菜单
    /// </summary>
    [ContextMenu("创建VR开始菜单")]
    public void CreateVRStartMenu()
    {
        Debug.Log("[VRStartMenuSetupHelper] 开始创建VR开始菜单");
        
        // 1. 创建Canvas
        Canvas canvas = CreateMenuCanvas();
        
        // 2. 创建事件系统
        if (createEventSystem)
        {
            CreateEventSystemIfNeeded();
        }
        
        // 3. 创建UI元素
        CreateMenuUI(canvas);
        
        // 4. 添加VR组件
        if (setupVRComponents)
        {
            SetupVRComponents(canvas);
        }
        
        // 5. 添加菜单管理器
        SetupMenuManager(canvas);

        // 6. 添加VR输入处理器
        SetupVRInputHandler(canvas);

        Debug.Log("[VRStartMenuSetupHelper] VR开始菜单创建完成");
    }
    
    /// <summary>
    /// 创建菜单Canvas
    /// </summary>
    private Canvas CreateMenuCanvas()
    {
        Canvas existingCanvas = FindObjectOfType<Canvas>();
        if (existingCanvas != null && !createCanvas)
        {
            Debug.Log("[VRStartMenuSetupHelper] 使用现有Canvas");
            return existingCanvas;
        }
        
        // 创建Canvas GameObject
        GameObject canvasGO = new GameObject("VR Start Menu Canvas");
        Canvas canvas = canvasGO.AddComponent<Canvas>();
        CanvasScaler scaler = canvasGO.AddComponent<CanvasScaler>();
        GraphicRaycaster raycaster = canvasGO.AddComponent<GraphicRaycaster>();
        
        // 配置Canvas
        canvas.renderMode = RenderMode.WorldSpace;
        canvas.worldCamera = Camera.main;
        
        // 配置CanvasScaler
        scaler.uiScaleMode = CanvasScaler.ScaleMode.ScaleWithScreenSize;
        scaler.referenceResolution = new Vector2(1920, 1080);
        scaler.screenMatchMode = CanvasScaler.ScreenMatchMode.MatchWidthOrHeight;
        scaler.matchWidthOrHeight = 0.5f;
        
        // 设置位置和缩放
        canvasGO.transform.localScale = Vector3.one * uiScale;
        PositionCanvas(canvasGO.transform);
        
        Debug.Log("[VRStartMenuSetupHelper] Canvas创建完成");
        return canvas;
    }
    
    /// <summary>
    /// 定位Canvas
    /// </summary>
    private void PositionCanvas(Transform canvasTransform)
    {
        Camera mainCamera = Camera.main;
        if (mainCamera == null) return;
        
        Vector3 cameraPosition = mainCamera.transform.position;
        Vector3 cameraForward = mainCamera.transform.forward;
        
        Vector3 targetPosition = cameraPosition + cameraForward * uiDistance + uiOffset;
        canvasTransform.position = targetPosition;
        
        canvasTransform.LookAt(mainCamera.transform);
        canvasTransform.Rotate(0, 180, 0);
    }
    
    /// <summary>
    /// 创建事件系统
    /// </summary>
    private void CreateEventSystemIfNeeded()
    {
        EventSystem existingEventSystem = FindObjectOfType<EventSystem>();
        if (existingEventSystem != null)
        {
            Debug.Log("[VRStartMenuSetupHelper] 使用现有EventSystem");
            return;
        }
        
        GameObject eventSystemGO = new GameObject("EventSystem");
        eventSystemGO.AddComponent<EventSystem>();
        eventSystemGO.AddComponent<StandaloneInputModule>();
        
        Debug.Log("[VRStartMenuSetupHelper] EventSystem创建完成");
    }
    
    /// <summary>
    /// 创建菜单UI
    /// </summary>
    private void CreateMenuUI(Canvas canvas)
    {
        // 创建主面板
        GameObject mainPanel = CreateMainPanel(canvas.transform);
        
        // 创建标题
        CreateTitleText(mainPanel.transform);
        
        // 创建欢迎文本
        CreateWelcomeText(mainPanel.transform);
        
        // 创建输入区域
        CreateInputArea(mainPanel.transform);
        
        // 创建开始按钮
        CreateStartButton(mainPanel.transform);
    }
    
    /// <summary>
    /// 创建主面板
    /// </summary>
    private GameObject CreateMainPanel(Transform parent)
    {
        GameObject panel = new GameObject("Main Panel");
        panel.transform.SetParent(parent, false);
        
        Image panelImage = panel.AddComponent<Image>();
        panelImage.color = backgroundColor;
        
        RectTransform rectTransform = panel.GetComponent<RectTransform>();
        rectTransform.anchorMin = Vector2.zero;
        rectTransform.anchorMax = Vector2.one;
        rectTransform.offsetMin = Vector2.zero;
        rectTransform.offsetMax = Vector2.zero;
        
        return panel;
    }
    
    /// <summary>
    /// 创建标题文本
    /// </summary>
    private void CreateTitleText(Transform parent)
    {
        GameObject titleGO = new GameObject("Title Text");
        titleGO.transform.SetParent(parent, false);
        
        TextMeshProUGUI titleText = titleGO.AddComponent<TextMeshProUGUI>();
        titleText.text = "VR装配动画系统";
        titleText.fontSize = 48;
        titleText.color = textColor;
        titleText.alignment = TextAlignmentOptions.Center;
        titleText.fontStyle = FontStyles.Bold;
        
        RectTransform rectTransform = titleGO.GetComponent<RectTransform>();
        rectTransform.anchorMin = new Vector2(0, 0.8f);
        rectTransform.anchorMax = new Vector2(1, 1f);
        rectTransform.offsetMin = new Vector2(50, 0);
        rectTransform.offsetMax = new Vector2(-50, -20);
    }
    
    /// <summary>
    /// 创建欢迎文本
    /// </summary>
    private void CreateWelcomeText(Transform parent)
    {
        GameObject welcomeGO = new GameObject("Welcome Text");
        welcomeGO.transform.SetParent(parent, false);
        
        TextMeshProUGUI welcomeText = welcomeGO.AddComponent<TextMeshProUGUI>();
        welcomeText.text = "欢迎使用VR装配动画系统\n请输入您的用户名开始体验";
        welcomeText.fontSize = 24;
        welcomeText.color = textColor;
        welcomeText.alignment = TextAlignmentOptions.Center;
        
        RectTransform rectTransform = welcomeGO.GetComponent<RectTransform>();
        rectTransform.anchorMin = new Vector2(0, 0.6f);
        rectTransform.anchorMax = new Vector2(1, 0.8f);
        rectTransform.offsetMin = new Vector2(50, 0);
        rectTransform.offsetMax = new Vector2(-50, 0);
    }
    
    /// <summary>
    /// 创建输入区域
    /// </summary>
    private void CreateInputArea(Transform parent)
    {
        GameObject inputGO = new GameObject("Username Input");
        inputGO.transform.SetParent(parent, false);
        
        Image inputImage = inputGO.AddComponent<Image>();
        inputImage.color = Color.white;
        
        TMP_InputField inputField = inputGO.AddComponent<TMP_InputField>();
        
        // 创建占位符文本
        GameObject placeholderGO = new GameObject("Placeholder");
        placeholderGO.transform.SetParent(inputGO.transform, false);
        TextMeshProUGUI placeholderText = placeholderGO.AddComponent<TextMeshProUGUI>();
        placeholderText.text = "请输入用户名...";
        placeholderText.fontSize = 20;
        placeholderText.color = new Color(0.5f, 0.5f, 0.5f, 1f);
        placeholderText.alignment = TextAlignmentOptions.Left;
        
        RectTransform placeholderRect = placeholderGO.GetComponent<RectTransform>();
        placeholderRect.anchorMin = Vector2.zero;
        placeholderRect.anchorMax = Vector2.one;
        placeholderRect.offsetMin = new Vector2(10, 0);
        placeholderRect.offsetMax = new Vector2(-10, 0);
        
        // 创建输入文本
        GameObject textGO = new GameObject("Text");
        textGO.transform.SetParent(inputGO.transform, false);
        TextMeshProUGUI inputText = textGO.AddComponent<TextMeshProUGUI>();
        inputText.fontSize = 20;
        inputText.color = Color.black;
        inputText.alignment = TextAlignmentOptions.Left;
        
        RectTransform textRect = textGO.GetComponent<RectTransform>();
        textRect.anchorMin = Vector2.zero;
        textRect.anchorMax = Vector2.one;
        textRect.offsetMin = new Vector2(10, 0);
        textRect.offsetMax = new Vector2(-10, 0);
        
        // 配置输入框
        inputField.textComponent = inputText;
        inputField.placeholder = placeholderText;
        inputField.characterLimit = 20;
        
        RectTransform inputRect = inputGO.GetComponent<RectTransform>();
        inputRect.anchorMin = new Vector2(0.2f, 0.4f);
        inputRect.anchorMax = new Vector2(0.8f, 0.5f);
        inputRect.offsetMin = Vector2.zero;
        inputRect.offsetMax = Vector2.zero;
    }
    
    /// <summary>
    /// 创建开始按钮
    /// </summary>
    private void CreateStartButton(Transform parent)
    {
        GameObject buttonGO = new GameObject("Start Button");
        buttonGO.transform.SetParent(parent, false);
        
        Image buttonImage = buttonGO.AddComponent<Image>();
        buttonImage.color = primaryColor;
        
        Button button = buttonGO.AddComponent<Button>();
        
        // 创建按钮文本
        GameObject textGO = new GameObject("Text");
        textGO.transform.SetParent(buttonGO.transform, false);
        TextMeshProUGUI buttonText = textGO.AddComponent<TextMeshProUGUI>();
        buttonText.text = "开始体验";
        buttonText.fontSize = 24;
        buttonText.color = Color.white;
        buttonText.alignment = TextAlignmentOptions.Center;
        buttonText.fontStyle = FontStyles.Bold;
        
        RectTransform textRect = textGO.GetComponent<RectTransform>();
        textRect.anchorMin = Vector2.zero;
        textRect.anchorMax = Vector2.one;
        textRect.offsetMin = Vector2.zero;
        textRect.offsetMax = Vector2.zero;
        
        // 配置按钮颜色
        ColorBlock colors = button.colors;
        colors.normalColor = primaryColor;
        colors.highlightedColor = Color.Lerp(primaryColor, Color.white, 0.2f);
        colors.pressedColor = Color.Lerp(primaryColor, Color.black, 0.2f);
        colors.disabledColor = Color.gray;
        button.colors = colors;
        
        RectTransform buttonRect = buttonGO.GetComponent<RectTransform>();
        buttonRect.anchorMin = new Vector2(0.3f, 0.2f);
        buttonRect.anchorMax = new Vector2(0.7f, 0.35f);
        buttonRect.offsetMin = Vector2.zero;
        buttonRect.offsetMax = Vector2.zero;
    }
    
    /// <summary>
    /// 设置VR组件
    /// </summary>
    private void SetupVRComponents(Canvas canvas)
    {
        // 添加VR UI管理器
        if (canvas.GetComponent<VRUIManager>() == null)
        {
            canvas.gameObject.AddComponent<VRUIManager>();
        }
        
        // 添加VR UI交互器
        if (canvas.GetComponent<VRUIInteractor>() == null)
        {
            canvas.gameObject.AddComponent<VRUIInteractor>();
        }
        
        // 添加VR UI布局优化器
        if (canvas.GetComponent<VRUILayoutOptimizer>() == null)
        {
            canvas.gameObject.AddComponent<VRUILayoutOptimizer>();
        }
        
        Debug.Log("[VRStartMenuSetupHelper] VR组件设置完成");
    }
    
    /// <summary>
    /// 设置菜单管理器
    /// </summary>
    private void SetupMenuManager(Canvas canvas)
    {
        VRStartMenuManager manager = canvas.GetComponent<VRStartMenuManager>();
        if (manager == null)
        {
            manager = canvas.gameObject.AddComponent<VRStartMenuManager>();
        }
        
        // 自动分配UI组件引用
        AssignUIReferences(manager, canvas);
        
        Debug.Log("[VRStartMenuSetupHelper] 菜单管理器设置完成");
    }

    /// <summary>
    /// 设置VR输入处理器
    /// </summary>
    private void SetupVRInputHandler(Canvas canvas)
    {
        VRStartMenuInputHandler inputHandler = canvas.GetComponent<VRStartMenuInputHandler>();
        if (inputHandler == null)
        {
            inputHandler = canvas.gameObject.AddComponent<VRStartMenuInputHandler>();
        }

        // 自动分配输入组件引用
        AssignInputReferences(inputHandler, canvas);

        Debug.Log("[VRStartMenuSetupHelper] VR输入处理器设置完成");
    }

    /// <summary>
    /// 分配输入引用
    /// </summary>
    private void AssignInputReferences(VRStartMenuInputHandler inputHandler, Canvas canvas)
    {
        // 使用反射设置私有字段（在编辑器中）
#if UNITY_EDITOR
        var inputHandlerType = typeof(VRStartMenuInputHandler);

        // 查找并设置UI组件
        var inputField = canvas.GetComponentInChildren<TMP_InputField>();
        var button = canvas.GetComponentInChildren<Button>();

        var inputFieldField = inputHandlerType.GetField("targetInputField", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
        inputFieldField?.SetValue(inputHandler, inputField);

        var buttonField = inputHandlerType.GetField("startButton", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
        buttonField?.SetValue(inputHandler, button);
#endif
    }
    
    /// <summary>
    /// 分配UI引用
    /// </summary>
    private void AssignUIReferences(VRStartMenuManager manager, Canvas canvas)
    {
        // 使用反射设置私有字段（在编辑器中）
#if UNITY_EDITOR
        var managerType = typeof(VRStartMenuManager);
        
        // 设置Canvas引用
        var canvasField = managerType.GetField("menuCanvas", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
        canvasField?.SetValue(manager, canvas);
        
        // 查找并设置其他UI组件
        var titleText = canvas.GetComponentInChildren<TextMeshProUGUI>();
        var inputField = canvas.GetComponentInChildren<TMP_InputField>();
        var button = canvas.GetComponentInChildren<Button>();
        
        var titleField = managerType.GetField("titleText", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
        titleField?.SetValue(manager, titleText);
        
        var inputField_field = managerType.GetField("usernameInput", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
        inputField_field?.SetValue(manager, inputField);
        
        var buttonField = managerType.GetField("startButton", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
        buttonField?.SetValue(manager, button);
        
        // 设置摄像机引用
        var cameraField = managerType.GetField("vrCamera", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
        cameraField?.SetValue(manager, Camera.main);
#endif
    }
    
#if UNITY_EDITOR
    /// <summary>
    /// 编辑器菜单：创建VR开始菜单
    /// </summary>
    [MenuItem("VR Assembly/Create VR Start Menu")]
    public static void CreateVRStartMenuFromMenu()
    {
        VRStartMenuSetupHelper helper = FindObjectOfType<VRStartMenuSetupHelper>();
        if (helper == null)
        {
            GameObject helperGO = new GameObject("VRStartMenuSetupHelper");
            helper = helperGO.AddComponent<VRStartMenuSetupHelper>();
        }
        
        helper.CreateVRStartMenu();
        
        EditorUtility.DisplayDialog("VR开始菜单创建", "VR开始菜单创建完成！", "确定");
    }
#endif
}
