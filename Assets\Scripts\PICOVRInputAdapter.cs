using UnityEngine;

// 条件编译：只在安装了XR Interaction Toolkit时编译XR相关代码
#if UNITY_XR_INTERACTION_TOOLKIT
using UnityEngine.XR.Interaction.Toolkit;
using UnityEngine.InputSystem;
#endif

/// <summary>
/// PICO VR输入适配器 (Action-based)
/// 将PICO手柄输入映射到VRAssemblyDebugger功能
/// 支持新版本的Action-based XR Controller
/// </summary>
public class PICOVRInputAdapter : MonoBehaviour
{
    [Header("PICO输入映射")]
    [SerializeField] private bool enablePICOInput = true;
    [SerializeField] private bool showInputDebug = true;

    [Header("功能映射")]
    [SerializeField] private bool triggerTestsPositioning = false; // 扳机键禁用（避免与UI点击冲突）
    [SerializeField] private bool primaryButtonTestsPositioning = true; // A按钮(主按钮)测试位置调整
    [SerializeField] private bool secondaryButtonTestsOrientation = true; // B按钮(副按钮)测试朝向调整
    [SerializeField] private bool menuButtonToggleDebug = true; // 菜单键切换调试界面

    [Header("组件引用")]
    [SerializeField] private VRAssemblyDebugger debugger;

#if UNITY_XR_INTERACTION_TOOLKIT
    [Header("Action-based控制器")]
    [SerializeField] private ActionBasedController leftController;
    [SerializeField] private ActionBasedController rightController;
#endif

    [Header("XR Toolkit状态")]
    [SerializeField] private bool xrToolkitAvailable = false;
    [SerializeField] private string xrToolkitStatus = "未检测";

    // 输入状态跟踪
    private bool lastTriggerState = false;
    private bool lastPrimaryState = false;
    private bool lastSecondaryState = false;
    private bool lastMenuState = false;

    void Start()
    {
        // 检测XR Toolkit是否可用
        DetectXRToolkit();

        InitializePICOInput();

        if (debugger == null)
        {
            debugger = FindObjectOfType<VRAssemblyDebugger>();
        }

        Debug.Log("[PICOVRInputAdapter] PICO输入适配器已启动");
        Debug.Log($"[PICOVRInputAdapter] XR Toolkit状态: {xrToolkitStatus}");
    }

    void Update()
    {
        if (!enablePICOInput) return;

        HandlePICOInput();
    }

    /// <summary>
    /// 检测XR Interaction Toolkit是否可用
    /// </summary>
    private void DetectXRToolkit()
    {
        try
        {
#if UNITY_XR_INTERACTION_TOOLKIT
            xrToolkitAvailable = true;
            xrToolkitStatus = "已安装并可用 (Action-based)";
            Debug.Log("[PICOVRInputAdapter] ✅ XR Interaction Toolkit (Action-based) 已检测到");
#else
            xrToolkitAvailable = false;
            xrToolkitStatus = "未安装或不可用";
            Debug.LogWarning("[PICOVRInputAdapter] ⚠️ XR Interaction Toolkit 未检测到，将使用备用输入");
#endif
        }
        catch (System.Exception e)
        {
            xrToolkitAvailable = false;
            xrToolkitStatus = $"检测失败: {e.Message}";
            Debug.LogWarning($"[PICOVRInputAdapter] ⚠️ XR Toolkit检测失败: {e.Message}");
        }
    }

    /// <summary>
    /// 初始化PICO输入系统
    /// </summary>
    private void InitializePICOInput()
    {
        if (xrToolkitAvailable)
        {
            try
            {
#if UNITY_XR_INTERACTION_TOOLKIT
                // 自动查找Action-based控制器
                if (leftController == null)
                {
                    var leftControllerGO = GameObject.Find("Left Controller");
                    if (leftControllerGO == null)
                        leftControllerGO = GameObject.Find("LeftHand Controller");

                    if (leftControllerGO != null)
                    {
                        leftController = leftControllerGO.GetComponent<ActionBasedController>();
                        if (leftController != null)
                        {
                            Debug.Log("[PICOVRInputAdapter] 找到左手Action-based控制器");
                        }
                    }
                }

                if (rightController == null)
                {
                    var rightControllerGO = GameObject.Find("Right Controller");
                    if (rightControllerGO == null)
                        rightControllerGO = GameObject.Find("RightHand Controller");

                    if (rightControllerGO != null)
                    {
                        rightController = rightControllerGO.GetComponent<ActionBasedController>();
                        if (rightController != null)
                        {
                            Debug.Log("[PICOVRInputAdapter] 找到右手Action-based控制器");
                        }
                    }
                }

                if (leftController == null && rightController == null)
                {
                    Debug.LogWarning("[PICOVRInputAdapter] 未找到Action-based VR控制器，将使用备用输入");
                }
#endif
            }
            catch (System.Exception e)
            {
                Debug.LogWarning($"[PICOVRInputAdapter] 控制器初始化失败: {e.Message}");
                xrToolkitAvailable = false;
                xrToolkitStatus = $"初始化失败: {e.Message}";
            }
        }
        else
        {
            Debug.LogWarning("[PICOVRInputAdapter] XR Interaction Toolkit不可用，将使用备用输入");
        }
    }

    /// <summary>
    /// 处理PICO输入
    /// </summary>
    private void HandlePICOInput()
    {
#if UNITY_XR_INTERACTION_TOOLKIT
        if (xrToolkitAvailable && (rightController != null || leftController != null))
        {
            // 使用右手控制器作为主要输入
            if (rightController != null)
            {
                HandleActionBasedControllerInput(rightController, "右手");
            }
            // 如果右手控制器不可用，使用左手控制器
            else if (leftController != null)
            {
                HandleActionBasedControllerInput(leftController, "左手");
            }
        }
        else
#endif
        {
            // 在没有XR包或控制器时，使用键盘输入作为备用
            HandleFallbackInput();
        }
    }

#if UNITY_XR_INTERACTION_TOOLKIT
    /// <summary>
    /// 处理Action-based控制器输入
    /// </summary>
    private void HandleActionBasedControllerInput(ActionBasedController controller, string handName)
    {
        if (controller == null) return;

        try
        {
            // 获取按钮状态 - 使用Action-based方式
            bool triggerPressed = GetActionButtonState(controller.selectAction);
            bool primaryPressed = GetActionButtonState(controller.activateAction);
            bool secondaryPressed = false; // 需要根据具体的Action配置
            bool menuPressed = false; // 需要根据具体的Action配置

            // 尝试获取更多按钮状态（如果有的话）
            // 这里可能需要根据你的具体Input Action配置来调整

            // 检测按钮按下事件（边缘检测）
            // 注意：扳机键现在只用于UI交互，不再控制装配区域

            if (primaryPressed && !lastPrimaryState)
            {
                OnPrimaryButtonPressed(handName);
            }

            if (secondaryPressed && !lastSecondaryState)
            {
                OnSecondaryButtonPressed(handName);
            }

            if (menuPressed && !lastMenuState)
            {
                OnMenuButtonPressed(handName);
            }

            // 更新状态
            lastTriggerState = triggerPressed;
            lastPrimaryState = primaryPressed;
            lastSecondaryState = secondaryPressed;
            lastMenuState = menuPressed;

            // 显示输入调试信息
            if (showInputDebug && (triggerPressed || primaryPressed || secondaryPressed || menuPressed))
            {
                Debug.Log($"[PICOVRInputAdapter] {handName}控制器输入 - T:{triggerPressed} P:{primaryPressed} S:{secondaryPressed} M:{menuPressed}");
            }
        }
        catch (System.Exception e)
        {
            Debug.LogWarning($"[PICOVRInputAdapter] Action-based控制器输入处理失败: {e.Message}");
        }
    }

    /// <summary>
    /// 获取Action按钮状态
    /// </summary>
    private bool GetActionButtonState(InputActionProperty actionProperty)
    {
        try
        {
            if (actionProperty.action != null && actionProperty.action.enabled)
            {
                return actionProperty.action.ReadValue<float>() > 0.5f;
            }
        }
        catch (System.Exception e)
        {
            Debug.LogWarning($"[PICOVRInputAdapter] 获取Action按钮状态失败: {e.Message}");
        }
        return false;
    }
#endif



    /// <summary>
    /// 扳机键按下事件 - 第一部分功能：装配区域移动到基于摄像机的固定位置
    /// </summary>
    // 扳机键方法已移除 - 现在扳机键只用于UI交互，不控制装配区域

    /// <summary>
    /// A按钮(主按钮)按下事件 - 装配区域移动到基于摄像机的固定位置
    /// </summary>
    private void OnPrimaryButtonPressed(string handName)
    {
        if (!primaryButtonTestsPositioning || debugger == null) return;

        Debug.Log($"[PICOVRInputAdapter] {handName}A按钮(主按钮)按下 - 执行装配区域移动到基于摄像机的固定位置");
        debugger.TestCameraBasedPositioning();

        ProvideTactileFeedback();
    }

    /// <summary>
    /// B按钮(副按钮)按下事件 - 装配面朝向摄像机
    /// </summary>
    private void OnSecondaryButtonPressed(string handName)
    {
        if (!secondaryButtonTestsOrientation || debugger == null) return;

        Debug.Log($"[PICOVRInputAdapter] {handName}B按钮(副按钮)按下 - 执行装配面朝向摄像机");
        debugger.TestOrientation();

        ProvideTactileFeedback();
    }

    /// <summary>
    /// 菜单按钮按下事件
    /// </summary>
    private void OnMenuButtonPressed(string handName)
    {
        if (!menuButtonToggleDebug) return;
        
        Debug.Log($"[PICOVRInputAdapter] {handName}菜单按钮按下 - 切换调试界面");
        
        // 模拟F1键按下来切换调试界面
        if (debugger != null)
        {
            // 通过反射调用私有方法
            var method = debugger.GetType().GetMethod("ToggleDebugMode", 
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            if (method != null)
            {
                method.Invoke(debugger, null);
            }
        }
        
        ProvideTactileFeedback();
    }

    /// <summary>
    /// 处理备用输入（当XR包不可用时）
    /// </summary>
    private void HandleFallbackInput()
    {
        // 使用键盘输入作为备用
        if (Input.GetKeyDown(KeyCode.T))
        {
            OnTriggerPressed("键盘");
        }

        if (Input.GetKeyDown(KeyCode.P))
        {
            OnPrimaryButtonPressed("键盘");
        }

        if (Input.GetKeyDown(KeyCode.G))
        {
            OnSecondaryButtonPressed("键盘");
        }

        if (Input.GetKeyDown(KeyCode.F1))
        {
            OnMenuButtonPressed("键盘");
        }
    }

    /// <summary>
    /// 提供触觉反馈
    /// </summary>
    private void ProvideTactileFeedback()
    {
#if UNITY_XR_INTERACTION_TOOLKIT
        if (xrToolkitAvailable && rightController != null)
        {
            try
            {
                // 使用Action-based控制器的触觉反馈
                if (rightController.hapticDeviceAction.action != null)
                {
                    // 发送触觉脉冲
                    // 注意：具体的触觉反馈实现可能需要根据PICO SDK调整
                    Debug.Log("[PICOVRInputAdapter] 触觉反馈已触发");
                }
            }
            catch (System.Exception e)
            {
                Debug.LogWarning($"[PICOVRInputAdapter] 触觉反馈失败: {e.Message}");
            }
        }
#endif
    }

    /// <summary>
    /// 显示输入映射帮助
    /// </summary>
    [ContextMenu("显示输入映射")]
    public void ShowInputMapping()
    {
        string mapping = @"
PICO VR输入映射：

右手控制器：
- 扳机键 (Trigger) → 第一部分功能：装配区域移动到基于摄像机的固定位置
- 主按钮 (A/X) → 第二部分功能：让装配面朝向摄像机
- 副按钮 (B/Y) → 重置装配区域位置
- 菜单按钮 (Menu) → 切换调试界面

功能说明：
1. 第一部分功能：将装配区域移动到摄像机视野的固定位置
2. 第二部分功能：旋转装配区域使装配面朝向摄像机
3. 重置功能：恢复装配区域到原始位置
4. 调试界面：显示/隐藏VR装配调试信息

注意：
- 确保PICO设备已正确连接
- 确保XR Origin已正确配置
- 确保VRAssemblyDebugger组件已添加到场景
- 确保测试零件已正确设置
";
        Debug.Log(mapping);
    }

    /// <summary>
    /// 测试所有功能
    /// </summary>
    [ContextMenu("测试所有VR功能")]
    public void TestAllVRFunctions()
    {
        if (debugger == null)
        {
            Debug.LogError("[PICOVRInputAdapter] VRSystemDebugger未找到！");
            return;
        }

        Debug.Log("[PICOVRInputAdapter] 开始测试所有VR功能...");
        
        // 延迟执行各个测试
        StartCoroutine(TestSequence());
    }

    /// <summary>
    /// 测试序列
    /// </summary>
    private System.Collections.IEnumerator TestSequence()
    {
        Debug.Log("1. 测试第一部分功能：装配区域移动到基于摄像机的固定位置...");
        debugger.TestCameraBasedPositioning();
        yield return new WaitForSeconds(3f);

        Debug.Log("2. 测试第二部分功能：让装配面朝向摄像机...");
        debugger.TestOrientation();
        yield return new WaitForSeconds(3f);

        Debug.Log("3. 重置装配区域位置...");
        debugger.ResetPosition();
        yield return new WaitForSeconds(2f);

        Debug.Log("[PICOVRInputAdapter] 所有VR装配功能测试完成！");
    }

    /// <summary>
    /// 启用/禁用PICO输入
    /// </summary>
    public void SetPICOInputEnabled(bool enabled)
    {
        enablePICOInput = enabled;
        Debug.Log($"[PICOVRInputAdapter] PICO输入 {(enabled ? "已启用" : "已禁用")}");
    }

    void OnDestroy()
    {
        Debug.Log("[PICOVRInputAdapter] PICO输入适配器已销毁");
    }
}
