using UnityEngine;

#if UNITY_XR_INTERACTION_TOOLKIT
using UnityEngine.XR.Interaction.Toolkit;
using UnityEngine.XR.Interaction.Toolkit.Inputs;
using UnityEngine.InputSystem;
#endif

/// <summary>
/// 最简单的输入测试
/// 用于验证基本的输入功能
///
/// 新的按键映射（避免与UI点击冲突）：
/// - A键/握把键 → 装配区域移动到摄像机固定位置
/// - B键/扳机键 → 装配面朝向摄像机
/// - 扳机键在UI交互中专用于点击确认
/// </summary>
public class SimpleInputTest : MonoBehaviour
{
    [Header("测试设置")]
    [SerializeField] private bool enableTest = true;
    [SerializeField] private bool enableKeyboardTest = true;

    [Header("组件引用")]
    [SerializeField] private VRAssemblyDebugger debugger;

    // 简单的状态跟踪
    private bool lastTriggerState = false;
    private bool lastGripState = false;

    void Start()
    {
        // 强制启用日志
        Debug.unityLogger.logEnabled = true;
        
        Debug.Log("=== 简单输入测试启动 ===");
        
        if (debugger == null)
        {
            debugger = FindObjectOfType<VRAssemblyDebugger>();
            Debug.Log($"VRAssemblyDebugger: {(debugger != null ? "找到" : "未找到")}");
        }

#if UNITY_XR_INTERACTION_TOOLKIT
        // 查找控制器
        var controllers = FindObjectsOfType<ActionBasedController>();
        Debug.Log($"找到 {controllers.Length} 个ActionBasedController");
        
        foreach (var controller in controllers)
        {
            Debug.Log($"控制器: {controller.name}");
            Debug.Log($"  Select Action: {(controller.selectAction.action != null ? "已配置" : "未配置")}");
            Debug.Log($"  Activate Action: {(controller.activateAction.action != null ? "已配置" : "未配置")}");
        }
#else
        Debug.Log("XR Interaction Toolkit 未安装");
#endif

        Debug.Log("=== 初始化完成 ===");

        // 显示按键映射
        ShowKeyMapping();
    }

    /// <summary>
    /// 显示当前按键映射
    /// </summary>
    private void ShowKeyMapping()
    {
        Debug.Log("=== 当前按键映射 ===");
        Debug.Log("键盘输入:");
        Debug.Log("  A键 → 装配区域移动到摄像机固定位置");
        Debug.Log("  B键 → 装配面朝向摄像机");
        Debug.Log("  T键 → 装配区域移动（备用）");
        Debug.Log("  P键 → 装配面朝向（备用）");
        Debug.Log("");
        Debug.Log("VR控制器输入:");
        Debug.Log("  A按钮(握把键) → 装配区域移动到摄像机固定位置");
        Debug.Log("  B按钮(扳机键) → 装配面朝向摄像机");
        Debug.Log("  注意：扳机键在UI交互中专用于点击确认");
        Debug.Log("=== 按键映射结束 ===");
    }

    void Update()
    {
        if (!enableTest) return;

        // 键盘测试（更新按键映射）
        if (enableKeyboardTest)
        {
            if (Input.GetKeyDown(KeyCode.A))
            {
                Debug.Log("[简单测试] A键按下 - 装配区域定位");
                TestFunction1();
            }

            if (Input.GetKeyDown(KeyCode.B))
            {
                Debug.Log("[简单测试] B键按下 - 装配面朝向");
                TestFunction2();
            }

            // 保留旧的T/P键作为备用
            if (Input.GetKeyDown(KeyCode.T))
            {
                Debug.Log("[简单测试] T键按下（备用）- 装配区域定位");
                TestFunction1();
            }

            if (Input.GetKeyDown(KeyCode.P))
            {
                Debug.Log("[简单测试] P键按下（备用）- 装配面朝向");
                TestFunction2();
            }
        }

#if UNITY_XR_INTERACTION_TOOLKIT
        // VR控制器测试
        TestVRInput();
#endif
    }

#if UNITY_XR_INTERACTION_TOOLKIT
    /// <summary>
    /// 测试VR输入
    /// </summary>
    private void TestVRInput()
    {
        var controllers = FindObjectsOfType<ActionBasedController>();
        
        foreach (var controller in controllers)
        {
            if (controller.name.ToLower().Contains("right"))
            {
                TestControllerInput(controller, "右手");
                break; // 只测试右手控制器
            }
        }
    }

    /// <summary>
    /// 测试单个控制器输入
    /// </summary>
    private void TestControllerInput(ActionBasedController controller, string handName)
    {
        // 检查扳机
        bool triggerPressed = false;
        if (controller.activateAction.action != null && controller.activateAction.action.enabled)
        {
            try
            {
                float triggerValue = controller.activateAction.action.ReadValue<float>();
                triggerPressed = triggerValue > 0.5f;
            }
            catch (System.Exception e)
            {
                Debug.LogWarning($"读取扳机值失败: {e.Message}");
            }
        }

        // 检查握把
        bool gripPressed = false;
        if (controller.selectAction.action != null && controller.selectAction.action.enabled)
        {
            try
            {
                float gripValue = controller.selectAction.action.ReadValue<float>();
                gripPressed = gripValue > 0.5f;
            }
            catch (System.Exception e)
            {
                Debug.LogWarning($"读取握把值失败: {e.Message}");
            }
        }

        // 检测按钮按下事件（更新按键映射）
        if (gripPressed && !lastGripState)
        {
            Debug.Log($"[简单测试] {handName}A按钮(握把)按下 - 装配区域定位");
            TestFunction1();
        }

        if (triggerPressed && !lastTriggerState)
        {
            Debug.Log($"[简单测试] {handName}B按钮(扳机)按下 - 装配面朝向");
            TestFunction2();
        }

        // 更新状态
        lastTriggerState = triggerPressed;
        lastGripState = gripPressed;

        // 定期输出状态（每秒一次）
        if (Time.frameCount % 60 == 0 && (triggerPressed || gripPressed))
        {
            Debug.Log($"[简单测试] {handName}状态 - B按钮(扳机):{triggerPressed} A按钮(握把):{gripPressed}");
        }
    }
#endif

    /// <summary>
    /// 测试功能1 - 装配区域定位
    /// </summary>
    private void TestFunction1()
    {
        Debug.Log("[简单测试] 执行功能1 - 装配区域定位");
        
        if (debugger != null)
        {
            try
            {
                debugger.TestCameraBasedPositioning();
                Debug.Log("[简单测试] 功能1执行成功");
            }
            catch (System.Exception e)
            {
                Debug.LogError($"[简单测试] 功能1执行失败: {e.Message}");
            }
        }
        else
        {
            Debug.LogError("[简单测试] VRAssemblyDebugger未找到");
        }
    }

    /// <summary>
    /// 测试功能2 - 装配面朝向
    /// </summary>
    private void TestFunction2()
    {
        Debug.Log("[简单测试] 执行功能2 - 装配面朝向");
        
        if (debugger != null)
        {
            try
            {
                debugger.TestOrientation();
                Debug.Log("[简单测试] 功能2执行成功");
            }
            catch (System.Exception e)
            {
                Debug.LogError($"[简单测试] 功能2执行失败: {e.Message}");
            }
        }
        else
        {
            Debug.LogError("[简单测试] VRAssemblyDebugger未找到");
        }
    }

    /// <summary>
    /// 手动测试功能1
    /// </summary>
    [ContextMenu("手动测试功能1")]
    public void ManualTestFunction1()
    {
        TestFunction1();
    }

    /// <summary>
    /// 手动测试功能2
    /// </summary>
    [ContextMenu("手动测试功能2")]
    public void ManualTestFunction2()
    {
        TestFunction2();
    }

    /// <summary>
    /// 显示按键映射
    /// </summary>
    [ContextMenu("显示按键映射")]
    public void ShowKeyMappingMenu()
    {
        ShowKeyMapping();
    }

    /// <summary>
    /// 输出详细状态
    /// </summary>
    [ContextMenu("输出详细状态")]
    public void LogDetailedStatus()
    {
        Debug.Log("=== 详细状态报告 ===");
        Debug.Log($"enableTest: {enableTest}");
        Debug.Log($"enableKeyboardTest: {enableKeyboardTest}");
        Debug.Log($"debugger: {(debugger != null ? "已找到" : "未找到")}");

#if UNITY_XR_INTERACTION_TOOLKIT
        var controllers = FindObjectsOfType<ActionBasedController>();
        Debug.Log($"ActionBasedController数量: {controllers.Length}");
        
        foreach (var controller in controllers)
        {
            Debug.Log($"控制器: {controller.name}");
            Debug.Log($"  GameObject激活: {controller.gameObject.activeInHierarchy}");
            Debug.Log($"  组件启用: {controller.enabled}");
            
            if (controller.selectAction.action != null)
            {
                Debug.Log($"  Select Action启用: {controller.selectAction.action.enabled}");
                Debug.Log($"  Select Action绑定数: {controller.selectAction.action.bindings.Count}");
            }
            
            if (controller.activateAction.action != null)
            {
                Debug.Log($"  Activate Action启用: {controller.activateAction.action.enabled}");
                Debug.Log($"  Activate Action绑定数: {controller.activateAction.action.bindings.Count}");
            }
        }

        var inputManager = FindObjectOfType<InputActionManager>();
        Debug.Log($"InputActionManager: {(inputManager != null ? "已找到" : "未找到")}");
        if (inputManager != null)
        {
            Debug.Log($"Action Assets数量: {inputManager.actionAssets.Count}");
        }
#else
        Debug.Log("XR Interaction Toolkit 未安装");
#endif
        Debug.Log("=== 状态报告结束 ===");
    }
}
