{ "pid": 35942, "tid": -1, "ph": "M", "name": "process_name", "args": { "name": "netcorerun.dll" } },
{ "pid": 35942, "tid": -1, "ph": "M", "name": "process_sort_index", "args": { "sort_index": "-1" } },
{ "pid": 35942, "tid": 1, "ph": "M", "name": "thread_name", "args": { "name": "" } },
{ "pid": 35942, "tid": 1, "ts": 1751349488895570, "dur": 380914, "ph": "X", "name": "BuildProgram", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1751349488896194, "dur": 30432, "ph": "X", "name": "BuildProgramContextConstructor", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1751349489126056, "dur": 77641, "ph": "X", "name": "SetupSpecificConfigImpl libil2cpp", "args": {"info": "release_Android_arm64"} },
{ "pid": 35942, "tid": 1, "ts": 1751349489130523, "dur": 48117, "ph": "X", "name": "SetupSpecificConfigImpl il2cpp", "args": {"info": "release_Android_arm64"} },
{ "pid": 35942, "tid": 1, "ts": 1751349489131030, "dur": 27905, "ph": "X", "name": "SetupSpecificConfigImpl bdwgc", "args": {"info": "release_Android_arm64"} },
{ "pid": 35942, "tid": 1, "ts": 1751349489159054, "dur": 2064, "ph": "X", "name": "SetupSpecificConfigImpl zlib", "args": {"info": "release_Android_arm64"} },
{ "pid": 35942, "tid": 1, "ts": 1751349489256502, "dur": 2201, "ph": "X", "name": "OutputData.Write", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1751349489258706, "dur": 17777, "ph": "X", "name": "Backend.Write", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1751349489259112, "dur": 12677, "ph": "X", "name": "JsonToString", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1751349489278825, "dur": 1162, "ph": "X", "name": "", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1751349489278575, "dur": 1428, "ph": "X", "name": "Write chrome-trace events", "args": {} },
