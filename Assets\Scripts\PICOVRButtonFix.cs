using UnityEngine;
using UnityEngine.UI;
using UnityEngine.EventSystems;

#if UNITY_XR_INTERACTION_TOOLKIT
using UnityEngine.XR.Interaction.Toolkit;
using UnityEngine.XR.Interaction.Toolkit.UI;
#endif

/// <summary>
/// PICO VR Button Fix
/// 
/// Fixes button interaction issues in PICO VR environment
/// Ensures proper event handling and interaction setup
/// </summary>
public class PICOVRButtonFix : MonoBehaviour
{
    [Header("Debug Settings")]
    [SerializeField] private bool enableDebugLogs = true;
    [SerializeField] private bool showRaycastHits = true;
    
    [Header("Interaction Settings")]
    [SerializeField] private bool forceTrackedDeviceRaycaster = true;
    [SerializeField] private bool ensureEventSystem = true;
    [SerializeField] private bool addButtonClickHandlers = true;
    
    // Private variables
    private Canvas targetCanvas;
    private Button[] allButtons;
    private EventSystem eventSystem;
    
    void Start()
    {
        StartCoroutine(InitializeVRButtonFix());
    }
    
    /// <summary>
    /// Initialize VR button fix
    /// </summary>
    private System.Collections.IEnumerator InitializeVRButtonFix()
    {
        // Wait a frame to ensure all components are loaded
        yield return null;
        
        if (enableDebugLogs)
            Debug.Log("[PICOVRButtonFix] Starting PICO VR button fix initialization");
        
        // Find target canvas
        FindTargetCanvas();
        
        // Setup event system
        if (ensureEventSystem)
        {
            SetupEventSystem();
        }
        
        // Setup raycaster
        if (forceTrackedDeviceRaycaster)
        {
            SetupTrackedDeviceRaycaster();
        }
        
        // Find all buttons
        FindAllButtons();
        
        // Add button click handlers
        if (addButtonClickHandlers)
        {
            SetupButtonClickHandlers();
        }
        
        // Verify setup
        VerifySetup();
        
        if (enableDebugLogs)
            Debug.Log("[PICOVRButtonFix] PICO VR button fix initialization completed");
    }
    
    /// <summary>
    /// Find target canvas
    /// </summary>
    private void FindTargetCanvas()
    {
        // First try to find canvas on this GameObject
        targetCanvas = GetComponent<Canvas>();
        
        // If not found, find any canvas in scene
        if (targetCanvas == null)
        {
            targetCanvas = FindObjectOfType<Canvas>();
        }
        
        if (targetCanvas == null)
        {
            Debug.LogError("[PICOVRButtonFix] No Canvas found in scene!");
            return;
        }
        
        if (enableDebugLogs)
            Debug.Log($"[PICOVRButtonFix] Found target canvas: {targetCanvas.name}");
    }
    
    /// <summary>
    /// Setup event system
    /// </summary>
    private void SetupEventSystem()
    {
        eventSystem = FindObjectOfType<EventSystem>();
        
        if (eventSystem == null)
        {
            GameObject eventSystemGO = new GameObject("EventSystem");
            eventSystem = eventSystemGO.AddComponent<EventSystem>();
            eventSystemGO.AddComponent<StandaloneInputModule>();
            
            if (enableDebugLogs)
                Debug.Log("[PICOVRButtonFix] Created new EventSystem");
        }
        else
        {
            if (enableDebugLogs)
                Debug.Log("[PICOVRButtonFix] Using existing EventSystem");
        }
        
#if UNITY_XR_INTERACTION_TOOLKIT
        // Add XR UI Input Module if not present
        var xrInputModule = eventSystem.GetComponent<XRUIInputModule>();
        if (xrInputModule == null)
        {
            xrInputModule = eventSystem.gameObject.AddComponent<XRUIInputModule>();
            
            // Disable StandaloneInputModule when using XR
            var standaloneModule = eventSystem.GetComponent<StandaloneInputModule>();
            if (standaloneModule != null)
            {
                standaloneModule.enabled = false;
            }
            
            if (enableDebugLogs)
                Debug.Log("[PICOVRButtonFix] Added XRUIInputModule");
        }
#endif
    }
    
    /// <summary>
    /// Setup tracked device raycaster
    /// </summary>
    private void SetupTrackedDeviceRaycaster()
    {
        if (targetCanvas == null) return;
        
#if UNITY_XR_INTERACTION_TOOLKIT
        var existingRaycaster = targetCanvas.GetComponent<GraphicRaycaster>();
        var trackedRaycaster = targetCanvas.GetComponent<TrackedDeviceGraphicRaycaster>();
        
        if (trackedRaycaster == null)
        {
            // Add TrackedDeviceGraphicRaycaster
            trackedRaycaster = targetCanvas.gameObject.AddComponent<TrackedDeviceGraphicRaycaster>();
            
            if (enableDebugLogs)
                Debug.Log("[PICOVRButtonFix] Added TrackedDeviceGraphicRaycaster");
        }
        
        // Remove standard GraphicRaycaster if TrackedDeviceGraphicRaycaster exists
        if (existingRaycaster != null && trackedRaycaster != null)
        {
            DestroyImmediate(existingRaycaster);
            
            if (enableDebugLogs)
                Debug.Log("[PICOVRButtonFix] Removed standard GraphicRaycaster");
        }
#else
        // Ensure standard GraphicRaycaster exists if XR Interaction Toolkit is not available
        if (targetCanvas.GetComponent<GraphicRaycaster>() == null)
        {
            targetCanvas.gameObject.AddComponent<GraphicRaycaster>();
            
            if (enableDebugLogs)
                Debug.Log("[PICOVRButtonFix] Added standard GraphicRaycaster");
        }
#endif
    }
    
    /// <summary>
    /// Find all buttons
    /// </summary>
    private void FindAllButtons()
    {
        if (targetCanvas == null) return;
        
        allButtons = targetCanvas.GetComponentsInChildren<Button>(true);
        
        if (enableDebugLogs)
            Debug.Log($"[PICOVRButtonFix] Found {allButtons.Length} buttons");
    }
    
    /// <summary>
    /// Setup button click handlers
    /// </summary>
    private void SetupButtonClickHandlers()
    {
        if (allButtons == null) return;
        
        foreach (var button in allButtons)
        {
            if (button == null) continue;
            
            // Ensure button is interactable
            if (!button.interactable && button.name.Contains("Start"))
            {
                // Special handling for start button - it might be disabled initially
                if (enableDebugLogs)
                    Debug.Log($"[PICOVRButtonFix] Start button found but disabled: {button.name}");
            }
            
            // Add event trigger for additional debugging
            var eventTrigger = button.GetComponent<EventTrigger>();
            if (eventTrigger == null)
            {
                eventTrigger = button.gameObject.AddComponent<EventTrigger>();
            }
            
            // Add pointer enter event
            var pointerEnter = new EventTrigger.Entry();
            pointerEnter.eventID = EventTriggerType.PointerEnter;
            pointerEnter.callback.AddListener((data) => {
                if (enableDebugLogs)
                    Debug.Log($"[PICOVRButtonFix] Pointer entered button: {button.name}");
            });
            eventTrigger.triggers.Add(pointerEnter);
            
            // Add pointer click event
            var pointerClick = new EventTrigger.Entry();
            pointerClick.eventID = EventTriggerType.PointerClick;
            pointerClick.callback.AddListener((data) => {
                if (enableDebugLogs)
                    Debug.Log($"[PICOVRButtonFix] Pointer clicked button: {button.name}");
                
                // Force invoke button click if it's interactable
                if (button.interactable)
                {
                    button.onClick.Invoke();
                    if (enableDebugLogs)
                        Debug.Log($"[PICOVRButtonFix] Manually invoked button click: {button.name}");
                }
            });
            eventTrigger.triggers.Add(pointerClick);
            
            if (enableDebugLogs)
                Debug.Log($"[PICOVRButtonFix] Setup click handler for button: {button.name}");
        }
    }
    
    /// <summary>
    /// Verify setup
    /// </summary>
    private void VerifySetup()
    {
        if (!enableDebugLogs) return;
        
        Debug.Log("=== PICO VR Button Fix Setup Verification ===");
        
        // Check Canvas
        if (targetCanvas != null)
        {
            Debug.Log($"Canvas: {targetCanvas.name} (RenderMode: {targetCanvas.renderMode})");
            
            // Check Raycaster
            var raycaster = targetCanvas.GetComponent<GraphicRaycaster>();
            var trackedRaycaster = targetCanvas.GetComponent<UnityEngine.XR.Interaction.Toolkit.UI.TrackedDeviceGraphicRaycaster>();
            
            if (trackedRaycaster != null)
            {
                Debug.Log("Raycaster: TrackedDeviceGraphicRaycaster (Good for VR)");
            }
            else if (raycaster != null)
            {
                Debug.Log("Raycaster: Standard GraphicRaycaster (May not work well in VR)");
            }
            else
            {
                Debug.LogWarning("Raycaster: None found!");
            }
        }
        
        // Check EventSystem
        if (eventSystem != null)
        {
            Debug.Log($"EventSystem: {eventSystem.name}");
            
#if UNITY_XR_INTERACTION_TOOLKIT
            var xrInputModule = eventSystem.GetComponent<XRUIInputModule>();
            if (xrInputModule != null)
            {
                Debug.Log("Input Module: XRUIInputModule (Good for VR)");
            }
            else
            {
                Debug.LogWarning("Input Module: XRUIInputModule not found!");
            }
#endif
        }
        
        // Check Buttons
        Debug.Log($"Buttons found: {(allButtons != null ? allButtons.Length : 0)}");
        
        Debug.Log("=== End Verification ===");
    }
    
    /// <summary>
    /// Manual button test (for debugging)
    /// </summary>
    [ContextMenu("Test Button Clicks")]
    public void TestButtonClicks()
    {
        if (allButtons == null) return;
        
        foreach (var button in allButtons)
        {
            if (button != null && button.interactable)
            {
                Debug.Log($"[PICOVRButtonFix] Testing button: {button.name}");
                button.onClick.Invoke();
            }
        }
    }
    
    void Update()
    {
        // Debug raycast hits
        if (showRaycastHits && enableDebugLogs)
        {
            // This will be called every frame, so we limit the logging
            if (Time.frameCount % 60 == 0) // Log once per second at 60fps
            {
                CheckRaycastHits();
            }
        }
    }
    
    /// <summary>
    /// Check raycast hits for debugging
    /// </summary>
    private void CheckRaycastHits()
    {
#if UNITY_XR_INTERACTION_TOOLKIT
        var rayInteractors = FindObjectsOfType<XRRayInteractor>();
        foreach (var rayInteractor in rayInteractors)
        {
            if (rayInteractor.TryGetCurrentUIRaycastResult(out RaycastResult result))
            {
                if (result.gameObject != null)
                {
                    Debug.Log($"[PICOVRButtonFix] Ray hitting UI: {result.gameObject.name}");
                }
            }
        }
#endif
    }
}
