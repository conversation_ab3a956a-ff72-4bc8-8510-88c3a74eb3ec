using UnityEngine;
using UnityEngine.UI;
using TMPro;

/// <summary>
/// VR设置UI调试器
/// 
/// 专门用于调试和修复VR设置UI在PICO设备上不可见的问题
/// 提供详细的诊断信息和自动修复功能
/// </summary>
public class VRSettingsUIDebugger : MonoBehaviour
{
    [Header("调试设置")]
    [SerializeField] private bool enableDebugLogs = true;
    [SerializeField] private bool autoFixOnStart = true;
    [SerializeField] private bool showDebugUI = true;
    
    [Header("强制显示设置")]
    [SerializeField] private bool forceShowSettingsButton = true;
    [SerializeField] private Vector3 forcedButtonPosition = new Vector3(-2f, 1f, 2f);
    [SerializeField] private Vector3 forcedButtonScale = new Vector3(0.1f, 0.1f, 0.1f);
    
    [Header("Canvas修复设置")]
    [SerializeField] private bool fixCanvasSettings = true;
    [SerializeField] private float canvasDistance = 3f;
    [SerializeField] private float canvasScale = 0.01f;
    
    // 私有变量
    private VRSettingsUI settingsUI;
    private Canvas settingsCanvas;
    private Button settingsButton;
    private Camera vrCamera;
    private bool isFixed = false;
    
    void Start()
    {
        if (autoFixOnStart)
        {
            StartCoroutine(InitializeAndFix());
        }
    }
    
    void Update()
    {
        // 按F11键手动触发调试和修复
        if (Input.GetKeyDown(KeyCode.F11))
        {
            StartCoroutine(InitializeAndFix());
        }
        
        // 按F12键显示详细状态
        if (Input.GetKeyDown(KeyCode.F12))
        {
            ShowDetailedStatus();
        }
    }
    
    /// <summary>
    /// 初始化并修复VR设置UI
    /// </summary>
    private System.Collections.IEnumerator InitializeAndFix()
    {
        Debug.Log("[VRSettingsUIDebugger] 开始诊断和修复VR设置UI");
        
        // 等待一帧确保所有组件都已加载
        yield return null;
        
        // 查找组件
        FindComponents();
        
        // 诊断问题
        DiagnoseIssues();
        
        // 应用修复
        ApplyFixes();
        
        // 验证修复结果
        VerifyFixes();
        
        Debug.Log("[VRSettingsUIDebugger] VR设置UI诊断和修复完成");
    }
    
    /// <summary>
    /// 查找VR设置UI组件
    /// </summary>
    private void FindComponents()
    {
        // 查找VRSettingsUI
        settingsUI = FindObjectOfType<VRSettingsUI>();
        if (settingsUI != null)
        {
            Debug.Log("[VRSettingsUIDebugger] ✅ 找到VRSettingsUI组件");
        }
        else
        {
            Debug.LogWarning("[VRSettingsUIDebugger] ⚠️ 未找到VRSettingsUI组件");
        }
        
        // 查找设置Canvas
        Canvas[] canvases = FindObjectsOfType<Canvas>();
        foreach (var canvas in canvases)
        {
            if (canvas.name.Contains("Settings") || canvas.name.Contains("设置"))
            {
                settingsCanvas = canvas;
                break;
            }
        }
        
        if (settingsCanvas == null && canvases.Length > 0)
        {
            // 如果没找到设置Canvas，使用第一个Canvas
            settingsCanvas = canvases[0];
        }
        
        if (settingsCanvas != null)
        {
            Debug.Log($"[VRSettingsUIDebugger] ✅ 找到设置Canvas: {settingsCanvas.name}");
        }
        else
        {
            Debug.LogError("[VRSettingsUIDebugger] ❌ 未找到任何Canvas");
        }
        
        // 查找设置按钮
        Button[] buttons = FindObjectsOfType<Button>();
        foreach (var button in buttons)
        {
            if (button.name.Contains("Settings") || button.name.Contains("设置"))
            {
                settingsButton = button;
                break;
            }
        }
        
        if (settingsButton != null)
        {
            Debug.Log($"[VRSettingsUIDebugger] ✅ 找到设置按钮: {settingsButton.name}");
        }
        else
        {
            Debug.LogWarning("[VRSettingsUIDebugger] ⚠️ 未找到设置按钮");
        }
        
        // 查找VR摄像机
        vrCamera = Camera.main;
        if (vrCamera == null)
        {
            vrCamera = FindObjectOfType<Camera>();
        }
        
        if (vrCamera != null)
        {
            Debug.Log($"[VRSettingsUIDebugger] ✅ 找到VR摄像机: {vrCamera.name}");
        }
        else
        {
            Debug.LogError("[VRSettingsUIDebugger] ❌ 未找到VR摄像机");
        }
    }
    
    /// <summary>
    /// 诊断问题
    /// </summary>
    private void DiagnoseIssues()
    {
        Debug.Log("=== VR设置UI问题诊断 ===");
        
        // 检查Canvas设置
        if (settingsCanvas != null)
        {
            Debug.Log($"Canvas渲染模式: {settingsCanvas.renderMode}");
            Debug.Log($"Canvas位置: {settingsCanvas.transform.position}");
            Debug.Log($"Canvas缩放: {settingsCanvas.transform.localScale}");
            Debug.Log($"Canvas旋转: {settingsCanvas.transform.rotation.eulerAngles}");
            Debug.Log($"Canvas激活状态: {settingsCanvas.gameObject.activeInHierarchy}");
            
            if (settingsCanvas.renderMode != RenderMode.WorldSpace)
            {
                Debug.LogWarning("⚠️ Canvas不是WorldSpace模式，在VR中可能不可见");
            }
            
            if (settingsCanvas.worldCamera == null)
            {
                Debug.LogWarning("⚠️ Canvas的worldCamera未设置");
            }
            
            // 检查Canvas距离
            if (vrCamera != null)
            {
                float distance = Vector3.Distance(settingsCanvas.transform.position, vrCamera.transform.position);
                Debug.Log($"Canvas距离摄像机: {distance:F2}米");
                
                if (distance < 0.5f)
                {
                    Debug.LogWarning("⚠️ Canvas距离摄像机太近，可能看不见");
                }
                else if (distance > 10f)
                {
                    Debug.LogWarning("⚠️ Canvas距离摄像机太远，可能看不清");
                }
            }
        }
        
        // 检查设置按钮
        if (settingsButton != null)
        {
            Debug.Log($"设置按钮位置: {settingsButton.transform.position}");
            Debug.Log($"设置按钮缩放: {settingsButton.transform.localScale}");
            Debug.Log($"设置按钮激活状态: {settingsButton.gameObject.activeInHierarchy}");
            Debug.Log($"设置按钮可交互: {settingsButton.interactable}");
            
            // 检查按钮的Image组件
            Image buttonImage = settingsButton.GetComponent<Image>();
            if (buttonImage != null)
            {
                Debug.Log($"按钮图片颜色: {buttonImage.color}");
                Debug.Log($"按钮图片Alpha: {buttonImage.color.a}");
                
                if (buttonImage.color.a < 0.1f)
                {
                    Debug.LogWarning("⚠️ 按钮透明度太低，可能看不见");
                }
            }
        }
        
        Debug.Log("=== 诊断完成 ===");
    }
    
    /// <summary>
    /// 应用修复
    /// </summary>
    private void ApplyFixes()
    {
        Debug.Log("[VRSettingsUIDebugger] 开始应用修复");
        
        // 修复Canvas设置
        if (fixCanvasSettings && settingsCanvas != null)
        {
            FixCanvasSettings();
        }
        
        // 强制显示设置按钮
        if (forceShowSettingsButton && settingsButton != null)
        {
            ForceShowSettingsButton();
        }
        
        // 如果没有设置按钮，创建一个简单的
        if (settingsButton == null && settingsCanvas != null)
        {
            CreateEmergencySettingsButton();
        }
        
        isFixed = true;
    }
    
    /// <summary>
    /// 修复Canvas设置
    /// </summary>
    private void FixCanvasSettings()
    {
        if (settingsCanvas == null || vrCamera == null) return;
        
        Debug.Log("[VRSettingsUIDebugger] 修复Canvas设置");
        
        // 设置为WorldSpace模式
        settingsCanvas.renderMode = RenderMode.WorldSpace;
        settingsCanvas.worldCamera = vrCamera;
        
        // 设置Canvas位置（在摄像机前方）
        Vector3 cameraPosition = vrCamera.transform.position;
        Vector3 cameraForward = vrCamera.transform.forward;
        Vector3 targetPosition = cameraPosition + cameraForward * canvasDistance;
        
        settingsCanvas.transform.position = targetPosition;
        settingsCanvas.transform.localScale = Vector3.one * canvasScale;
        
        // 让Canvas面向摄像机
        settingsCanvas.transform.LookAt(vrCamera.transform);
        settingsCanvas.transform.Rotate(0, 180, 0);
        
        // 确保Canvas激活
        settingsCanvas.gameObject.SetActive(true);
        
        Debug.Log($"[VRSettingsUIDebugger] Canvas位置设置为: {targetPosition}");
    }
    
    /// <summary>
    /// 强制显示设置按钮
    /// </summary>
    private void ForceShowSettingsButton()
    {
        if (settingsButton == null) return;
        
        Debug.Log("[VRSettingsUIDebugger] 强制显示设置按钮");
        
        // 确保按钮激活
        settingsButton.gameObject.SetActive(true);
        settingsButton.interactable = true;
        
        // 设置按钮位置（世界坐标）
        settingsButton.transform.position = forcedButtonPosition;
        settingsButton.transform.localScale = forcedButtonScale;
        
        // 确保按钮颜色可见
        Image buttonImage = settingsButton.GetComponent<Image>();
        if (buttonImage != null)
        {
            Color color = buttonImage.color;
            color.a = Mathf.Max(color.a, 0.8f); // 确保不透明
            buttonImage.color = color;
        }
        
        // 如果按钮在Canvas空间中，调整RectTransform
        RectTransform rectTransform = settingsButton.GetComponent<RectTransform>();
        if (rectTransform != null && settingsButton.transform.parent == settingsCanvas?.transform)
        {
            rectTransform.anchorMin = new Vector2(0f, 1f);
            rectTransform.anchorMax = new Vector2(0f, 1f);
            rectTransform.anchoredPosition = new Vector2(100, -100);
            rectTransform.sizeDelta = new Vector2(100, 100);
        }
        
        Debug.Log($"[VRSettingsUIDebugger] 设置按钮位置: {settingsButton.transform.position}");
    }
    
    /// <summary>
    /// 创建紧急设置按钮
    /// </summary>
    private void CreateEmergencySettingsButton()
    {
        Debug.Log("[VRSettingsUIDebugger] 创建紧急设置按钮");
        
        GameObject buttonGO = new GameObject("EmergencySettingsButton");
        buttonGO.transform.SetParent(settingsCanvas.transform, false);
        
        // 添加Image和Button组件
        Image buttonImage = buttonGO.AddComponent<Image>();
        Button button = buttonGO.AddComponent<Button>();
        
        // 设置按钮样式
        buttonImage.color = Color.red; // 使用红色确保可见
        
        // 设置RectTransform
        RectTransform rectTransform = buttonGO.GetComponent<RectTransform>();
        rectTransform.anchorMin = new Vector2(0f, 1f);
        rectTransform.anchorMax = new Vector2(0f, 1f);
        rectTransform.anchoredPosition = new Vector2(100, -100);
        rectTransform.sizeDelta = new Vector2(100, 100);
        
        // 添加文本
        GameObject textGO = new GameObject("Text");
        textGO.transform.SetParent(buttonGO.transform, false);
        
        TextMeshProUGUI buttonText = textGO.AddComponent<TextMeshProUGUI>();
        buttonText.text = "设置";
        buttonText.fontSize = 24;
        buttonText.color = Color.white;
        buttonText.alignment = TextAlignmentOptions.Center;
        
        RectTransform textRect = textGO.GetComponent<RectTransform>();
        textRect.anchorMin = Vector2.zero;
        textRect.anchorMax = Vector2.one;
        textRect.offsetMin = Vector2.zero;
        textRect.offsetMax = Vector2.zero;
        
        // 添加点击事件
        button.onClick.AddListener(() => {
            Debug.Log("[VRSettingsUIDebugger] 紧急设置按钮被点击");
        });
        
        settingsButton = button;
        
        Debug.Log("[VRSettingsUIDebugger] 紧急设置按钮创建完成");
    }
    
    /// <summary>
    /// 验证修复结果
    /// </summary>
    private void VerifyFixes()
    {
        Debug.Log("=== 修复结果验证 ===");
        
        if (settingsCanvas != null)
        {
            Debug.Log($"✅ Canvas位置: {settingsCanvas.transform.position}");
            Debug.Log($"✅ Canvas激活: {settingsCanvas.gameObject.activeInHierarchy}");
        }
        
        if (settingsButton != null)
        {
            Debug.Log($"✅ 设置按钮位置: {settingsButton.transform.position}");
            Debug.Log($"✅ 设置按钮激活: {settingsButton.gameObject.activeInHierarchy}");
            Debug.Log($"✅ 设置按钮可交互: {settingsButton.interactable}");
        }
        
        if (vrCamera != null && settingsCanvas != null)
        {
            float distance = Vector3.Distance(settingsCanvas.transform.position, vrCamera.transform.position);
            Debug.Log($"✅ Canvas距离摄像机: {distance:F2}米");
        }
        
        Debug.Log("=== 验证完成 ===");
    }
    
    /// <summary>
    /// 显示详细状态
    /// </summary>
    private void ShowDetailedStatus()
    {
        Debug.Log("=== VR设置UI详细状态 ===");
        
        // 重新查找组件
        FindComponents();
        
        // 显示所有Canvas信息
        Canvas[] allCanvases = FindObjectsOfType<Canvas>();
        Debug.Log($"场景中Canvas数量: {allCanvases.Length}");
        
        for (int i = 0; i < allCanvases.Length; i++)
        {
            Canvas canvas = allCanvases[i];
            Debug.Log($"Canvas {i}: {canvas.name}");
            Debug.Log($"  渲染模式: {canvas.renderMode}");
            Debug.Log($"  位置: {canvas.transform.position}");
            Debug.Log($"  激活: {canvas.gameObject.activeInHierarchy}");
        }
        
        // 显示所有Button信息
        Button[] allButtons = FindObjectsOfType<Button>();
        Debug.Log($"场景中Button数量: {allButtons.Length}");
        
        for (int i = 0; i < allButtons.Length; i++)
        {
            Button button = allButtons[i];
            Debug.Log($"Button {i}: {button.name}");
            Debug.Log($"  位置: {button.transform.position}");
            Debug.Log($"  激活: {button.gameObject.activeInHierarchy}");
            Debug.Log($"  可交互: {button.interactable}");
        }
        
        Debug.Log("=== 状态显示完成 ===");
    }
    
    void OnGUI()
    {
        if (!showDebugUI || !isFixed) return;
        
        GUILayout.BeginArea(new Rect(10, 10, 300, 200));
        GUILayout.Label("VR设置UI调试器", GUI.skin.box);
        
        if (GUILayout.Button("重新修复UI"))
        {
            StartCoroutine(InitializeAndFix());
        }
        
        if (GUILayout.Button("显示详细状态"))
        {
            ShowDetailedStatus();
        }
        
        if (settingsButton != null)
        {
            GUILayout.Label($"设置按钮位置: {settingsButton.transform.position}");
        }
        
        if (settingsCanvas != null && vrCamera != null)
        {
            float distance = Vector3.Distance(settingsCanvas.transform.position, vrCamera.transform.position);
            GUILayout.Label($"Canvas距离: {distance:F2}米");
        }
        
        GUILayout.EndArea();
    }
}
