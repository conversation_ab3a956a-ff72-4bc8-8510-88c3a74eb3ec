﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





// 0x00000001 System.Void AlignmentTest::Start()
extern void AlignmentTest_Start_m2992385C4EC00DF4CBD504AFE7C4733AB74EA27C (void);
// 0x00000002 System.Void AlignmentTest::Update()
extern void AlignmentTest_Update_mC549F80E002E8792FC35718DB75096B24417796C (void);
// 0x00000003 System.Void AlignmentTest::ValidateReferencesPoints()
extern void AlignmentTest_ValidateReferencesPoints_m204A4E87E164E0B9A7BF3EF65CF1A40DF03C9FD7 (void);
// 0x00000004 System.Void AlignmentTest::PrintReferencePointAxes(AssemblyPart)
extern void AlignmentTest_PrintReferencePointAxes_mC8B51A9797C0BAB6F31DF8B4856DBCCFD6DF07E5 (void);
// 0x00000005 System.Void AlignmentTest::TestAlignment()
extern void AlignmentTest_TestAlignment_m0FE6AF80AE013ABF0311E74A818A1739C362664B (void);
// 0x00000006 System.Int32 AlignmentTest::GetReferencePointIndex(AssemblyPart,UnityEngine.Transform)
extern void AlignmentTest_GetReferencePointIndex_mD953C570EF17CBE25C6A6FCEEAC7F098213346B6 (void);
// 0x00000007 System.Void AlignmentTest::ResetSourcePart()
extern void AlignmentTest_ResetSourcePart_mD9BD350634284BC34A214C90A9097D928BA02877 (void);
// 0x00000008 System.Void AlignmentTest::AnalyzeRotationConstraints()
extern void AlignmentTest_AnalyzeRotationConstraints_m6F5468E3CD3F9C5FF79861F198486D2FCF442C06 (void);
// 0x00000009 System.Void AlignmentTest::OnDrawGizmos()
extern void AlignmentTest_OnDrawGizmos_m3B6D469A036F4294CCB23D0FC7ED12B4370CD139 (void);
// 0x0000000A System.Void AlignmentTest::.ctor()
extern void AlignmentTest__ctor_m03EB6CA5E2A81935E8B7633CF50EC4512D82D9E4 (void);
// 0x0000000B System.Void AssemblyAnimation::Start()
extern void AssemblyAnimation_Start_m6B156AEC545BABFC9E83268A7242AB3E6AAD57A0 (void);
// 0x0000000C System.Collections.IEnumerator AssemblyAnimation::AssembleAnimation()
extern void AssemblyAnimation_AssembleAnimation_mD23C831759F0A86521787D49EB39059B229A87DF (void);
// 0x0000000D UnityEngine.Quaternion AssemblyAnimation::CalculateTargetRotation()
extern void AssemblyAnimation_CalculateTargetRotation_mA6508A3B3E9CD605EDDF12699318800E66B5A0D6 (void);
// 0x0000000E UnityEngine.Vector3 AssemblyAnimation::GetAxisDirection(UnityEngine.Transform)
extern void AssemblyAnimation_GetAxisDirection_m8AFF54066643C0EC9792A5D5060431F772F5F983 (void);
// 0x0000000F System.Collections.IEnumerator AssemblyAnimation::MoveAndRotate(UnityEngine.Transform,UnityEngine.Vector3,UnityEngine.Quaternion,System.Single,System.Boolean)
extern void AssemblyAnimation_MoveAndRotate_mBF7B193EA8886BD53D4AF584C7CC0226BFF99D49 (void);
// 0x00000010 System.Void AssemblyAnimation::UpdateMountPointPosition(UnityEngine.Transform,UnityEngine.Transform)
extern void AssemblyAnimation_UpdateMountPointPosition_mEF4CC16237F1B31ECDC5989FCAC099D13AE9F060 (void);
// 0x00000011 System.Collections.IEnumerator AssemblyAnimation::Move(UnityEngine.Transform,UnityEngine.Vector3,System.Single)
extern void AssemblyAnimation_Move_mDD30F014C6D6EB3049C99A57EEDA225B59E675FA (void);
// 0x00000012 System.Collections.IEnumerator AssemblyAnimation::RotateAndMoveNut(UnityEngine.Transform,System.Single,UnityEngine.Vector3,System.Single)
extern void AssemblyAnimation_RotateAndMoveNut_m2A5A259FCF9DB2A7C975DC329853C3C237218F2A (void);
// 0x00000013 System.Collections.IEnumerator AssemblyAnimation::RotateAroundPivot(UnityEngine.Transform,UnityEngine.Vector3,UnityEngine.Vector3,System.Single,System.Single)
extern void AssemblyAnimation_RotateAroundPivot_m11F4F892FE211459F0A8FE6E7E26A3D16020ED9C (void);
// 0x00000014 System.Void AssemblyAnimation::OnDrawGizmos()
extern void AssemblyAnimation_OnDrawGizmos_m9C46EA1309C3833F2A60966C729161043E990B0C (void);
// 0x00000015 System.Void AssemblyAnimation::PrintDebugInfo()
extern void AssemblyAnimation_PrintDebugInfo_m886797F456E00CE82212CA80B857BED824C4382E (void);
// 0x00000016 System.Void AssemblyAnimation::Update()
extern void AssemblyAnimation_Update_m4775FEC5063A69CB7B4062F471A710822952F9FF (void);
// 0x00000017 System.Collections.IEnumerator AssemblyAnimation::MoveScrewImproved(UnityEngine.Vector3)
extern void AssemblyAnimation_MoveScrewImproved_m34A71C209750820105B716048F82429A90D11EA8 (void);
// 0x00000018 System.Collections.IEnumerator AssemblyAnimation::RotateInPlace(UnityEngine.Transform,UnityEngine.Transform,UnityEngine.Quaternion,System.Single)
extern void AssemblyAnimation_RotateInPlace_mC016BA5F2E9EFCF628B4E57E3299B095D0087D82 (void);
// 0x00000019 System.Collections.IEnumerator AssemblyAnimation::MoveNutImproved(UnityEngine.Vector3)
extern void AssemblyAnimation_MoveNutImproved_m3787B1F890FBC2A73697F7FC99833FFC5360C68E (void);
// 0x0000001A System.Collections.IEnumerator AssemblyAnimation::MoveAndRotateByMountPoint(UnityEngine.Transform,UnityEngine.Transform,UnityEngine.Vector3,UnityEngine.Quaternion,System.Single)
extern void AssemblyAnimation_MoveAndRotateByMountPoint_m0490CD567207CBB769708FEC8C33887C1F0EDA3D (void);
// 0x0000001B System.Collections.IEnumerator AssemblyAnimation::MoveByMountPoint(UnityEngine.Transform,UnityEngine.Transform,UnityEngine.Vector3,System.Single)
extern void AssemblyAnimation_MoveByMountPoint_m3F9E1AB39B0010533A6D51206BA700BC14168F57 (void);
// 0x0000001C System.Void AssemblyAnimation::AlignTransformPosition(UnityEngine.Transform,UnityEngine.Vector3)
extern void AssemblyAnimation_AlignTransformPosition_m3D151DC8759C7E823C727DFD49ED7E446ADDF9F7 (void);
// 0x0000001D System.Void AssemblyAnimation::.ctor()
extern void AssemblyAnimation__ctor_m3B6ADFF0903CB9927E995FE63DC4FEDCE5AEE408 (void);
// 0x0000001E System.Void AssemblyAnimation/<AssembleAnimation>d__21::.ctor(System.Int32)
extern void U3CAssembleAnimationU3Ed__21__ctor_m486944FD17AD41EF0EB65721E434EDD0C859FBC0 (void);
// 0x0000001F System.Void AssemblyAnimation/<AssembleAnimation>d__21::System.IDisposable.Dispose()
extern void U3CAssembleAnimationU3Ed__21_System_IDisposable_Dispose_mC982219E4D12769F0121C6B9F5BC0A547268F74C (void);
// 0x00000020 System.Boolean AssemblyAnimation/<AssembleAnimation>d__21::MoveNext()
extern void U3CAssembleAnimationU3Ed__21_MoveNext_m9195D7E71EFAA2CD414BCE34293DD023C25543BA (void);
// 0x00000021 System.Object AssemblyAnimation/<AssembleAnimation>d__21::System.Collections.Generic.IEnumerator<System.Object>.get_Current()
extern void U3CAssembleAnimationU3Ed__21_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mDCA1BD8238361DF92C2206969BA75929D5886379 (void);
// 0x00000022 System.Void AssemblyAnimation/<AssembleAnimation>d__21::System.Collections.IEnumerator.Reset()
extern void U3CAssembleAnimationU3Ed__21_System_Collections_IEnumerator_Reset_m6E8DD4B28AB37EE167F18FB88D936E3295AAF610 (void);
// 0x00000023 System.Object AssemblyAnimation/<AssembleAnimation>d__21::System.Collections.IEnumerator.get_Current()
extern void U3CAssembleAnimationU3Ed__21_System_Collections_IEnumerator_get_Current_m0C9027DEDA977EF87AD0476301DAC9FAA27D0B67 (void);
// 0x00000024 System.Void AssemblyAnimation/<MoveAndRotate>d__24::.ctor(System.Int32)
extern void U3CMoveAndRotateU3Ed__24__ctor_m14BA0EF9D087AA07D1FA44644D8D76EE7B3C1EAC (void);
// 0x00000025 System.Void AssemblyAnimation/<MoveAndRotate>d__24::System.IDisposable.Dispose()
extern void U3CMoveAndRotateU3Ed__24_System_IDisposable_Dispose_m122DFE0E8B6098A97535CBDE8823AB03ECE2275F (void);
// 0x00000026 System.Boolean AssemblyAnimation/<MoveAndRotate>d__24::MoveNext()
extern void U3CMoveAndRotateU3Ed__24_MoveNext_mFA6F675DE65A4AA8970D97C434AEBA3849B55BEE (void);
// 0x00000027 System.Object AssemblyAnimation/<MoveAndRotate>d__24::System.Collections.Generic.IEnumerator<System.Object>.get_Current()
extern void U3CMoveAndRotateU3Ed__24_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m252FE712A52F09C50A074FB2D672EEB600BFB317 (void);
// 0x00000028 System.Void AssemblyAnimation/<MoveAndRotate>d__24::System.Collections.IEnumerator.Reset()
extern void U3CMoveAndRotateU3Ed__24_System_Collections_IEnumerator_Reset_mEE4599C8344317F907FAFE052180C8CAC745C1E8 (void);
// 0x00000029 System.Object AssemblyAnimation/<MoveAndRotate>d__24::System.Collections.IEnumerator.get_Current()
extern void U3CMoveAndRotateU3Ed__24_System_Collections_IEnumerator_get_Current_mEB6C6624554E112D7CB1C745504DC9ECC5D01589 (void);
// 0x0000002A System.Void AssemblyAnimation/<Move>d__26::.ctor(System.Int32)
extern void U3CMoveU3Ed__26__ctor_m9716B47FCF49AC1DB55C0D018EBC2A9E888E1351 (void);
// 0x0000002B System.Void AssemblyAnimation/<Move>d__26::System.IDisposable.Dispose()
extern void U3CMoveU3Ed__26_System_IDisposable_Dispose_m13AAE265F854DD6C74470BA76FB235FC2A3AE5BD (void);
// 0x0000002C System.Boolean AssemblyAnimation/<Move>d__26::MoveNext()
extern void U3CMoveU3Ed__26_MoveNext_m09017BC9C35757AEB0E89872C7623A4ED9CA3205 (void);
// 0x0000002D System.Object AssemblyAnimation/<Move>d__26::System.Collections.Generic.IEnumerator<System.Object>.get_Current()
extern void U3CMoveU3Ed__26_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m184E395C74F4E6536C76E8F549D0102279BD21EA (void);
// 0x0000002E System.Void AssemblyAnimation/<Move>d__26::System.Collections.IEnumerator.Reset()
extern void U3CMoveU3Ed__26_System_Collections_IEnumerator_Reset_m48E2711DAA629D40F110620E7C00F3DF1F28234E (void);
// 0x0000002F System.Object AssemblyAnimation/<Move>d__26::System.Collections.IEnumerator.get_Current()
extern void U3CMoveU3Ed__26_System_Collections_IEnumerator_get_Current_m971AC46F3CF6750BBE7F0C586BD91ACC97DCD68E (void);
// 0x00000030 System.Void AssemblyAnimation/<RotateAndMoveNut>d__27::.ctor(System.Int32)
extern void U3CRotateAndMoveNutU3Ed__27__ctor_m0AA3BFBE304E14564FA817E68A63F9E529D8C6DB (void);
// 0x00000031 System.Void AssemblyAnimation/<RotateAndMoveNut>d__27::System.IDisposable.Dispose()
extern void U3CRotateAndMoveNutU3Ed__27_System_IDisposable_Dispose_m9CCAE6E643EA4FC7C0EADAF67D1357BC131FAE0A (void);
// 0x00000032 System.Boolean AssemblyAnimation/<RotateAndMoveNut>d__27::MoveNext()
extern void U3CRotateAndMoveNutU3Ed__27_MoveNext_mB1AAAF5FEBAF1D2B290AB05349BF2683CB52FA17 (void);
// 0x00000033 System.Object AssemblyAnimation/<RotateAndMoveNut>d__27::System.Collections.Generic.IEnumerator<System.Object>.get_Current()
extern void U3CRotateAndMoveNutU3Ed__27_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m7417189A45F030516B044C8A83724507E296718C (void);
// 0x00000034 System.Void AssemblyAnimation/<RotateAndMoveNut>d__27::System.Collections.IEnumerator.Reset()
extern void U3CRotateAndMoveNutU3Ed__27_System_Collections_IEnumerator_Reset_m8368D49D750EF526B25970EDAF45917ADC48FA54 (void);
// 0x00000035 System.Object AssemblyAnimation/<RotateAndMoveNut>d__27::System.Collections.IEnumerator.get_Current()
extern void U3CRotateAndMoveNutU3Ed__27_System_Collections_IEnumerator_get_Current_mC164F69E0B2620D4B773E91FDD95E76EDE2183D5 (void);
// 0x00000036 System.Void AssemblyAnimation/<RotateAroundPivot>d__28::.ctor(System.Int32)
extern void U3CRotateAroundPivotU3Ed__28__ctor_m7514BDD06AE5CBE0394539FF5B388116E252AF59 (void);
// 0x00000037 System.Void AssemblyAnimation/<RotateAroundPivot>d__28::System.IDisposable.Dispose()
extern void U3CRotateAroundPivotU3Ed__28_System_IDisposable_Dispose_mFFC5549CC8E641BDC1D8D67D1899ED2DF9F577C9 (void);
// 0x00000038 System.Boolean AssemblyAnimation/<RotateAroundPivot>d__28::MoveNext()
extern void U3CRotateAroundPivotU3Ed__28_MoveNext_m7049D492430B44C01E36B6966FCBF05AE1CA7487 (void);
// 0x00000039 System.Object AssemblyAnimation/<RotateAroundPivot>d__28::System.Collections.Generic.IEnumerator<System.Object>.get_Current()
extern void U3CRotateAroundPivotU3Ed__28_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mD87D0B599C83DB3030C042558E7BC11DF134029F (void);
// 0x0000003A System.Void AssemblyAnimation/<RotateAroundPivot>d__28::System.Collections.IEnumerator.Reset()
extern void U3CRotateAroundPivotU3Ed__28_System_Collections_IEnumerator_Reset_m8F009E68606B9434FF94AEA9025D3D0220F95A0D (void);
// 0x0000003B System.Object AssemblyAnimation/<RotateAroundPivot>d__28::System.Collections.IEnumerator.get_Current()
extern void U3CRotateAroundPivotU3Ed__28_System_Collections_IEnumerator_get_Current_m995BEB903FD7837F6FECCE90B73058479F3E57F3 (void);
// 0x0000003C System.Void AssemblyAnimation/<MoveScrewImproved>d__32::.ctor(System.Int32)
extern void U3CMoveScrewImprovedU3Ed__32__ctor_mCDE0CDA7508BE39FB54800AF3DB2A2F399815EC6 (void);
// 0x0000003D System.Void AssemblyAnimation/<MoveScrewImproved>d__32::System.IDisposable.Dispose()
extern void U3CMoveScrewImprovedU3Ed__32_System_IDisposable_Dispose_mC1BD0D23CC393EBECFCEB68B629D2BB8900C11D2 (void);
// 0x0000003E System.Boolean AssemblyAnimation/<MoveScrewImproved>d__32::MoveNext()
extern void U3CMoveScrewImprovedU3Ed__32_MoveNext_mE0EFAD28F37BFBBD90687BAA2F449DC7136C5459 (void);
// 0x0000003F System.Object AssemblyAnimation/<MoveScrewImproved>d__32::System.Collections.Generic.IEnumerator<System.Object>.get_Current()
extern void U3CMoveScrewImprovedU3Ed__32_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mE6549B0292EA7BBD8E33A7CA1AA62171C351CC4A (void);
// 0x00000040 System.Void AssemblyAnimation/<MoveScrewImproved>d__32::System.Collections.IEnumerator.Reset()
extern void U3CMoveScrewImprovedU3Ed__32_System_Collections_IEnumerator_Reset_mCC6F396AE06E5BCA37EBB1F5F43EDDB5EF0AFFA7 (void);
// 0x00000041 System.Object AssemblyAnimation/<MoveScrewImproved>d__32::System.Collections.IEnumerator.get_Current()
extern void U3CMoveScrewImprovedU3Ed__32_System_Collections_IEnumerator_get_Current_m822E2E6F2C3DC8D60CDFA7D759B9588A8B793FDE (void);
// 0x00000042 System.Void AssemblyAnimation/<RotateInPlace>d__33::.ctor(System.Int32)
extern void U3CRotateInPlaceU3Ed__33__ctor_mC8C4126A76A5AFA2BED0922A9A560AEBA42FD4C4 (void);
// 0x00000043 System.Void AssemblyAnimation/<RotateInPlace>d__33::System.IDisposable.Dispose()
extern void U3CRotateInPlaceU3Ed__33_System_IDisposable_Dispose_m4D19E780BD89A5BE3FD6033C757793FAAED1EB0C (void);
// 0x00000044 System.Boolean AssemblyAnimation/<RotateInPlace>d__33::MoveNext()
extern void U3CRotateInPlaceU3Ed__33_MoveNext_mAFB49C696CAC83C5682FBBEDD560A018337E4A1F (void);
// 0x00000045 System.Object AssemblyAnimation/<RotateInPlace>d__33::System.Collections.Generic.IEnumerator<System.Object>.get_Current()
extern void U3CRotateInPlaceU3Ed__33_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m966453D359708348AD2877B2B65FC557002CC921 (void);
// 0x00000046 System.Void AssemblyAnimation/<RotateInPlace>d__33::System.Collections.IEnumerator.Reset()
extern void U3CRotateInPlaceU3Ed__33_System_Collections_IEnumerator_Reset_mA9E151254C98D3129A5608CF72644B7A44523D7F (void);
// 0x00000047 System.Object AssemblyAnimation/<RotateInPlace>d__33::System.Collections.IEnumerator.get_Current()
extern void U3CRotateInPlaceU3Ed__33_System_Collections_IEnumerator_get_Current_mA2EF39F00D69519A69EB032BBAB65571E639FA92 (void);
// 0x00000048 System.Void AssemblyAnimation/<MoveNutImproved>d__34::.ctor(System.Int32)
extern void U3CMoveNutImprovedU3Ed__34__ctor_mAE734DEEF29803125421859097231EB174A0A816 (void);
// 0x00000049 System.Void AssemblyAnimation/<MoveNutImproved>d__34::System.IDisposable.Dispose()
extern void U3CMoveNutImprovedU3Ed__34_System_IDisposable_Dispose_m81E68AF574B8DD98DCECC368AE6EDCDF4239C945 (void);
// 0x0000004A System.Boolean AssemblyAnimation/<MoveNutImproved>d__34::MoveNext()
extern void U3CMoveNutImprovedU3Ed__34_MoveNext_m3D73BBEA683BC5404B7865852255F53FBC907B94 (void);
// 0x0000004B System.Object AssemblyAnimation/<MoveNutImproved>d__34::System.Collections.Generic.IEnumerator<System.Object>.get_Current()
extern void U3CMoveNutImprovedU3Ed__34_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m118403148DDF0A023CDF1CFC10FD389EE84E0285 (void);
// 0x0000004C System.Void AssemblyAnimation/<MoveNutImproved>d__34::System.Collections.IEnumerator.Reset()
extern void U3CMoveNutImprovedU3Ed__34_System_Collections_IEnumerator_Reset_m6766341120AA0057CA9A4A2D67B3FFB6F6950613 (void);
// 0x0000004D System.Object AssemblyAnimation/<MoveNutImproved>d__34::System.Collections.IEnumerator.get_Current()
extern void U3CMoveNutImprovedU3Ed__34_System_Collections_IEnumerator_get_Current_m3D758031FBF1166270AB8B33D8ECE2BD134C9802 (void);
// 0x0000004E System.Void AssemblyAnimation/<MoveAndRotateByMountPoint>d__35::.ctor(System.Int32)
extern void U3CMoveAndRotateByMountPointU3Ed__35__ctor_mBA636007EAF50870880909CB73BE2FB864AD4914 (void);
// 0x0000004F System.Void AssemblyAnimation/<MoveAndRotateByMountPoint>d__35::System.IDisposable.Dispose()
extern void U3CMoveAndRotateByMountPointU3Ed__35_System_IDisposable_Dispose_mF8426BDEA58C677F89E48D79F73EFA8CBFCB524A (void);
// 0x00000050 System.Boolean AssemblyAnimation/<MoveAndRotateByMountPoint>d__35::MoveNext()
extern void U3CMoveAndRotateByMountPointU3Ed__35_MoveNext_m04B0FA0693C400947042F490CFF4F4B06C920270 (void);
// 0x00000051 System.Object AssemblyAnimation/<MoveAndRotateByMountPoint>d__35::System.Collections.Generic.IEnumerator<System.Object>.get_Current()
extern void U3CMoveAndRotateByMountPointU3Ed__35_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m9321065DDE127745BAFF0090A1529BE7A4D10A3E (void);
// 0x00000052 System.Void AssemblyAnimation/<MoveAndRotateByMountPoint>d__35::System.Collections.IEnumerator.Reset()
extern void U3CMoveAndRotateByMountPointU3Ed__35_System_Collections_IEnumerator_Reset_mE4D4C4AA40D64F22503CEC2EBEF702B06A4593B7 (void);
// 0x00000053 System.Object AssemblyAnimation/<MoveAndRotateByMountPoint>d__35::System.Collections.IEnumerator.get_Current()
extern void U3CMoveAndRotateByMountPointU3Ed__35_System_Collections_IEnumerator_get_Current_mC27596FEB950ECF7C6BD79B33462B221947244EF (void);
// 0x00000054 System.Void AssemblyAnimation/<MoveByMountPoint>d__36::.ctor(System.Int32)
extern void U3CMoveByMountPointU3Ed__36__ctor_m1F54AE1061A0248056DF5DD5999A009334899B3B (void);
// 0x00000055 System.Void AssemblyAnimation/<MoveByMountPoint>d__36::System.IDisposable.Dispose()
extern void U3CMoveByMountPointU3Ed__36_System_IDisposable_Dispose_m97D03CFD8318D2EA71F3DA9ADAA0243214A851D1 (void);
// 0x00000056 System.Boolean AssemblyAnimation/<MoveByMountPoint>d__36::MoveNext()
extern void U3CMoveByMountPointU3Ed__36_MoveNext_mA51856E916803374B00A52F35F194BBCA0356FA2 (void);
// 0x00000057 System.Object AssemblyAnimation/<MoveByMountPoint>d__36::System.Collections.Generic.IEnumerator<System.Object>.get_Current()
extern void U3CMoveByMountPointU3Ed__36_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mF9266A75C9C4C9A8B0B0F64E4F7AD3C5E478D2D7 (void);
// 0x00000058 System.Void AssemblyAnimation/<MoveByMountPoint>d__36::System.Collections.IEnumerator.Reset()
extern void U3CMoveByMountPointU3Ed__36_System_Collections_IEnumerator_Reset_mAFA5F0C0C5A727A53F1189E6195C432A9890B47E (void);
// 0x00000059 System.Object AssemblyAnimation/<MoveByMountPoint>d__36::System.Collections.IEnumerator.get_Current()
extern void U3CMoveByMountPointU3Ed__36_System_Collections_IEnumerator_get_Current_mC81D9F1CB442057CF329B637CF00BACF54CA3CE4 (void);
// 0x0000005A System.Single AssemblyAnimationManager::get_AnimationSpeedRate()
extern void AssemblyAnimationManager_get_AnimationSpeedRate_mDDBE70FD2FAD13DD46C69E376953215CF3B33E68 (void);
// 0x0000005B System.Void AssemblyAnimationManager::set_AnimationSpeedRate(System.Single)
extern void AssemblyAnimationManager_set_AnimationSpeedRate_m40A5C4BD1F9B4DF2AB78FE5932B160E32FE67292 (void);
// 0x0000005C System.Boolean AssemblyAnimationManager::ValidatePart(IAssemblyPart,System.String)
extern void AssemblyAnimationManager_ValidatePart_m46F20A6F8EC1C83A4BA17E29EEE6938FA86433DA (void);
// 0x0000005D System.Collections.IEnumerator AssemblyAnimationManager::AnimateOverTime(System.Single,System.Action`1<System.Single>,System.Action)
extern void AssemblyAnimationManager_AnimateOverTime_m81DD5756B67A920512A58FF3DE976E2B1512249F (void);
// 0x0000005E System.Collections.IEnumerator AssemblyAnimationManager::MovePart(IAssemblyPart,UnityEngine.Vector3,System.Nullable`1<UnityEngine.Quaternion>,System.Single,System.Boolean)
extern void AssemblyAnimationManager_MovePart_m09C69C191E8C81AA6DAC05CD7EE6A7509B2ABA29 (void);
// 0x0000005F System.Collections.IEnumerator AssemblyAnimationManager::RotatePartInPlace(IAssemblyPart,UnityEngine.Quaternion,System.Single)
extern void AssemblyAnimationManager_RotatePartInPlace_m9347E51B925042884D46A5C1908FD9E911D59D46 (void);
// 0x00000060 System.Collections.IEnumerator AssemblyAnimationManager::RotatePartAroundAxis(IAssemblyPart,UnityEngine.Vector3,System.Single,System.Nullable`1<UnityEngine.Vector3>,System.Single)
extern void AssemblyAnimationManager_RotatePartAroundAxis_m63484611346ED485115C86577179D0DF3B21470A (void);
// 0x00000061 System.Collections.IEnumerator AssemblyAnimationManager::MoveAndRotateByMountPoint(IAssemblyPart,UnityEngine.Vector3,UnityEngine.Quaternion,System.Single)
extern void AssemblyAnimationManager_MoveAndRotateByMountPoint_m8DD0338BCBBD7B54E03416BE6B2532177419EA73 (void);
// 0x00000062 System.Collections.IEnumerator AssemblyAnimationManager::MoveByMountPoint(IAssemblyPart,UnityEngine.Vector3,System.Single)
extern void AssemblyAnimationManager_MoveByMountPoint_m81F522832892F3795A0E865113CAD3800B7DCC25 (void);
// 0x00000063 System.Void AssemblyAnimationManager::AlignTransformPosition(UnityEngine.Transform,UnityEngine.Vector3)
extern void AssemblyAnimationManager_AlignTransformPosition_m9D6C3792968DBDB142757118D3C0298A1E2C9391 (void);
// 0x00000064 System.Collections.IEnumerator AssemblyAnimationManager::AlignParts(AssemblyPart,AssemblyPart,System.Int32,System.Int32,System.Single)
extern void AssemblyAnimationManager_AlignParts_m7A6EA927C278B2855C41C4AA9534EAB306868E78 (void);
// 0x00000065 UnityEngine.Quaternion AssemblyAnimationManager::CalculateAlignmentRotation(UnityEngine.Transform,UnityEngine.Transform,AssemblyPart)
extern void AssemblyAnimationManager_CalculateAlignmentRotation_m11CBD762346D5454230FD794143B9994E37F4CA9 (void);
// 0x00000066 UnityEngine.Vector3 AssemblyAnimationManager::GetAxisDirection(UnityEngine.Transform)
extern void AssemblyAnimationManager_GetAxisDirection_m4D43AF8864F81864ACCC4EC249DB55B8B9A82DB4 (void);
// 0x00000067 System.Collections.IEnumerator AssemblyAnimationManager::InstallScrew(AssemblyPart,UnityEngine.Transform,UnityEngine.Vector3,System.Single)
extern void AssemblyAnimationManager_InstallScrew_mD607DC25321427D9DF79BD546E1EEE269D5CC6E1 (void);
// 0x00000068 System.Collections.IEnumerator AssemblyAnimationManager::InstallNut(AssemblyPart,UnityEngine.Transform,UnityEngine.Vector3,System.Single)
extern void AssemblyAnimationManager_InstallNut_m402E3A46E8C657C86E6C3B7465E58B09F682D35F (void);
// 0x00000069 System.Void AssemblyAnimationManager::AlignPartsAsync(AssemblyPart,AssemblyPart,System.Int32,System.Int32)
extern void AssemblyAnimationManager_AlignPartsAsync_m5E585B7D17D642E4074BFD40D17713910AB09CBE (void);
// 0x0000006A System.Collections.IEnumerator AssemblyAnimationManager::AlignPartsSync(AssemblyPart,AssemblyPart,System.Int32,System.Int32,System.Single)
extern void AssemblyAnimationManager_AlignPartsSync_m688E20F88A62998DAF49DDB8D7EBFD2A62F982E4 (void);
// 0x0000006B System.Collections.IEnumerator AssemblyAnimationManager::CompleteAssemblySequence(AssemblyPart,AssemblyPart,AssemblyPart,AssemblyPart,UnityEngine.Transform,UnityEngine.Vector3,System.Int32,System.Int32,System.Single)
extern void AssemblyAnimationManager_CompleteAssemblySequence_m2D88E4AFFF901634E3B1D159D3AC9C12CB12C03D (void);
// 0x0000006C System.Void AssemblyAnimationManager::.ctor()
extern void AssemblyAnimationManager__ctor_mBEC5D5A7F30D782D77777D225EC4EB2BE97E1531 (void);
// 0x0000006D System.Void AssemblyAnimationManager/<AnimateOverTime>d__10::.ctor(System.Int32)
extern void U3CAnimateOverTimeU3Ed__10__ctor_m6FCD3B0A5AF03A24AF7855090ECB2A3599AD626F (void);
// 0x0000006E System.Void AssemblyAnimationManager/<AnimateOverTime>d__10::System.IDisposable.Dispose()
extern void U3CAnimateOverTimeU3Ed__10_System_IDisposable_Dispose_mF4C77CC94ED80E76C76F571E6811ADB7ADE3454F (void);
// 0x0000006F System.Boolean AssemblyAnimationManager/<AnimateOverTime>d__10::MoveNext()
extern void U3CAnimateOverTimeU3Ed__10_MoveNext_m195A1FC6B97364310709D23CE85B1D4F86854370 (void);
// 0x00000070 System.Object AssemblyAnimationManager/<AnimateOverTime>d__10::System.Collections.Generic.IEnumerator<System.Object>.get_Current()
extern void U3CAnimateOverTimeU3Ed__10_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m0B993B2148D70AAD993C1717D53A5784D27ECACA (void);
// 0x00000071 System.Void AssemblyAnimationManager/<AnimateOverTime>d__10::System.Collections.IEnumerator.Reset()
extern void U3CAnimateOverTimeU3Ed__10_System_Collections_IEnumerator_Reset_mB732BBA347180FD8B94FBEA662120F00D8786111 (void);
// 0x00000072 System.Object AssemblyAnimationManager/<AnimateOverTime>d__10::System.Collections.IEnumerator.get_Current()
extern void U3CAnimateOverTimeU3Ed__10_System_Collections_IEnumerator_get_Current_m4F0F9A98AAEFDEEB272405E86F8B02A5335C4F60 (void);
// 0x00000073 System.Void AssemblyAnimationManager/<>c__DisplayClass11_0::.ctor()
extern void U3CU3Ec__DisplayClass11_0__ctor_m967251DBE7B3B10F213ADD91883755FADA548AB4 (void);
// 0x00000074 System.Void AssemblyAnimationManager/<>c__DisplayClass11_0::<MovePart>b__0(System.Single)
extern void U3CU3Ec__DisplayClass11_0_U3CMovePartU3Eb__0_mFE9AE39CD908349C0EAEE49C2A8FFF428D94C85F (void);
// 0x00000075 System.Void AssemblyAnimationManager/<>c__DisplayClass11_0::<MovePart>b__1()
extern void U3CU3Ec__DisplayClass11_0_U3CMovePartU3Eb__1_m17628096485283FE54E09C9F938CD11ED9CC6E06 (void);
// 0x00000076 System.Void AssemblyAnimationManager/<MovePart>d__11::.ctor(System.Int32)
extern void U3CMovePartU3Ed__11__ctor_mFA3733646E1A3F848FE6AF0D2FCEEC8D68E50693 (void);
// 0x00000077 System.Void AssemblyAnimationManager/<MovePart>d__11::System.IDisposable.Dispose()
extern void U3CMovePartU3Ed__11_System_IDisposable_Dispose_mA079DA89C6672151C2CD1314F5765C125AF96C57 (void);
// 0x00000078 System.Boolean AssemblyAnimationManager/<MovePart>d__11::MoveNext()
extern void U3CMovePartU3Ed__11_MoveNext_m21218F444A2D0047B188088AB460646A93D9305B (void);
// 0x00000079 System.Object AssemblyAnimationManager/<MovePart>d__11::System.Collections.Generic.IEnumerator<System.Object>.get_Current()
extern void U3CMovePartU3Ed__11_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m55FC00A208A5DB3DD89DA1674D16386029538640 (void);
// 0x0000007A System.Void AssemblyAnimationManager/<MovePart>d__11::System.Collections.IEnumerator.Reset()
extern void U3CMovePartU3Ed__11_System_Collections_IEnumerator_Reset_m5F3B4BD4B900E1216395E69DF7DA981CA1A1B549 (void);
// 0x0000007B System.Object AssemblyAnimationManager/<MovePart>d__11::System.Collections.IEnumerator.get_Current()
extern void U3CMovePartU3Ed__11_System_Collections_IEnumerator_get_Current_m69793D09EC560C4DD5F3515011D0D4A2D5D06386 (void);
// 0x0000007C System.Void AssemblyAnimationManager/<>c__DisplayClass12_0::.ctor()
extern void U3CU3Ec__DisplayClass12_0__ctor_mB6F29EF3938BE271918C03B9E60F474FCB91F1A0 (void);
// 0x0000007D System.Void AssemblyAnimationManager/<>c__DisplayClass12_0::<RotatePartInPlace>b__0(System.Single)
extern void U3CU3Ec__DisplayClass12_0_U3CRotatePartInPlaceU3Eb__0_m72522C877F6F4AEFE8203DADB1C07D2BBFC61115 (void);
// 0x0000007E System.Void AssemblyAnimationManager/<>c__DisplayClass12_0::<RotatePartInPlace>b__1()
extern void U3CU3Ec__DisplayClass12_0_U3CRotatePartInPlaceU3Eb__1_m44ACA0310BCB59F931DCB5B64F963C9D23CA1C85 (void);
// 0x0000007F System.Void AssemblyAnimationManager/<RotatePartInPlace>d__12::.ctor(System.Int32)
extern void U3CRotatePartInPlaceU3Ed__12__ctor_mBEAA0D6912A77EF3CA7345E3F8536984DFA9BD7E (void);
// 0x00000080 System.Void AssemblyAnimationManager/<RotatePartInPlace>d__12::System.IDisposable.Dispose()
extern void U3CRotatePartInPlaceU3Ed__12_System_IDisposable_Dispose_m447EE95281F6777373242F2B98710E144AEFEC9F (void);
// 0x00000081 System.Boolean AssemblyAnimationManager/<RotatePartInPlace>d__12::MoveNext()
extern void U3CRotatePartInPlaceU3Ed__12_MoveNext_mA8B419B80BF09EF8959DCF95FEE2B073EB740F1C (void);
// 0x00000082 System.Object AssemblyAnimationManager/<RotatePartInPlace>d__12::System.Collections.Generic.IEnumerator<System.Object>.get_Current()
extern void U3CRotatePartInPlaceU3Ed__12_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mFB47B3D54C6AB9FF50DE010DAB96D2C8AEA412ED (void);
// 0x00000083 System.Void AssemblyAnimationManager/<RotatePartInPlace>d__12::System.Collections.IEnumerator.Reset()
extern void U3CRotatePartInPlaceU3Ed__12_System_Collections_IEnumerator_Reset_m02A1EA77E16E756AC9EC6DC86F8B7E856065BE46 (void);
// 0x00000084 System.Object AssemblyAnimationManager/<RotatePartInPlace>d__12::System.Collections.IEnumerator.get_Current()
extern void U3CRotatePartInPlaceU3Ed__12_System_Collections_IEnumerator_get_Current_m06B320F837DFD7E7B8CDCA391D2A97C3CE263DB7 (void);
// 0x00000085 System.Void AssemblyAnimationManager/<>c__DisplayClass13_0::.ctor()
extern void U3CU3Ec__DisplayClass13_0__ctor_mBCC63144A53BF221E89F58AF6381A540E801EB51 (void);
// 0x00000086 System.Void AssemblyAnimationManager/<>c__DisplayClass13_0::<RotatePartAroundAxis>b__0(System.Single)
extern void U3CU3Ec__DisplayClass13_0_U3CRotatePartAroundAxisU3Eb__0_mA262C9AAA7259FB504A9C0603F0FAC70549F5C6F (void);
// 0x00000087 System.Void AssemblyAnimationManager/<>c__DisplayClass13_0::<RotatePartAroundAxis>b__1()
extern void U3CU3Ec__DisplayClass13_0_U3CRotatePartAroundAxisU3Eb__1_mE14982D28A9AA313AE0092D73E6FAAC5B6950B25 (void);
// 0x00000088 System.Void AssemblyAnimationManager/<RotatePartAroundAxis>d__13::.ctor(System.Int32)
extern void U3CRotatePartAroundAxisU3Ed__13__ctor_mBB1D7E0F7826D2919AA3B89AB3BE3A8D739A92D7 (void);
// 0x00000089 System.Void AssemblyAnimationManager/<RotatePartAroundAxis>d__13::System.IDisposable.Dispose()
extern void U3CRotatePartAroundAxisU3Ed__13_System_IDisposable_Dispose_m2AE5F0A9BE26DA938FBC3E7D2CDE3CBCA540ADC4 (void);
// 0x0000008A System.Boolean AssemblyAnimationManager/<RotatePartAroundAxis>d__13::MoveNext()
extern void U3CRotatePartAroundAxisU3Ed__13_MoveNext_m850999FBC7D938BAB9B0DD1819C0F7600F56ADE9 (void);
// 0x0000008B System.Object AssemblyAnimationManager/<RotatePartAroundAxis>d__13::System.Collections.Generic.IEnumerator<System.Object>.get_Current()
extern void U3CRotatePartAroundAxisU3Ed__13_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mFC1A4041D2B788A31244F310786491D89C1ADAFE (void);
// 0x0000008C System.Void AssemblyAnimationManager/<RotatePartAroundAxis>d__13::System.Collections.IEnumerator.Reset()
extern void U3CRotatePartAroundAxisU3Ed__13_System_Collections_IEnumerator_Reset_mA646940B486F8DF2ABDC54D9BB4A522D6DF474D2 (void);
// 0x0000008D System.Object AssemblyAnimationManager/<RotatePartAroundAxis>d__13::System.Collections.IEnumerator.get_Current()
extern void U3CRotatePartAroundAxisU3Ed__13_System_Collections_IEnumerator_get_Current_mC12DCAF15357B258D61791DDD82B51736D590D71 (void);
// 0x0000008E System.Void AssemblyAnimationManager/<>c__DisplayClass14_0::.ctor()
extern void U3CU3Ec__DisplayClass14_0__ctor_m5B7FEE20A5C07A27340298EAE5D539571DF7A66C (void);
// 0x0000008F System.Void AssemblyAnimationManager/<>c__DisplayClass14_0::<MoveAndRotateByMountPoint>b__0(System.Single)
extern void U3CU3Ec__DisplayClass14_0_U3CMoveAndRotateByMountPointU3Eb__0_m7EC2C1E485309DC5260F9CE9562BEF09F7B54D85 (void);
// 0x00000090 System.Void AssemblyAnimationManager/<>c__DisplayClass14_0::<MoveAndRotateByMountPoint>b__1()
extern void U3CU3Ec__DisplayClass14_0_U3CMoveAndRotateByMountPointU3Eb__1_mC71660A82CF4BC0BDE3B78AE5DF10942D7F105EA (void);
// 0x00000091 System.Void AssemblyAnimationManager/<MoveAndRotateByMountPoint>d__14::.ctor(System.Int32)
extern void U3CMoveAndRotateByMountPointU3Ed__14__ctor_m9A080D9627117975CA8C32F3604679177706FDD0 (void);
// 0x00000092 System.Void AssemblyAnimationManager/<MoveAndRotateByMountPoint>d__14::System.IDisposable.Dispose()
extern void U3CMoveAndRotateByMountPointU3Ed__14_System_IDisposable_Dispose_m3D9FCEC1E9F76AA3208DE678E6DF2DC0DB8A6D91 (void);
// 0x00000093 System.Boolean AssemblyAnimationManager/<MoveAndRotateByMountPoint>d__14::MoveNext()
extern void U3CMoveAndRotateByMountPointU3Ed__14_MoveNext_m9BF3CBA940C3AE5D8149D70FD9B41515E2C3274A (void);
// 0x00000094 System.Object AssemblyAnimationManager/<MoveAndRotateByMountPoint>d__14::System.Collections.Generic.IEnumerator<System.Object>.get_Current()
extern void U3CMoveAndRotateByMountPointU3Ed__14_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mB8382E78AF30CFC27ACF7CE8D7C003BF4BB807D4 (void);
// 0x00000095 System.Void AssemblyAnimationManager/<MoveAndRotateByMountPoint>d__14::System.Collections.IEnumerator.Reset()
extern void U3CMoveAndRotateByMountPointU3Ed__14_System_Collections_IEnumerator_Reset_m0910B6ADAB14DD9159A00C7F31AB2DED22F786F9 (void);
// 0x00000096 System.Object AssemblyAnimationManager/<MoveAndRotateByMountPoint>d__14::System.Collections.IEnumerator.get_Current()
extern void U3CMoveAndRotateByMountPointU3Ed__14_System_Collections_IEnumerator_get_Current_m1487B11E1F693248CEC406CA5AC9C2DB0D325389 (void);
// 0x00000097 System.Void AssemblyAnimationManager/<>c__DisplayClass15_0::.ctor()
extern void U3CU3Ec__DisplayClass15_0__ctor_mF6E8D22BC5FFBFB118E420CC73A0C72A976B8107 (void);
// 0x00000098 System.Void AssemblyAnimationManager/<>c__DisplayClass15_0::<MoveByMountPoint>b__0(System.Single)
extern void U3CU3Ec__DisplayClass15_0_U3CMoveByMountPointU3Eb__0_mB05EF60579F08183A9971542487DC38E3615E699 (void);
// 0x00000099 System.Void AssemblyAnimationManager/<>c__DisplayClass15_0::<MoveByMountPoint>b__1()
extern void U3CU3Ec__DisplayClass15_0_U3CMoveByMountPointU3Eb__1_m0F56C3D3B73ACFAE4A74FAF6945D2277689980D7 (void);
// 0x0000009A System.Void AssemblyAnimationManager/<MoveByMountPoint>d__15::.ctor(System.Int32)
extern void U3CMoveByMountPointU3Ed__15__ctor_m982295494133219B3C01226A883BEA8EC569A4D6 (void);
// 0x0000009B System.Void AssemblyAnimationManager/<MoveByMountPoint>d__15::System.IDisposable.Dispose()
extern void U3CMoveByMountPointU3Ed__15_System_IDisposable_Dispose_m8EEC45AECB0F1B2462BD6F6476952424DA8F3A6E (void);
// 0x0000009C System.Boolean AssemblyAnimationManager/<MoveByMountPoint>d__15::MoveNext()
extern void U3CMoveByMountPointU3Ed__15_MoveNext_mCDBA24FC2646E2FCD400F35F974795C9F1E71FC7 (void);
// 0x0000009D System.Object AssemblyAnimationManager/<MoveByMountPoint>d__15::System.Collections.Generic.IEnumerator<System.Object>.get_Current()
extern void U3CMoveByMountPointU3Ed__15_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m1BC767D8A33C5A375BDF4ECE75E0C38D3E1B2B95 (void);
// 0x0000009E System.Void AssemblyAnimationManager/<MoveByMountPoint>d__15::System.Collections.IEnumerator.Reset()
extern void U3CMoveByMountPointU3Ed__15_System_Collections_IEnumerator_Reset_mD64E62DB133113EFD531A989ECB1EF4731F19BEB (void);
// 0x0000009F System.Object AssemblyAnimationManager/<MoveByMountPoint>d__15::System.Collections.IEnumerator.get_Current()
extern void U3CMoveByMountPointU3Ed__15_System_Collections_IEnumerator_get_Current_m1581E82FD713A836C717677202408056D67DDAEC (void);
// 0x000000A0 System.Void AssemblyAnimationManager/<AlignParts>d__17::.ctor(System.Int32)
extern void U3CAlignPartsU3Ed__17__ctor_mB5D8DDBCF1971CBDF22EF7C5809AE214E88DE8ED (void);
// 0x000000A1 System.Void AssemblyAnimationManager/<AlignParts>d__17::System.IDisposable.Dispose()
extern void U3CAlignPartsU3Ed__17_System_IDisposable_Dispose_m40C91ED59A35B8A76984C5CD07D3FCB2788BDA6C (void);
// 0x000000A2 System.Boolean AssemblyAnimationManager/<AlignParts>d__17::MoveNext()
extern void U3CAlignPartsU3Ed__17_MoveNext_mA1B59E17C37E16B8B82BA8BA15F105A0B08FF6FD (void);
// 0x000000A3 System.Object AssemblyAnimationManager/<AlignParts>d__17::System.Collections.Generic.IEnumerator<System.Object>.get_Current()
extern void U3CAlignPartsU3Ed__17_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mF518E7555FF4B2DCF918B67E313FDC7CAC4AA2CC (void);
// 0x000000A4 System.Void AssemblyAnimationManager/<AlignParts>d__17::System.Collections.IEnumerator.Reset()
extern void U3CAlignPartsU3Ed__17_System_Collections_IEnumerator_Reset_m65CC8619FC89A9B803813020F70F0C87A6B1C3EB (void);
// 0x000000A5 System.Object AssemblyAnimationManager/<AlignParts>d__17::System.Collections.IEnumerator.get_Current()
extern void U3CAlignPartsU3Ed__17_System_Collections_IEnumerator_get_Current_m215BB3886B1F0545F172FBEC19FA6DE0E8BF0C6E (void);
// 0x000000A6 System.Void AssemblyAnimationManager/<InstallScrew>d__20::.ctor(System.Int32)
extern void U3CInstallScrewU3Ed__20__ctor_mFFF5D2B23896C8D28E7DE9404D1B6FEC58AB0DAF (void);
// 0x000000A7 System.Void AssemblyAnimationManager/<InstallScrew>d__20::System.IDisposable.Dispose()
extern void U3CInstallScrewU3Ed__20_System_IDisposable_Dispose_mD805219881F7C390067A14B1E9613DE9E44DE154 (void);
// 0x000000A8 System.Boolean AssemblyAnimationManager/<InstallScrew>d__20::MoveNext()
extern void U3CInstallScrewU3Ed__20_MoveNext_m11E25DE6C83D14583BB5BF047D814D128578A261 (void);
// 0x000000A9 System.Object AssemblyAnimationManager/<InstallScrew>d__20::System.Collections.Generic.IEnumerator<System.Object>.get_Current()
extern void U3CInstallScrewU3Ed__20_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mADFCEB5C97976EDD36BFE11DD4F52875C8A3298F (void);
// 0x000000AA System.Void AssemblyAnimationManager/<InstallScrew>d__20::System.Collections.IEnumerator.Reset()
extern void U3CInstallScrewU3Ed__20_System_Collections_IEnumerator_Reset_m002E0B711EBA27F381052CD9A368205A8793FCDE (void);
// 0x000000AB System.Object AssemblyAnimationManager/<InstallScrew>d__20::System.Collections.IEnumerator.get_Current()
extern void U3CInstallScrewU3Ed__20_System_Collections_IEnumerator_get_Current_m34CF5377FB92312DC437E444E2F20043D48BAEB4 (void);
// 0x000000AC System.Void AssemblyAnimationManager/<InstallNut>d__21::.ctor(System.Int32)
extern void U3CInstallNutU3Ed__21__ctor_m33D0919297A18444AA527119F6AF425631EE8F58 (void);
// 0x000000AD System.Void AssemblyAnimationManager/<InstallNut>d__21::System.IDisposable.Dispose()
extern void U3CInstallNutU3Ed__21_System_IDisposable_Dispose_m442604572C4481A33B63F08FDDD8231B86AE8803 (void);
// 0x000000AE System.Boolean AssemblyAnimationManager/<InstallNut>d__21::MoveNext()
extern void U3CInstallNutU3Ed__21_MoveNext_m7B3938DCEF64AA43B11A0AE6C0528729F57A6FD1 (void);
// 0x000000AF System.Object AssemblyAnimationManager/<InstallNut>d__21::System.Collections.Generic.IEnumerator<System.Object>.get_Current()
extern void U3CInstallNutU3Ed__21_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m574645F338EBBD73499835737B97CBC00F9D0582 (void);
// 0x000000B0 System.Void AssemblyAnimationManager/<InstallNut>d__21::System.Collections.IEnumerator.Reset()
extern void U3CInstallNutU3Ed__21_System_Collections_IEnumerator_Reset_m3B4DBFF0D24901B7C680AB35FB771F4AF80422DF (void);
// 0x000000B1 System.Object AssemblyAnimationManager/<InstallNut>d__21::System.Collections.IEnumerator.get_Current()
extern void U3CInstallNutU3Ed__21_System_Collections_IEnumerator_get_Current_m4F217576A9159CEA37515A906EED62555AF6979C (void);
// 0x000000B2 System.Void AssemblyAnimationManager/<AlignPartsSync>d__23::.ctor(System.Int32)
extern void U3CAlignPartsSyncU3Ed__23__ctor_m89125A48D794F5FCE14384806D911DA878A8E1D9 (void);
// 0x000000B3 System.Void AssemblyAnimationManager/<AlignPartsSync>d__23::System.IDisposable.Dispose()
extern void U3CAlignPartsSyncU3Ed__23_System_IDisposable_Dispose_m44C3F9883751EFD57D16BD3A4344FC439C9F5054 (void);
// 0x000000B4 System.Boolean AssemblyAnimationManager/<AlignPartsSync>d__23::MoveNext()
extern void U3CAlignPartsSyncU3Ed__23_MoveNext_m702695B033F90E38B6C660E6300DA10F66B81D19 (void);
// 0x000000B5 System.Object AssemblyAnimationManager/<AlignPartsSync>d__23::System.Collections.Generic.IEnumerator<System.Object>.get_Current()
extern void U3CAlignPartsSyncU3Ed__23_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mF1984E7DCA1A253A1BE2FE00D871366DFA7B9C87 (void);
// 0x000000B6 System.Void AssemblyAnimationManager/<AlignPartsSync>d__23::System.Collections.IEnumerator.Reset()
extern void U3CAlignPartsSyncU3Ed__23_System_Collections_IEnumerator_Reset_mDDFD01B78693121FD30943BD678F0738412C2892 (void);
// 0x000000B7 System.Object AssemblyAnimationManager/<AlignPartsSync>d__23::System.Collections.IEnumerator.get_Current()
extern void U3CAlignPartsSyncU3Ed__23_System_Collections_IEnumerator_get_Current_m930CCA9D1CFD7BFB03B5F5A32E3EF53C5980D745 (void);
// 0x000000B8 System.Void AssemblyAnimationManager/<CompleteAssemblySequence>d__24::.ctor(System.Int32)
extern void U3CCompleteAssemblySequenceU3Ed__24__ctor_m6873ECA5E7A1B716F0EBC7343F2352A0BFEA116B (void);
// 0x000000B9 System.Void AssemblyAnimationManager/<CompleteAssemblySequence>d__24::System.IDisposable.Dispose()
extern void U3CCompleteAssemblySequenceU3Ed__24_System_IDisposable_Dispose_mBFA49DF4E24E707B41CB3E9103F535A909F97718 (void);
// 0x000000BA System.Boolean AssemblyAnimationManager/<CompleteAssemblySequence>d__24::MoveNext()
extern void U3CCompleteAssemblySequenceU3Ed__24_MoveNext_mF07AF18D2B7FE9D8FDAEC0CB35E06D72BEA061CE (void);
// 0x000000BB System.Object AssemblyAnimationManager/<CompleteAssemblySequence>d__24::System.Collections.Generic.IEnumerator<System.Object>.get_Current()
extern void U3CCompleteAssemblySequenceU3Ed__24_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m6A0F91B1BA366E512665D752B47AB37752D22CBF (void);
// 0x000000BC System.Void AssemblyAnimationManager/<CompleteAssemblySequence>d__24::System.Collections.IEnumerator.Reset()
extern void U3CCompleteAssemblySequenceU3Ed__24_System_Collections_IEnumerator_Reset_m4E60FE5DEFC1708BF1B59877D3C882F2689A7F10 (void);
// 0x000000BD System.Object AssemblyAnimationManager/<CompleteAssemblySequence>d__24::System.Collections.IEnumerator.get_Current()
extern void U3CCompleteAssemblySequenceU3Ed__24_System_Collections_IEnumerator_get_Current_mE1E14AAB8061094B63E88C56F85DB287B36BFF7D (void);
// 0x000000BE AssemblyAnimationManager AssemblyAnimationService::get_AnimationManager()
extern void AssemblyAnimationService_get_AnimationManager_m2390D3B47AAE7E2447E4C67572CB7C27DC1B3AC5 (void);
// 0x000000BF System.Void AssemblyAnimationService::Awake()
extern void AssemblyAnimationService_Awake_mB7E5899A0B2D0BAFC2C5CB6A6A0A47FE12DAAC93 (void);
// 0x000000C0 System.Void AssemblyAnimationService::Start()
extern void AssemblyAnimationService_Start_m2F6680EF6EF0EF87313B5852350D6CD2AA65A9E4 (void);
// 0x000000C1 System.Void AssemblyAnimationService::AlignPartsPublic(AssemblyPart,AssemblyPart,System.Int32,System.Int32,System.Single)
extern void AssemblyAnimationService_AlignPartsPublic_mA8042FD975498F9879E139EE96089CBA5CADB774 (void);
// 0x000000C2 System.Collections.IEnumerator AssemblyAnimationService::MoveScrewImprovedPublic(AssemblyPart,UnityEngine.Transform,UnityEngine.Vector3,System.Int32)
extern void AssemblyAnimationService_MoveScrewImprovedPublic_m60D285E03B83A1FB2FD0A5D91BCCFECBC55EFB9B (void);
// 0x000000C3 System.Collections.IEnumerator AssemblyAnimationService::MoveNutImprovedPublic(AssemblyPart,UnityEngine.Transform,UnityEngine.Vector3,System.Int32)
extern void AssemblyAnimationService_MoveNutImprovedPublic_mDE246AD0253F5EB806E42CB521680F08C3C06AE4 (void);
// 0x000000C4 UnityEngine.Vector3 AssemblyAnimationService::GetAxisDirection(UnityEngine.Transform)
extern void AssemblyAnimationService_GetAxisDirection_m16F7FB7DF40731F203B4972B7013BEC0AABEB1D5 (void);
// 0x000000C5 System.Collections.IEnumerator AssemblyAnimationService::MoveScrewImproved(AssemblyPart,UnityEngine.Transform,UnityEngine.Vector3,System.Int32)
extern void AssemblyAnimationService_MoveScrewImproved_m84B33DE65006D0CC6399AFFAC3DD9DC7333C1E35 (void);
// 0x000000C6 System.Collections.IEnumerator AssemblyAnimationService::MoveNutImproved(AssemblyPart,UnityEngine.Transform,UnityEngine.Vector3,System.Int32)
extern void AssemblyAnimationService_MoveNutImproved_m6DF3B64C6188156C89239B005BBC0AECB6EFCCB8 (void);
// 0x000000C7 System.Void AssemblyAnimationService::.ctor()
extern void AssemblyAnimationService__ctor_mFE7BB99BDB3CC7DA6229F2F87F925E7C8378C41B (void);
// 0x000000C8 System.Void AssemblyAnimationService/<MoveScrewImproved>d__14::.ctor(System.Int32)
extern void U3CMoveScrewImprovedU3Ed__14__ctor_mF7CAD27EE50C47CE7D9B100CA08BBD1818CDFA17 (void);
// 0x000000C9 System.Void AssemblyAnimationService/<MoveScrewImproved>d__14::System.IDisposable.Dispose()
extern void U3CMoveScrewImprovedU3Ed__14_System_IDisposable_Dispose_m8C2780039594A2BA7CABE0108A0BDDA2738D6EE6 (void);
// 0x000000CA System.Boolean AssemblyAnimationService/<MoveScrewImproved>d__14::MoveNext()
extern void U3CMoveScrewImprovedU3Ed__14_MoveNext_mA0D4AA02C62875E2B30B4B68AE49BD83FEDDB258 (void);
// 0x000000CB System.Object AssemblyAnimationService/<MoveScrewImproved>d__14::System.Collections.Generic.IEnumerator<System.Object>.get_Current()
extern void U3CMoveScrewImprovedU3Ed__14_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m5E42E26C3E4B6EB3606DC959E3B2ED1F247538F9 (void);
// 0x000000CC System.Void AssemblyAnimationService/<MoveScrewImproved>d__14::System.Collections.IEnumerator.Reset()
extern void U3CMoveScrewImprovedU3Ed__14_System_Collections_IEnumerator_Reset_mB3378F9CAB2D4FBD28B57B19AB2E4A09552342EA (void);
// 0x000000CD System.Object AssemblyAnimationService/<MoveScrewImproved>d__14::System.Collections.IEnumerator.get_Current()
extern void U3CMoveScrewImprovedU3Ed__14_System_Collections_IEnumerator_get_Current_mCA66715AE837146DF72DEEA7DDA288A029C70BFD (void);
// 0x000000CE System.Void AssemblyAnimationService/<MoveNutImproved>d__15::.ctor(System.Int32)
extern void U3CMoveNutImprovedU3Ed__15__ctor_mDD0643FFE94EA0DE1C955C8BF1A4E3C6A5A40355 (void);
// 0x000000CF System.Void AssemblyAnimationService/<MoveNutImproved>d__15::System.IDisposable.Dispose()
extern void U3CMoveNutImprovedU3Ed__15_System_IDisposable_Dispose_mDADC295F6FF625100DC91523F1B1D93D9A05CD28 (void);
// 0x000000D0 System.Boolean AssemblyAnimationService/<MoveNutImproved>d__15::MoveNext()
extern void U3CMoveNutImprovedU3Ed__15_MoveNext_m7ACA9DC3B5514A5366E06476CA100C79F95070CF (void);
// 0x000000D1 System.Object AssemblyAnimationService/<MoveNutImproved>d__15::System.Collections.Generic.IEnumerator<System.Object>.get_Current()
extern void U3CMoveNutImprovedU3Ed__15_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m81FDD57950948E47486F944A75DB02644169422C (void);
// 0x000000D2 System.Void AssemblyAnimationService/<MoveNutImproved>d__15::System.Collections.IEnumerator.Reset()
extern void U3CMoveNutImprovedU3Ed__15_System_Collections_IEnumerator_Reset_mDB3E2043E8BEF95FF79784AA22A37105DF1CB65B (void);
// 0x000000D3 System.Object AssemblyAnimationService/<MoveNutImproved>d__15::System.Collections.IEnumerator.get_Current()
extern void U3CMoveNutImprovedU3Ed__15_System_Collections_IEnumerator_get_Current_m532C2F3BA34DD38F05EBF7C256BABA6533E37531 (void);
// 0x000000D4 UnityEngine.Transform IAssemblyPart::get_PartTransform()
// 0x000000D5 System.String IAssemblyPart::get_PartName()
// 0x000000D6 UnityEngine.Transform[] IAssemblyPart::get_ReferencePoints()
// 0x000000D7 UnityEngine.Transform IAssemblyPart::GetReferencePoint(System.Int32)
// 0x000000D8 System.Int32 IAssemblyPart::get_ReferencePointCount()
// 0x000000D9 System.Boolean IAssemblyPart::get_HasReferencePoints()
// 0x000000DA UnityEngine.Transform IAssemblyPart::get_MountPoint()
// 0x000000DB UnityEngine.Transform IAssemblyPart::GetMountPoint(System.Int32)
// 0x000000DC UnityEngine.Transform AssemblyPart::get_PartTransform()
extern void AssemblyPart_get_PartTransform_m4DBAD54DBC01EA8AA0B9CE3F213F16BF39F4185B (void);
// 0x000000DD System.String AssemblyPart::get_PartName()
extern void AssemblyPart_get_PartName_m6214ABEAAF046BDED82DEF234113EC5242D85403 (void);
// 0x000000DE PartType AssemblyPart::get_Type()
extern void AssemblyPart_get_Type_m7F1E8F177BCDAF6F36A237322F53A5B0923E4461 (void);
// 0x000000DF UnityEngine.Transform[] AssemblyPart::get_ReferencePoints()
extern void AssemblyPart_get_ReferencePoints_m78FB08E4A8A88DB9C21CE9D33063B7186F578282 (void);
// 0x000000E0 System.Boolean AssemblyPart::get_HasReferencePoints()
extern void AssemblyPart_get_HasReferencePoints_m4101AA83A26052106AD881D2DC804625891542F5 (void);
// 0x000000E1 System.Int32 AssemblyPart::get_ReferencePointCount()
extern void AssemblyPart_get_ReferencePointCount_mF33DFB60760BFAF332ABD9E985F0CD490B7C9B3B (void);
// 0x000000E2 UnityEngine.Transform AssemblyPart::get_MountPoint()
extern void AssemblyPart_get_MountPoint_mD5D437BBFF699DBD34FB735B8A5C2A9B873CDBEC (void);
// 0x000000E3 UnityEngine.Transform AssemblyPart::GetReferencePoint(System.Int32)
extern void AssemblyPart_GetReferencePoint_mA201236E27083AF2890401B12D8DD8EAE59A319A (void);
// 0x000000E4 UnityEngine.Transform AssemblyPart::GetReferencePointByName(System.String)
extern void AssemblyPart_GetReferencePointByName_mD7F8DDB6004E03A542114CC8A0741B3516BC5237 (void);
// 0x000000E5 UnityEngine.Transform AssemblyPart::GetMountPoint(System.Int32)
extern void AssemblyPart_GetMountPoint_m2222F2090704F0CDC758B755808AEEC450942A74 (void);
// 0x000000E6 System.Void AssemblyPart::OnValidate()
extern void AssemblyPart_OnValidate_m38D200EFECAD3B6FF377AC38D18AAC318BDE752E (void);
// 0x000000E7 System.Void AssemblyPart::ValidateReferencePoints()
extern void AssemblyPart_ValidateReferencePoints_m89909ADC56847528A6D64C919908BA4011F9D627 (void);
// 0x000000E8 System.Void AssemblyPart::OnDrawGizmosSelected()
extern void AssemblyPart_OnDrawGizmosSelected_mA4CDB6064D0D756A45BA6A6803030B952DCABBF9 (void);
// 0x000000E9 System.Void AssemblyPart::DrawReferencePointGizmo(UnityEngine.Transform,UnityEngine.Color,System.Int32)
extern void AssemblyPart_DrawReferencePointGizmo_m1CCA44422B01AFC722B02C7A9032345CC8264F44 (void);
// 0x000000EA System.Void AssemblyPart::.ctor()
extern void AssemblyPart__ctor_m3A868BA0F7EC280E2368E4F589546FDD3695782A (void);
// 0x000000EB AssemblyStep AssemblyStep::CreateDirect(System.String,System.String,System.String,System.String)
extern void AssemblyStep_CreateDirect_m12C41BB8FF5AC02BEF0B44BD7CDFBD717F12E0E8 (void);
// 0x000000EC AssemblyStep AssemblyStep::CreateWithScrew(System.String,System.String,System.String,System.String,System.String,System.String)
extern void AssemblyStep_CreateWithScrew_m34C4D028BD6F7243EA8B441F9A66089668760BA4 (void);
// 0x000000ED AssemblyStep AssemblyStep::Create(System.String,System.String,System.String,System.String,System.String,AssemblyStep/FastenerInfo)
extern void AssemblyStep_Create_m6DB78A29CAB7CEA6B3615B08A2B35907A4165D7A (void);
// 0x000000EE System.Void AssemblyStep::AddMountPoint(System.String,System.String,System.String,System.String)
extern void AssemblyStep_AddMountPoint_m194EA8814100C8F34D6BB078603A47010B7B707D (void);
// 0x000000EF System.String AssemblyStep::ToString()
extern void AssemblyStep_ToString_m320E7A2E2902A63202043DA88272743C043F82EB (void);
// 0x000000F0 System.Boolean AssemblyStep::IsValid()
extern void AssemblyStep_IsValid_mF9FCF7AB253203EA0B1C0D39E09B71CE913F6A41 (void);
// 0x000000F1 System.Boolean AssemblyStep::RequiresFastener()
extern void AssemblyStep_RequiresFastener_mFD703603DDAD3E14B20E48D70E9EE9A5E3988974 (void);
// 0x000000F2 System.Boolean AssemblyStep::HasAdditionalMountPoints()
extern void AssemblyStep_HasAdditionalMountPoints_m5341C821D4FE304D28E15E715126EF409BEA18E3 (void);
// 0x000000F3 AssemblyStep/FastenerInfo AssemblyStep/FastenerInfo::get_Empty()
extern void FastenerInfo_get_Empty_mB6E6BDF922B577CDD26EF4EAFE797397DB6D0F09 (void);
// 0x000000F4 AssemblyStep/FastenerInfo AssemblyStep/FastenerInfo::CreateScrew(System.String,System.String)
extern void FastenerInfo_CreateScrew_m81C9F9116D2CFC59BA2634D6D3365B9927E530C1 (void);
// 0x000000F5 System.String AssemblyStep/FastenerInfo::GetScrewPrefabName(System.String)
extern void FastenerInfo_GetScrewPrefabName_mF8FF0A3357E0D93D0E92BEAB58C43B420954CC6F (void);
// 0x000000F6 System.String AssemblyStep/FastenerInfo::GetNutPrefabName(System.String)
extern void FastenerInfo_GetNutPrefabName_mE1B530F590FD507BC8E6580B643F4362D2853EAE (void);
// 0x000000F7 AssemblyStep/AdditionalMountPoint AssemblyStep/AdditionalMountPoint::Create(System.String,System.String,System.String,AssemblyStep/FastenerInfo)
extern void AdditionalMountPoint_Create_m599AEDDB62836E0EE9104830BF83B4AE2D3D96C0 (void);
// 0x000000F8 System.Collections.IEnumerator IAssemblyDataProvider::LoadAssemblySteps(System.String,System.Action`1<System.Collections.Generic.List`1<AssemblyStepData>>)
// 0x000000F9 System.Collections.IEnumerator IAssemblyDataProvider::TestConnection(System.Action`1<System.Boolean>)
// 0x000000FA System.String IAssemblyDataProvider::get_DataSourceType()
// 0x000000FB System.Boolean IAssemblyDataProvider::get_IsConnected()
// 0x000000FC System.String AssemblyStepData::ToString()
extern void AssemblyStepData_ToString_mE8C413BE6001E1478285B8AA28545AB3EBC65360 (void);
// 0x000000FD System.String ExternalSystemDataProvider::get_DataSourceType()
extern void ExternalSystemDataProvider_get_DataSourceType_m14CDFE335CB35D4AD1AC1DE3BBB0B2E25D4977DA (void);
// 0x000000FE System.Boolean ExternalSystemDataProvider::get_IsConnected()
extern void ExternalSystemDataProvider_get_IsConnected_m428A4A6807AE65917050F1D4BD1FDE5384743B94 (void);
// 0x000000FF System.Void ExternalSystemDataProvider::set_IsConnected(System.Boolean)
extern void ExternalSystemDataProvider_set_IsConnected_m6D819239718C5DCC0C40836B3077E444E3EC2178 (void);
// 0x00000100 System.Void ExternalSystemDataProvider::Start()
extern void ExternalSystemDataProvider_Start_mC5926556AE446A1DC315041738E77D2C3977BF05 (void);
// 0x00000101 System.Collections.IEnumerator ExternalSystemDataProvider::LoadAssemblySteps(System.String,System.Action`1<System.Collections.Generic.List`1<AssemblyStepData>>)
extern void ExternalSystemDataProvider_LoadAssemblySteps_m1472E0480FA42AFF7360021D4F824FB8C3D5A452 (void);
// 0x00000102 System.Collections.IEnumerator ExternalSystemDataProvider::TestConnection(System.Action`1<System.Boolean>)
extern void ExternalSystemDataProvider_TestConnection_mDB39828320D3F62AB5BCBC56EB6EF8248B5CE976 (void);
// 0x00000103 System.Collections.IEnumerator ExternalSystemDataProvider::RequestAssemblyDataFromExternalSystem(System.String,System.Action`2<System.Boolean,System.Collections.Generic.List`1<AssemblyStepData>>)
extern void ExternalSystemDataProvider_RequestAssemblyDataFromExternalSystem_mC61C99CC5DB66CBE28CB449494EAF4F4E8AB73EE (void);
// 0x00000104 System.Collections.Generic.List`1<AssemblyStepData> ExternalSystemDataProvider::ParseAssemblyDataFromJson(System.String)
extern void ExternalSystemDataProvider_ParseAssemblyDataFromJson_m8DCB25C7EAC9468A6EE2E03D4C0250320A667893 (void);
// 0x00000105 System.Collections.Generic.List`1<AssemblyStepData> ExternalSystemDataProvider::LoadFallbackData(System.String)
extern void ExternalSystemDataProvider_LoadFallbackData_m1B98A53A671D82EA503AE632E1B5415C5DDE1AC9 (void);
// 0x00000106 System.Void ExternalSystemDataProvider::SetAssemblyData(System.String,System.Collections.Generic.List`1<AssemblyStepData>)
extern void ExternalSystemDataProvider_SetAssemblyData_m3B14BDC51384FD61843D7F9DC1BF8CF9F7B8A166 (void);
// 0x00000107 System.Void ExternalSystemDataProvider::ClearCache()
extern void ExternalSystemDataProvider_ClearCache_m9D6121B0F4F95CBF9C21A89CB25B173D131E83C7 (void);
// 0x00000108 System.Void ExternalSystemDataProvider::.ctor()
extern void ExternalSystemDataProvider__ctor_m9590CBEF733E2D7E55508B2FB9B25C5DBFD152BD (void);
// 0x00000109 System.Void ExternalSystemDataProvider::<Start>b__11_0(System.Boolean)
extern void ExternalSystemDataProvider_U3CStartU3Eb__11_0_mA2C973F8D30BE3B3D3D798DCCC3516D0BCFDDCCB (void);
// 0x0000010A System.Void ExternalSystemDataProvider/<>c__DisplayClass12_0::.ctor()
extern void U3CU3Ec__DisplayClass12_0__ctor_mC73E03CEA17878AD793BE79F4E4B3CAE982D06D2 (void);
// 0x0000010B System.Void ExternalSystemDataProvider/<>c__DisplayClass12_0::<LoadAssemblySteps>b__0(System.Boolean,System.Collections.Generic.List`1<AssemblyStepData>)
extern void U3CU3Ec__DisplayClass12_0_U3CLoadAssemblyStepsU3Eb__0_m16AF587FC247F8F9393586C243453E0879517F8D (void);
// 0x0000010C System.Void ExternalSystemDataProvider/<LoadAssemblySteps>d__12::.ctor(System.Int32)
extern void U3CLoadAssemblyStepsU3Ed__12__ctor_m3C4467A00F1A52FA235CC197F8154C46821806AF (void);
// 0x0000010D System.Void ExternalSystemDataProvider/<LoadAssemblySteps>d__12::System.IDisposable.Dispose()
extern void U3CLoadAssemblyStepsU3Ed__12_System_IDisposable_Dispose_m823495F27CE6FA0DDB5A212DE42F0B86008943C8 (void);
// 0x0000010E System.Boolean ExternalSystemDataProvider/<LoadAssemblySteps>d__12::MoveNext()
extern void U3CLoadAssemblyStepsU3Ed__12_MoveNext_mE99C821ED07EFE7AF1BBB874E479E5033CABF493 (void);
// 0x0000010F System.Object ExternalSystemDataProvider/<LoadAssemblySteps>d__12::System.Collections.Generic.IEnumerator<System.Object>.get_Current()
extern void U3CLoadAssemblyStepsU3Ed__12_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m0E4CE5851A7B106D55ADAD5F7E9F1C6D597A441D (void);
// 0x00000110 System.Void ExternalSystemDataProvider/<LoadAssemblySteps>d__12::System.Collections.IEnumerator.Reset()
extern void U3CLoadAssemblyStepsU3Ed__12_System_Collections_IEnumerator_Reset_mAD9121A9CE9C097EE3A7F42FD4D274661A6BC2A3 (void);
// 0x00000111 System.Object ExternalSystemDataProvider/<LoadAssemblySteps>d__12::System.Collections.IEnumerator.get_Current()
extern void U3CLoadAssemblyStepsU3Ed__12_System_Collections_IEnumerator_get_Current_m04E3677891C88CDBD5394C91BD4E70518891BCC8 (void);
// 0x00000112 System.Void ExternalSystemDataProvider/<TestConnection>d__13::.ctor(System.Int32)
extern void U3CTestConnectionU3Ed__13__ctor_mED859D27EA19C44ED202C9A522933A077C6B2122 (void);
// 0x00000113 System.Void ExternalSystemDataProvider/<TestConnection>d__13::System.IDisposable.Dispose()
extern void U3CTestConnectionU3Ed__13_System_IDisposable_Dispose_m64E944147C46A76E1472CEE05499759C04CF0925 (void);
// 0x00000114 System.Boolean ExternalSystemDataProvider/<TestConnection>d__13::MoveNext()
extern void U3CTestConnectionU3Ed__13_MoveNext_m17B3C0EFCABCFD6194690E8D5E1EA25D24900CDC (void);
// 0x00000115 System.Void ExternalSystemDataProvider/<TestConnection>d__13::<>m__Finally1()
extern void U3CTestConnectionU3Ed__13_U3CU3Em__Finally1_mF77D5135A870834B987AED604F7F619AD1DC645B (void);
// 0x00000116 System.Object ExternalSystemDataProvider/<TestConnection>d__13::System.Collections.Generic.IEnumerator<System.Object>.get_Current()
extern void U3CTestConnectionU3Ed__13_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m57C0DAED84E9128B87049D4DB24DC7FFEF2FF4CF (void);
// 0x00000117 System.Void ExternalSystemDataProvider/<TestConnection>d__13::System.Collections.IEnumerator.Reset()
extern void U3CTestConnectionU3Ed__13_System_Collections_IEnumerator_Reset_m0109DE10A1A3A1B19A8D19D94B86B6CA750FF7F7 (void);
// 0x00000118 System.Object ExternalSystemDataProvider/<TestConnection>d__13::System.Collections.IEnumerator.get_Current()
extern void U3CTestConnectionU3Ed__13_System_Collections_IEnumerator_get_Current_m0A71964B7E2DB3173D0C01DCF06CE10A79F61A59 (void);
// 0x00000119 System.Void ExternalSystemDataProvider/<RequestAssemblyDataFromExternalSystem>d__14::.ctor(System.Int32)
extern void U3CRequestAssemblyDataFromExternalSystemU3Ed__14__ctor_m646BB5DCB05AFE7A08F5EA4D5666CF4E426C0FC4 (void);
// 0x0000011A System.Void ExternalSystemDataProvider/<RequestAssemblyDataFromExternalSystem>d__14::System.IDisposable.Dispose()
extern void U3CRequestAssemblyDataFromExternalSystemU3Ed__14_System_IDisposable_Dispose_mD38B1871DF3B73A3E411F2E513100C439DA7CBF2 (void);
// 0x0000011B System.Boolean ExternalSystemDataProvider/<RequestAssemblyDataFromExternalSystem>d__14::MoveNext()
extern void U3CRequestAssemblyDataFromExternalSystemU3Ed__14_MoveNext_m9011DA563EDA71964BD5306F8F12A8549F6E7956 (void);
// 0x0000011C System.Void ExternalSystemDataProvider/<RequestAssemblyDataFromExternalSystem>d__14::<>m__Finally1()
extern void U3CRequestAssemblyDataFromExternalSystemU3Ed__14_U3CU3Em__Finally1_mDBFECEC7CE9D75CCBE4A55DABA1B94ADF841E02D (void);
// 0x0000011D System.Object ExternalSystemDataProvider/<RequestAssemblyDataFromExternalSystem>d__14::System.Collections.Generic.IEnumerator<System.Object>.get_Current()
extern void U3CRequestAssemblyDataFromExternalSystemU3Ed__14_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m7CF71093660F98A17DFCDB1CF90366D9783825BC (void);
// 0x0000011E System.Void ExternalSystemDataProvider/<RequestAssemblyDataFromExternalSystem>d__14::System.Collections.IEnumerator.Reset()
extern void U3CRequestAssemblyDataFromExternalSystemU3Ed__14_System_Collections_IEnumerator_Reset_m5A7B8010C71A615ED9E3C3B6F59146359ECEBC9D (void);
// 0x0000011F System.Object ExternalSystemDataProvider/<RequestAssemblyDataFromExternalSystem>d__14::System.Collections.IEnumerator.get_Current()
extern void U3CRequestAssemblyDataFromExternalSystemU3Ed__14_System_Collections_IEnumerator_get_Current_m73D5DF270A82D9EBDCAD575060CFED95C809EF74 (void);
// 0x00000120 System.Void VRStartMenuDebugger::Start()
extern void VRStartMenuDebugger_Start_mCA11FD2E79A9F2411E1F92AEA1D4D4FE598EFCA2 (void);
// 0x00000121 System.Collections.IEnumerator VRStartMenuDebugger::InitializeDebugger()
extern void VRStartMenuDebugger_InitializeDebugger_m50DCD85046ED23A21922790766C2C5BF58598AF2 (void);
// 0x00000122 System.Void VRStartMenuDebugger::FindComponents()
extern void VRStartMenuDebugger_FindComponents_m195F2B12B6D65A57C1C5EDD4E541ABD55341DE64 (void);
// 0x00000123 System.Void VRStartMenuDebugger::CheckComponentStatus()
extern void VRStartMenuDebugger_CheckComponentStatus_m887ADEF2C22CC1903D56E36E2174340C4FB57EC1 (void);
// 0x00000124 System.Void VRStartMenuDebugger::AutoFixIssues()
extern void VRStartMenuDebugger_AutoFixIssues_mFD6C6EAA452662400EE66E4ACFB0E62EEBD8A2E0 (void);
// 0x00000125 System.Void VRStartMenuDebugger::FixInputFieldColors()
extern void VRStartMenuDebugger_FixInputFieldColors_m0B7EA43D6125FE83F7EC30C0EE5C4B6248A25D7C (void);
// 0x00000126 System.Void VRStartMenuDebugger::EnsureEventListeners()
extern void VRStartMenuDebugger_EnsureEventListeners_mB6B8DDD8F3557834CC84593804D604D718C21961 (void);
// 0x00000127 System.Void VRStartMenuDebugger::SetupDebugListeners()
extern void VRStartMenuDebugger_SetupDebugListeners_m0F67D6CD44B6E69A5CC77EDE6B059DCB74123EDB (void);
// 0x00000128 System.Void VRStartMenuDebugger::OnDebugUsernameChanged(System.String)
extern void VRStartMenuDebugger_OnDebugUsernameChanged_m3783811BCAA798FE77F5AF503A2DAF6DF55A6EC1 (void);
// 0x00000129 System.Void VRStartMenuDebugger::OnDebugStartButtonClicked()
extern void VRStartMenuDebugger_OnDebugStartButtonClicked_mFD69A072CC76586FE7569FB6D4436CD4B20F7659 (void);
// 0x0000012A System.Void VRStartMenuDebugger::OnUsernameChangedDebug(System.String)
extern void VRStartMenuDebugger_OnUsernameChangedDebug_m4660AE06B8591298F6CBAD1BEA86100E3F7F04C5 (void);
// 0x0000012B System.Void VRStartMenuDebugger::OnStartButtonClickedDebug()
extern void VRStartMenuDebugger_OnStartButtonClickedDebug_mD085B289A5C05AC0B77C482C7A045D531545817B (void);
// 0x0000012C System.Void VRStartMenuDebugger::Update()
extern void VRStartMenuDebugger_Update_m109186679A78C4613687189ABAAF04284FFA0DE0 (void);
// 0x0000012D System.Void VRStartMenuDebugger::TestUsernameValidation()
extern void VRStartMenuDebugger_TestUsernameValidation_mE81B392B607AFE5B27637FD9517ABE15673476C5 (void);
// 0x0000012E System.Void VRStartMenuDebugger::SimulateButtonClick()
extern void VRStartMenuDebugger_SimulateButtonClick_m7D76FCB9BAD45F0604B456776E03EBC46A9D7B04 (void);
// 0x0000012F System.Void VRStartMenuDebugger::OnDestroy()
extern void VRStartMenuDebugger_OnDestroy_m47501387F1037E4067C9D35EA88571E2952BDBA8 (void);
// 0x00000130 System.Void VRStartMenuDebugger::.ctor()
extern void VRStartMenuDebugger__ctor_m7BE0E184B4DCD358DFE8598CDA06134701DF1941 (void);
// 0x00000131 System.Void VRStartMenuDebugger/<InitializeDebugger>d__6::.ctor(System.Int32)
extern void U3CInitializeDebuggerU3Ed__6__ctor_mF601DC61C87C6320172FFD4DD28B30F13B8AC96D (void);
// 0x00000132 System.Void VRStartMenuDebugger/<InitializeDebugger>d__6::System.IDisposable.Dispose()
extern void U3CInitializeDebuggerU3Ed__6_System_IDisposable_Dispose_m5EB40F4521B70B4D9DE8A1E350604A8672CA5C0E (void);
// 0x00000133 System.Boolean VRStartMenuDebugger/<InitializeDebugger>d__6::MoveNext()
extern void U3CInitializeDebuggerU3Ed__6_MoveNext_mF5F5CA347F139A978FAA9CCF71246754F38DE723 (void);
// 0x00000134 System.Object VRStartMenuDebugger/<InitializeDebugger>d__6::System.Collections.Generic.IEnumerator<System.Object>.get_Current()
extern void U3CInitializeDebuggerU3Ed__6_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m05E97CBD35B96A1EBE2F44EC9102D2ED3AD3C97B (void);
// 0x00000135 System.Void VRStartMenuDebugger/<InitializeDebugger>d__6::System.Collections.IEnumerator.Reset()
extern void U3CInitializeDebuggerU3Ed__6_System_Collections_IEnumerator_Reset_m70984B78FEA26B0E8266FCB0AF86AA0E0EB7B96A (void);
// 0x00000136 System.Object VRStartMenuDebugger/<InitializeDebugger>d__6::System.Collections.IEnumerator.get_Current()
extern void U3CInitializeDebuggerU3Ed__6_System_Collections_IEnumerator_get_Current_m26C5B45A5E24AAD3AAE213B7ECC10882B1BA2D17 (void);
// 0x00000137 System.Void VRStartMenuInputHandler::Start()
extern void VRStartMenuInputHandler_Start_m137CD96555FE4F55446BFD50245245BFA03969BF (void);
// 0x00000138 System.Void VRStartMenuInputHandler::Update()
extern void VRStartMenuInputHandler_Update_mF1FB12C8E91A65089D2DE2182931F759FC6FD334 (void);
// 0x00000139 System.Void VRStartMenuInputHandler::InitializeInputHandler()
extern void VRStartMenuInputHandler_InitializeInputHandler_mFCF368DFE40DB98269297E9241AA74887F07F773 (void);
// 0x0000013A System.Void VRStartMenuInputHandler::InitializeVRControllers()
extern void VRStartMenuInputHandler_InitializeVRControllers_mED30E8BBEE14FD4BBDFBBD7475AA9C30E8D9DCB5 (void);
// 0x0000013B System.Void VRStartMenuInputHandler::SetupInputFieldEvents()
extern void VRStartMenuInputHandler_SetupInputFieldEvents_mB71CAB34CB1B25C9086FDBC6A3E91A03F3EE366D (void);
// 0x0000013C System.Void VRStartMenuInputHandler::HandleVRInput()
extern void VRStartMenuInputHandler_HandleVRInput_mBF6807512C22D0483E70E8A6EB24C8836F191516 (void);
// 0x0000013D System.Void VRStartMenuInputHandler::OnInputFieldSelected(System.String)
extern void VRStartMenuInputHandler_OnInputFieldSelected_m7AEB482780B962F80F36DFC5A0EBFEEDF1B4FA1E (void);
// 0x0000013E System.Void VRStartMenuInputHandler::OnInputFieldDeselected(System.String)
extern void VRStartMenuInputHandler_OnInputFieldDeselected_m57C1A10FCB11AF304502C7399A5FA2C04DEC4DE3 (void);
// 0x0000013F System.Void VRStartMenuInputHandler::OnInputValueChanged(System.String)
extern void VRStartMenuInputHandler_OnInputValueChanged_m1E0C4D176DDCF4D060C783B0735B6E9CC5782928 (void);
// 0x00000140 System.String VRStartMenuInputHandler::ValidateInput(System.String)
extern void VRStartMenuInputHandler_ValidateInput_mDF07421BFAEFFD767FFA9AA4C36ECFD53BFCB20A (void);
// 0x00000141 System.Void VRStartMenuInputHandler::ShowVirtualKeyboard()
extern void VRStartMenuInputHandler_ShowVirtualKeyboard_m6658344E465C06E44B7ECF1CE71FF16BAE562EA7 (void);
// 0x00000142 System.Void VRStartMenuInputHandler::HideVirtualKeyboard()
extern void VRStartMenuInputHandler_HideVirtualKeyboard_m0DACE19F455726E6559CBAAD034896F8DBAC9C3A (void);
// 0x00000143 System.Void VRStartMenuInputHandler::PositionVirtualKeyboard()
extern void VRStartMenuInputHandler_PositionVirtualKeyboard_m3A7124F39BB7EF5848EB1CECBB10ED93B98FF22E (void);
// 0x00000144 System.Void VRStartMenuInputHandler::CreateVirtualKeyboard()
extern void VRStartMenuInputHandler_CreateVirtualKeyboard_m6DFCDF81906C0BA1D7C957DB6A48744E98D52920 (void);
// 0x00000145 System.Void VRStartMenuInputHandler::CreateSimpleKeyboard(UnityEngine.Transform)
extern void VRStartMenuInputHandler_CreateSimpleKeyboard_mE607E49D01315D6BFD795D6080DFE911AB15CDA3 (void);
// 0x00000146 System.Void VRStartMenuInputHandler::CreateKeyboardButton(UnityEngine.Transform,System.String,UnityEngine.Vector2,System.Action)
extern void VRStartMenuInputHandler_CreateKeyboardButton_m6BB7DC28C3DCA398CF1E5111786C32064E7269EA (void);
// 0x00000147 System.Void VRStartMenuInputHandler::ConfirmInput()
extern void VRStartMenuInputHandler_ConfirmInput_mAA6FF7DD95A7626C4A63AF49EEDD2D3AAFE86435 (void);
// 0x00000148 System.Void VRStartMenuInputHandler::DeleteLastCharacter()
extern void VRStartMenuInputHandler_DeleteLastCharacter_m655E23F3537BA2E3C17E840CE6558DD5435A298A (void);
// 0x00000149 System.Void VRStartMenuInputHandler::CancelInput()
extern void VRStartMenuInputHandler_CancelInput_m81A357F1B947E35EC2E6D730361295A00AAB45AD (void);
// 0x0000014A System.Void VRStartMenuInputHandler::TriggerHapticFeedback(System.Single)
extern void VRStartMenuInputHandler_TriggerHapticFeedback_mFB1075B4A1A79ED8FD6B10FB783B6EA33915E503 (void);
// 0x0000014B System.Void VRStartMenuInputHandler::PlaySound(UnityEngine.AudioClip)
extern void VRStartMenuInputHandler_PlaySound_mA2D4E417DDC3D6F8F07759659D13F7FA77A1AE22 (void);
// 0x0000014C System.Void VRStartMenuInputHandler::SetTargetInputField(TMPro.TMP_InputField)
extern void VRStartMenuInputHandler_SetTargetInputField_m17B5E22D37C983BDB24E012E9F0FF227FA0575C9 (void);
// 0x0000014D System.Void VRStartMenuInputHandler::SetStartButton(UnityEngine.UI.Button)
extern void VRStartMenuInputHandler_SetStartButton_m6C4809C34295CFEA5076390A05172AB46AECE738 (void);
// 0x0000014E System.Void VRStartMenuInputHandler::OnDestroy()
extern void VRStartMenuInputHandler_OnDestroy_m3F6822CD6DE09F9AA4413E5B56D29AFCB5EB6312 (void);
// 0x0000014F System.Void VRStartMenuInputHandler::.ctor()
extern void VRStartMenuInputHandler__ctor_m4692B8956B477837C04B0A7F332CAF987CFEC6F0 (void);
// 0x00000150 System.Void VRStartMenuInputHandler/<>c__DisplayClass41_0::.ctor()
extern void U3CU3Ec__DisplayClass41_0__ctor_m499D9EA61ECC2731D1395ABAA2215DCB62466B1C (void);
// 0x00000151 System.Void VRStartMenuInputHandler/<>c__DisplayClass41_0::<CreateKeyboardButton>b__0()
extern void U3CU3Ec__DisplayClass41_0_U3CCreateKeyboardButtonU3Eb__0_m7BF9B61A50A8B7E38BB86766226D87687BAFF3B7 (void);
// 0x00000152 System.Void VRStartMenuManager::Start()
extern void VRStartMenuManager_Start_m9F886633AC413F63DBDE41E7E222EE5F56E67DFF (void);
// 0x00000153 System.Collections.IEnumerator VRStartMenuManager::InitializeStartMenu()
extern void VRStartMenuManager_InitializeStartMenu_m04C795CD3CA78AA19228ABFFD9643A0FDFC2973A (void);
// 0x00000154 System.Void VRStartMenuManager::SetupComponents()
extern void VRStartMenuManager_SetupComponents_m2DEAF59F164ABFB7EBFC878307013E3D21FB7E13 (void);
// 0x00000155 System.Void VRStartMenuManager::EnsureUIReferences()
extern void VRStartMenuManager_EnsureUIReferences_mA6ACD2FC19B8289E3A341E43286A7164D3BA6783 (void);
// 0x00000156 System.Void VRStartMenuManager::SetupVRUI()
extern void VRStartMenuManager_SetupVRUI_m7EDB14F93B2BD9EEBA68CAD61E49C7CBC9AD347C (void);
// 0x00000157 System.Void VRStartMenuManager::PositionUIForVR()
extern void VRStartMenuManager_PositionUIForVR_mD6DCC7F14DB8A9E4C8B0532F304EB0C8D0E7FE48 (void);
// 0x00000158 System.Void VRStartMenuManager::SetupUIElements()
extern void VRStartMenuManager_SetupUIElements_m67BE95F818BB4CEA7242B1FB31495986523C7ACA (void);
// 0x00000159 System.Void VRStartMenuManager::SetupEventListeners()
extern void VRStartMenuManager_SetupEventListeners_m2906A0BC9B1589C20053F36A4311D43F330D8C6B (void);
// 0x0000015A System.Void VRStartMenuManager::OnUsernameInputChanged(System.String)
extern void VRStartMenuManager_OnUsernameInputChanged_m6EA9A6A6FAAFBFC0B29B9E55F69D6920816392D4 (void);
// 0x0000015B System.Boolean VRStartMenuManager::ValidateUsername(System.String)
extern void VRStartMenuManager_ValidateUsername_m9D7AA04294D2029192A994968BBACD87573A740C (void);
// 0x0000015C System.Void VRStartMenuManager::OnStartButtonPressed()
extern void VRStartMenuManager_OnStartButtonPressed_mC9C370BE0AEEE182B836745018DFB5DC151E8097 (void);
// 0x0000015D System.Collections.IEnumerator VRStartMenuManager::TransitionToNextScene()
extern void VRStartMenuManager_TransitionToNextScene_mA7E052CDD2051BB40382F29938340448D42859C0 (void);
// 0x0000015E System.Void VRStartMenuManager::ShowUsernameEmptyWarning()
extern void VRStartMenuManager_ShowUsernameEmptyWarning_mB03BFC06B721F52AF75A5094F5529ACF081BC6D8 (void);
// 0x0000015F System.Collections.IEnumerator VRStartMenuManager::ShowTemporaryWarning()
extern void VRStartMenuManager_ShowTemporaryWarning_m17C50A332A89F46B83B1EE53AA54D4A090702DB4 (void);
// 0x00000160 System.Void VRStartMenuManager::PlaySound(UnityEngine.AudioClip)
extern void VRStartMenuManager_PlaySound_m7D90BFA6B6751A6528C69413FE3E8020F6A425BC (void);
// 0x00000161 System.String VRStartMenuManager::GetCurrentUsername()
extern void VRStartMenuManager_GetCurrentUsername_m7CE4CF30AE7FFC1511D50D52224D425ECDA91214 (void);
// 0x00000162 System.Void VRStartMenuManager::SetNextSceneName(System.String)
extern void VRStartMenuManager_SetNextSceneName_mAFEABA75AB99D71A60CB31CD75931FE12835CA72 (void);
// 0x00000163 System.Void VRStartMenuManager::RepositionUI()
extern void VRStartMenuManager_RepositionUI_m9F928B3131C92D4C94A5E88A41678BDA74444BFA (void);
// 0x00000164 System.Void VRStartMenuManager::OnDestroy()
extern void VRStartMenuManager_OnDestroy_m15A4AC7DC8A6973085C5E7750F33DB3901D8793E (void);
// 0x00000165 System.Void VRStartMenuManager::.ctor()
extern void VRStartMenuManager__ctor_m4A4A9EB001BD35898F06BFE63B5A3FFB73D1143D (void);
// 0x00000166 System.Void VRStartMenuManager/<InitializeStartMenu>d__24::.ctor(System.Int32)
extern void U3CInitializeStartMenuU3Ed__24__ctor_m437041FE0499D7785CB9D91A232CBB3A4EAB5CC1 (void);
// 0x00000167 System.Void VRStartMenuManager/<InitializeStartMenu>d__24::System.IDisposable.Dispose()
extern void U3CInitializeStartMenuU3Ed__24_System_IDisposable_Dispose_mE32D883D9F5F72EB7D43339577E3C2D09B6DDFBC (void);
// 0x00000168 System.Boolean VRStartMenuManager/<InitializeStartMenu>d__24::MoveNext()
extern void U3CInitializeStartMenuU3Ed__24_MoveNext_m936FAC86DD24ECE91E0DFC919CAE158E2702FC58 (void);
// 0x00000169 System.Object VRStartMenuManager/<InitializeStartMenu>d__24::System.Collections.Generic.IEnumerator<System.Object>.get_Current()
extern void U3CInitializeStartMenuU3Ed__24_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mED8EE2B49708FE33CFAE34E993E6F34D8B9991BC (void);
// 0x0000016A System.Void VRStartMenuManager/<InitializeStartMenu>d__24::System.Collections.IEnumerator.Reset()
extern void U3CInitializeStartMenuU3Ed__24_System_Collections_IEnumerator_Reset_m1C5131F125A503B0C61288BA2046401EC7ADE6DD (void);
// 0x0000016B System.Object VRStartMenuManager/<InitializeStartMenu>d__24::System.Collections.IEnumerator.get_Current()
extern void U3CInitializeStartMenuU3Ed__24_System_Collections_IEnumerator_get_Current_m0F4F5C8508EAB01CD23A77923615CD8AFBC1938B (void);
// 0x0000016C System.Void VRStartMenuManager/<TransitionToNextScene>d__34::.ctor(System.Int32)
extern void U3CTransitionToNextSceneU3Ed__34__ctor_m5E47491BF11C1DA429A0D9EA87F87CFD087F050B (void);
// 0x0000016D System.Void VRStartMenuManager/<TransitionToNextScene>d__34::System.IDisposable.Dispose()
extern void U3CTransitionToNextSceneU3Ed__34_System_IDisposable_Dispose_mFCE1E18878E683311B0CAA4C0ACE5E43EA95A800 (void);
// 0x0000016E System.Boolean VRStartMenuManager/<TransitionToNextScene>d__34::MoveNext()
extern void U3CTransitionToNextSceneU3Ed__34_MoveNext_m6B537140FDC6FA1D94A964827AEA30D2A506246A (void);
// 0x0000016F System.Object VRStartMenuManager/<TransitionToNextScene>d__34::System.Collections.Generic.IEnumerator<System.Object>.get_Current()
extern void U3CTransitionToNextSceneU3Ed__34_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m1BD547156554140B100A0C5478482173B5FAE1D5 (void);
// 0x00000170 System.Void VRStartMenuManager/<TransitionToNextScene>d__34::System.Collections.IEnumerator.Reset()
extern void U3CTransitionToNextSceneU3Ed__34_System_Collections_IEnumerator_Reset_mF1B8A22D6F272DFE16BA19705CB6F41E48E74DD1 (void);
// 0x00000171 System.Object VRStartMenuManager/<TransitionToNextScene>d__34::System.Collections.IEnumerator.get_Current()
extern void U3CTransitionToNextSceneU3Ed__34_System_Collections_IEnumerator_get_Current_m19A156A6A14955E57DBBF11AAFE000F3B2F80896 (void);
// 0x00000172 System.Void VRStartMenuManager/<ShowTemporaryWarning>d__36::.ctor(System.Int32)
extern void U3CShowTemporaryWarningU3Ed__36__ctor_m27F57359E19E5CFD4B26954401A3D12D22AAF0F7 (void);
// 0x00000173 System.Void VRStartMenuManager/<ShowTemporaryWarning>d__36::System.IDisposable.Dispose()
extern void U3CShowTemporaryWarningU3Ed__36_System_IDisposable_Dispose_mC401D53FA967D9A0D6F8BE627B642AC722DE8816 (void);
// 0x00000174 System.Boolean VRStartMenuManager/<ShowTemporaryWarning>d__36::MoveNext()
extern void U3CShowTemporaryWarningU3Ed__36_MoveNext_m85AB959FDD14873C823CA5060DADD1CFFFE0E2C2 (void);
// 0x00000175 System.Object VRStartMenuManager/<ShowTemporaryWarning>d__36::System.Collections.Generic.IEnumerator<System.Object>.get_Current()
extern void U3CShowTemporaryWarningU3Ed__36_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m100151301AA8DDCDF651527EB5F49B71C1BB8F1F (void);
// 0x00000176 System.Void VRStartMenuManager/<ShowTemporaryWarning>d__36::System.Collections.IEnumerator.Reset()
extern void U3CShowTemporaryWarningU3Ed__36_System_Collections_IEnumerator_Reset_m7DDCBE8B54528677BEE4D10993A450182F40DCB5 (void);
// 0x00000177 System.Object VRStartMenuManager/<ShowTemporaryWarning>d__36::System.Collections.IEnumerator.get_Current()
extern void U3CShowTemporaryWarningU3Ed__36_System_Collections_IEnumerator_get_Current_mD149B075A0BF1318C8B796F78F2974598F377BA7 (void);
// 0x00000178 System.Void Neo4jAssemblyController::add_OnPartSelected(System.Action`1<System.String>)
extern void Neo4jAssemblyController_add_OnPartSelected_m4D16A619571CE65FD08CFEDDBE34D00E3469E194 (void);
// 0x00000179 System.Void Neo4jAssemblyController::remove_OnPartSelected(System.Action`1<System.String>)
extern void Neo4jAssemblyController_remove_OnPartSelected_m35A3259406D73B2A30DCA1699D81B0D376B7426F (void);
// 0x0000017A System.Void Neo4jAssemblyController::add_OnAssemblyStepsLoaded(System.Action`1<System.Int32>)
extern void Neo4jAssemblyController_add_OnAssemblyStepsLoaded_mEF161A9592C509DEC5BEE31A2DAB43AA2FE177E3 (void);
// 0x0000017B System.Void Neo4jAssemblyController::remove_OnAssemblyStepsLoaded(System.Action`1<System.Int32>)
extern void Neo4jAssemblyController_remove_OnAssemblyStepsLoaded_mB8CAA0AEC52E6FFED644C55D5B8FC8FF6A513C2C (void);
// 0x0000017C System.Void Neo4jAssemblyController::add_OnStepExecuted(System.Action`3<System.Int32,System.Int32,System.String>)
extern void Neo4jAssemblyController_add_OnStepExecuted_mA0F70E2031DA9BE90755EEB10F1CC206475F157F (void);
// 0x0000017D System.Void Neo4jAssemblyController::remove_OnStepExecuted(System.Action`3<System.Int32,System.Int32,System.String>)
extern void Neo4jAssemblyController_remove_OnStepExecuted_mFF08276D8FE610DB524C365FD2D26D3DA6D2293E (void);
// 0x0000017E System.Void Neo4jAssemblyController::add_OnAssemblyCompleted(System.Action)
extern void Neo4jAssemblyController_add_OnAssemblyCompleted_m2BAC78501536C56C9D59EBD9AD15360EE42BE25C (void);
// 0x0000017F System.Void Neo4jAssemblyController::remove_OnAssemblyCompleted(System.Action)
extern void Neo4jAssemblyController_remove_OnAssemblyCompleted_m6124236E0195C0B5275A79A7094326324CE87755 (void);
// 0x00000180 System.Single Neo4jAssemblyController::get_AnimationDuration()
extern void Neo4jAssemblyController_get_AnimationDuration_mD65987078E3F5628F74EDBC6FA7FB8D1AC9A7FA6 (void);
// 0x00000181 System.Boolean Neo4jAssemblyController::HasRemainingSteps()
extern void Neo4jAssemblyController_HasRemainingSteps_mA3C07C4AA8D348AF02248CDFCA02C4B860395570 (void);
// 0x00000182 System.Single Neo4jAssemblyController::get_AnimationSpeedRate()
extern void Neo4jAssemblyController_get_AnimationSpeedRate_m4F8403F7507EF50FFC1AF78331AAD835FBCE46FC (void);
// 0x00000183 System.Void Neo4jAssemblyController::set_AnimationSpeedRate(System.Single)
extern void Neo4jAssemblyController_set_AnimationSpeedRate_mEC2065E1CE70858518AACC29AF215DAD353C069F (void);
// 0x00000184 System.Void Neo4jAssemblyController::Start()
extern void Neo4jAssemblyController_Start_m3AECDA8E796F8C997D6FCBF00A3854965D62EE8D (void);
// 0x00000185 System.Void Neo4jAssemblyController::InitializeDataSource()
extern void Neo4jAssemblyController_InitializeDataSource_m4A02EBD71B98D99A666978180F80837642FB5544 (void);
// 0x00000186 System.Void Neo4jAssemblyController::InitializeVRInput()
extern void Neo4jAssemblyController_InitializeVRInput_mF5AAAE73C17E0A6C8AE2B105BDE77F59AE76DB23 (void);
// 0x00000187 System.Void Neo4jAssemblyController::Update()
extern void Neo4jAssemblyController_Update_m55852F29A8A74946F9E1C94B8F7772DDBAECA3A5 (void);
// 0x00000188 System.Void Neo4jAssemblyController::HandleVRPartSelection(AssemblyPart)
extern void Neo4jAssemblyController_HandleVRPartSelection_m8D20EC8D130555634039D85720CC60A9C2500E88 (void);
// 0x00000189 System.Collections.IEnumerator Neo4jAssemblyController::LoadAssemblyStepsFromExternalSource(System.String)
extern void Neo4jAssemblyController_LoadAssemblyStepsFromExternalSource_m220FD7EEB23B401993043C74AAB99DD339DB643E (void);
// 0x0000018A System.Void Neo4jAssemblyController::ReceiveExternalAssemblyData(System.String,System.String)
extern void Neo4jAssemblyController_ReceiveExternalAssemblyData_mADA684E475D76B1FB3BDD135696FAA1AB1F5E521 (void);
// 0x0000018B System.Collections.Generic.List`1<AssemblyStepData> Neo4jAssemblyController::ParseExternalAssemblyData(System.String)
extern void Neo4jAssemblyController_ParseExternalAssemblyData_m539B7AC68C9A8C7C0F0DC85ECCC0E54AD54500D1 (void);
// 0x0000018C System.Void Neo4jAssemblyController::ProcessExternalAssemblySteps(System.Collections.Generic.List`1<AssemblyStepData>)
extern void Neo4jAssemblyController_ProcessExternalAssemblySteps_m9E85D256E06AB803A13D72A63B39F08DEB1BE0CF (void);
// 0x0000018D System.Void Neo4jAssemblyController::RegisterEvents()
extern void Neo4jAssemblyController_RegisterEvents_m25E701F38AB55F43F3C27000F68194C07E2A5296 (void);
// 0x0000018E System.Void Neo4jAssemblyController::UnregisterEvents()
extern void Neo4jAssemblyController_UnregisterEvents_mFAA72A59582464448EC861E7F37EA49AD36B925E (void);
// 0x0000018F System.Void Neo4jAssemblyController::InitializeVRSystem()
extern void Neo4jAssemblyController_InitializeVRSystem_mAC7B055F97C12134DB0841F1A985BE7D00FA3AC8 (void);
// 0x00000190 System.Collections.IEnumerator Neo4jAssemblyController::WaitForVRPreparation(AssemblyPart,AssemblyPart,System.String)
extern void Neo4jAssemblyController_WaitForVRPreparation_m54E9E7B24E841DD3B151F5CD649A72FE3D5C1BDA (void);
// 0x00000191 System.Void Neo4jAssemblyController::OnDestroy()
extern void Neo4jAssemblyController_OnDestroy_mAEAF19F1505C989BC014E45A134099F7BAD6C13A (void);
// 0x00000192 System.Void Neo4jAssemblyController::InitializePartMapping()
extern void Neo4jAssemblyController_InitializePartMapping_m682509B14C0F5C5E53EC3D2C7AE87D9BF4988C1D (void);
// 0x00000193 System.Void Neo4jAssemblyController::SaveAllPartsInitialState()
extern void Neo4jAssemblyController_SaveAllPartsInitialState_mF8E0AE3F5A1EF7EB7D69D072BEAD9583623F8FD2 (void);
// 0x00000194 System.Void Neo4jAssemblyController::SaveAllFastenersInitialState()
extern void Neo4jAssemblyController_SaveAllFastenersInitialState_m76BBA96BD40D615670BF2C04E489AEA4811E8582 (void);
// 0x00000195 System.Void Neo4jAssemblyController::RestoreAllPartsInitialState()
extern void Neo4jAssemblyController_RestoreAllPartsInitialState_mF422227C574FAE5053B7A1D338846B44E6208405 (void);
// 0x00000196 System.Void Neo4jAssemblyController::RestoreAllFastenersInitialState()
extern void Neo4jAssemblyController_RestoreAllFastenersInitialState_mAA05BEFC1C4A70827ECEA501FE997AC0D8C4D959 (void);
// 0x00000197 System.Void Neo4jAssemblyController::SaveStepPartsInitialState(Neo4jAssemblyController/AssemblyStep)
extern void Neo4jAssemblyController_SaveStepPartsInitialState_mC896D7D191AD4AF4378380D20610E7740D4AD93A (void);
// 0x00000198 System.Void Neo4jAssemblyController::RestoreStepPartsInitialState()
extern void Neo4jAssemblyController_RestoreStepPartsInitialState_m4E32E569376ECCA5FA209A3CDC143684E4FD7FB1 (void);
// 0x00000199 System.Void Neo4jAssemblyController::SelectPartWithRaycast()
extern void Neo4jAssemblyController_SelectPartWithRaycast_m81F9DEE660CCC6206D273753209E8A25D69F0AAA (void);
// 0x0000019A System.Void Neo4jAssemblyController::ResetAssemblySteps()
extern void Neo4jAssemblyController_ResetAssemblySteps_mC66B1EE8CE40B790B643BAB96909241CC8B03381 (void);
// 0x0000019B System.Collections.IEnumerator Neo4jAssemblyController::QueryAssemblyRelationships(System.String)
extern void Neo4jAssemblyController_QueryAssemblyRelationships_m510B66812C23BBC224DA63FF670E640873B240A7 (void);
// 0x0000019C System.Void Neo4jAssemblyController::ProcessAssemblyRelationships(System.String)
extern void Neo4jAssemblyController_ProcessAssemblyRelationships_mFF290E89F175D17B687084ACDE6CA8EDAF88641F (void);
// 0x0000019D System.Void Neo4jAssemblyController::ExecuteNextStep()
extern void Neo4jAssemblyController_ExecuteNextStep_mACFB3484B07642D7E1AB5DD2FCEF0BF28EFD0C5A (void);
// 0x0000019E System.Void Neo4jAssemblyController::ReplayLastStep()
extern void Neo4jAssemblyController_ReplayLastStep_mE733ACCC650508A829BD8BC1B4B6583E4D03195D (void);
// 0x0000019F System.Collections.IEnumerator Neo4jAssemblyController::ExecuteNextAssemblyStepCoroutine()
extern void Neo4jAssemblyController_ExecuteNextAssemblyStepCoroutine_m3CF8AF855E6F76DF59E55F173D5D9A09B96035EA (void);
// 0x000001A0 System.Collections.IEnumerator Neo4jAssemblyController::ReplayAssemblyStepCoroutine(Neo4jAssemblyController/AssemblyStep)
extern void Neo4jAssemblyController_ReplayAssemblyStepCoroutine_m3297ABAA72F89163A828E1D6124F9FC1A330AF5E (void);
// 0x000001A1 System.Void Neo4jAssemblyController::ResetAssembly()
extern void Neo4jAssemblyController_ResetAssembly_mD59C8122A2FB6FB3EFEC64F7CD6269AC2E6EFE23 (void);
// 0x000001A2 System.Collections.IEnumerator Neo4jAssemblyController::InstallFastener(Neo4jAssemblyController/AssemblyStep/FastenerInfo,AssemblyPart,System.Int32,System.Int32)
extern void Neo4jAssemblyController_InstallFastener_m9359B647D3FE9CA58FA7E9178FEACEF6EEC6B353 (void);
// 0x000001A3 System.String Neo4jAssemblyController::GetScrewPrefabName(System.String)
extern void Neo4jAssemblyController_GetScrewPrefabName_m11120109918806CCD7A9C806AEA1B0FDCF201DC3 (void);
// 0x000001A4 System.String Neo4jAssemblyController::GetNutPrefabName(System.String)
extern void Neo4jAssemblyController_GetNutPrefabName_m5E19A285FC2691F9273945519D3D4AF0ECA55D25 (void);
// 0x000001A5 AssemblyPart Neo4jAssemblyController::GetOrCreateFastener(System.String,System.Int32)
extern void Neo4jAssemblyController_GetOrCreateFastener_m5668C21E677AD73F08DB03CD0BB4A446D65B32D1 (void);
// 0x000001A6 AssemblyPart Neo4jAssemblyController::GetFastenerFromScene(System.String,System.Int32)
extern void Neo4jAssemblyController_GetFastenerFromScene_m71CCB551C77E2F8132E2178C1E00F39E20ABB059 (void);
// 0x000001A7 System.Void Neo4jAssemblyController::CleanupFasteners()
extern void Neo4jAssemblyController_CleanupFasteners_m2082E46B24B9678AB1A28D93A0BECE9EA2BA2C9C (void);
// 0x000001A8 System.Void Neo4jAssemblyController::ValidateReferencePointMappings()
extern void Neo4jAssemblyController_ValidateReferencePointMappings_mD89452E7D5999153B80C25E96FC5F98824180A0A (void);
// 0x000001A9 System.Int32 Neo4jAssemblyController::GetReferencePointIndex(System.String,System.String)
extern void Neo4jAssemblyController_GetReferencePointIndex_m0D83B603692A1137C3BC03DC391D1CC85EE8B728 (void);
// 0x000001AA System.Void Neo4jAssemblyController::.ctor()
extern void Neo4jAssemblyController__ctor_m4ADC9376EA623CE58D22058DA0C60245D51BBA9F (void);
// 0x000001AB System.Void Neo4jAssemblyController/AssemblyStep::.ctor(System.String,System.String,System.String,System.String)
extern void AssemblyStep__ctor_m40EF831184A0632EF7E7D625F994BB6378CEE04E (void);
// 0x000001AC System.Void Neo4jAssemblyController/AssemblyStep::.ctor(System.String,System.String,System.String,System.String,System.String,Neo4jAssemblyController/AssemblyStep/FastenerInfo)
extern void AssemblyStep__ctor_m9E10466D3C2D06685921F7B5B8A0C782FF812EFE (void);
// 0x000001AD System.String Neo4jAssemblyController/AssemblyStep::ToString()
extern void AssemblyStep_ToString_mF97FA0F6A8BF46F54FA596AB0A2C5C7F43719B1B (void);
// 0x000001AE System.Void Neo4jAssemblyController/<>c__DisplayClass59_0::.ctor()
extern void U3CU3Ec__DisplayClass59_0__ctor_m674F3B8084624F71F29620390EBB50983E196BA8 (void);
// 0x000001AF System.Void Neo4jAssemblyController/<>c__DisplayClass59_0::<LoadAssemblyStepsFromExternalSource>b__0(System.Collections.Generic.List`1<AssemblyStepData>)
extern void U3CU3Ec__DisplayClass59_0_U3CLoadAssemblyStepsFromExternalSourceU3Eb__0_m4F0C3DBE566EB3831FD70B0AEAEB01AC61D340A5 (void);
// 0x000001B0 System.Void Neo4jAssemblyController/<LoadAssemblyStepsFromExternalSource>d__59::.ctor(System.Int32)
extern void U3CLoadAssemblyStepsFromExternalSourceU3Ed__59__ctor_m2C1F4EFC774BF14272FF4C1DE89DE90681AD6C55 (void);
// 0x000001B1 System.Void Neo4jAssemblyController/<LoadAssemblyStepsFromExternalSource>d__59::System.IDisposable.Dispose()
extern void U3CLoadAssemblyStepsFromExternalSourceU3Ed__59_System_IDisposable_Dispose_m319F5BB430D02162EF3D195381B7E0DE3470B642 (void);
// 0x000001B2 System.Boolean Neo4jAssemblyController/<LoadAssemblyStepsFromExternalSource>d__59::MoveNext()
extern void U3CLoadAssemblyStepsFromExternalSourceU3Ed__59_MoveNext_m4C664DDEE23B0F7DB2C62D6A1D9AD7D437EF41EF (void);
// 0x000001B3 System.Object Neo4jAssemblyController/<LoadAssemblyStepsFromExternalSource>d__59::System.Collections.Generic.IEnumerator<System.Object>.get_Current()
extern void U3CLoadAssemblyStepsFromExternalSourceU3Ed__59_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m9E75C1167D32508D6D41500462CB256C3E65D4F5 (void);
// 0x000001B4 System.Void Neo4jAssemblyController/<LoadAssemblyStepsFromExternalSource>d__59::System.Collections.IEnumerator.Reset()
extern void U3CLoadAssemblyStepsFromExternalSourceU3Ed__59_System_Collections_IEnumerator_Reset_m08F35C22B001B0C70C07A90DAFD20092C2B8B704 (void);
// 0x000001B5 System.Object Neo4jAssemblyController/<LoadAssemblyStepsFromExternalSource>d__59::System.Collections.IEnumerator.get_Current()
extern void U3CLoadAssemblyStepsFromExternalSourceU3Ed__59_System_Collections_IEnumerator_get_Current_m6300756178A711F4FDAA4DD544522FEEC89132A3 (void);
// 0x000001B6 System.Void Neo4jAssemblyController/<WaitForVRPreparation>d__66::.ctor(System.Int32)
extern void U3CWaitForVRPreparationU3Ed__66__ctor_mA26F8D438CFBCC764A3973DF91BB079E5FC86820 (void);
// 0x000001B7 System.Void Neo4jAssemblyController/<WaitForVRPreparation>d__66::System.IDisposable.Dispose()
extern void U3CWaitForVRPreparationU3Ed__66_System_IDisposable_Dispose_m5490BA5AB8662E4178645EA3F755E64B9B077266 (void);
// 0x000001B8 System.Boolean Neo4jAssemblyController/<WaitForVRPreparation>d__66::MoveNext()
extern void U3CWaitForVRPreparationU3Ed__66_MoveNext_mCA451A1C7C47A420465B31D6CB316F6958D32F42 (void);
// 0x000001B9 System.Object Neo4jAssemblyController/<WaitForVRPreparation>d__66::System.Collections.Generic.IEnumerator<System.Object>.get_Current()
extern void U3CWaitForVRPreparationU3Ed__66_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mD036D6150E98DC02DE3A8F510FDC7A8FDAFD89F0 (void);
// 0x000001BA System.Void Neo4jAssemblyController/<WaitForVRPreparation>d__66::System.Collections.IEnumerator.Reset()
extern void U3CWaitForVRPreparationU3Ed__66_System_Collections_IEnumerator_Reset_m5F772293B065B07DF676A82107C0BBB0A5F92428 (void);
// 0x000001BB System.Object Neo4jAssemblyController/<WaitForVRPreparation>d__66::System.Collections.IEnumerator.get_Current()
extern void U3CWaitForVRPreparationU3Ed__66_System_Collections_IEnumerator_get_Current_m52AC34BA7D5DFF18B219A7BD2CD4EBE0BDF352B3 (void);
// 0x000001BC System.Void Neo4jAssemblyController/<QueryAssemblyRelationships>d__77::.ctor(System.Int32)
extern void U3CQueryAssemblyRelationshipsU3Ed__77__ctor_m63FF621B2605D1CE12644713FAD955EF8AF70741 (void);
// 0x000001BD System.Void Neo4jAssemblyController/<QueryAssemblyRelationships>d__77::System.IDisposable.Dispose()
extern void U3CQueryAssemblyRelationshipsU3Ed__77_System_IDisposable_Dispose_mF250A1D07A074FFBD2179196CB4C4A9A5D39F144 (void);
// 0x000001BE System.Boolean Neo4jAssemblyController/<QueryAssemblyRelationships>d__77::MoveNext()
extern void U3CQueryAssemblyRelationshipsU3Ed__77_MoveNext_m68B7BEE9C9852EC055960456DEB832C85417BDA4 (void);
// 0x000001BF System.Object Neo4jAssemblyController/<QueryAssemblyRelationships>d__77::System.Collections.Generic.IEnumerator<System.Object>.get_Current()
extern void U3CQueryAssemblyRelationshipsU3Ed__77_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m14ACC263AD0586D408DAF65D36ECC65EC0301CBA (void);
// 0x000001C0 System.Void Neo4jAssemblyController/<QueryAssemblyRelationships>d__77::System.Collections.IEnumerator.Reset()
extern void U3CQueryAssemblyRelationshipsU3Ed__77_System_Collections_IEnumerator_Reset_m37782519971FBCD9FBD71AF51DF5E8441868B9B2 (void);
// 0x000001C1 System.Object Neo4jAssemblyController/<QueryAssemblyRelationships>d__77::System.Collections.IEnumerator.get_Current()
extern void U3CQueryAssemblyRelationshipsU3Ed__77_System_Collections_IEnumerator_get_Current_m6A7ABA42EC552ED61B2FA04C827210B6B6B1887D (void);
// 0x000001C2 System.Void Neo4jAssemblyController/<ExecuteNextAssemblyStepCoroutine>d__81::.ctor(System.Int32)
extern void U3CExecuteNextAssemblyStepCoroutineU3Ed__81__ctor_m5316EDF961735308B4F4DE6657C8705AE634486C (void);
// 0x000001C3 System.Void Neo4jAssemblyController/<ExecuteNextAssemblyStepCoroutine>d__81::System.IDisposable.Dispose()
extern void U3CExecuteNextAssemblyStepCoroutineU3Ed__81_System_IDisposable_Dispose_mFA8525A8DD6ABC5567010D9ABD1A101F54B7A5FD (void);
// 0x000001C4 System.Boolean Neo4jAssemblyController/<ExecuteNextAssemblyStepCoroutine>d__81::MoveNext()
extern void U3CExecuteNextAssemblyStepCoroutineU3Ed__81_MoveNext_m75E73CA60ABC40F906CA9BD24676B7375D36AE37 (void);
// 0x000001C5 System.Void Neo4jAssemblyController/<ExecuteNextAssemblyStepCoroutine>d__81::<>m__Finally1()
extern void U3CExecuteNextAssemblyStepCoroutineU3Ed__81_U3CU3Em__Finally1_m0AC89C812FC7EBC2D1CE5369314AE31F4AEFB13A (void);
// 0x000001C6 System.Object Neo4jAssemblyController/<ExecuteNextAssemblyStepCoroutine>d__81::System.Collections.Generic.IEnumerator<System.Object>.get_Current()
extern void U3CExecuteNextAssemblyStepCoroutineU3Ed__81_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m795F6DC87ECFB5D7634AB43CA5B8A81967CED80B (void);
// 0x000001C7 System.Void Neo4jAssemblyController/<ExecuteNextAssemblyStepCoroutine>d__81::System.Collections.IEnumerator.Reset()
extern void U3CExecuteNextAssemblyStepCoroutineU3Ed__81_System_Collections_IEnumerator_Reset_m90271590611B26E1C589AA1B03B223B7125CFD80 (void);
// 0x000001C8 System.Object Neo4jAssemblyController/<ExecuteNextAssemblyStepCoroutine>d__81::System.Collections.IEnumerator.get_Current()
extern void U3CExecuteNextAssemblyStepCoroutineU3Ed__81_System_Collections_IEnumerator_get_Current_m5AA9A1AA8D9E4AE14CECA949F637D68E2690EE69 (void);
// 0x000001C9 System.Void Neo4jAssemblyController/<ReplayAssemblyStepCoroutine>d__82::.ctor(System.Int32)
extern void U3CReplayAssemblyStepCoroutineU3Ed__82__ctor_m683C4A2977ACE95F8A4B3212D8B74DCDFDF9985F (void);
// 0x000001CA System.Void Neo4jAssemblyController/<ReplayAssemblyStepCoroutine>d__82::System.IDisposable.Dispose()
extern void U3CReplayAssemblyStepCoroutineU3Ed__82_System_IDisposable_Dispose_m5C94E4437FC7601FD1A3EB687903BC868F3D84FB (void);
// 0x000001CB System.Boolean Neo4jAssemblyController/<ReplayAssemblyStepCoroutine>d__82::MoveNext()
extern void U3CReplayAssemblyStepCoroutineU3Ed__82_MoveNext_mEEF55F7F8BE7B275D426DB98664325A9C0750FFA (void);
// 0x000001CC System.Void Neo4jAssemblyController/<ReplayAssemblyStepCoroutine>d__82::<>m__Finally1()
extern void U3CReplayAssemblyStepCoroutineU3Ed__82_U3CU3Em__Finally1_m4D496C152DE3BBA6D1A114155AB7325053288EE7 (void);
// 0x000001CD System.Object Neo4jAssemblyController/<ReplayAssemblyStepCoroutine>d__82::System.Collections.Generic.IEnumerator<System.Object>.get_Current()
extern void U3CReplayAssemblyStepCoroutineU3Ed__82_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m0D0A92E990BE7D4B853008F555BF55C1C0DA9914 (void);
// 0x000001CE System.Void Neo4jAssemblyController/<ReplayAssemblyStepCoroutine>d__82::System.Collections.IEnumerator.Reset()
extern void U3CReplayAssemblyStepCoroutineU3Ed__82_System_Collections_IEnumerator_Reset_mE6F070F38D905A98889F0349BD79FADA66F63596 (void);
// 0x000001CF System.Object Neo4jAssemblyController/<ReplayAssemblyStepCoroutine>d__82::System.Collections.IEnumerator.get_Current()
extern void U3CReplayAssemblyStepCoroutineU3Ed__82_System_Collections_IEnumerator_get_Current_mCEBE7A15C471402777371677167DE1FC168AD000 (void);
// 0x000001D0 System.Void Neo4jAssemblyController/<InstallFastener>d__84::.ctor(System.Int32)
extern void U3CInstallFastenerU3Ed__84__ctor_m4FD9BF41A9BFE32F2D0B379420BF7D3B47AC6532 (void);
// 0x000001D1 System.Void Neo4jAssemblyController/<InstallFastener>d__84::System.IDisposable.Dispose()
extern void U3CInstallFastenerU3Ed__84_System_IDisposable_Dispose_m415B97167EC0304AE3FAEA6F4A8F5EC96B942B9E (void);
// 0x000001D2 System.Boolean Neo4jAssemblyController/<InstallFastener>d__84::MoveNext()
extern void U3CInstallFastenerU3Ed__84_MoveNext_m83532E4BE67AD72FF1A99FF40711360D039B7E40 (void);
// 0x000001D3 System.Object Neo4jAssemblyController/<InstallFastener>d__84::System.Collections.Generic.IEnumerator<System.Object>.get_Current()
extern void U3CInstallFastenerU3Ed__84_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m86C08B0FEEBD5B379113B97BD31D19E8D1184862 (void);
// 0x000001D4 System.Void Neo4jAssemblyController/<InstallFastener>d__84::System.Collections.IEnumerator.Reset()
extern void U3CInstallFastenerU3Ed__84_System_Collections_IEnumerator_Reset_m6B5303B72F2DA1B747CE3A057C9BE9D5E73A69EF (void);
// 0x000001D5 System.Object Neo4jAssemblyController/<InstallFastener>d__84::System.Collections.IEnumerator.get_Current()
extern void U3CInstallFastenerU3Ed__84_System_Collections_IEnumerator_get_Current_mB6CB41312E67EFED9789A3AABB5CD51B452A4B45 (void);
// 0x000001D6 System.Void Neo4jAssemblyUI::Start()
extern void Neo4jAssemblyUI_Start_mCDBF12C4FCBB62A6578482CB3130B39E429DB8B1 (void);
// 0x000001D7 System.Void Neo4jAssemblyUI::InitializeUI()
extern void Neo4jAssemblyUI_InitializeUI_m05FCB4604C7F9AFC19E8C254447704E0A83F8158 (void);
// 0x000001D8 System.Void Neo4jAssemblyUI::UpdateStatusText(System.String)
extern void Neo4jAssemblyUI_UpdateStatusText_m073B99500339696C19FF9D540DFD383B45EFD509 (void);
// 0x000001D9 System.Void Neo4jAssemblyUI::UpdateSelectedPartText(System.String)
extern void Neo4jAssemblyUI_UpdateSelectedPartText_m42F4B327A9CDE05D64706788A964637867EE1C9F (void);
// 0x000001DA System.Void Neo4jAssemblyUI::UpdateStepsCountText(System.Int32,System.Int32)
extern void Neo4jAssemblyUI_UpdateStepsCountText_m767BFA50F60CDD8717A5CB624DA986A4C484D3F5 (void);
// 0x000001DB System.Void Neo4jAssemblyUI::OnNextStepButtonClicked()
extern void Neo4jAssemblyUI_OnNextStepButtonClicked_m89A46D40A824A84DB2D5BCED0694AF59297F74C9 (void);
// 0x000001DC System.Void Neo4jAssemblyUI::OnResetButtonClicked()
extern void Neo4jAssemblyUI_OnResetButtonClicked_mC352298942248A3EAB6C13E668BDD2A640DEF32E (void);
// 0x000001DD System.Void Neo4jAssemblyUI::OnAutoPlayToggleChanged(System.Boolean)
extern void Neo4jAssemblyUI_OnAutoPlayToggleChanged_mD6E10ECFDD2336CE79F14D364972F22ECEE14BD5 (void);
// 0x000001DE System.Void Neo4jAssemblyUI::StartAutoPlay()
extern void Neo4jAssemblyUI_StartAutoPlay_m14DC99B95C55D759A7C8C9F09391D105DB4E4161 (void);
// 0x000001DF System.Void Neo4jAssemblyUI::StopAutoPlay()
extern void Neo4jAssemblyUI_StopAutoPlay_mEA3969A97E2AE15517BDDCF3CABA44AAD0B221B8 (void);
// 0x000001E0 System.Collections.IEnumerator Neo4jAssemblyUI::AutoPlayCoroutine()
extern void Neo4jAssemblyUI_AutoPlayCoroutine_mEC3062D30DE1321E94E76FB8A224816EB264C6B1 (void);
// 0x000001E1 System.Void Neo4jAssemblyUI::EnableNextStepButton(System.Boolean)
extern void Neo4jAssemblyUI_EnableNextStepButton_m5F22F1526B195F1CACBF8E394D3D4E706C77E18F (void);
// 0x000001E2 System.Void Neo4jAssemblyUI::EnableReplayButton(System.Boolean)
extern void Neo4jAssemblyUI_EnableReplayButton_mE8BAB509106ECCB7533C84A7402E6BF5A4E25846 (void);
// 0x000001E3 System.Void Neo4jAssemblyUI::OnReplayButtonClicked()
extern void Neo4jAssemblyUI_OnReplayButtonClicked_m59F31CA63E639C3FDD53F42CCAA3E3CA030769A8 (void);
// 0x000001E4 System.Void Neo4jAssemblyUI::OnSpeedSliderChanged(System.Single)
extern void Neo4jAssemblyUI_OnSpeedSliderChanged_m92803AE875B80617418DF329FB59CC4816F9AF59 (void);
// 0x000001E5 System.Void Neo4jAssemblyUI::UpdateSpeedValueText(System.Single)
extern void Neo4jAssemblyUI_UpdateSpeedValueText_m204D5414571DCA126E0FC211D05C7309FF849FA8 (void);
// 0x000001E6 System.Void Neo4jAssemblyUI::OnPartSelected(System.String)
extern void Neo4jAssemblyUI_OnPartSelected_m24FF709F553104F87BF3472EEBD5E7B4BC484A21 (void);
// 0x000001E7 System.Void Neo4jAssemblyUI::OnAssemblyStepsLoaded(System.Int32)
extern void Neo4jAssemblyUI_OnAssemblyStepsLoaded_mF3A4DDE5C3A0C4605A57BBFF65655881EA4EA879 (void);
// 0x000001E8 System.Void Neo4jAssemblyUI::OnStepExecuted(System.Int32,System.Int32,System.String)
extern void Neo4jAssemblyUI_OnStepExecuted_m5B708537395770CEA2BA9E44A16EA6F625EB5CF3 (void);
// 0x000001E9 System.Void Neo4jAssemblyUI::OnAssemblyCompleted()
extern void Neo4jAssemblyUI_OnAssemblyCompleted_m5C7B8529034EF917149CDA74A7F93D781A7FBA88 (void);
// 0x000001EA System.Void Neo4jAssemblyUI::DetectAndConfigureVRMode()
extern void Neo4jAssemblyUI_DetectAndConfigureVRMode_m78FFD5A1A55259CA0F1C02927E236847AE6E88B2 (void);
// 0x000001EB System.Boolean Neo4jAssemblyUI::DetectVREnvironment()
extern void Neo4jAssemblyUI_DetectVREnvironment_m732348695CF92B7A2BAFA44E81BD4ACEDD166F0B (void);
// 0x000001EC System.Void Neo4jAssemblyUI::ConfigureVRUI()
extern void Neo4jAssemblyUI_ConfigureVRUI_m8948C893F1758447C1261DD4420B805B19BC46A5 (void);
// 0x000001ED System.Void Neo4jAssemblyUI::ConfigureCanvasForVR(UnityEngine.Canvas)
extern void Neo4jAssemblyUI_ConfigureCanvasForVR_m91AD432342AD7207153A2B3E7B07FB22E4522D90 (void);
// 0x000001EE System.Void Neo4jAssemblyUI::PositionVRUI(UnityEngine.Transform)
extern void Neo4jAssemblyUI_PositionVRUI_m4232E667A9CBD0671EC8737C21E65113B53CB0EC (void);
// 0x000001EF System.Void Neo4jAssemblyUI::UpdateVRUIPosition()
extern void Neo4jAssemblyUI_UpdateVRUIPosition_mB06EB09924C3F77E2749787054D5A154CDED7861 (void);
// 0x000001F0 System.Void Neo4jAssemblyUI::.ctor()
extern void Neo4jAssemblyUI__ctor_m6F3E7FB649ADDE8690BC38471688F5EBBA39A66C (void);
// 0x000001F1 System.Void Neo4jAssemblyUI/<AutoPlayCoroutine>d__33::.ctor(System.Int32)
extern void U3CAutoPlayCoroutineU3Ed__33__ctor_m894E972DB222B6440DC956DBFE118C917518E8B6 (void);
// 0x000001F2 System.Void Neo4jAssemblyUI/<AutoPlayCoroutine>d__33::System.IDisposable.Dispose()
extern void U3CAutoPlayCoroutineU3Ed__33_System_IDisposable_Dispose_m5EC2DB12AE6973E7279E6B0C2AB768C7916B928B (void);
// 0x000001F3 System.Boolean Neo4jAssemblyUI/<AutoPlayCoroutine>d__33::MoveNext()
extern void U3CAutoPlayCoroutineU3Ed__33_MoveNext_m9B1141BF801C1A5FDF5495E4D6A4309474B893C2 (void);
// 0x000001F4 System.Object Neo4jAssemblyUI/<AutoPlayCoroutine>d__33::System.Collections.Generic.IEnumerator<System.Object>.get_Current()
extern void U3CAutoPlayCoroutineU3Ed__33_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mA69BADFC70ADCE26A2110E9CBC66F1442312306D (void);
// 0x000001F5 System.Void Neo4jAssemblyUI/<AutoPlayCoroutine>d__33::System.Collections.IEnumerator.Reset()
extern void U3CAutoPlayCoroutineU3Ed__33_System_Collections_IEnumerator_Reset_m4C3050A99DA581ECE711B4BFF1F6F883B5C171AB (void);
// 0x000001F6 System.Object Neo4jAssemblyUI/<AutoPlayCoroutine>d__33::System.Collections.IEnumerator.get_Current()
extern void U3CAutoPlayCoroutineU3Ed__33_System_Collections_IEnumerator_get_Current_mFB2351BF4ED4DF4F0BAB8B18ED888603AFA5E4BA (void);
// 0x000001F7 System.Collections.IEnumerator Neo4jConnector::ExecuteQuery(System.String,System.Action`1<System.String>)
extern void Neo4jConnector_ExecuteQuery_mEAAA9076CCEACB71D39D0CB2B2C435F2482E902A (void);
// 0x000001F8 System.String Neo4jConnector::get_DatabaseUrl()
extern void Neo4jConnector_get_DatabaseUrl_m818FFB7AB8FBCCBF2B20198871C4BAF4F3A87406 (void);
// 0x000001F9 System.String Neo4jConnector::get_DatabaseUsername()
extern void Neo4jConnector_get_DatabaseUsername_m3A33077D70412DDA80A989E4ED3A80EA0988AADC (void);
// 0x000001FA System.String Neo4jConnector::get_DatabasePassword()
extern void Neo4jConnector_get_DatabasePassword_m5FDE24B2B8E1ACF7487AFCEC1B3C77EE026C0AE9 (void);
// 0x000001FB System.Collections.IEnumerator Neo4jConnector::TestConnection(System.Action`1<System.Boolean>)
extern void Neo4jConnector_TestConnection_m35B27310317D2B22ADA87DE18797F6680A670975 (void);
// 0x000001FC System.Collections.IEnumerator Neo4jConnector::QueryConnections(System.String,System.Action`1<System.Collections.Generic.List`1<System.ValueTuple`4<System.String,System.String,System.String,System.String>>>)
extern void Neo4jConnector_QueryConnections_m20F0AB449482A817B28427DA4182C5AF63780ADA (void);
// 0x000001FD System.Void Neo4jConnector::.ctor()
extern void Neo4jConnector__ctor_m3A334A82D94E2CCDF167E43D7A2F762E6BCC9934 (void);
// 0x000001FE System.Void Neo4jConnector/<ExecuteQuery>d__3::.ctor(System.Int32)
extern void U3CExecuteQueryU3Ed__3__ctor_mF382C62277E30FCD9C5529C0F149ACDBD2ED51B3 (void);
// 0x000001FF System.Void Neo4jConnector/<ExecuteQuery>d__3::System.IDisposable.Dispose()
extern void U3CExecuteQueryU3Ed__3_System_IDisposable_Dispose_m987DD58EE3222A4AF70C7FE9CC9D6EA214487C59 (void);
// 0x00000200 System.Boolean Neo4jConnector/<ExecuteQuery>d__3::MoveNext()
extern void U3CExecuteQueryU3Ed__3_MoveNext_m6C2962BA6BFD5B1A97CA394F19EF50DC6C6F71AE (void);
// 0x00000201 System.Void Neo4jConnector/<ExecuteQuery>d__3::<>m__Finally1()
extern void U3CExecuteQueryU3Ed__3_U3CU3Em__Finally1_mCDA4EE30E34C7960A59D93912BDF64C1EABE8927 (void);
// 0x00000202 System.Object Neo4jConnector/<ExecuteQuery>d__3::System.Collections.Generic.IEnumerator<System.Object>.get_Current()
extern void U3CExecuteQueryU3Ed__3_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mA0A3756E3BDFC5451D48651394FD8F5B729CD492 (void);
// 0x00000203 System.Void Neo4jConnector/<ExecuteQuery>d__3::System.Collections.IEnumerator.Reset()
extern void U3CExecuteQueryU3Ed__3_System_Collections_IEnumerator_Reset_m20E488C05406BAFE351959C407556F69D5EE09D5 (void);
// 0x00000204 System.Object Neo4jConnector/<ExecuteQuery>d__3::System.Collections.IEnumerator.get_Current()
extern void U3CExecuteQueryU3Ed__3_System_Collections_IEnumerator_get_Current_m40360A3B3B6ADB0982FB0CCB874DB747A142F5D3 (void);
// 0x00000205 System.Void Neo4jConnector/<>c__DisplayClass10_0::.ctor()
extern void U3CU3Ec__DisplayClass10_0__ctor_m2D6F0E70743D756A75C9B0210FDF2B01DCEA797C (void);
// 0x00000206 System.Void Neo4jConnector/<>c__DisplayClass10_0::<TestConnection>b__0(System.String)
extern void U3CU3Ec__DisplayClass10_0_U3CTestConnectionU3Eb__0_mD98388D4B0828C0E8454B39CE13470D8E20D9B3D (void);
// 0x00000207 System.Void Neo4jConnector/<TestConnection>d__10::.ctor(System.Int32)
extern void U3CTestConnectionU3Ed__10__ctor_m04FE15BC317A8298369BEAD645E40BFD404767D4 (void);
// 0x00000208 System.Void Neo4jConnector/<TestConnection>d__10::System.IDisposable.Dispose()
extern void U3CTestConnectionU3Ed__10_System_IDisposable_Dispose_mCA99EF51488D04A2E93023FCC08B2DA489A05EE7 (void);
// 0x00000209 System.Boolean Neo4jConnector/<TestConnection>d__10::MoveNext()
extern void U3CTestConnectionU3Ed__10_MoveNext_m96A27E3B5C8CB1E4819A172195EB3FA0F8607C4A (void);
// 0x0000020A System.Object Neo4jConnector/<TestConnection>d__10::System.Collections.Generic.IEnumerator<System.Object>.get_Current()
extern void U3CTestConnectionU3Ed__10_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mB6722C108C3DF5120114EF33DABBB98028A477A0 (void);
// 0x0000020B System.Void Neo4jConnector/<TestConnection>d__10::System.Collections.IEnumerator.Reset()
extern void U3CTestConnectionU3Ed__10_System_Collections_IEnumerator_Reset_m47CA6D10F214B3C95F0C47E3385A560B639FE44C (void);
// 0x0000020C System.Object Neo4jConnector/<TestConnection>d__10::System.Collections.IEnumerator.get_Current()
extern void U3CTestConnectionU3Ed__10_System_Collections_IEnumerator_get_Current_mA687283225AABB53C7277F963BE5FF3EC2F763E4 (void);
// 0x0000020D System.Void Neo4jConnector/<>c__DisplayClass11_0::.ctor()
extern void U3CU3Ec__DisplayClass11_0__ctor_m71B8992D0FB0926ACE415E53A4684A2DF6714732 (void);
// 0x0000020E System.Void Neo4jConnector/<>c__DisplayClass11_0::<QueryConnections>b__0(System.String)
extern void U3CU3Ec__DisplayClass11_0_U3CQueryConnectionsU3Eb__0_mB885CD288657C90B46973ED4A83370E2EB43F1E1 (void);
// 0x0000020F System.Void Neo4jConnector/<QueryConnections>d__11::.ctor(System.Int32)
extern void U3CQueryConnectionsU3Ed__11__ctor_m4901A0BADFEAAD19E68A799040D9219B08A784B8 (void);
// 0x00000210 System.Void Neo4jConnector/<QueryConnections>d__11::System.IDisposable.Dispose()
extern void U3CQueryConnectionsU3Ed__11_System_IDisposable_Dispose_m9B6A9EAFA1DF81B82CA4736BF8C5CDDAEDEE53C4 (void);
// 0x00000211 System.Boolean Neo4jConnector/<QueryConnections>d__11::MoveNext()
extern void U3CQueryConnectionsU3Ed__11_MoveNext_m9C9706538C675FFC6BBAFF18A15E02B14F0F87B0 (void);
// 0x00000212 System.Object Neo4jConnector/<QueryConnections>d__11::System.Collections.Generic.IEnumerator<System.Object>.get_Current()
extern void U3CQueryConnectionsU3Ed__11_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m2AE7E242201CF1333C7E094F5478EF48E1FF84FE (void);
// 0x00000213 System.Void Neo4jConnector/<QueryConnections>d__11::System.Collections.IEnumerator.Reset()
extern void U3CQueryConnectionsU3Ed__11_System_Collections_IEnumerator_Reset_mEC7096B0DC3ECFCBD13C0E0172E69C12B2AA13B1 (void);
// 0x00000214 System.Object Neo4jConnector/<QueryConnections>d__11::System.Collections.IEnumerator.get_Current()
extern void U3CQueryConnectionsU3Ed__11_System_Collections_IEnumerator_get_Current_mF188E3532A258CACD997C4E0CEDA25586E84D3CA (void);
// 0x00000215 System.Void PICOActionBasedInputHandler::Start()
extern void PICOActionBasedInputHandler_Start_m5283276783CEB7F35A612223A226FF975708ABC4 (void);
// 0x00000216 System.Void PICOActionBasedInputHandler::Update()
extern void PICOActionBasedInputHandler_Update_mFCE96BE462AF65AFDD4CB9726AD0B065619BEEA3 (void);
// 0x00000217 System.Void PICOActionBasedInputHandler::InitializeComponents()
extern void PICOActionBasedInputHandler_InitializeComponents_mE8F9A747698CF519F19F16E6ACB4E80D9975C075 (void);
// 0x00000218 System.Void PICOActionBasedInputHandler::LogInitializationStatus()
extern void PICOActionBasedInputHandler_LogInitializationStatus_mBC9F0A470D8A398A0D7442D9E27FF63C8F8E169E (void);
// 0x00000219 System.Void PICOActionBasedInputHandler::LogCurrentStatus()
extern void PICOActionBasedInputHandler_LogCurrentStatus_mEF634A96468D6034ACA5F57F8D18896788F7596A (void);
// 0x0000021A System.Void PICOActionBasedInputHandler::LogActionStatus(System.String,UnityEngine.InputSystem.InputActionProperty)
extern void PICOActionBasedInputHandler_LogActionStatus_mFB08C4828F36409A7DBDF030AA5C336476293784 (void);
// 0x0000021B System.Void PICOActionBasedInputHandler::HandleInput()
extern void PICOActionBasedInputHandler_HandleInput_m24C7B04C2DA52B90C5A12D794FFEDD19CAEC966B (void);
// 0x0000021C System.Void PICOActionBasedInputHandler::HandleControllerInput(UnityEngine.XR.Interaction.Toolkit.ActionBasedController,System.String)
extern void PICOActionBasedInputHandler_HandleControllerInput_mA220D0C9EA4D19845DD7BA8AF7DDCE3859D80DEE (void);
// 0x0000021D System.Boolean PICOActionBasedInputHandler::IsActionPressed(UnityEngine.InputSystem.InputActionProperty)
extern void PICOActionBasedInputHandler_IsActionPressed_m3155B73DCCE30E9E19FE85CC5B9EDDE898C9E0FD (void);
// 0x0000021E System.Void PICOActionBasedInputHandler::OnActivatePressed(System.String)
extern void PICOActionBasedInputHandler_OnActivatePressed_m7AE24EB01CCA071648CEF003F9E560851898C3B8 (void);
// 0x0000021F System.Void PICOActionBasedInputHandler::OnSelectPressed(System.String)
extern void PICOActionBasedInputHandler_OnSelectPressed_m3F32F937F7083102736C5C01AEF8ADC5B9DF1CBC (void);
// 0x00000220 System.Void PICOActionBasedInputHandler::OnResetPressed(System.String)
extern void PICOActionBasedInputHandler_OnResetPressed_mACA5F4619F25C6DD52D4D8AD59E58B82BB11122F (void);
// 0x00000221 System.Void PICOActionBasedInputHandler::OnDebugTogglePressed(System.String)
extern void PICOActionBasedInputHandler_OnDebugTogglePressed_mACB69A49A4B556FF347ED7444F59CD4FD361E4BB (void);
// 0x00000222 System.Void PICOActionBasedInputHandler::TestAllFunctions()
extern void PICOActionBasedInputHandler_TestAllFunctions_mE7DC4B29F48BE120AF163829198CEA367CC444AF (void);
// 0x00000223 System.Collections.IEnumerator PICOActionBasedInputHandler::TestSequence()
extern void PICOActionBasedInputHandler_TestSequence_m2439BAA1E2CF92615F5E7824F9FF9ED691CEDB16 (void);
// 0x00000224 System.Void PICOActionBasedInputHandler::ShowUsageInstructions()
extern void PICOActionBasedInputHandler_ShowUsageInstructions_m428E6AB3B279034D9D47FE2E001D0BA4ECB738BB (void);
// 0x00000225 System.Void PICOActionBasedInputHandler::.ctor()
extern void PICOActionBasedInputHandler__ctor_mD0592C42B59D6D4A72DB777A70A4E0AA6D375ABB (void);
// 0x00000226 System.Void PICOActionBasedInputHandler/<TestSequence>d__24::.ctor(System.Int32)
extern void U3CTestSequenceU3Ed__24__ctor_m63E784D465340FA1C25FD3DD2C216FB9B056DF78 (void);
// 0x00000227 System.Void PICOActionBasedInputHandler/<TestSequence>d__24::System.IDisposable.Dispose()
extern void U3CTestSequenceU3Ed__24_System_IDisposable_Dispose_m43FD54F0F3A9654496327DB209EC2BDB705495F6 (void);
// 0x00000228 System.Boolean PICOActionBasedInputHandler/<TestSequence>d__24::MoveNext()
extern void U3CTestSequenceU3Ed__24_MoveNext_mD08BA0C261532540EEF62EEDEF7795FF0D742FA5 (void);
// 0x00000229 System.Object PICOActionBasedInputHandler/<TestSequence>d__24::System.Collections.Generic.IEnumerator<System.Object>.get_Current()
extern void U3CTestSequenceU3Ed__24_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m23690C82448D5379FF16AE261106A9DCF15978CD (void);
// 0x0000022A System.Void PICOActionBasedInputHandler/<TestSequence>d__24::System.Collections.IEnumerator.Reset()
extern void U3CTestSequenceU3Ed__24_System_Collections_IEnumerator_Reset_m1BB26E9A1E2EBA6C7185E542B721215770A5F551 (void);
// 0x0000022B System.Object PICOActionBasedInputHandler/<TestSequence>d__24::System.Collections.IEnumerator.get_Current()
extern void U3CTestSequenceU3Ed__24_System_Collections_IEnumerator_get_Current_m4297DB5CA68EAC52EA07F7F203AA2FF51288D9C1 (void);
// 0x0000022C System.Void PICOControllerSetup::Start()
extern void PICOControllerSetup_Start_m5F241870CB9B80BC017272322883503AC49BE051 (void);
// 0x0000022D System.Void PICOControllerSetup::SetupControllers()
extern void PICOControllerSetup_SetupControllers_m72805A02CEBE60DEAA4D0E07B4C7095A84032425 (void);
// 0x0000022E System.Void PICOControllerSetup::SetupSingleController(UnityEngine.GameObject,UnityEngine.XR.XRNode,System.String)
extern void PICOControllerSetup_SetupSingleController_m74AE3179F36E9CC86FDA2E2C89B8AF6E27AEBF06 (void);
// 0x0000022F System.Void PICOControllerSetup::SetupLineRenderer(UnityEngine.LineRenderer)
extern void PICOControllerSetup_SetupLineRenderer_m33DC79CAF616908E90EC738D905B1D2BCE1E56F1 (void);
// 0x00000230 System.Void PICOControllerSetup::CheckControllerStatus()
extern void PICOControllerSetup_CheckControllerStatus_mC841641D357AE21EF8F113B28F95955B2FB04ED7 (void);
// 0x00000231 System.Void PICOControllerSetup::ListControllerObjects()
extern void PICOControllerSetup_ListControllerObjects_m4CBE0C35E608A1D638C73488570176E02DBF359F (void);
// 0x00000232 System.Void PICOControllerSetup::ForceReconfigure()
extern void PICOControllerSetup_ForceReconfigure_m132EB69610160E6C96E214AD15D5DE0FDD4EBC58 (void);
// 0x00000233 System.Void PICOControllerSetup::.ctor()
extern void PICOControllerSetup__ctor_m374CC7BC77C813A3447D2169DEA6673EE82A51B4 (void);
// 0x00000234 System.Void PICODeploymentManager::Awake()
extern void PICODeploymentManager_Awake_m398A9B609C4E8098A44BA316F9B6F9F4892CF687 (void);
// 0x00000235 System.Void PICODeploymentManager::Start()
extern void PICODeploymentManager_Start_m513829B8D2BF62E541D3FE0AF705EFCB2F5E488D (void);
// 0x00000236 System.Void PICODeploymentManager::ConfigureEnvironment()
extern void PICODeploymentManager_ConfigureEnvironment_mC5E0B52F460FB39BAD2CC61A7755B3D764B2BBF5 (void);
// 0x00000237 System.Void PICODeploymentManager::DetectPlatform()
extern void PICODeploymentManager_DetectPlatform_m4BCBE611C15930811EC55D0CA3898BB30BA2AD25 (void);
// 0x00000238 System.Void PICODeploymentManager::FindComponents()
extern void PICODeploymentManager_FindComponents_m2AC3FB0DA8784A5D6C521CA5D0B039EB3432E492 (void);
// 0x00000239 System.Void PICODeploymentManager::ConfigureForPICO()
extern void PICODeploymentManager_ConfigureForPICO_m756046194F9532F301AE2B2855F2C7F73B88F6E8 (void);
// 0x0000023A System.Void PICODeploymentManager::ConfigureForDevelopment()
extern void PICODeploymentManager_ConfigureForDevelopment_mCC48CBBFF4897FCFF273D76E47EB9086CE94E527 (void);
// 0x0000023B System.Void PICODeploymentManager::OptimizeForPICO()
extern void PICODeploymentManager_OptimizeForPICO_m590101ECBEA7FA00F93B945F73D89115837764F1 (void);
// 0x0000023C System.Void PICODeploymentManager::SetPrivateField(System.Object,System.String,System.Object)
extern void PICODeploymentManager_SetPrivateField_m782293148798ED51D338230F031D468CA3A51006 (void);
// 0x0000023D System.Void PICODeploymentManager::ValidateConfiguration()
extern void PICODeploymentManager_ValidateConfiguration_mA06D72971650E2CF927865E2D89F9DB4CD379578 (void);
// 0x0000023E System.Void PICODeploymentManager::LogCurrentConfiguration()
extern void PICODeploymentManager_LogCurrentConfiguration_m4654CFA8462DDCE883E072EA523387DD7490DB03 (void);
// 0x0000023F System.Void PICODeploymentManager::SwitchToPICOMode()
extern void PICODeploymentManager_SwitchToPICOMode_mB6A30668470129DBAB3854E37345B0191D21710E (void);
// 0x00000240 System.Void PICODeploymentManager::SwitchToDevelopmentMode()
extern void PICODeploymentManager_SwitchToDevelopmentMode_mE31EA7C035E616FB8C204469B7448EA4AB2B2C6A (void);
// 0x00000241 System.Boolean PICODeploymentManager::get_IsPICODeployment()
extern void PICODeploymentManager_get_IsPICODeployment_m499D3B221CB2EBFE1FD38A93A875C6EDA71F9859 (void);
// 0x00000242 System.String PICODeploymentManager::GetConfigurationInfo()
extern void PICODeploymentManager_GetConfigurationInfo_m991D723DCB734ECFB9CD217B0DA8F099D88B31E7 (void);
// 0x00000243 System.Void PICODeploymentManager::.ctor()
extern void PICODeploymentManager__ctor_m96ABD81A1A0A99CA26934178D14E45362E75D80F (void);
// 0x00000244 System.Void PICODirectInputAdapter::Start()
extern void PICODirectInputAdapter_Start_m2A5C87BB9F216B129FA843A4734167C8B0724B1A (void);
// 0x00000245 System.Void PICODirectInputAdapter::Update()
extern void PICODirectInputAdapter_Update_m53D03CB5A4777E9CE93230AE27AF649FFDDF2469 (void);
// 0x00000246 System.Void PICODirectInputAdapter::InitializeControllers()
extern void PICODirectInputAdapter_InitializeControllers_mDA453B95268607367503AC070B4C58996183E871 (void);
// 0x00000247 System.Void PICODirectInputAdapter::HandleVRInput()
extern void PICODirectInputAdapter_HandleVRInput_m75DF374F763D76534563DDE63C35617326D5FE06 (void);
// 0x00000248 System.Void PICODirectInputAdapter::CheckControllerInput(System.Object,System.String)
extern void PICODirectInputAdapter_CheckControllerInput_m79F7B68788B0B88991A44D3370E19F65DC208A88 (void);
// 0x00000249 System.Void PICODirectInputAdapter::HandleKeyboardInput()
extern void PICODirectInputAdapter_HandleKeyboardInput_mA07CEE79E4DB0D3F459A89BA1943A78FCE235C84 (void);
// 0x0000024A System.Boolean PICODirectInputAdapter::CanProcessInput()
extern void PICODirectInputAdapter_CanProcessInput_m6423337FAB7AA30B8AABD625FDAE7C99EB135138 (void);
// 0x0000024B System.Void PICODirectInputAdapter::OnTriggerPressed(System.String)
extern void PICODirectInputAdapter_OnTriggerPressed_m61E64A311BBBEB5E0AC7C10EA2A075E5B0C536EE (void);
// 0x0000024C System.Void PICODirectInputAdapter::OnPrimaryButtonPressed(System.String)
extern void PICODirectInputAdapter_OnPrimaryButtonPressed_m701C225A85724F67079D76B624A00F6FC864A4F5 (void);
// 0x0000024D System.Void PICODirectInputAdapter::OnSecondaryButtonPressed(System.String)
extern void PICODirectInputAdapter_OnSecondaryButtonPressed_mA08085BAE1FCDE4B30EA445F48427CFA18680E13 (void);
// 0x0000024E System.Void PICODirectInputAdapter::OnMenuButtonPressed(System.String)
extern void PICODirectInputAdapter_OnMenuButtonPressed_mB2F3F1874919E309CD97FC304D49BAD939BBD6E3 (void);
// 0x0000024F System.Void PICODirectInputAdapter::ExecuteFirstPartFunction()
extern void PICODirectInputAdapter_ExecuteFirstPartFunction_mBAB619E77578BCD65F5313823BBBA6B6F211225F (void);
// 0x00000250 System.Void PICODirectInputAdapter::ExecuteSecondPartFunction()
extern void PICODirectInputAdapter_ExecuteSecondPartFunction_mE2EE88871A087E61B6B2C184092C38A041901C50 (void);
// 0x00000251 System.Void PICODirectInputAdapter::ExecuteResetFunction()
extern void PICODirectInputAdapter_ExecuteResetFunction_mB73A2CF2ADD761D62F7A2C8DC96CC422154D1EF5 (void);
// 0x00000252 System.Void PICODirectInputAdapter::ExecuteToggleDebugFunction()
extern void PICODirectInputAdapter_ExecuteToggleDebugFunction_m3FC5101094C5BFDA0929092548C3106AC3CE7831 (void);
// 0x00000253 System.Void PICODirectInputAdapter::ShowInputMapping()
extern void PICODirectInputAdapter_ShowInputMapping_mAD8FBC4112115DC41086B5E0855477251034961D (void);
// 0x00000254 System.Void PICODirectInputAdapter::TestControllerStatus()
extern void PICODirectInputAdapter_TestControllerStatus_m97FC164A3583F27048BAA60F0BAACF519B304E83 (void);
// 0x00000255 System.Void PICODirectInputAdapter::.ctor()
extern void PICODirectInputAdapter__ctor_m3E8D206240305C09F89FC996ED7D851C830071D0 (void);
// 0x00000256 System.Void PICODirectInputHandler::Start()
extern void PICODirectInputHandler_Start_mFA57C80FF4DD17A223A4FAAB857964C93EB9C077 (void);
// 0x00000257 System.Void PICODirectInputHandler::Update()
extern void PICODirectInputHandler_Update_mB2C5A8308C90BE2312F054D7DEF955D61567DDDC (void);
// 0x00000258 System.Void PICODirectInputHandler::HandleInput()
extern void PICODirectInputHandler_HandleInput_m8ECCDF65B9B809B6638155D49D80EE5FE7960465 (void);
// 0x00000259 System.Void PICODirectInputHandler::HandleVRInput()
extern void PICODirectInputHandler_HandleVRInput_m6F7326C95D586B11B55273BCE7C7EEFEC63063A4 (void);
// 0x0000025A System.Void PICODirectInputHandler::TryUnityInputSystem()
extern void PICODirectInputHandler_TryUnityInputSystem_m9610CC50C9330F7604859757A9E59C9DAC06896E (void);
// 0x0000025B System.Void PICODirectInputHandler::TryPICOSDKInput()
extern void PICODirectInputHandler_TryPICOSDKInput_m1D5069857220BD2873882E5D51E0490E253AA280 (void);
// 0x0000025C System.Void PICODirectInputHandler::CheckPICOControllerInput()
extern void PICODirectInputHandler_CheckPICOControllerInput_m442CE8CD224B60F1C5D184A2F52EE8536E7CBCCC (void);
// 0x0000025D System.Void PICODirectInputHandler::CheckGenericVRInput()
extern void PICODirectInputHandler_CheckGenericVRInput_mA97072C7EBEF0620A7332BCE6C64DF7A5E1D7C2B (void);
// 0x0000025E System.Void PICODirectInputHandler::OnTriggerPressed(System.String)
extern void PICODirectInputHandler_OnTriggerPressed_m8DDD5FFAB2F3A3EF8B9CE6810540B6636E5343C6 (void);
// 0x0000025F System.Void PICODirectInputHandler::OnGripPressed(System.String)
extern void PICODirectInputHandler_OnGripPressed_m90F3D8ED65734A08E5260F0827D324FBC0DBB79D (void);
// 0x00000260 System.Void PICODirectInputHandler::OnResetPressed(System.String)
extern void PICODirectInputHandler_OnResetPressed_mB2FD40429877E2DD142F46436C4A2DCB85AC8F79 (void);
// 0x00000261 System.Void PICODirectInputHandler::TestAllInputMethods()
extern void PICODirectInputHandler_TestAllInputMethods_mB2FD465BA2EAF2367DB3EE361173FDEA7EA803D7 (void);
// 0x00000262 System.Collections.IEnumerator PICODirectInputHandler::DelayedTest()
extern void PICODirectInputHandler_DelayedTest_m9613806AB31CF36E449D1BF410D02DEF7830D047 (void);
// 0x00000263 System.Void PICODirectInputHandler::ShowInputStatus()
extern void PICODirectInputHandler_ShowInputStatus_m21D8C7097304A8AA2EEF0A772FE29CD2FE320394 (void);
// 0x00000264 System.Void PICODirectInputHandler::.ctor()
extern void PICODirectInputHandler__ctor_mEB43796546A76A87159B11D9647C0BA00E28BFEA (void);
// 0x00000265 System.Void PICODirectInputHandler/<DelayedTest>d__19::.ctor(System.Int32)
extern void U3CDelayedTestU3Ed__19__ctor_m22D837F7EC054246C6355B1D45C3C2C9B9028024 (void);
// 0x00000266 System.Void PICODirectInputHandler/<DelayedTest>d__19::System.IDisposable.Dispose()
extern void U3CDelayedTestU3Ed__19_System_IDisposable_Dispose_m61C957866B42E0D17C39AA5168B7587B9CCCFF9B (void);
// 0x00000267 System.Boolean PICODirectInputHandler/<DelayedTest>d__19::MoveNext()
extern void U3CDelayedTestU3Ed__19_MoveNext_mFBE1886C895FAC7E907CA4999156AEC87BE92394 (void);
// 0x00000268 System.Object PICODirectInputHandler/<DelayedTest>d__19::System.Collections.Generic.IEnumerator<System.Object>.get_Current()
extern void U3CDelayedTestU3Ed__19_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mA677564398654DD24ED6479643B1A44486DDC193 (void);
// 0x00000269 System.Void PICODirectInputHandler/<DelayedTest>d__19::System.Collections.IEnumerator.Reset()
extern void U3CDelayedTestU3Ed__19_System_Collections_IEnumerator_Reset_m65D495376BC721741112BF5908EB1B13577919F0 (void);
// 0x0000026A System.Object PICODirectInputHandler/<DelayedTest>d__19::System.Collections.IEnumerator.get_Current()
extern void U3CDelayedTestU3Ed__19_System_Collections_IEnumerator_get_Current_mC07BD713285BC3EDD0B897CE3405B2ED4C9B3CC8 (void);
// 0x0000026B System.Void PICOInputActionAdapter::Start()
extern void PICOInputActionAdapter_Start_mFEC27B900794FDBC51E077338A32857BB3C49D58 (void);
// 0x0000026C System.Void PICOInputActionAdapter::Update()
extern void PICOInputActionAdapter_Update_m4B21C7C5439C480AD4201092F4139B8A9D3452BA (void);
// 0x0000026D System.Void PICOInputActionAdapter::SetupInputActions()
extern void PICOInputActionAdapter_SetupInputActions_mE70DC8336068BB2FBC092500AFDD4395E3040B42 (void);
// 0x0000026E System.Void PICOInputActionAdapter::CreateDefaultInputActions()
extern void PICOInputActionAdapter_CreateDefaultInputActions_m7482E0CA4BF18FB85A87F47DCE5BC702C8F09841 (void);
// 0x0000026F System.Void PICOInputActionAdapter::EnableInputAction(UnityEngine.InputSystem.InputActionReference,System.Action`1<UnityEngine.InputSystem.InputAction/CallbackContext>,System.String)
extern void PICOInputActionAdapter_EnableInputAction_mFD90517072CBD0770C7495692B4AFDE394D58937 (void);
// 0x00000270 System.Void PICOInputActionAdapter::OnTriggerPressed(UnityEngine.InputSystem.InputAction/CallbackContext)
extern void PICOInputActionAdapter_OnTriggerPressed_m1849F3BC4F4CCB77E1103D06A1BFF02648066A24 (void);
// 0x00000271 System.Void PICOInputActionAdapter::OnPrimaryButtonPressed(UnityEngine.InputSystem.InputAction/CallbackContext)
extern void PICOInputActionAdapter_OnPrimaryButtonPressed_m0FCB5C5F95D7D6F39A5FC54B3B6EE9132D0F5890 (void);
// 0x00000272 System.Void PICOInputActionAdapter::OnSecondaryButtonPressed(UnityEngine.InputSystem.InputAction/CallbackContext)
extern void PICOInputActionAdapter_OnSecondaryButtonPressed_m8AB9EBE1E4186CE3B46CC42AD8E14F62D6DEAA6A (void);
// 0x00000273 System.Void PICOInputActionAdapter::OnMenuButtonPressed(UnityEngine.InputSystem.InputAction/CallbackContext)
extern void PICOInputActionAdapter_OnMenuButtonPressed_m319615EA8A0A25FAF97FBD6FCB600CCD01A68EE8 (void);
// 0x00000274 System.Void PICOInputActionAdapter::HandleKeyboardInput()
extern void PICOInputActionAdapter_HandleKeyboardInput_m5C1FEC4F016F9BE4C662334D83DBC35EFFBD1AF7 (void);
// 0x00000275 System.Boolean PICOInputActionAdapter::CanProcessInput()
extern void PICOInputActionAdapter_CanProcessInput_m8C3DDBB9ECE1BA6C4A3BB3994863BACD8EE912B8 (void);
// 0x00000276 System.Void PICOInputActionAdapter::ExecuteFirstPartFunction()
extern void PICOInputActionAdapter_ExecuteFirstPartFunction_mB4A3F1E28C8AA3A206BF4D65BA0C4D0FC4583508 (void);
// 0x00000277 System.Void PICOInputActionAdapter::ExecuteSecondPartFunction()
extern void PICOInputActionAdapter_ExecuteSecondPartFunction_m3B67E965A4F601858216A3688F20BAE3D7441BFF (void);
// 0x00000278 System.Void PICOInputActionAdapter::ExecuteResetFunction()
extern void PICOInputActionAdapter_ExecuteResetFunction_mB6CF2C0B10B463C9976D1AB0B475D978C7119856 (void);
// 0x00000279 System.Void PICOInputActionAdapter::ExecuteToggleDebugFunction()
extern void PICOInputActionAdapter_ExecuteToggleDebugFunction_m70421F91327638B3B6557C76B017EAC35F948E84 (void);
// 0x0000027A System.Void PICOInputActionAdapter::ShowInputMapping()
extern void PICOInputActionAdapter_ShowInputMapping_mDF347EBB94EE12A360E8768B182C96ED22A38089 (void);
// 0x0000027B System.Void PICOInputActionAdapter::TestAllFunctions()
extern void PICOInputActionAdapter_TestAllFunctions_m92CEF00FF6ED66D2E00A1D1F945532EBF3E573CF (void);
// 0x0000027C System.Collections.IEnumerator PICOInputActionAdapter::TestSequence()
extern void PICOInputActionAdapter_TestSequence_m473100F307494B3A880FB273588A413025A449D4 (void);
// 0x0000027D System.Void PICOInputActionAdapter::OnDestroy()
extern void PICOInputActionAdapter_OnDestroy_m8D918E4B75748E507C0D18DDC371F1ED155B9BCC (void);
// 0x0000027E System.Void PICOInputActionAdapter::DisableInputAction(UnityEngine.InputSystem.InputActionReference)
extern void PICOInputActionAdapter_DisableInputAction_m6AD74DB2931357C77AC8EB085E36C416F8C8604C (void);
// 0x0000027F System.Void PICOInputActionAdapter::.ctor()
extern void PICOInputActionAdapter__ctor_m4CCE4C65359A357DB4BE1593D372CEBC14562723 (void);
// 0x00000280 System.Void PICOInputActionAdapter/<TestSequence>d__31::.ctor(System.Int32)
extern void U3CTestSequenceU3Ed__31__ctor_m3BDC0345881C884010DB9F4AB4A77C716A0BF75A (void);
// 0x00000281 System.Void PICOInputActionAdapter/<TestSequence>d__31::System.IDisposable.Dispose()
extern void U3CTestSequenceU3Ed__31_System_IDisposable_Dispose_m6603F96B83077892E9959F9FE27C292F4C80C426 (void);
// 0x00000282 System.Boolean PICOInputActionAdapter/<TestSequence>d__31::MoveNext()
extern void U3CTestSequenceU3Ed__31_MoveNext_mB9F2DDCAD0CBA6E9B97F419623ABE5C2DA40F1B0 (void);
// 0x00000283 System.Object PICOInputActionAdapter/<TestSequence>d__31::System.Collections.Generic.IEnumerator<System.Object>.get_Current()
extern void U3CTestSequenceU3Ed__31_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mF9E5243783320DBDC7C1AD34EB7DEAD500CB9C9B (void);
// 0x00000284 System.Void PICOInputActionAdapter/<TestSequence>d__31::System.Collections.IEnumerator.Reset()
extern void U3CTestSequenceU3Ed__31_System_Collections_IEnumerator_Reset_m17C2E78A6796DA3243E2AD3A3CA92D29E5E7D6C4 (void);
// 0x00000285 System.Object PICOInputActionAdapter/<TestSequence>d__31::System.Collections.IEnumerator.get_Current()
extern void U3CTestSequenceU3Ed__31_System_Collections_IEnumerator_get_Current_m0FA97FB4F51EC3D5958A3F4751DB285E2BB7EE95 (void);
// 0x00000286 System.Void PICOInputDebugger::Start()
extern void PICOInputDebugger_Start_mED0B9109DDF286712F33A5816BBD3847D9248FF3 (void);
// 0x00000287 System.Void PICOInputDebugger::Update()
extern void PICOInputDebugger_Update_m71252699CD44C4ECD91C5985457DA373767DDD8D (void);
// 0x00000288 System.Void PICOInputDebugger::InitializeControllers()
extern void PICOInputDebugger_InitializeControllers_m9EE9976021D71E0487CE3E4FD5BDFDA6ABD218B6 (void);
// 0x00000289 System.Void PICOInputDebugger::CheckControllerInput()
extern void PICOInputDebugger_CheckControllerInput_mA88B8ED1A9DB62D1ADA2290D2A14B01997BE5A59 (void);
// 0x0000028A System.Void PICOInputDebugger::CheckSingleControllerInput(System.Object,System.String)
extern void PICOInputDebugger_CheckSingleControllerInput_mAD64D79BCBF450D23F16BB6AA41F32EA09724664 (void);
// 0x0000028B System.Boolean PICOInputDebugger::CanProcessInput()
extern void PICOInputDebugger_CanProcessInput_m14315930B9BD4842600F8FBA96183734E74DF9E6 (void);
// 0x0000028C System.Void PICOInputDebugger::OnTriggerPressed(System.String)
extern void PICOInputDebugger_OnTriggerPressed_m62BF0C3DEFE2D5CEB2165992BA62436541392E6E (void);
// 0x0000028D System.Void PICOInputDebugger::OnPrimaryButtonPressed(System.String)
extern void PICOInputDebugger_OnPrimaryButtonPressed_m6787EA7385B9DBBA01AB4DF2BCB3594C4F8F0870 (void);
// 0x0000028E System.Void PICOInputDebugger::OnSecondaryButtonPressed(System.String)
extern void PICOInputDebugger_OnSecondaryButtonPressed_m1FA1F3FDCD22AC5E5FCC0EE2273CF5C74D7E1C52 (void);
// 0x0000028F System.Void PICOInputDebugger::OnMenuButtonPressed(System.String)
extern void PICOInputDebugger_OnMenuButtonPressed_m22D79F630907E8AFAC94B5C83FBC3A38CE892DE3 (void);
// 0x00000290 System.Void PICOInputDebugger::ExecuteFirstPartFunction()
extern void PICOInputDebugger_ExecuteFirstPartFunction_m8B3F8F75228CA7DB2E46B768A306BD8F7E2AAE64 (void);
// 0x00000291 System.Void PICOInputDebugger::ExecuteSecondPartFunction()
extern void PICOInputDebugger_ExecuteSecondPartFunction_mC4ACE39CFF09C9640EE82CC9D7AD8E18531F24CF (void);
// 0x00000292 System.Void PICOInputDebugger::ExecuteResetFunction()
extern void PICOInputDebugger_ExecuteResetFunction_m846321D60499460E318444698A61CB8CF00B7053 (void);
// 0x00000293 System.Void PICOInputDebugger::CreateDebugUI()
extern void PICOInputDebugger_CreateDebugUI_m4F5B4264B4509FD49DD3517DD4286963D94514E1 (void);
// 0x00000294 System.Void PICOInputDebugger::CreateDebugText(System.String,UnityEngine.Transform,UnityEngine.Vector2,UnityEngine.UI.Text&)
extern void PICOInputDebugger_CreateDebugText_m6228F6ADE47D963C8705244CDA6F5D6DCCE7BF87 (void);
// 0x00000295 System.Void PICOInputDebugger::UpdateDebugUI()
extern void PICOInputDebugger_UpdateDebugUI_m9D6DB340C65E3B1AC435C2CB143391D40F46084C (void);
// 0x00000296 System.Void PICOInputDebugger::UpdateStatusInfo(System.String)
extern void PICOInputDebugger_UpdateStatusInfo_m8EE31D24EAECB4E97D1C61522EF9D3AD94760CCC (void);
// 0x00000297 System.Void PICOInputDebugger::UpdateFunctionInfo(System.String)
extern void PICOInputDebugger_UpdateFunctionInfo_m1102E26D73645C9A0EFF783CC44F7B9FE796B975 (void);
// 0x00000298 System.Void PICOInputDebugger::ToggleDebugUI()
extern void PICOInputDebugger_ToggleDebugUI_m7E0A289813700F9028679EE5F8E5C7F0783CC1E5 (void);
// 0x00000299 System.Void PICOInputDebugger::.ctor()
extern void PICOInputDebugger__ctor_m4DF1BD100C5257BF139FFB5E21B2CE39E7EAD3F4 (void);
// 0x0000029A System.Void PICOVRButtonFix::Start()
extern void PICOVRButtonFix_Start_m863E3D5ED8A30D027933CF2F043753AFCC04E55A (void);
// 0x0000029B System.Collections.IEnumerator PICOVRButtonFix::InitializeVRButtonFix()
extern void PICOVRButtonFix_InitializeVRButtonFix_mE84E183E7A2C421946BB79CA6C845FDB180C0EBC (void);
// 0x0000029C System.Void PICOVRButtonFix::FindTargetCanvas()
extern void PICOVRButtonFix_FindTargetCanvas_mE672F7057FF83F93D86D68A72DC2CACD3C4A56C1 (void);
// 0x0000029D System.Void PICOVRButtonFix::SetupEventSystem()
extern void PICOVRButtonFix_SetupEventSystem_mEC0D203EF6A83C49CBCBCCB34622D940A552F7E3 (void);
// 0x0000029E System.Void PICOVRButtonFix::SetupTrackedDeviceRaycaster()
extern void PICOVRButtonFix_SetupTrackedDeviceRaycaster_m30B52569843DE9BB416C8CF78E70D058896719C2 (void);
// 0x0000029F System.Void PICOVRButtonFix::FindAllButtons()
extern void PICOVRButtonFix_FindAllButtons_m7F8AF0C7963BF9052A72B12ED18E34483AD0CE7C (void);
// 0x000002A0 System.Void PICOVRButtonFix::SetupButtonClickHandlers()
extern void PICOVRButtonFix_SetupButtonClickHandlers_m83ADE23C18D19C2B55E4F3FCE18748A9A416B0A2 (void);
// 0x000002A1 System.Void PICOVRButtonFix::VerifySetup()
extern void PICOVRButtonFix_VerifySetup_mF4F5C1E6AD7AC9A5DD53D4E4CB566A88AFD034C2 (void);
// 0x000002A2 System.Void PICOVRButtonFix::TestButtonClicks()
extern void PICOVRButtonFix_TestButtonClicks_m845830A8F2616F95EEFAA0E39FE821D13DFBDF62 (void);
// 0x000002A3 System.Void PICOVRButtonFix::Update()
extern void PICOVRButtonFix_Update_mD5F25898AFCE2083C51B535E1C820EB28175D76B (void);
// 0x000002A4 System.Void PICOVRButtonFix::CheckRaycastHits()
extern void PICOVRButtonFix_CheckRaycastHits_m4D8D382FCB487F00836BF48449912E6888E8EC4C (void);
// 0x000002A5 System.Void PICOVRButtonFix::.ctor()
extern void PICOVRButtonFix__ctor_mDBC32389DB6A751F70D9071F413E361206BDDF8E (void);
// 0x000002A6 System.Void PICOVRButtonFix/<InitializeVRButtonFix>d__9::.ctor(System.Int32)
extern void U3CInitializeVRButtonFixU3Ed__9__ctor_mBC83A0BE871EC38F3496C42FA108BA7140BC9F18 (void);
// 0x000002A7 System.Void PICOVRButtonFix/<InitializeVRButtonFix>d__9::System.IDisposable.Dispose()
extern void U3CInitializeVRButtonFixU3Ed__9_System_IDisposable_Dispose_m10516B8928FA30A71EFCA756CE607A7BEA92535A (void);
// 0x000002A8 System.Boolean PICOVRButtonFix/<InitializeVRButtonFix>d__9::MoveNext()
extern void U3CInitializeVRButtonFixU3Ed__9_MoveNext_mBBE50AAC0295DC33DF499A5598D71636E2D3FAA7 (void);
// 0x000002A9 System.Object PICOVRButtonFix/<InitializeVRButtonFix>d__9::System.Collections.Generic.IEnumerator<System.Object>.get_Current()
extern void U3CInitializeVRButtonFixU3Ed__9_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m7E51F56247E552C9CB9DFB00E8C2560C20CEA3F5 (void);
// 0x000002AA System.Void PICOVRButtonFix/<InitializeVRButtonFix>d__9::System.Collections.IEnumerator.Reset()
extern void U3CInitializeVRButtonFixU3Ed__9_System_Collections_IEnumerator_Reset_m1009D198068D05E302196CCE9B19E6F9987BFDDE (void);
// 0x000002AB System.Object PICOVRButtonFix/<InitializeVRButtonFix>d__9::System.Collections.IEnumerator.get_Current()
extern void U3CInitializeVRButtonFixU3Ed__9_System_Collections_IEnumerator_get_Current_mA380C41067C5E93A5BF8E6413B4695D93E1BE5DE (void);
// 0x000002AC System.Void PICOVRButtonFix/<>c__DisplayClass14_0::.ctor()
extern void U3CU3Ec__DisplayClass14_0__ctor_m3B9616EC977A54C02786F41DE88DBE7A5BE9B64A (void);
// 0x000002AD System.Void PICOVRButtonFix/<>c__DisplayClass14_0::<SetupButtonClickHandlers>b__0(UnityEngine.EventSystems.BaseEventData)
extern void U3CU3Ec__DisplayClass14_0_U3CSetupButtonClickHandlersU3Eb__0_m99AAD07E456DAEF5C523DD2E920F39E9CC32F052 (void);
// 0x000002AE System.Void PICOVRButtonFix/<>c__DisplayClass14_0::<SetupButtonClickHandlers>b__1(UnityEngine.EventSystems.BaseEventData)
extern void U3CU3Ec__DisplayClass14_0_U3CSetupButtonClickHandlersU3Eb__1_mECE202021E314136C21FE4FD5A50B942EA806D20 (void);
// 0x000002AF System.Void PICOVRInputAdapter::Start()
extern void PICOVRInputAdapter_Start_m871A5B4EFAE0F6D3DF7460A683D070FAE7D3FD17 (void);
// 0x000002B0 System.Void PICOVRInputAdapter::Update()
extern void PICOVRInputAdapter_Update_m6BB22EC20ED960A5D818DF991517B25085452F68 (void);
// 0x000002B1 System.Void PICOVRInputAdapter::DetectXRToolkit()
extern void PICOVRInputAdapter_DetectXRToolkit_mECE5EC00D1EEF0C477E6DC7884640E3402E981CB (void);
// 0x000002B2 System.Void PICOVRInputAdapter::InitializePICOInput()
extern void PICOVRInputAdapter_InitializePICOInput_m9A4DCCE6BDE9CAD0161EDA915C8376B99B1AA324 (void);
// 0x000002B3 System.Void PICOVRInputAdapter::HandlePICOInput()
extern void PICOVRInputAdapter_HandlePICOInput_mF3940DD2E6CBE4CAB8899C35BD271933AADF8AB6 (void);
// 0x000002B4 System.Void PICOVRInputAdapter::HandleActionBasedControllerInput(UnityEngine.XR.Interaction.Toolkit.ActionBasedController,System.String)
extern void PICOVRInputAdapter_HandleActionBasedControllerInput_m5D3404D6628BF88659B371101B0F29592BB87B89 (void);
// 0x000002B5 System.Boolean PICOVRInputAdapter::GetActionButtonState(UnityEngine.InputSystem.InputActionProperty)
extern void PICOVRInputAdapter_GetActionButtonState_m1C65E51590AACC2CAE5776CEE4BF33E509E0EAF6 (void);
// 0x000002B6 System.Void PICOVRInputAdapter::OnTriggerPressed(System.String)
extern void PICOVRInputAdapter_OnTriggerPressed_mE549CF40673C509C9D9E9A81D05FCA1ABF6D7C26 (void);
// 0x000002B7 System.Void PICOVRInputAdapter::OnPrimaryButtonPressed(System.String)
extern void PICOVRInputAdapter_OnPrimaryButtonPressed_m5B191F23E444682348C83342EE77E5F7733CBED5 (void);
// 0x000002B8 System.Void PICOVRInputAdapter::OnSecondaryButtonPressed(System.String)
extern void PICOVRInputAdapter_OnSecondaryButtonPressed_mBE6FE162441EBEF8CBC7258253688BDC42639150 (void);
// 0x000002B9 System.Void PICOVRInputAdapter::OnMenuButtonPressed(System.String)
extern void PICOVRInputAdapter_OnMenuButtonPressed_m07CD6C45894DC25455589BFB78C4C75BEB70A195 (void);
// 0x000002BA System.Void PICOVRInputAdapter::HandleFallbackInput()
extern void PICOVRInputAdapter_HandleFallbackInput_m725E2F706B69D03843CCECD9CACE628846D07366 (void);
// 0x000002BB System.Void PICOVRInputAdapter::ProvideTactileFeedback()
extern void PICOVRInputAdapter_ProvideTactileFeedback_mA106D6E843A8B64A4B8F1A1713D176EADB2F9B00 (void);
// 0x000002BC System.Void PICOVRInputAdapter::ShowInputMapping()
extern void PICOVRInputAdapter_ShowInputMapping_mD80922B53B9A1B8A9923F45FB7350B8F658E45B4 (void);
// 0x000002BD System.Void PICOVRInputAdapter::TestAllVRFunctions()
extern void PICOVRInputAdapter_TestAllVRFunctions_m422DC00A07F0828D9B4715651B75700B74E40E08 (void);
// 0x000002BE System.Collections.IEnumerator PICOVRInputAdapter::TestSequence()
extern void PICOVRInputAdapter_TestSequence_m1F1934F4900216A04B1000AB2543D2B8A6A6DE48 (void);
// 0x000002BF System.Void PICOVRInputAdapter::SetPICOInputEnabled(System.Boolean)
extern void PICOVRInputAdapter_SetPICOInputEnabled_m3997B8537C9BDB14CE2F8835E6FAABDC20DC34C5 (void);
// 0x000002C0 System.Void PICOVRInputAdapter::OnDestroy()
extern void PICOVRInputAdapter_OnDestroy_mC342C42272011A4436B1CF27A9119CDB0CC2EF8B (void);
// 0x000002C1 System.Void PICOVRInputAdapter::.ctor()
extern void PICOVRInputAdapter__ctor_m7A5E3D1662C082337811679E8795E15FB3B0202F (void);
// 0x000002C2 System.Void PICOVRInputAdapter/<TestSequence>d__30::.ctor(System.Int32)
extern void U3CTestSequenceU3Ed__30__ctor_m11C7FEDBED6C0F71E70CB6E5D390B4E4EA66474D (void);
// 0x000002C3 System.Void PICOVRInputAdapter/<TestSequence>d__30::System.IDisposable.Dispose()
extern void U3CTestSequenceU3Ed__30_System_IDisposable_Dispose_m7ECB7389819098C0B04C39F19232040DAC259AAB (void);
// 0x000002C4 System.Boolean PICOVRInputAdapter/<TestSequence>d__30::MoveNext()
extern void U3CTestSequenceU3Ed__30_MoveNext_mD5EEA90FBCB09F77542CC10B4BD0FEF2C6538336 (void);
// 0x000002C5 System.Object PICOVRInputAdapter/<TestSequence>d__30::System.Collections.Generic.IEnumerator<System.Object>.get_Current()
extern void U3CTestSequenceU3Ed__30_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mDAF53EFACFE8B126E6591229FE67ECF5E2C26E95 (void);
// 0x000002C6 System.Void PICOVRInputAdapter/<TestSequence>d__30::System.Collections.IEnumerator.Reset()
extern void U3CTestSequenceU3Ed__30_System_Collections_IEnumerator_Reset_mB35907C0426BAFAC88BC3CA4695585834F57CBE4 (void);
// 0x000002C7 System.Object PICOVRInputAdapter/<TestSequence>d__30::System.Collections.IEnumerator.get_Current()
extern void U3CTestSequenceU3Ed__30_System_Collections_IEnumerator_get_Current_m7507C329E70FB9E08412B645C57EE1C04F77898F (void);
// 0x000002C8 System.Void PICOVRInputHandler::Start()
extern void PICOVRInputHandler_Start_m040FD6075E73153DEDD3315F6C3476739C77AFD9 (void);
// 0x000002C9 System.Void PICOVRInputHandler::Update()
extern void PICOVRInputHandler_Update_m3A120BCFDCF1C2B42C985E13048CD32A454A4A39 (void);
// 0x000002CA System.Void PICOVRInputHandler::InitializeComponents()
extern void PICOVRInputHandler_InitializeComponents_m53757037F6AB72E5146CC91603B6638C0B51E36B (void);
// 0x000002CB System.Void PICOVRInputHandler::LogInitializationStatus()
extern void PICOVRInputHandler_LogInitializationStatus_m6FF7C0513BB4188C8CAD136D2A122BC8DD6F0DE5 (void);
// 0x000002CC System.Void PICOVRInputHandler::HandleInput()
extern void PICOVRInputHandler_HandleInput_m33C53CE04B4F943981EED2BC6762483DBF06689E (void);
// 0x000002CD System.Void PICOVRInputHandler::HandleControllerInput(UnityEngine.XR.Interaction.Toolkit.ActionBasedController,System.String)
extern void PICOVRInputHandler_HandleControllerInput_mF6C3E09F22D3860BF14FC25ED30589E8BC2EFC8D (void);
// 0x000002CE System.Boolean PICOVRInputHandler::IsActionPressed(UnityEngine.InputSystem.InputActionProperty)
extern void PICOVRInputHandler_IsActionPressed_m868FCCE0FDCE4F0F8AEE912FFAEEF0E647915372 (void);
// 0x000002CF System.Void PICOVRInputHandler::CheckAndHandleButtonPress(System.Boolean,System.Boolean,System.Action)
extern void PICOVRInputHandler_CheckAndHandleButtonPress_m85D2F84C0C71891829283FEFE5F66C6C10ECE1BE (void);
// 0x000002D0 System.Void PICOVRInputHandler::OnTriggerPressed(System.String)
extern void PICOVRInputHandler_OnTriggerPressed_m96E981ED765712ED65751D598F61C389EB273E79 (void);
// 0x000002D1 System.Void PICOVRInputHandler::OnPrimaryPressed(System.String)
extern void PICOVRInputHandler_OnPrimaryPressed_m2ABD8130AF22D9A47B8703AFB9408B92ECD1C700 (void);
// 0x000002D2 System.Void PICOVRInputHandler::OnSecondaryPressed(System.String)
extern void PICOVRInputHandler_OnSecondaryPressed_m7B04E81B614BB222F5CCF4E2B0557738C5C968CA (void);
// 0x000002D3 System.Void PICOVRInputHandler::OnMenuPressed(System.String)
extern void PICOVRInputHandler_OnMenuPressed_m717A6F52AF2EA6E53D72094BC205585C347E2F0D (void);
// 0x000002D4 System.Void PICOVRInputHandler::HandleKeyboardFallback()
extern void PICOVRInputHandler_HandleKeyboardFallback_mE2ACE1AA53696A22C7F7D4BCC972C262C8B54A39 (void);
// 0x000002D5 System.Void PICOVRInputHandler::TestAllFunctions()
extern void PICOVRInputHandler_TestAllFunctions_mC0DFED38901ED358C4359274C59EEA7BDD2CCC2D (void);
// 0x000002D6 System.Collections.IEnumerator PICOVRInputHandler::TestSequence()
extern void PICOVRInputHandler_TestSequence_mA7F235A3619845B15F9FB8F24D79627642208F31 (void);
// 0x000002D7 System.Void PICOVRInputHandler::ShowUsageInstructions()
extern void PICOVRInputHandler_ShowUsageInstructions_m29ED5CFD4C5FDEFA350FF0A57ED239413958FECE (void);
// 0x000002D8 System.Void PICOVRInputHandler::.ctor()
extern void PICOVRInputHandler__ctor_m1767D45B151903DF3735AC430E4A2359DE38500F (void);
// 0x000002D9 System.Void PICOVRInputHandler/<>c__DisplayClass18_0::.ctor()
extern void U3CU3Ec__DisplayClass18_0__ctor_m499730C506EDA8F4B0428AD941FDE43D2891CDA5 (void);
// 0x000002DA System.Void PICOVRInputHandler/<>c__DisplayClass18_0::<HandleControllerInput>b__0()
extern void U3CU3Ec__DisplayClass18_0_U3CHandleControllerInputU3Eb__0_m1054D905ED79E90B5887023E8AA4F061806C2420 (void);
// 0x000002DB System.Void PICOVRInputHandler/<>c__DisplayClass18_0::<HandleControllerInput>b__1()
extern void U3CU3Ec__DisplayClass18_0_U3CHandleControllerInputU3Eb__1_mFE5AABC3D2A7CCD66C07B5B5C36F777CF2A12C3D (void);
// 0x000002DC System.Void PICOVRInputHandler/<>c__DisplayClass18_0::<HandleControllerInput>b__2()
extern void U3CU3Ec__DisplayClass18_0_U3CHandleControllerInputU3Eb__2_m76726ABD87662B0D4ADDF651594731C5AEA3D9DE (void);
// 0x000002DD System.Void PICOVRInputHandler/<>c__DisplayClass18_0::<HandleControllerInput>b__3()
extern void U3CU3Ec__DisplayClass18_0_U3CHandleControllerInputU3Eb__3_mDDABDD2DC74231131DE3FD5E38C3B1A2E736DA6F (void);
// 0x000002DE System.Void PICOVRInputHandler/<TestSequence>d__27::.ctor(System.Int32)
extern void U3CTestSequenceU3Ed__27__ctor_mC616EFEDA99D028A468A9825316DB1F69A092121 (void);
// 0x000002DF System.Void PICOVRInputHandler/<TestSequence>d__27::System.IDisposable.Dispose()
extern void U3CTestSequenceU3Ed__27_System_IDisposable_Dispose_m10362276E247EB7B3A3C9500C6719389CCE74095 (void);
// 0x000002E0 System.Boolean PICOVRInputHandler/<TestSequence>d__27::MoveNext()
extern void U3CTestSequenceU3Ed__27_MoveNext_m015A6CFDFEE15927FF40E2D1B4FE175AC71EE316 (void);
// 0x000002E1 System.Object PICOVRInputHandler/<TestSequence>d__27::System.Collections.Generic.IEnumerator<System.Object>.get_Current()
extern void U3CTestSequenceU3Ed__27_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mF3E8793DA39E562916284E74E2166B83401C8AA9 (void);
// 0x000002E2 System.Void PICOVRInputHandler/<TestSequence>d__27::System.Collections.IEnumerator.Reset()
extern void U3CTestSequenceU3Ed__27_System_Collections_IEnumerator_Reset_mA10DFBD13E14E0C2EA4D071B3F966AE52F8DBB53 (void);
// 0x000002E3 System.Object PICOVRInputHandler/<TestSequence>d__27::System.Collections.IEnumerator.get_Current()
extern void U3CTestSequenceU3Ed__27_System_Collections_IEnumerator_get_Current_m65EA1B1AC3F0E1D0A41FECD3106BF52E3A7DA32D (void);
// 0x000002E4 System.Void SimpleAssemblyDataReceiver::Start()
extern void SimpleAssemblyDataReceiver_Start_mB44AE545C79D458750DBEE53F78418DC34163055 (void);
// 0x000002E5 System.Void SimpleAssemblyDataReceiver::ReceiveSimpleAssemblyStep(System.String,System.String,System.String,System.String)
extern void SimpleAssemblyDataReceiver_ReceiveSimpleAssemblyStep_m751135B0629BE3E50283B250FE3E9D6BA623B337 (void);
// 0x000002E6 System.Void SimpleAssemblyDataReceiver::ReceiveAssemblyStepWithScrew(System.String,System.String,System.String,System.String,System.String,System.String)
extern void SimpleAssemblyDataReceiver_ReceiveAssemblyStepWithScrew_m87F12585CCFD4136F3B9DE8A367A6B051BD831B7 (void);
// 0x000002E7 System.Void SimpleAssemblyDataReceiver::TryAddStepToController(AssemblyStep)
extern void SimpleAssemblyDataReceiver_TryAddStepToController_m89E596EAEFA6294F5C076400486EDBB1B423C77B (void);
// 0x000002E8 System.Void SimpleAssemblyDataReceiver::ClearAllSteps()
extern void SimpleAssemblyDataReceiver_ClearAllSteps_m817241E6B6F8B229EF35A686EBDD8283CDA50F9D (void);
// 0x000002E9 System.Void SimpleAssemblyDataReceiver::StartAssemblyAnimation()
extern void SimpleAssemblyDataReceiver_StartAssemblyAnimation_mD334AECAA1620754F45521B94BCE7D035CD8F42E (void);
// 0x000002EA System.String SimpleAssemblyDataReceiver::GetSystemStatus()
extern void SimpleAssemblyDataReceiver_GetSystemStatus_mE12B7F1DC369CC97FF75AA09133188F5E2548636 (void);
// 0x000002EB System.Void SimpleAssemblyDataReceiver::ReceiveAssemblyDataJSON(System.String)
extern void SimpleAssemblyDataReceiver_ReceiveAssemblyDataJSON_m67F383928F0E66CA5A0DD51CB8C534C62DA78C8F (void);
// 0x000002EC System.Void SimpleAssemblyDataReceiver::ReceiveAssemblyDataArray(System.String[])
extern void SimpleAssemblyDataReceiver_ReceiveAssemblyDataArray_m5E561D92167351D9352518004CAA3C5008D735F1 (void);
// 0x000002ED System.Void SimpleAssemblyDataReceiver::ParseAndReceiveStep(System.String)
extern void SimpleAssemblyDataReceiver_ParseAndReceiveStep_m0372DAFEAEAD2350244888876E8FBA058EEA1B0A (void);
// 0x000002EE System.Void SimpleAssemblyDataReceiver::TestSimpleAssembly()
extern void SimpleAssemblyDataReceiver_TestSimpleAssembly_mE5581D0D3574FD6F73497D42A0EF5614434C4B7B (void);
// 0x000002EF System.Void SimpleAssemblyDataReceiver::TestScrewAssembly()
extern void SimpleAssemblyDataReceiver_TestScrewAssembly_m259B3BD72BE9EBAF72CE540C8D0D482021D92726 (void);
// 0x000002F0 System.Void SimpleAssemblyDataReceiver::TestMultipleAssembly()
extern void SimpleAssemblyDataReceiver_TestMultipleAssembly_m6243B7111800784F8D077E28386A821F4744ED16 (void);
// 0x000002F1 System.Void SimpleAssemblyDataReceiver::TestClearSteps()
extern void SimpleAssemblyDataReceiver_TestClearSteps_mC8257389399356339D993907E42103EA4D4731DA (void);
// 0x000002F2 System.Void SimpleAssemblyDataReceiver::TestStartAnimation()
extern void SimpleAssemblyDataReceiver_TestStartAnimation_m5023F5E501A2FE7524A3FDF4E5B9C11656E91A5B (void);
// 0x000002F3 System.Void SimpleAssemblyDataReceiver::.ctor()
extern void SimpleAssemblyDataReceiver__ctor_mCA7A0DCFEE31FF8AE677EF481BD4960C1B118BF6 (void);
// 0x000002F4 System.Void SimpleExternalDataReceiver::Start()
extern void SimpleExternalDataReceiver_Start_mECA24017984741ED3C4CF74C3C90DFDE417E2417 (void);
// 0x000002F5 System.Void SimpleExternalDataReceiver::ReceiveAssemblyData(System.String,System.String)
extern void SimpleExternalDataReceiver_ReceiveAssemblyData_m7AF8687829EA89619C310A2227EBE18FB25688CF (void);
// 0x000002F6 System.Void SimpleExternalDataReceiver::ReceiveSimpleAssemblyStep(System.String,System.String,System.String,System.String,System.String)
extern void SimpleExternalDataReceiver_ReceiveSimpleAssemblyStep_mE520344B7689530EB470781513F8A325343BE3D4 (void);
// 0x000002F7 System.Void SimpleExternalDataReceiver::ReceiveAssemblyStepWithFastener(System.String,System.String,System.String,System.String,System.String,System.String)
extern void SimpleExternalDataReceiver_ReceiveAssemblyStepWithFastener_m1984DFABC245B69573A8A4A7E2032CD82CA99F9D (void);
// 0x000002F8 System.Void SimpleExternalDataReceiver::LoadTestData()
extern void SimpleExternalDataReceiver_LoadTestData_m0FC21159F061F075440F99AAF799A2649056FCD6 (void);
// 0x000002F9 System.Void SimpleExternalDataReceiver::CreateSampleAssemblyData()
extern void SimpleExternalDataReceiver_CreateSampleAssemblyData_m2108FA0D0233FBA46269FE7F1124445B43A90E3D (void);
// 0x000002FA System.Void SimpleExternalDataReceiver::TestSimpleAssemblyStep()
extern void SimpleExternalDataReceiver_TestSimpleAssemblyStep_m63592CCDBFE2ABD91516C20F4458EE3B5F03C1B2 (void);
// 0x000002FB System.Void SimpleExternalDataReceiver::TestScrewAssemblyStep()
extern void SimpleExternalDataReceiver_TestScrewAssemblyStep_m09B2C7220E1CEA8C5CD5C6C5C394B59291B06166 (void);
// 0x000002FC System.Void SimpleExternalDataReceiver::ClearAssemblySteps()
extern void SimpleExternalDataReceiver_ClearAssemblySteps_m05F35556FEC69C68310A9F7240ABA243154F2B88 (void);
// 0x000002FD System.Boolean SimpleExternalDataReceiver::IsReadyToReceiveData()
extern void SimpleExternalDataReceiver_IsReadyToReceiveData_m5B34E4D6E78899F1FA1686BE5716E45FDFAB7821 (void);
// 0x000002FE System.String SimpleExternalDataReceiver::GetAssemblyStatus()
extern void SimpleExternalDataReceiver_GetAssemblyStatus_m8BF8F36E42416F776DE765767DD5BBC2A3A7F508 (void);
// 0x000002FF System.Void SimpleExternalDataReceiver::Update()
extern void SimpleExternalDataReceiver_Update_mABB95B818A82D5CBA18DEFB12504B29CB06F99CB (void);
// 0x00000300 System.Void SimpleExternalDataReceiver::.ctor()
extern void SimpleExternalDataReceiver__ctor_m430FD562BDA65E920723B815DF9F5E4F8992225D (void);
// 0x00000301 System.Void SimpleInputTest::Start()
extern void SimpleInputTest_Start_m17B5289622AD00616A837197FE2E354B5C9B02AC (void);
// 0x00000302 System.Void SimpleInputTest::Update()
extern void SimpleInputTest_Update_m40448C64D41AE6A8E8046F9F46C988DEB58EBAA8 (void);
// 0x00000303 System.Void SimpleInputTest::TestVRInput()
extern void SimpleInputTest_TestVRInput_mBC8781020E96405DE9100985B0743C50B1444930 (void);
// 0x00000304 System.Void SimpleInputTest::TestControllerInput(UnityEngine.XR.Interaction.Toolkit.ActionBasedController,System.String)
extern void SimpleInputTest_TestControllerInput_m1F8394486B18C0961AF9318078B5E6AB054ED2B0 (void);
// 0x00000305 System.Void SimpleInputTest::TestFunction1()
extern void SimpleInputTest_TestFunction1_m5A15960E8E4BB342A760DD24DE3374A44069A771 (void);
// 0x00000306 System.Void SimpleInputTest::TestFunction2()
extern void SimpleInputTest_TestFunction2_m227FE2856E933C6D0E2D092FECD1C509F52EF021 (void);
// 0x00000307 System.Void SimpleInputTest::ManualTestFunction1()
extern void SimpleInputTest_ManualTestFunction1_m2DD2FF7422D331117A98D0A45606ECA0056A03C9 (void);
// 0x00000308 System.Void SimpleInputTest::ManualTestFunction2()
extern void SimpleInputTest_ManualTestFunction2_m76EA3FE9A4DCA31519FDD9B5BDA1C5322BBF8E28 (void);
// 0x00000309 System.Void SimpleInputTest::LogDetailedStatus()
extern void SimpleInputTest_LogDetailedStatus_m25B3CDC2FCFFC40FB1129BA5BE4FFEFBA92ADC1A (void);
// 0x0000030A System.Void SimpleInputTest::.ctor()
extern void SimpleInputTest__ctor_mF47AC1968882E5C6AC8E7F58533A4E22C7D5E370 (void);
// 0x0000030B System.Void Test::Start()
extern void Test_Start_mA902842AF55C0A063D71F22B280F28BF0FB01497 (void);
// 0x0000030C System.Void Test::Update()
extern void Test_Update_m68AE52707CAA861E0DBDC29971974A5BF62D82D1 (void);
// 0x0000030D System.Void Test::OnDrawGizmos()
extern void Test_OnDrawGizmos_m6481F8828A0F683CCAE82CCB2C6CB8A94F05E6E8 (void);
// 0x0000030E System.Void Test::.ctor()
extern void Test__ctor_mB84DF4A3888723C395E76E3879FDFB8AA1EFEDCB (void);
// 0x0000030F System.Void VRSettingsUI::Start()
extern void VRSettingsUI_Start_m30BE28F2E1966ACE15BDAAF844B86A58DBA33285 (void);
// 0x00000310 System.Void VRSettingsUI::Update()
extern void VRSettingsUI_Update_m8B10435EEB34CCB961FB9121A6EA01BCCA9234E4 (void);
// 0x00000311 System.Void VRSettingsUI::InitializeVRSettingsUI()
extern void VRSettingsUI_InitializeVRSettingsUI_m8896DB93E4A7759E966DB63FDC865D121450DCF1 (void);
// 0x00000312 System.Void VRSettingsUI::FindVRCamera()
extern void VRSettingsUI_FindVRCamera_m04A785B2FCCE82D2EFDA6AB65C84D2F73F2895B7 (void);
// 0x00000313 System.Void VRSettingsUI::SetupUIComponents()
extern void VRSettingsUI_SetupUIComponents_mB247929990339F171ED94BC69D5C7F4321ED79C4 (void);
// 0x00000314 System.Void VRSettingsUI::SetupEventListeners()
extern void VRSettingsUI_SetupEventListeners_m63A480EAEBF617337C9398B208CB1D991A98939A (void);
// 0x00000315 System.Void VRSettingsUI::UpdateUIPositions()
extern void VRSettingsUI_UpdateUIPositions_m8AB9678BAC7E439CCE1DD099C5B3D5B537410804 (void);
// 0x00000316 System.Void VRSettingsUI::UpdateSettingsButtonPosition()
extern void VRSettingsUI_UpdateSettingsButtonPosition_m896F7DFEA3F92420DC25667B030AE2D6B5ED659D (void);
// 0x00000317 System.Void VRSettingsUI::UpdateSettingsPanelPosition()
extern void VRSettingsUI_UpdateSettingsPanelPosition_m9C7457CE93B1AE5B070DC7A7302F3D5AD136A9F7 (void);
// 0x00000318 System.Void VRSettingsUI::OnSettingsButtonClicked()
extern void VRSettingsUI_OnSettingsButtonClicked_m950F8E15EBB2DE76B69385B091FFB084439936AF (void);
// 0x00000319 System.Void VRSettingsUI::OnCloseButtonClicked()
extern void VRSettingsUI_OnCloseButtonClicked_m7B08C422C384B040914C81382389729844FB8D62 (void);
// 0x0000031A System.Void VRSettingsUI::OpenSettingsPanel()
extern void VRSettingsUI_OpenSettingsPanel_m689F5D2613DA04C5BD94EAB49F04BA4A4B68FF9D (void);
// 0x0000031B System.Void VRSettingsUI::CloseSettingsPanel()
extern void VRSettingsUI_CloseSettingsPanel_m03F55AB561F7F44A935034B5067228B44C27B8D5 (void);
// 0x0000031C System.Collections.IEnumerator VRSettingsUI::AnimatePanel(System.Boolean)
extern void VRSettingsUI_AnimatePanel_m6E567DD5B75D303F82BC840F3ED79E58EC6B3C4B (void);
// 0x0000031D System.Void VRSettingsUI::PlaySound(UnityEngine.AudioClip)
extern void VRSettingsUI_PlaySound_m0B2549385BCCB3FDEE807B2BD08EE4C769E630FC (void);
// 0x0000031E System.Void VRSettingsUI::ToggleSettingsPanel()
extern void VRSettingsUI_ToggleSettingsPanel_m950B852B6D4BFEEA844FEF2DD4572DE7FCC7E1D5 (void);
// 0x0000031F System.Boolean VRSettingsUI::IsSettingsPanelOpen()
extern void VRSettingsUI_IsSettingsPanelOpen_m6F92428A01E88BF983F26B7EE840D204D3DEA50C (void);
// 0x00000320 System.Void VRSettingsUI::OnDestroy()
extern void VRSettingsUI_OnDestroy_mC01C9302B355759B16E86FCFEA28BBE3664F9E49 (void);
// 0x00000321 System.Void VRSettingsUI::.ctor()
extern void VRSettingsUI__ctor_m0C165A9F8A137ACB24C59B97C5AE5278269F7650 (void);
// 0x00000322 System.Void VRSettingsUI/<AnimatePanel>d__35::.ctor(System.Int32)
extern void U3CAnimatePanelU3Ed__35__ctor_mB7BCAEA220978C80A503C1E1546E9E133F835019 (void);
// 0x00000323 System.Void VRSettingsUI/<AnimatePanel>d__35::System.IDisposable.Dispose()
extern void U3CAnimatePanelU3Ed__35_System_IDisposable_Dispose_m23915BDCA288098FB04FDBDA3F8EC90F87482EC0 (void);
// 0x00000324 System.Boolean VRSettingsUI/<AnimatePanel>d__35::MoveNext()
extern void U3CAnimatePanelU3Ed__35_MoveNext_mF54258248E9478A7E9A462A5D15D34E85B964425 (void);
// 0x00000325 System.Object VRSettingsUI/<AnimatePanel>d__35::System.Collections.Generic.IEnumerator<System.Object>.get_Current()
extern void U3CAnimatePanelU3Ed__35_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m98AD8560F99A98052798B70E31DA293AA3341B93 (void);
// 0x00000326 System.Void VRSettingsUI/<AnimatePanel>d__35::System.Collections.IEnumerator.Reset()
extern void U3CAnimatePanelU3Ed__35_System_Collections_IEnumerator_Reset_mC549C4CA1369482D512B455DED0B30E6FADF4DC7 (void);
// 0x00000327 System.Object VRSettingsUI/<AnimatePanel>d__35::System.Collections.IEnumerator.get_Current()
extern void U3CAnimatePanelU3Ed__35_System_Collections_IEnumerator_get_Current_mE4B13B80E0CDE07BABF8CCB559FBDACE35FB51E0 (void);
// 0x00000328 System.Void VRAssemblyDebugger::Start()
extern void VRAssemblyDebugger_Start_m88057DC24FD49105662F4E7E745676D3B7362827 (void);
// 0x00000329 System.Void VRAssemblyDebugger::Update()
extern void VRAssemblyDebugger_Update_mEE80CE3E4E82FFB2C7D1BBD866F3561C6A3C577F (void);
// 0x0000032A System.Void VRAssemblyDebugger::FindComponents()
extern void VRAssemblyDebugger_FindComponents_m9609506EED6196374D2A6551B8361EF71DBBE20E (void);
// 0x0000032B System.Void VRAssemblyDebugger::ValidateSetup()
extern void VRAssemblyDebugger_ValidateSetup_m1200B28D916E56DA6D6A94CD18B92BDA862F7CF5 (void);
// 0x0000032C System.Void VRAssemblyDebugger::LogSystemStatus()
extern void VRAssemblyDebugger_LogSystemStatus_m67504E1AA45FE48557C8AEE8E518A2B406845D26 (void);
// 0x0000032D System.Void VRAssemblyDebugger::HandleDebugInput()
extern void VRAssemblyDebugger_HandleDebugInput_m95DC4AEC014F5083F4C6B3667F9181BB3D68087A (void);
// 0x0000032E System.Void VRAssemblyDebugger::TestPositioning()
extern void VRAssemblyDebugger_TestPositioning_mE4BE9287A0C26F70480A633E0AA84F01AD8EF095 (void);
// 0x0000032F System.Void VRAssemblyDebugger::TestCameraBasedPositioning()
extern void VRAssemblyDebugger_TestCameraBasedPositioning_m8805EAF07380827CDF88D5AAF4C1A997FCED85AD (void);
// 0x00000330 UnityEngine.Camera VRAssemblyDebugger::GetVRSimulatorCamera()
extern void VRAssemblyDebugger_GetVRSimulatorCamera_m42AE5CF74DEF231BDA5DFC445E0C78DD7894017F (void);
// 0x00000331 System.Void VRAssemblyDebugger::TestOrientation()
extern void VRAssemblyDebugger_TestOrientation_m8F0A58E8070694652BF410D17CD1ECAC13FFB120 (void);
// 0x00000332 System.Void VRAssemblyDebugger::ShowCurrentCameraInfo()
extern void VRAssemblyDebugger_ShowCurrentCameraInfo_mACE249A98EA7F9D20B8A794EF3EAFB5A9E85DBBF (void);
// 0x00000333 System.Void VRAssemblyDebugger::ShowTargetAssemblyPointInfo()
extern void VRAssemblyDebugger_ShowTargetAssemblyPointInfo_mDA6C48E266723BDE26C51399073099CF693FCAF4 (void);
// 0x00000334 System.Collections.IEnumerator VRAssemblyDebugger::TestOnlyOrientationStep()
extern void VRAssemblyDebugger_TestOnlyOrientationStep_m8B8B5D222D95300345C45D99402858AD873CAD66 (void);
// 0x00000335 System.Collections.IEnumerator VRAssemblyDebugger::TestSpecificAssemblyPointOrientation()
extern void VRAssemblyDebugger_TestSpecificAssemblyPointOrientation_m5CF4EB0F31A75F6F7B1B4D25A1D628F0C74760B4 (void);
// 0x00000336 System.Void VRAssemblyDebugger::VerifyAssemblyPointOrientation()
extern void VRAssemblyDebugger_VerifyAssemblyPointOrientation_mA1EF2FC7FA25374B28C61701E6D87DA824571E65 (void);
// 0x00000337 System.Void VRAssemblyDebugger::DiagnoseOrientationIssue()
extern void VRAssemblyDebugger_DiagnoseOrientationIssue_mAFFF4A3D6D5A704B1940C563634D21D0A245B0FC (void);
// 0x00000338 System.Void VRAssemblyDebugger::ResetPosition()
extern void VRAssemblyDebugger_ResetPosition_m1DEA543BFBA73DA26F0BAE298036516BB32A3A33 (void);
// 0x00000339 System.Void VRAssemblyDebugger::ToggleDebugMode()
extern void VRAssemblyDebugger_ToggleDebugMode_mF30A6483F7D73F8DE00EA69AD570FEE3CA1AC22B (void);
// 0x0000033A System.Void VRAssemblyDebugger::ForceInitializePositioner()
extern void VRAssemblyDebugger_ForceInitializePositioner_mA640224DBD231DE04B6D914DF72CF66D2705AA1C (void);
// 0x0000033B System.Object VRAssemblyDebugger::GetPrivateField(System.Object,System.String)
extern void VRAssemblyDebugger_GetPrivateField_m25929B441736549ED4A599156FF67034D765AEA4 (void);
// 0x0000033C System.Void VRAssemblyDebugger::SetRelativePosition(UnityEngine.Vector3)
extern void VRAssemblyDebugger_SetRelativePosition_m708C810BBC71BCD7A7CB44F87BC5916D45BD4B4E (void);
// 0x0000033D UnityEngine.GUIStyle VRAssemblyDebugger::EditorGUIStyle()
extern void VRAssemblyDebugger_EditorGUIStyle_m35445EEFA87748810503E4CB9FEE18A444015057 (void);
// 0x0000033E System.Void VRAssemblyDebugger::.ctor()
extern void VRAssemblyDebugger__ctor_m846C941FB4DC221B0C49AE40AEA08D325BDF0136 (void);
// 0x0000033F System.Void VRAssemblyDebugger/<TestOnlyOrientationStep>d__28::.ctor(System.Int32)
extern void U3CTestOnlyOrientationStepU3Ed__28__ctor_m9B662B94B759AC3B9F552E18AB7AB013101B1AB1 (void);
// 0x00000340 System.Void VRAssemblyDebugger/<TestOnlyOrientationStep>d__28::System.IDisposable.Dispose()
extern void U3CTestOnlyOrientationStepU3Ed__28_System_IDisposable_Dispose_m3E2C3C2CD915EEF1D58B5DB881A5002DD759BF0B (void);
// 0x00000341 System.Boolean VRAssemblyDebugger/<TestOnlyOrientationStep>d__28::MoveNext()
extern void U3CTestOnlyOrientationStepU3Ed__28_MoveNext_m78AF374949E36ABCB83706E8894069978470B4B5 (void);
// 0x00000342 System.Object VRAssemblyDebugger/<TestOnlyOrientationStep>d__28::System.Collections.Generic.IEnumerator<System.Object>.get_Current()
extern void U3CTestOnlyOrientationStepU3Ed__28_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m44B4CCBEEF7749175E732B612ABBD49F0C0CFEFC (void);
// 0x00000343 System.Void VRAssemblyDebugger/<TestOnlyOrientationStep>d__28::System.Collections.IEnumerator.Reset()
extern void U3CTestOnlyOrientationStepU3Ed__28_System_Collections_IEnumerator_Reset_m3590A923984CE409A45AF837D1B56CDCA672A6C8 (void);
// 0x00000344 System.Object VRAssemblyDebugger/<TestOnlyOrientationStep>d__28::System.Collections.IEnumerator.get_Current()
extern void U3CTestOnlyOrientationStepU3Ed__28_System_Collections_IEnumerator_get_Current_m230BEBBF16972C25F2A2FA0D950A3C34AD46C0D5 (void);
// 0x00000345 System.Void VRAssemblyDebugger/<TestSpecificAssemblyPointOrientation>d__29::.ctor(System.Int32)
extern void U3CTestSpecificAssemblyPointOrientationU3Ed__29__ctor_m5A6F84491B8F67D391E2BFA2D4B975587423C7DA (void);
// 0x00000346 System.Void VRAssemblyDebugger/<TestSpecificAssemblyPointOrientation>d__29::System.IDisposable.Dispose()
extern void U3CTestSpecificAssemblyPointOrientationU3Ed__29_System_IDisposable_Dispose_m9F953536DBFB57E85D98A21E9440BEBB7535B17E (void);
// 0x00000347 System.Boolean VRAssemblyDebugger/<TestSpecificAssemblyPointOrientation>d__29::MoveNext()
extern void U3CTestSpecificAssemblyPointOrientationU3Ed__29_MoveNext_m68332CED94D7FA13E233246364AEFF987BB7F35F (void);
// 0x00000348 System.Object VRAssemblyDebugger/<TestSpecificAssemblyPointOrientation>d__29::System.Collections.Generic.IEnumerator<System.Object>.get_Current()
extern void U3CTestSpecificAssemblyPointOrientationU3Ed__29_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m8D12D60712C0259F9A0F546EA7BD12623ECA146F (void);
// 0x00000349 System.Void VRAssemblyDebugger/<TestSpecificAssemblyPointOrientation>d__29::System.Collections.IEnumerator.Reset()
extern void U3CTestSpecificAssemblyPointOrientationU3Ed__29_System_Collections_IEnumerator_Reset_mF04B89F437DADBE1B555DF45F4471373FE045AFF (void);
// 0x0000034A System.Object VRAssemblyDebugger/<TestSpecificAssemblyPointOrientation>d__29::System.Collections.IEnumerator.get_Current()
extern void U3CTestSpecificAssemblyPointOrientationU3Ed__29_System_Collections_IEnumerator_get_Current_m70C8B298768F8D3616C6993EADE553B9C97E9A7D (void);
// 0x0000034B System.Void VRAssemblyInputManager::Start()
extern void VRAssemblyInputManager_Start_mA071D9867E841360986E8C25E0B0689565C15B51 (void);
// 0x0000034C System.Void VRAssemblyInputManager::Update()
extern void VRAssemblyInputManager_Update_m5F5EFC6D39C57E239CEFA69E578E9789DBEB4A5D (void);
// 0x0000034D System.Void VRAssemblyInputManager::HandleUserInput()
extern void VRAssemblyInputManager_HandleUserInput_mD95622AC11C59557AAD9693685AC4E4AF0B9F9B6 (void);
// 0x0000034E System.Void VRAssemblyInputManager::TriggerViewAdjustment()
extern void VRAssemblyInputManager_TriggerViewAdjustment_mCF5B8656FFFF2F4313D81F47D48EF3D824D3AEEA (void);
// 0x0000034F System.Void VRAssemblyInputManager::SetInputEnabled(System.Boolean)
extern void VRAssemblyInputManager_SetInputEnabled_mBAFD23791634AB7C79B70274AC68511F8DB47B56 (void);
// 0x00000350 System.Void VRAssemblyInputManager::SetAdjustViewKey(UnityEngine.KeyCode)
extern void VRAssemblyInputManager_SetAdjustViewKey_m4CD9D688CC342CB065496781B0263907C71D3415 (void);
// 0x00000351 System.Boolean VRAssemblyInputManager::IsInputEnabled()
extern void VRAssemblyInputManager_IsInputEnabled_mB36B4B78547223AC46C3B29212BBAD8AD8EDFDA6 (void);
// 0x00000352 UnityEngine.KeyCode VRAssemblyInputManager::GetAdjustViewKey()
extern void VRAssemblyInputManager_GetAdjustViewKey_m1F671ABA14E0F0F762D8DF3431A60F6ECE65FB0A (void);
// 0x00000353 System.Void VRAssemblyInputManager::ShowConfigInfo()
extern void VRAssemblyInputManager_ShowConfigInfo_mAE157D5244A6CAA8CB37FD237DABCD39A11815F8 (void);
// 0x00000354 System.Void VRAssemblyInputManager::ManualTriggerViewAdjustment()
extern void VRAssemblyInputManager_ManualTriggerViewAdjustment_mBA2B0E3CB91C06DF5386F52DEF8259F0E30B2699 (void);
// 0x00000355 System.Void VRAssemblyInputManager::.ctor()
extern void VRAssemblyInputManager__ctor_mE112945EA6B078E0A6C7847779FC55D210937E1C (void);
// 0x00000356 System.Void VRAssemblyManager::Start()
extern void VRAssemblyManager_Start_mDA576639DB8A0362297780138D4F3BC36920A994 (void);
// 0x00000357 System.Void VRAssemblyManager::InitializeVRAssembly()
extern void VRAssemblyManager_InitializeVRAssembly_m61B42336F65A0C38B9B418AC8570C935F1DA2467 (void);
// 0x00000358 System.Void VRAssemblyManager::ValidateComponents()
extern void VRAssemblyManager_ValidateComponents_m0F445CC8D29914F78350C91C215AC8A0702AFE15 (void);
// 0x00000359 System.Void VRAssemblyManager::RegisterEvents()
extern void VRAssemblyManager_RegisterEvents_m2955263D52A07D862808B447F67DB13A10D1CC3A (void);
// 0x0000035A System.Void VRAssemblyManager::StartVRAssembly()
extern void VRAssemblyManager_StartVRAssembly_mDD774F0335B0D92CC78ECD48E28FE47DF484BFE2 (void);
// 0x0000035B System.Collections.IEnumerator VRAssemblyManager::VRAssemblySequence()
extern void VRAssemblyManager_VRAssemblySequence_m4F60D5FAF4CA6701136808FE51BD120DB956FBCF (void);
// 0x0000035C System.Void VRAssemblyManager::OnAssemblyStepStart(AssemblyPart,AssemblyPart,System.String)
extern void VRAssemblyManager_OnAssemblyStepStart_m68CEC5F770F53F8920BC948047D761031F8C6D56 (void);
// 0x0000035D System.Collections.IEnumerator VRAssemblyManager::HandleVRAssemblyStep(AssemblyPart,AssemblyPart,System.String)
extern void VRAssemblyManager_HandleVRAssemblyStep_m0230321D5CC43CA35079CDA265EE3A58A81F0DFB (void);
// 0x0000035E System.Boolean VRAssemblyManager::get_IsVRPreparationComplete()
extern void VRAssemblyManager_get_IsVRPreparationComplete_mB97875686DCB650689742F75CD81C51FD8346F74 (void);
// 0x0000035F System.Collections.IEnumerator VRAssemblyManager::WaitForVRPreparationComplete()
extern void VRAssemblyManager_WaitForVRPreparationComplete_mDB066982391FD0FFCF4B8D5740CA3D5A23705A48 (void);
// 0x00000360 System.Void VRAssemblyManager::OnAssemblyStepEnd()
extern void VRAssemblyManager_OnAssemblyStepEnd_mAB514E9B1D4F5C641513091A2E6EA1B88EE73021 (void);
// 0x00000361 System.Boolean VRAssemblyManager::ShouldProvideGuidance(AssemblyPart,AssemblyPart)
extern void VRAssemblyManager_ShouldProvideGuidance_m31AA3A4E040B7D4DDD7282F100EE48617A63248B (void);
// 0x00000362 UnityEngine.Vector3 VRAssemblyManager::CalculateOptimalViewingPosition(AssemblyPart,AssemblyPart)
extern void VRAssemblyManager_CalculateOptimalViewingPosition_m60BD83EDB1B475046C19BB370054F21C9EB514DD (void);
// 0x00000363 System.Collections.IEnumerator VRAssemblyManager::WaitForUserConfirmation(System.String)
extern void VRAssemblyManager_WaitForUserConfirmation_mE0967F620CDABE033D93E2E06F8235D0CF79EE9D (void);
// 0x00000364 System.Void VRAssemblyManager::SetVRMode(System.Boolean)
extern void VRAssemblyManager_SetVRMode_m569524A5A1C7422D5C9B7186A742C0AD971D1016 (void);
// 0x00000365 System.Void VRAssemblyManager::SetAutoPositioning(System.Boolean)
extern void VRAssemblyManager_SetAutoPositioning_mBE16B86A3539819DDBE0A4C6B7CC7E923714E3F8 (void);
// 0x00000366 System.Void VRAssemblyManager::SetPreviewEnabled(System.Boolean)
extern void VRAssemblyManager_SetPreviewEnabled_m74C5E6007111F93F5D1AEAD155144F1A35815495 (void);
// 0x00000367 System.Void VRAssemblyManager::SetGuidanceEnabled(System.Boolean)
extern void VRAssemblyManager_SetGuidanceEnabled_m8EF37827F65350446E0B3A23699BB276AECF6E1E (void);
// 0x00000368 System.Void VRAssemblyManager::EmergencyStop()
extern void VRAssemblyManager_EmergencyStop_m81E3F4576F8183D2094D9B11DBFB734B9E2E9614 (void);
// 0x00000369 System.Boolean VRAssemblyManager::get_IsVRActive()
extern void VRAssemblyManager_get_IsVRActive_m7DAF896FB9B21197C36955BD5E67E4700AFD03DB (void);
// 0x0000036A System.Void VRAssemblyManager::Update()
extern void VRAssemblyManager_Update_m7BD02B95DCF357D82EFFB9FC7EE883C46CA96266 (void);
// 0x0000036B System.Void VRAssemblyManager::OnDestroy()
extern void VRAssemblyManager_OnDestroy_mC7E461C5726252E563A6CB849A40DF1AA8D2C26D (void);
// 0x0000036C System.Void VRAssemblyManager::.ctor()
extern void VRAssemblyManager__ctor_mB8DFC25C0B600A60B3AE7F237FFAEF2A280163CB (void);
// 0x0000036D System.Void VRAssemblyManager/<VRAssemblySequence>d__20::.ctor(System.Int32)
extern void U3CVRAssemblySequenceU3Ed__20__ctor_m15C1BA205A65889F3B1AA4687FA21CF3CBDB15E5 (void);
// 0x0000036E System.Void VRAssemblyManager/<VRAssemblySequence>d__20::System.IDisposable.Dispose()
extern void U3CVRAssemblySequenceU3Ed__20_System_IDisposable_Dispose_m4E450CF9D5A7DAA93754195466037BBA4332AC7A (void);
// 0x0000036F System.Boolean VRAssemblyManager/<VRAssemblySequence>d__20::MoveNext()
extern void U3CVRAssemblySequenceU3Ed__20_MoveNext_m94504F439833F8C405887E9B22DCA8D74C74F110 (void);
// 0x00000370 System.Object VRAssemblyManager/<VRAssemblySequence>d__20::System.Collections.Generic.IEnumerator<System.Object>.get_Current()
extern void U3CVRAssemblySequenceU3Ed__20_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m7C9ADD12540525BB4E1E21A8EA0298FA27BDD0EE (void);
// 0x00000371 System.Void VRAssemblyManager/<VRAssemblySequence>d__20::System.Collections.IEnumerator.Reset()
extern void U3CVRAssemblySequenceU3Ed__20_System_Collections_IEnumerator_Reset_m25E4812F67F15F87525A091CFC2800258B5EC185 (void);
// 0x00000372 System.Object VRAssemblyManager/<VRAssemblySequence>d__20::System.Collections.IEnumerator.get_Current()
extern void U3CVRAssemblySequenceU3Ed__20_System_Collections_IEnumerator_get_Current_m37BD81E157F96F2B0D238706E54D60333BA4FF8D (void);
// 0x00000373 System.Void VRAssemblyManager/<HandleVRAssemblyStep>d__22::.ctor(System.Int32)
extern void U3CHandleVRAssemblyStepU3Ed__22__ctor_m5B8BD705CB5456122031EAC852D509B48AFC7BDC (void);
// 0x00000374 System.Void VRAssemblyManager/<HandleVRAssemblyStep>d__22::System.IDisposable.Dispose()
extern void U3CHandleVRAssemblyStepU3Ed__22_System_IDisposable_Dispose_mC252729B34F64E9BE86FD0F29859A4342EEFFFC3 (void);
// 0x00000375 System.Boolean VRAssemblyManager/<HandleVRAssemblyStep>d__22::MoveNext()
extern void U3CHandleVRAssemblyStepU3Ed__22_MoveNext_mC4148A804FBEEDFE908BBDC1A5E0812BC93B3A44 (void);
// 0x00000376 System.Object VRAssemblyManager/<HandleVRAssemblyStep>d__22::System.Collections.Generic.IEnumerator<System.Object>.get_Current()
extern void U3CHandleVRAssemblyStepU3Ed__22_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m91CB3B7FBE56222FB39BB88518933448D3CE5638 (void);
// 0x00000377 System.Void VRAssemblyManager/<HandleVRAssemblyStep>d__22::System.Collections.IEnumerator.Reset()
extern void U3CHandleVRAssemblyStepU3Ed__22_System_Collections_IEnumerator_Reset_m616ED41E4B560E64832BE9DAC1166B4A548B9A79 (void);
// 0x00000378 System.Object VRAssemblyManager/<HandleVRAssemblyStep>d__22::System.Collections.IEnumerator.get_Current()
extern void U3CHandleVRAssemblyStepU3Ed__22_System_Collections_IEnumerator_get_Current_mFAF826A344407DA9BFFA9CFF231756CF4BDF86D6 (void);
// 0x00000379 System.Void VRAssemblyManager/<WaitForVRPreparationComplete>d__25::.ctor(System.Int32)
extern void U3CWaitForVRPreparationCompleteU3Ed__25__ctor_mB6081A3F963A624E483D6E2CB20EB43B42D6D259 (void);
// 0x0000037A System.Void VRAssemblyManager/<WaitForVRPreparationComplete>d__25::System.IDisposable.Dispose()
extern void U3CWaitForVRPreparationCompleteU3Ed__25_System_IDisposable_Dispose_m14D28E34281E8A0D815BD510A4BE3EEDC50BD334 (void);
// 0x0000037B System.Boolean VRAssemblyManager/<WaitForVRPreparationComplete>d__25::MoveNext()
extern void U3CWaitForVRPreparationCompleteU3Ed__25_MoveNext_m974547382090787B2AC4948000C97135A3D19717 (void);
// 0x0000037C System.Object VRAssemblyManager/<WaitForVRPreparationComplete>d__25::System.Collections.Generic.IEnumerator<System.Object>.get_Current()
extern void U3CWaitForVRPreparationCompleteU3Ed__25_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m87E49B1C17012F82B835A84F695CCE93669C23C9 (void);
// 0x0000037D System.Void VRAssemblyManager/<WaitForVRPreparationComplete>d__25::System.Collections.IEnumerator.Reset()
extern void U3CWaitForVRPreparationCompleteU3Ed__25_System_Collections_IEnumerator_Reset_m0DE129C8A70A9F4C2EC4EE4010E1BDE0D735630F (void);
// 0x0000037E System.Object VRAssemblyManager/<WaitForVRPreparationComplete>d__25::System.Collections.IEnumerator.get_Current()
extern void U3CWaitForVRPreparationCompleteU3Ed__25_System_Collections_IEnumerator_get_Current_m9AF6C3D510AEDD059A65F4808C80017060DA52A6 (void);
// 0x0000037F System.Void VRAssemblyManager/<WaitForUserConfirmation>d__29::.ctor(System.Int32)
extern void U3CWaitForUserConfirmationU3Ed__29__ctor_m828B8C111734364C413EEA80DBAE7818B6693AAB (void);
// 0x00000380 System.Void VRAssemblyManager/<WaitForUserConfirmation>d__29::System.IDisposable.Dispose()
extern void U3CWaitForUserConfirmationU3Ed__29_System_IDisposable_Dispose_m8B19E83C5666C051EB8ABA41181E537E72D7852C (void);
// 0x00000381 System.Boolean VRAssemblyManager/<WaitForUserConfirmation>d__29::MoveNext()
extern void U3CWaitForUserConfirmationU3Ed__29_MoveNext_m9A99851260094FB73B76C0B38B120393B41E223D (void);
// 0x00000382 System.Object VRAssemblyManager/<WaitForUserConfirmation>d__29::System.Collections.Generic.IEnumerator<System.Object>.get_Current()
extern void U3CWaitForUserConfirmationU3Ed__29_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mE2C104580BEDF91066B06522A1CC39F5FF5DE8B9 (void);
// 0x00000383 System.Void VRAssemblyManager/<WaitForUserConfirmation>d__29::System.Collections.IEnumerator.Reset()
extern void U3CWaitForUserConfirmationU3Ed__29_System_Collections_IEnumerator_Reset_m20CAE4230AA07AF2942969D9850D2EF181B074DA (void);
// 0x00000384 System.Object VRAssemblyManager/<WaitForUserConfirmation>d__29::System.Collections.IEnumerator.get_Current()
extern void U3CWaitForUserConfirmationU3Ed__29_System_Collections_IEnumerator_get_Current_m9F68820E433A8DAA48D59E06547A2A4C41734743 (void);
// 0x00000385 UnityEngine.Quaternion VRAssemblyOrientationHelper::CalculateOrientationToCamera(UnityEngine.Transform,UnityEngine.Transform)
extern void VRAssemblyOrientationHelper_CalculateOrientationToCamera_mF47564ED969D38B30467970E816E9B3173192493 (void);
// 0x00000386 UnityEngine.Quaternion VRAssemblyOrientationHelper::CalculateOptimalOrientationToCamera(UnityEngine.Transform,UnityEngine.Transform)
extern void VRAssemblyOrientationHelper_CalculateOptimalOrientationToCamera_m049E4AF65711CD58D2666CFFCCBD2AB9CBAA8642 (void);
// 0x00000387 System.Void VRAssemblyOrientationHelper::TestAssemblyFaceOrientation()
extern void VRAssemblyOrientationHelper_TestAssemblyFaceOrientation_mEC996FE985F0125881DC009D65528AA547AC7695 (void);
// 0x00000388 System.Void VRAssemblyOrientationHelper::ApplyOrientationToAssemblyRoot(UnityEngine.Transform,UnityEngine.Transform,UnityEngine.Transform,System.Boolean)
extern void VRAssemblyOrientationHelper_ApplyOrientationToAssemblyRoot_mFB1217FF274605584C302693443A8DE70B8395AD (void);
// 0x00000389 System.Void VRAssemblyOrientationHelper::.ctor()
extern void VRAssemblyOrientationHelper__ctor_mE949CDCE7B99F7F2DA680CBF98BE4940A8722DAF (void);
// 0x0000038A System.Void VRAssemblyPositioner::Start()
extern void VRAssemblyPositioner_Start_mFFF6952551522C27AD0B0E56278CFC8B8941FD26 (void);
// 0x0000038B System.Void VRAssemblyPositioner::InitializePositioner()
extern void VRAssemblyPositioner_InitializePositioner_m9124E7DB33BAAF7E4A553CB33D0D19AACAFFA47A (void);
// 0x0000038C UnityEngine.Transform VRAssemblyPositioner::FindAssemblyRoot()
extern void VRAssemblyPositioner_FindAssemblyRoot_m76F73E3280D092430590A3C5935EA6906082FC54 (void);
// 0x0000038D UnityEngine.Transform VRAssemblyPositioner::FindCommonParent(AssemblyPart[])
extern void VRAssemblyPositioner_FindCommonParent_mFD993971AE6B55D04A2F1BB88F3BFF82BE71DBF5 (void);
// 0x0000038E UnityEngine.Transform VRAssemblyPositioner::FindCommonAncestor(UnityEngine.Transform,UnityEngine.Transform)
extern void VRAssemblyPositioner_FindCommonAncestor_m8BF526F0357F659DBA58046175FF095B320458B7 (void);
// 0x0000038F UnityEngine.Vector3 VRAssemblyPositioner::CalculateOptimalAssemblyPosition()
extern void VRAssemblyPositioner_CalculateOptimalAssemblyPosition_mCCCA0B80E9FB0D9CBC24BA71FAE44CDC2E9C39C0 (void);
// 0x00000390 System.ValueTuple`2<UnityEngine.Vector3,UnityEngine.Quaternion> VRAssemblyPositioner::CalculateOptimalPositionForAssemblyPoint(AssemblyPart,AssemblyPart,System.Int32,System.Int32)
extern void VRAssemblyPositioner_CalculateOptimalPositionForAssemblyPoint_mF76630F9A037F2F91553F2827D908FA293BDDBEB (void);
// 0x00000391 UnityEngine.Quaternion VRAssemblyPositioner::CalculateOptimalAssemblyRotation(UnityEngine.Vector3)
extern void VRAssemblyPositioner_CalculateOptimalAssemblyRotation_m1B4D78A138191B6394B7C26C3C2D0A7E4FF01FF2 (void);
// 0x00000392 UnityEngine.Quaternion VRAssemblyPositioner::CalculateSimpleOrientationToCamera(UnityEngine.Transform,UnityEngine.Transform)
extern void VRAssemblyPositioner_CalculateSimpleOrientationToCamera_mB325A81E6266A102E64FD0399CA9E1600FFE81F3 (void);
// 0x00000393 System.Collections.IEnumerator VRAssemblyPositioner::AlignMountPointToCameraByRotatingAssemblyRoot(AssemblyPart,System.Int32,UnityEngine.Transform,System.Boolean)
extern void VRAssemblyPositioner_AlignMountPointToCameraByRotatingAssemblyRoot_m4C5C316E1F8245C13EC6254EA57E8036216077DB (void);
// 0x00000394 System.Collections.IEnumerator VRAssemblyPositioner::AdjustViewForUser()
extern void VRAssemblyPositioner_AdjustViewForUser_mC79C505C188EA75655EABB206EF0FA4C2E151118 (void);
// 0x00000395 System.Collections.IEnumerator VRAssemblyPositioner::AdjustOrientationForAssemblyStep(System.String,System.Int32,System.Boolean)
extern void VRAssemblyPositioner_AdjustOrientationForAssemblyStep_m7CB1A2538FBC14BE0B4DAFCF6F0279783B6B0D6F (void);
// 0x00000396 AssemblyPart VRAssemblyPositioner::FindAssemblyPartByName(System.String)
extern void VRAssemblyPositioner_FindAssemblyPartByName_mFC89B480A960886DB31C7EE3AF53E4813A11E33A (void);
// 0x00000397 UnityEngine.Transform VRAssemblyPositioner::GetCurrentCamera()
extern void VRAssemblyPositioner_GetCurrentCamera_mCFCABE3ADCC01A83A7D5AA9293E9268E4383E727 (void);
// 0x00000398 UnityEngine.Quaternion VRAssemblyPositioner::CalculateConfigurableOrientationToCamera(UnityEngine.Transform,UnityEngine.Transform,System.Boolean)
extern void VRAssemblyPositioner_CalculateConfigurableOrientationToCamera_mA0A6EE3AFD791D09A5C27FF83B58706DC252B755 (void);
// 0x00000399 System.Collections.IEnumerator VRAssemblyPositioner::MoveAssemblyToOptimalPosition()
extern void VRAssemblyPositioner_MoveAssemblyToOptimalPosition_m64DD6F7BC72200DCB2B5D1495437C304CF1BF1FA (void);
// 0x0000039A System.Collections.IEnumerator VRAssemblyPositioner::AdjustOrientationForAssemblyStep(AssemblyPart,AssemblyPart)
extern void VRAssemblyPositioner_AdjustOrientationForAssemblyStep_m893501C12F225D2ADF3A2AA5A69285FBB35B833E (void);
// 0x0000039B System.Collections.IEnumerator VRAssemblyPositioner::AdjustOrientationForAssemblyStepSimple(AssemblyPart,AssemblyPart)
extern void VRAssemblyPositioner_AdjustOrientationForAssemblyStepSimple_m45A85A5F4AEE0991EED1A32389FA0BC4C9C5C4A5 (void);
// 0x0000039C System.Collections.IEnumerator VRAssemblyPositioner::RotateAssemblyToOrientation(UnityEngine.Quaternion,System.Single)
extern void VRAssemblyPositioner_RotateAssemblyToOrientation_m903DDB21A7FB97B74763E9CD47A81045A9985342 (void);
// 0x0000039D System.Collections.IEnumerator VRAssemblyPositioner::RestoreOriginalPosition()
extern void VRAssemblyPositioner_RestoreOriginalPosition_m229244D2F6DC89EE58101C4E71A089FF5CC5EBAB (void);
// 0x0000039E System.Collections.IEnumerator VRAssemblyPositioner::MoveAssemblyToPosition(UnityEngine.Vector3,UnityEngine.Quaternion)
extern void VRAssemblyPositioner_MoveAssemblyToPosition_m3265AEF75C780D7AF67AF754063FC623CFD9D381 (void);
// 0x0000039F System.Void VRAssemblyPositioner::OnAssemblyStart()
extern void VRAssemblyPositioner_OnAssemblyStart_m1CC06174648066EB18079253DAEC0CF93F6CDCE4 (void);
// 0x000003A0 System.Void VRAssemblyPositioner::OnAssemblyStepStart(AssemblyPart,AssemblyPart)
extern void VRAssemblyPositioner_OnAssemblyStepStart_mB2FDD80F3FF3212DA2D1DAB197AACFAC04C4DC54 (void);
// 0x000003A1 System.Void VRAssemblyPositioner::OnAssemblyEnd()
extern void VRAssemblyPositioner_OnAssemblyEnd_m05C00CAB697658AA0CA5145709B5828DD36B3807 (void);
// 0x000003A2 System.Void VRAssemblyPositioner::OnDrawGizmos()
extern void VRAssemblyPositioner_OnDrawGizmos_mD45B6543C4D4D7B39E5FB7C31F5FEF7B7D814AFE (void);
// 0x000003A3 System.Void VRAssemblyPositioner::.ctor()
extern void VRAssemblyPositioner__ctor_mBDA40D1EDDAD06C5DF5C442F6384186127C9F720 (void);
// 0x000003A4 System.Void VRAssemblyPositioner/<AlignMountPointToCameraByRotatingAssemblyRoot>d__21::.ctor(System.Int32)
extern void U3CAlignMountPointToCameraByRotatingAssemblyRootU3Ed__21__ctor_m0D0B0C195FCC2C55AA072ECC3862628B281A9D11 (void);
// 0x000003A5 System.Void VRAssemblyPositioner/<AlignMountPointToCameraByRotatingAssemblyRoot>d__21::System.IDisposable.Dispose()
extern void U3CAlignMountPointToCameraByRotatingAssemblyRootU3Ed__21_System_IDisposable_Dispose_mD275EFBB76EF555B83760CB9046C83B871785FBB (void);
// 0x000003A6 System.Boolean VRAssemblyPositioner/<AlignMountPointToCameraByRotatingAssemblyRoot>d__21::MoveNext()
extern void U3CAlignMountPointToCameraByRotatingAssemblyRootU3Ed__21_MoveNext_mC27FF8AD8F88F10E3E52A291E183C5A0CA66D378 (void);
// 0x000003A7 System.Object VRAssemblyPositioner/<AlignMountPointToCameraByRotatingAssemblyRoot>d__21::System.Collections.Generic.IEnumerator<System.Object>.get_Current()
extern void U3CAlignMountPointToCameraByRotatingAssemblyRootU3Ed__21_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m72870BED84F078D5D9CEF398B90449A8118FB154 (void);
// 0x000003A8 System.Void VRAssemblyPositioner/<AlignMountPointToCameraByRotatingAssemblyRoot>d__21::System.Collections.IEnumerator.Reset()
extern void U3CAlignMountPointToCameraByRotatingAssemblyRootU3Ed__21_System_Collections_IEnumerator_Reset_mBD2FD28D6BB83701D6B287AF9D6FD9EBDB1744FE (void);
// 0x000003A9 System.Object VRAssemblyPositioner/<AlignMountPointToCameraByRotatingAssemblyRoot>d__21::System.Collections.IEnumerator.get_Current()
extern void U3CAlignMountPointToCameraByRotatingAssemblyRootU3Ed__21_System_Collections_IEnumerator_get_Current_m6D3579E96F47F1CA0C3D4FADF6932540CE116777 (void);
// 0x000003AA System.Void VRAssemblyPositioner/<AdjustViewForUser>d__22::.ctor(System.Int32)
extern void U3CAdjustViewForUserU3Ed__22__ctor_m006678A83626B4118E9B48C8834C283A399CF69A (void);
// 0x000003AB System.Void VRAssemblyPositioner/<AdjustViewForUser>d__22::System.IDisposable.Dispose()
extern void U3CAdjustViewForUserU3Ed__22_System_IDisposable_Dispose_mB593F54BDCD8AC0E1CC9500E2D7D43EE916CCA8E (void);
// 0x000003AC System.Boolean VRAssemblyPositioner/<AdjustViewForUser>d__22::MoveNext()
extern void U3CAdjustViewForUserU3Ed__22_MoveNext_mF5D76945924B72DD55643866906B3FAAC8665913 (void);
// 0x000003AD System.Object VRAssemblyPositioner/<AdjustViewForUser>d__22::System.Collections.Generic.IEnumerator<System.Object>.get_Current()
extern void U3CAdjustViewForUserU3Ed__22_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m9525B598EB0788482F6E660F0DD6398E62D3A450 (void);
// 0x000003AE System.Void VRAssemblyPositioner/<AdjustViewForUser>d__22::System.Collections.IEnumerator.Reset()
extern void U3CAdjustViewForUserU3Ed__22_System_Collections_IEnumerator_Reset_m17778E32EB826F2FBC94DD0D4442326D189DACAE (void);
// 0x000003AF System.Object VRAssemblyPositioner/<AdjustViewForUser>d__22::System.Collections.IEnumerator.get_Current()
extern void U3CAdjustViewForUserU3Ed__22_System_Collections_IEnumerator_get_Current_m1A8EEC0A5344E81D4FB3DABC780690563123AFC5 (void);
// 0x000003B0 System.Void VRAssemblyPositioner/<AdjustOrientationForAssemblyStep>d__23::.ctor(System.Int32)
extern void U3CAdjustOrientationForAssemblyStepU3Ed__23__ctor_m75AC51E639A3509D82202FAF8ECCEAC579740E26 (void);
// 0x000003B1 System.Void VRAssemblyPositioner/<AdjustOrientationForAssemblyStep>d__23::System.IDisposable.Dispose()
extern void U3CAdjustOrientationForAssemblyStepU3Ed__23_System_IDisposable_Dispose_mB6CBC1270D197E10FCCB502F48CBCDE46D7EE528 (void);
// 0x000003B2 System.Boolean VRAssemblyPositioner/<AdjustOrientationForAssemblyStep>d__23::MoveNext()
extern void U3CAdjustOrientationForAssemblyStepU3Ed__23_MoveNext_m2A0113A7AD49513801B6DC2923BA555882E62C7E (void);
// 0x000003B3 System.Object VRAssemblyPositioner/<AdjustOrientationForAssemblyStep>d__23::System.Collections.Generic.IEnumerator<System.Object>.get_Current()
extern void U3CAdjustOrientationForAssemblyStepU3Ed__23_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m68E2F9F06421CDDC956139C23A66C6226F60D96A (void);
// 0x000003B4 System.Void VRAssemblyPositioner/<AdjustOrientationForAssemblyStep>d__23::System.Collections.IEnumerator.Reset()
extern void U3CAdjustOrientationForAssemblyStepU3Ed__23_System_Collections_IEnumerator_Reset_m27AF98F6C2E32EA7C3DB046B287D21D4BD916D91 (void);
// 0x000003B5 System.Object VRAssemblyPositioner/<AdjustOrientationForAssemblyStep>d__23::System.Collections.IEnumerator.get_Current()
extern void U3CAdjustOrientationForAssemblyStepU3Ed__23_System_Collections_IEnumerator_get_Current_mC92DDC079FD5FA257BE79FF918079A209B053A5F (void);
// 0x000003B6 System.Void VRAssemblyPositioner/<MoveAssemblyToOptimalPosition>d__27::.ctor(System.Int32)
extern void U3CMoveAssemblyToOptimalPositionU3Ed__27__ctor_mF2989CE76E89429FECCFD292F46546426A0BD51D (void);
// 0x000003B7 System.Void VRAssemblyPositioner/<MoveAssemblyToOptimalPosition>d__27::System.IDisposable.Dispose()
extern void U3CMoveAssemblyToOptimalPositionU3Ed__27_System_IDisposable_Dispose_m040CC09EF1C1C55E0A1429C1C40AA6D4ACD18992 (void);
// 0x000003B8 System.Boolean VRAssemblyPositioner/<MoveAssemblyToOptimalPosition>d__27::MoveNext()
extern void U3CMoveAssemblyToOptimalPositionU3Ed__27_MoveNext_m5C84A6F53876BCAAB8462568964A8CD5B9959E1B (void);
// 0x000003B9 System.Object VRAssemblyPositioner/<MoveAssemblyToOptimalPosition>d__27::System.Collections.Generic.IEnumerator<System.Object>.get_Current()
extern void U3CMoveAssemblyToOptimalPositionU3Ed__27_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mC25F303E68D8FD213AD91A8AB6A1664B9F46AC1F (void);
// 0x000003BA System.Void VRAssemblyPositioner/<MoveAssemblyToOptimalPosition>d__27::System.Collections.IEnumerator.Reset()
extern void U3CMoveAssemblyToOptimalPositionU3Ed__27_System_Collections_IEnumerator_Reset_m65FF3DC73365597E0EF08CB82AB70FE8FBCF9E2E (void);
// 0x000003BB System.Object VRAssemblyPositioner/<MoveAssemblyToOptimalPosition>d__27::System.Collections.IEnumerator.get_Current()
extern void U3CMoveAssemblyToOptimalPositionU3Ed__27_System_Collections_IEnumerator_get_Current_m029D021F3BBF7BAFD5E3F6877BF280E47EE6737D (void);
// 0x000003BC System.Void VRAssemblyPositioner/<AdjustOrientationForAssemblyStep>d__28::.ctor(System.Int32)
extern void U3CAdjustOrientationForAssemblyStepU3Ed__28__ctor_m478B426DA67564F70769C7A8DCC77DE206C9296B (void);
// 0x000003BD System.Void VRAssemblyPositioner/<AdjustOrientationForAssemblyStep>d__28::System.IDisposable.Dispose()
extern void U3CAdjustOrientationForAssemblyStepU3Ed__28_System_IDisposable_Dispose_mCF1920AC9A0761E522D9FFBE517B830A0E6E09EA (void);
// 0x000003BE System.Boolean VRAssemblyPositioner/<AdjustOrientationForAssemblyStep>d__28::MoveNext()
extern void U3CAdjustOrientationForAssemblyStepU3Ed__28_MoveNext_m7F684594D13601CDB11CD33A337A026CCFCBE4EF (void);
// 0x000003BF System.Object VRAssemblyPositioner/<AdjustOrientationForAssemblyStep>d__28::System.Collections.Generic.IEnumerator<System.Object>.get_Current()
extern void U3CAdjustOrientationForAssemblyStepU3Ed__28_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mA6FDFBF40E74D2CF45C6C8AA05D54FE498D0CA7D (void);
// 0x000003C0 System.Void VRAssemblyPositioner/<AdjustOrientationForAssemblyStep>d__28::System.Collections.IEnumerator.Reset()
extern void U3CAdjustOrientationForAssemblyStepU3Ed__28_System_Collections_IEnumerator_Reset_m08D72A9C82C45E47C81AD2A5F18EECE757317490 (void);
// 0x000003C1 System.Object VRAssemblyPositioner/<AdjustOrientationForAssemblyStep>d__28::System.Collections.IEnumerator.get_Current()
extern void U3CAdjustOrientationForAssemblyStepU3Ed__28_System_Collections_IEnumerator_get_Current_m7D7EC06E9FAFA81FF34F504DD9D2F105C59CCF5F (void);
// 0x000003C2 System.Void VRAssemblyPositioner/<AdjustOrientationForAssemblyStepSimple>d__29::.ctor(System.Int32)
extern void U3CAdjustOrientationForAssemblyStepSimpleU3Ed__29__ctor_mADF6F2936523932BC402AD3721A098844C8419B5 (void);
// 0x000003C3 System.Void VRAssemblyPositioner/<AdjustOrientationForAssemblyStepSimple>d__29::System.IDisposable.Dispose()
extern void U3CAdjustOrientationForAssemblyStepSimpleU3Ed__29_System_IDisposable_Dispose_mAD796BF9E9853F1DC3634FABD1C54A8918968F96 (void);
// 0x000003C4 System.Boolean VRAssemblyPositioner/<AdjustOrientationForAssemblyStepSimple>d__29::MoveNext()
extern void U3CAdjustOrientationForAssemblyStepSimpleU3Ed__29_MoveNext_mDC6ACD100243A12AC9AD74433CA7052F8C161322 (void);
// 0x000003C5 System.Object VRAssemblyPositioner/<AdjustOrientationForAssemblyStepSimple>d__29::System.Collections.Generic.IEnumerator<System.Object>.get_Current()
extern void U3CAdjustOrientationForAssemblyStepSimpleU3Ed__29_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m686C03651D73FBCE812B60EBC0AA79BB8C76707B (void);
// 0x000003C6 System.Void VRAssemblyPositioner/<AdjustOrientationForAssemblyStepSimple>d__29::System.Collections.IEnumerator.Reset()
extern void U3CAdjustOrientationForAssemblyStepSimpleU3Ed__29_System_Collections_IEnumerator_Reset_m45A1412C3E752EC244BA907D757D5351F56AB467 (void);
// 0x000003C7 System.Object VRAssemblyPositioner/<AdjustOrientationForAssemblyStepSimple>d__29::System.Collections.IEnumerator.get_Current()
extern void U3CAdjustOrientationForAssemblyStepSimpleU3Ed__29_System_Collections_IEnumerator_get_Current_m88D91229D28C156D57D2358A3771A1C0DDFEA2DD (void);
// 0x000003C8 System.Void VRAssemblyPositioner/<RotateAssemblyToOrientation>d__30::.ctor(System.Int32)
extern void U3CRotateAssemblyToOrientationU3Ed__30__ctor_m7B8DAFB80E21B1B53DEBFBD384F9BF86AAA0F147 (void);
// 0x000003C9 System.Void VRAssemblyPositioner/<RotateAssemblyToOrientation>d__30::System.IDisposable.Dispose()
extern void U3CRotateAssemblyToOrientationU3Ed__30_System_IDisposable_Dispose_m939C4A22BD5B47EDB386FCB36DB15CA9103F11BF (void);
// 0x000003CA System.Boolean VRAssemblyPositioner/<RotateAssemblyToOrientation>d__30::MoveNext()
extern void U3CRotateAssemblyToOrientationU3Ed__30_MoveNext_m89840F02335F25724577553BB0F98938DC5396A7 (void);
// 0x000003CB System.Object VRAssemblyPositioner/<RotateAssemblyToOrientation>d__30::System.Collections.Generic.IEnumerator<System.Object>.get_Current()
extern void U3CRotateAssemblyToOrientationU3Ed__30_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m9957A50D7FFE8FCBF787F150F08B29E8C9A12C3E (void);
// 0x000003CC System.Void VRAssemblyPositioner/<RotateAssemblyToOrientation>d__30::System.Collections.IEnumerator.Reset()
extern void U3CRotateAssemblyToOrientationU3Ed__30_System_Collections_IEnumerator_Reset_mA3B3F7E80BA89970F06B335F2DEF61DC5382F552 (void);
// 0x000003CD System.Object VRAssemblyPositioner/<RotateAssemblyToOrientation>d__30::System.Collections.IEnumerator.get_Current()
extern void U3CRotateAssemblyToOrientationU3Ed__30_System_Collections_IEnumerator_get_Current_mAF40BAC1D918D12C264ED6A163C0D7FE04467025 (void);
// 0x000003CE System.Void VRAssemblyPositioner/<RestoreOriginalPosition>d__31::.ctor(System.Int32)
extern void U3CRestoreOriginalPositionU3Ed__31__ctor_m06EFB24057F46B530E96E42095C5E2362D416F07 (void);
// 0x000003CF System.Void VRAssemblyPositioner/<RestoreOriginalPosition>d__31::System.IDisposable.Dispose()
extern void U3CRestoreOriginalPositionU3Ed__31_System_IDisposable_Dispose_m2A69E59EC0674E66CE7BEDA074798713FF27CD37 (void);
// 0x000003D0 System.Boolean VRAssemblyPositioner/<RestoreOriginalPosition>d__31::MoveNext()
extern void U3CRestoreOriginalPositionU3Ed__31_MoveNext_m80D2FA6C72C39A0A2F83614CCBF460D87D756AF0 (void);
// 0x000003D1 System.Object VRAssemblyPositioner/<RestoreOriginalPosition>d__31::System.Collections.Generic.IEnumerator<System.Object>.get_Current()
extern void U3CRestoreOriginalPositionU3Ed__31_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m85C10E5BD35459696CD125F61ABB83EDF4E4FCD4 (void);
// 0x000003D2 System.Void VRAssemblyPositioner/<RestoreOriginalPosition>d__31::System.Collections.IEnumerator.Reset()
extern void U3CRestoreOriginalPositionU3Ed__31_System_Collections_IEnumerator_Reset_m32B03686DB82B27D976FD318D1F224ED9B619BAA (void);
// 0x000003D3 System.Object VRAssemblyPositioner/<RestoreOriginalPosition>d__31::System.Collections.IEnumerator.get_Current()
extern void U3CRestoreOriginalPositionU3Ed__31_System_Collections_IEnumerator_get_Current_m84B7DA2C6FBE436E8E986594089D5AF24D864FBB (void);
// 0x000003D4 System.Void VRAssemblyPositioner/<MoveAssemblyToPosition>d__32::.ctor(System.Int32)
extern void U3CMoveAssemblyToPositionU3Ed__32__ctor_mF47F7D46AF4B998252FC4221BD01E1F5A76D08DE (void);
// 0x000003D5 System.Void VRAssemblyPositioner/<MoveAssemblyToPosition>d__32::System.IDisposable.Dispose()
extern void U3CMoveAssemblyToPositionU3Ed__32_System_IDisposable_Dispose_mFAD0EF442D75C85F4B3AB09EC97831B6A2A7A86D (void);
// 0x000003D6 System.Boolean VRAssemblyPositioner/<MoveAssemblyToPosition>d__32::MoveNext()
extern void U3CMoveAssemblyToPositionU3Ed__32_MoveNext_m92BD3A350F9DBD14408C6194EF811A8929866C41 (void);
// 0x000003D7 System.Object VRAssemblyPositioner/<MoveAssemblyToPosition>d__32::System.Collections.Generic.IEnumerator<System.Object>.get_Current()
extern void U3CMoveAssemblyToPositionU3Ed__32_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m7ED8174506B440633059D41488C7870D6F75D53D (void);
// 0x000003D8 System.Void VRAssemblyPositioner/<MoveAssemblyToPosition>d__32::System.Collections.IEnumerator.Reset()
extern void U3CMoveAssemblyToPositionU3Ed__32_System_Collections_IEnumerator_Reset_mE6810E007FA9A72966ABA8F23CC76723DABE127B (void);
// 0x000003D9 System.Object VRAssemblyPositioner/<MoveAssemblyToPosition>d__32::System.Collections.IEnumerator.get_Current()
extern void U3CMoveAssemblyToPositionU3Ed__32_System_Collections_IEnumerator_get_Current_m8E567E0EFA0301F933E85C3639F4E49431E7D0A0 (void);
// 0x000003DA System.Void VRAssemblyPreview::Start()
extern void VRAssemblyPreview_Start_mED668E216CD1BF3238274F38FB189F86749D67D4 (void);
// 0x000003DB System.Void VRAssemblyPreview::InitializePreview()
extern void VRAssemblyPreview_InitializePreview_mDFF4DA69A92C3C640A66CE0485E05AB370ACF703 (void);
// 0x000003DC UnityEngine.Transform VRAssemblyPreview::FindVRControllerTransform()
extern void VRAssemblyPreview_FindVRControllerTransform_mAFFDB44E12C416D7C144012FDDFF63D731B7CA0E (void);
// 0x000003DD System.Void VRAssemblyPreview::CreateTransparentPreviewMaterial()
extern void VRAssemblyPreview_CreateTransparentPreviewMaterial_mE497510E391F2D45B8B5792CB5CDBEC2F665AAB4 (void);
// 0x000003DE UnityEngine.GameObject VRAssemblyPreview::CreateAssemblyPreview(AssemblyPart[],System.String)
extern void VRAssemblyPreview_CreateAssemblyPreview_mD5A7DCD6F4AF1B208E0BF28B8723C3D1FA161067 (void);
// 0x000003DF UnityEngine.GameObject VRAssemblyPreview::CreatePreviewPart(UnityEngine.GameObject,UnityEngine.Transform)
extern void VRAssemblyPreview_CreatePreviewPart_m4CFA31DC394DC9C74857A26BB7A4B0AC5C4803CE (void);
// 0x000003E0 System.Void VRAssemblyPreview::RemoveUnnecessaryComponents(UnityEngine.GameObject)
extern void VRAssemblyPreview_RemoveUnnecessaryComponents_m563B40629121E9F57F2F9371B41AF992EDFC9260 (void);
// 0x000003E1 System.Void VRAssemblyPreview::ApplyPreviewMaterial(UnityEngine.GameObject)
extern void VRAssemblyPreview_ApplyPreviewMaterial_m3384BA6F9D4268A6D2E2F156094CB725B3729724 (void);
// 0x000003E2 UnityEngine.Vector3 VRAssemblyPreview::CalculatePreviewPosition()
extern void VRAssemblyPreview_CalculatePreviewPosition_m82C1750BB96AB66FB89ABFCE5AB1B9040D9F3061 (void);
// 0x000003E3 System.Void VRAssemblyPreview::StartAutoRotation()
extern void VRAssemblyPreview_StartAutoRotation_m94C67A95F47BAE5E3A0E4592258CBF29F5FE9E7E (void);
// 0x000003E4 System.Collections.IEnumerator VRAssemblyPreview::AutoRotatePreview()
extern void VRAssemblyPreview_AutoRotatePreview_m36965A4615E1D102B093C8ADF3BC80401625343F (void);
// 0x000003E5 System.Void VRAssemblyPreview::StopAutoRotation()
extern void VRAssemblyPreview_StopAutoRotation_m997BA0A1A20DF95CD892A377F0BAF4DFCC6AA4D7 (void);
// 0x000003E6 System.Void VRAssemblyPreview::HighlightPart(System.String)
extern void VRAssemblyPreview_HighlightPart_mED7B531F23C5CA7259EA2981C38FD8BCF70EBE26 (void);
// 0x000003E7 System.Void VRAssemblyPreview::ApplyHighlightMaterial(UnityEngine.GameObject)
extern void VRAssemblyPreview_ApplyHighlightMaterial_mF7D0CB782613B0A798605598ACBD2A84D4AFC6D7 (void);
// 0x000003E8 System.Void VRAssemblyPreview::UpdatePreviewLabel(System.String)
extern void VRAssemblyPreview_UpdatePreviewLabel_mDDACD5208B82FAA0FB2E138005D6F656C62C7709 (void);
// 0x000003E9 System.Void VRAssemblyPreview::ClearPreview()
extern void VRAssemblyPreview_ClearPreview_m4C94B8C33901A3F0ACF93AC9431C3D0C97BC840D (void);
// 0x000003EA System.Void VRAssemblyPreview::SetPreviewVisible(System.Boolean)
extern void VRAssemblyPreview_SetPreviewVisible_m748E67C64D66E4A7E23E38DAE1FC705D6C0937A7 (void);
// 0x000003EB System.Void VRAssemblyPreview::Update()
extern void VRAssemblyPreview_Update_m08B0D97AC62AB87B51812F77552F9325944AA62B (void);
// 0x000003EC System.Void VRAssemblyPreview::CreateStepPreview(AssemblyPart,AssemblyPart,System.String)
extern void VRAssemblyPreview_CreateStepPreview_m0C8F9ACC6A716360C2C05840DE6C831A88F7107F (void);
// 0x000003ED System.Void VRAssemblyPreview::OnAssemblyStepEnd()
extern void VRAssemblyPreview_OnAssemblyStepEnd_mEDDD449732FEF79B41FB9C25EC54F56ABEE5BCA9 (void);
// 0x000003EE System.Void VRAssemblyPreview::OnDestroy()
extern void VRAssemblyPreview_OnDestroy_mF713FB4825A4B3722D6293AE530BD67592A58C42 (void);
// 0x000003EF System.Void VRAssemblyPreview::.ctor()
extern void VRAssemblyPreview__ctor_m64135A87EB8C83AEEAE0361EDA4D9B23F679ED71 (void);
// 0x000003F0 System.Void VRAssemblyPreview/<AutoRotatePreview>d__27::.ctor(System.Int32)
extern void U3CAutoRotatePreviewU3Ed__27__ctor_mA2DA3759648FF5E918D549B13DF0350BBCE2697D (void);
// 0x000003F1 System.Void VRAssemblyPreview/<AutoRotatePreview>d__27::System.IDisposable.Dispose()
extern void U3CAutoRotatePreviewU3Ed__27_System_IDisposable_Dispose_m677C9339AED9A4AE57E1E5B145D36F20BE8D224D (void);
// 0x000003F2 System.Boolean VRAssemblyPreview/<AutoRotatePreview>d__27::MoveNext()
extern void U3CAutoRotatePreviewU3Ed__27_MoveNext_m1D3C137D39CCB4D977B7264816433D6110B35BA6 (void);
// 0x000003F3 System.Object VRAssemblyPreview/<AutoRotatePreview>d__27::System.Collections.Generic.IEnumerator<System.Object>.get_Current()
extern void U3CAutoRotatePreviewU3Ed__27_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m8F92CD0884F8448279C86F50188C9BE659B93638 (void);
// 0x000003F4 System.Void VRAssemblyPreview/<AutoRotatePreview>d__27::System.Collections.IEnumerator.Reset()
extern void U3CAutoRotatePreviewU3Ed__27_System_Collections_IEnumerator_Reset_m80E579BC4C4F965234CD610773047C40FF719F5B (void);
// 0x000003F5 System.Object VRAssemblyPreview/<AutoRotatePreview>d__27::System.Collections.IEnumerator.get_Current()
extern void U3CAutoRotatePreviewU3Ed__27_System_Collections_IEnumerator_get_Current_mC019D63C87CE3E229D18D6E48A62D08E6295B529 (void);
// 0x000003F6 System.Void VRAssemblyStepManager::Start()
extern void VRAssemblyStepManager_Start_m72D6B27C41F36AFE890F7B37056B3ABE02FB78DF (void);
// 0x000003F7 System.Collections.IEnumerator VRAssemblyStepManager::PrepareAssemblyStep(System.String,System.Int32)
extern void VRAssemblyStepManager_PrepareAssemblyStep_mC750835B08545AACBDA36F421C977E18BAAD81B3 (void);
// 0x000003F8 System.Collections.IEnumerator VRAssemblyStepManager::PrepareAssemblyStep(System.String,System.Int32,System.Boolean)
extern void VRAssemblyStepManager_PrepareAssemblyStep_m28CA76769B5FF321AC452C60258EF6280381AD09 (void);
// 0x000003F9 System.Void VRAssemblyStepManager::SetAutoAdjustOrientation(System.Boolean)
extern void VRAssemblyStepManager_SetAutoAdjustOrientation_m1C11B0B943C64F84C28303644EB74763F19EB50D (void);
// 0x000003FA System.Void VRAssemblyStepManager::SetDefaultOrientationAxis(System.Boolean)
extern void VRAssemblyStepManager_SetDefaultOrientationAxis_mCAA7E4A6207F9682DE1C53B60B6CD085A602DDC8 (void);
// 0x000003FB System.Void VRAssemblyStepManager::SetDelayBeforeAnimation(System.Single)
extern void VRAssemblyStepManager_SetDelayBeforeAnimation_m35708D0C4A5C72490B86021A7A3D43CD8A9C2ED2 (void);
// 0x000003FC System.ValueTuple`3<System.Boolean,System.Boolean,System.Single> VRAssemblyStepManager::GetCurrentConfig()
extern void VRAssemblyStepManager_GetCurrentConfig_m441D5C2FFB1F9895ECC368223B9A20EB199CF5B4 (void);
// 0x000003FD System.Void VRAssemblyStepManager::ShowConfigInfo()
extern void VRAssemblyStepManager_ShowConfigInfo_m1DA49B5FF6DAAF830F30F97ADB163D06F47BFA53 (void);
// 0x000003FE System.Void VRAssemblyStepManager::TestPrepareAssemblyStep()
extern void VRAssemblyStepManager_TestPrepareAssemblyStep_m7A7F21A20B8C077A72E132692860D1F721C252BA (void);
// 0x000003FF System.Void VRAssemblyStepManager::.ctor()
extern void VRAssemblyStepManager__ctor_m1653B6CD8E821AE2C032729D4A23943C122C08D6 (void);
// 0x00000400 System.Void VRAssemblyStepManager/<PrepareAssemblyStep>d__6::.ctor(System.Int32)
extern void U3CPrepareAssemblyStepU3Ed__6__ctor_mE7075AE756C42E03383ABE6827CB451A2D02007E (void);
// 0x00000401 System.Void VRAssemblyStepManager/<PrepareAssemblyStep>d__6::System.IDisposable.Dispose()
extern void U3CPrepareAssemblyStepU3Ed__6_System_IDisposable_Dispose_m02AC0D6BD3E36876681CE177F5F02859A0965580 (void);
// 0x00000402 System.Boolean VRAssemblyStepManager/<PrepareAssemblyStep>d__6::MoveNext()
extern void U3CPrepareAssemblyStepU3Ed__6_MoveNext_m39E448C266CCAEC4CA68E7F237C5BE944CC5D689 (void);
// 0x00000403 System.Object VRAssemblyStepManager/<PrepareAssemblyStep>d__6::System.Collections.Generic.IEnumerator<System.Object>.get_Current()
extern void U3CPrepareAssemblyStepU3Ed__6_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mC153B0CD5B76ABE23D9B62949080B20BF56F057A (void);
// 0x00000404 System.Void VRAssemblyStepManager/<PrepareAssemblyStep>d__6::System.Collections.IEnumerator.Reset()
extern void U3CPrepareAssemblyStepU3Ed__6_System_Collections_IEnumerator_Reset_m982FF1DAFB00EAC4B78A875EB23D6EF877AF136A (void);
// 0x00000405 System.Object VRAssemblyStepManager/<PrepareAssemblyStep>d__6::System.Collections.IEnumerator.get_Current()
extern void U3CPrepareAssemblyStepU3Ed__6_System_Collections_IEnumerator_get_Current_mE2B078D3C6BF8242FF514A3A11A69C6B77DA7044 (void);
// 0x00000406 System.Void VRAssemblyStepManager/<PrepareAssemblyStep>d__7::.ctor(System.Int32)
extern void U3CPrepareAssemblyStepU3Ed__7__ctor_m4CA782BAFF0957AC85A3C7BE7DD61D4DCDC43B1C (void);
// 0x00000407 System.Void VRAssemblyStepManager/<PrepareAssemblyStep>d__7::System.IDisposable.Dispose()
extern void U3CPrepareAssemblyStepU3Ed__7_System_IDisposable_Dispose_mC4B289C29A307D5B6CB38523CED148B3B53D0929 (void);
// 0x00000408 System.Boolean VRAssemblyStepManager/<PrepareAssemblyStep>d__7::MoveNext()
extern void U3CPrepareAssemblyStepU3Ed__7_MoveNext_m12D236973EB066E62EDC93CCF70130E0D2F6A039 (void);
// 0x00000409 System.Object VRAssemblyStepManager/<PrepareAssemblyStep>d__7::System.Collections.Generic.IEnumerator<System.Object>.get_Current()
extern void U3CPrepareAssemblyStepU3Ed__7_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m28B749622E1767C8347A58468F1438DD18C21ED8 (void);
// 0x0000040A System.Void VRAssemblyStepManager/<PrepareAssemblyStep>d__7::System.Collections.IEnumerator.Reset()
extern void U3CPrepareAssemblyStepU3Ed__7_System_Collections_IEnumerator_Reset_mD1C68AF192BFAEF82C17C4654A2C796FEE9207FF (void);
// 0x0000040B System.Object VRAssemblyStepManager/<PrepareAssemblyStep>d__7::System.Collections.IEnumerator.get_Current()
extern void U3CPrepareAssemblyStepU3Ed__7_System_Collections_IEnumerator_get_Current_m688E40F03DE06B7464051366E169A2447EC14E95 (void);
// 0x0000040C System.Void VRAssemblyUIIntegrator::Start()
extern void VRAssemblyUIIntegrator_Start_m2858EE5A73A57506632DB9C52006A563EAD5C587 (void);
// 0x0000040D System.Void VRAssemblyUIIntegrator::OnDestroy()
extern void VRAssemblyUIIntegrator_OnDestroy_mA2A9C954FDC1B1DB493A3C66E3745C7D7E449EA8 (void);
// 0x0000040E System.Void VRAssemblyUIIntegrator::InitializeVRUI()
extern void VRAssemblyUIIntegrator_InitializeVRUI_m58B2BC1016D85CFFEB615525DA485053019052F6 (void);
// 0x0000040F System.Void VRAssemblyUIIntegrator::FindRequiredComponents()
extern void VRAssemblyUIIntegrator_FindRequiredComponents_m61BB39388E6109BC46E9BE4E2894F55EA65E2F51 (void);
// 0x00000410 System.Boolean VRAssemblyUIIntegrator::ValidateComponents()
extern void VRAssemblyUIIntegrator_ValidateComponents_m48917C090134589A8FF18ECD6007AF63879EBFDC (void);
// 0x00000411 System.Void VRAssemblyUIIntegrator::ConfigureVRMode()
extern void VRAssemblyUIIntegrator_ConfigureVRMode_mC85A5412619C8CAF67618BF54EBD11D9995E37A3 (void);
// 0x00000412 System.Void VRAssemblyUIIntegrator::SetupEventListeners()
extern void VRAssemblyUIIntegrator_SetupEventListeners_m1BB80FD0277F914B0C3470CCA2B078CAE5F48F9B (void);
// 0x00000413 System.Void VRAssemblyUIIntegrator::ConfigureUIPanels()
extern void VRAssemblyUIIntegrator_ConfigureUIPanels_mBD95B6D6EF0F5D63DFED14659B6E90BE9E963DA7 (void);
// 0x00000414 System.Void VRAssemblyUIIntegrator::UpdateVRModeDisplay()
extern void VRAssemblyUIIntegrator_UpdateVRModeDisplay_mA60ACCD40821975FE3A0DF7DB3EAAB246813D08A (void);
// 0x00000415 System.Collections.IEnumerator VRAssemblyUIIntegrator::UpdateStatusCoroutine()
extern void VRAssemblyUIIntegrator_UpdateStatusCoroutine_m156D938C6424126227D22A74952B553C71507B35 (void);
// 0x00000416 System.Void VRAssemblyUIIntegrator::UpdateStatusDisplay()
extern void VRAssemblyUIIntegrator_UpdateStatusDisplay_m808206FFF757FEB6F1D44914E35C09AE4DCF4643 (void);
// 0x00000417 System.Void VRAssemblyUIIntegrator::OnVRPartSelected(AssemblyPart)
extern void VRAssemblyUIIntegrator_OnVRPartSelected_mEC74F2DE2C749B1AD6B1774364E106921A4F4E3F (void);
// 0x00000418 System.Void VRAssemblyUIIntegrator::OnVRNextStepRequested()
extern void VRAssemblyUIIntegrator_OnVRNextStepRequested_mA61225F282739DB1AD76065087C7A71519BACF99 (void);
// 0x00000419 System.Void VRAssemblyUIIntegrator::OnVRResetRequested()
extern void VRAssemblyUIIntegrator_OnVRResetRequested_m00066F50130A0DD3A756316EC1D9BCAE9942940B (void);
// 0x0000041A System.Void VRAssemblyUIIntegrator::OnVRReplayRequested()
extern void VRAssemblyUIIntegrator_OnVRReplayRequested_m1332ECBF324F73B8669EDAD40D25A560F0FCF412 (void);
// 0x0000041B System.Void VRAssemblyUIIntegrator::OnVRRepositionButtonClicked()
extern void VRAssemblyUIIntegrator_OnVRRepositionButtonClicked_mA043FC7122548A1DCCBB500B07BC72580E3DCA83 (void);
// 0x0000041C System.Void VRAssemblyUIIntegrator::OnVRResetViewButtonClicked()
extern void VRAssemblyUIIntegrator_OnVRResetViewButtonClicked_m057772BDB12E14E096914CE0F5BDDA316165856F (void);
// 0x0000041D System.Void VRAssemblyUIIntegrator::OnVRFollowModeToggleChanged(System.Boolean)
extern void VRAssemblyUIIntegrator_OnVRFollowModeToggleChanged_mEB6E4326B62570B6990BFC198130639B488641DB (void);
// 0x0000041E System.Void VRAssemblyUIIntegrator::OnVRUIDistanceSliderChanged(System.Single)
extern void VRAssemblyUIIntegrator_OnVRUIDistanceSliderChanged_mA032ED9671D87F112C99D50F6E085C0342DDBB3E (void);
// 0x0000041F System.Void VRAssemblyUIIntegrator::OnVRUIScaleSliderChanged(System.Single)
extern void VRAssemblyUIIntegrator_OnVRUIScaleSliderChanged_m9A434A2A174CFDF4725ABD14816983B5A2AC95D5 (void);
// 0x00000420 System.Void VRAssemblyUIIntegrator::SetVRMode(System.Boolean)
extern void VRAssemblyUIIntegrator_SetVRMode_mA80559E71BAB9B017C68E8AD512E389F5ACDF807 (void);
// 0x00000421 System.Void VRAssemblyUIIntegrator::SetDebugMode(System.Boolean)
extern void VRAssemblyUIIntegrator_SetDebugMode_mA11F154628D379365A26EF29768245228CF9CC49 (void);
// 0x00000422 System.Boolean VRAssemblyUIIntegrator::IsVRModeActive()
extern void VRAssemblyUIIntegrator_IsVRModeActive_m1019A7EFE2C9C8F796DF8D5503363B9B3E3A8309 (void);
// 0x00000423 System.Boolean VRAssemblyUIIntegrator::IsInitialized()
extern void VRAssemblyUIIntegrator_IsInitialized_m077C708DBA137957916F719F21E2782D95999576 (void);
// 0x00000424 System.Void VRAssemblyUIIntegrator::.ctor()
extern void VRAssemblyUIIntegrator__ctor_m84002463FA3CA1230ED8E5597D5D4B14AB47E3DA (void);
// 0x00000425 System.Void VRAssemblyUIIntegrator/<UpdateStatusCoroutine>d__32::.ctor(System.Int32)
extern void U3CUpdateStatusCoroutineU3Ed__32__ctor_m18A39C340BD15FAF604FA4613F6C3249682A812F (void);
// 0x00000426 System.Void VRAssemblyUIIntegrator/<UpdateStatusCoroutine>d__32::System.IDisposable.Dispose()
extern void U3CUpdateStatusCoroutineU3Ed__32_System_IDisposable_Dispose_m68233148CE7B6B381FA20B3E87983ADE8392254F (void);
// 0x00000427 System.Boolean VRAssemblyUIIntegrator/<UpdateStatusCoroutine>d__32::MoveNext()
extern void U3CUpdateStatusCoroutineU3Ed__32_MoveNext_m4AE101EE3A5E86958838EECD951C267730C7E332 (void);
// 0x00000428 System.Object VRAssemblyUIIntegrator/<UpdateStatusCoroutine>d__32::System.Collections.Generic.IEnumerator<System.Object>.get_Current()
extern void U3CUpdateStatusCoroutineU3Ed__32_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mF137B78CDEE29643F99FC0D9EBC2DC044030F7C8 (void);
// 0x00000429 System.Void VRAssemblyUIIntegrator/<UpdateStatusCoroutine>d__32::System.Collections.IEnumerator.Reset()
extern void U3CUpdateStatusCoroutineU3Ed__32_System_Collections_IEnumerator_Reset_m93D76977FBB483D15EA0AE1FA782D32A7C10ED52 (void);
// 0x0000042A System.Object VRAssemblyUIIntegrator/<UpdateStatusCoroutine>d__32::System.Collections.IEnumerator.get_Current()
extern void U3CUpdateStatusCoroutineU3Ed__32_System_Collections_IEnumerator_get_Current_mCA2982354EB2AC4F43D03904F7C898ADCEE8DE90 (void);
// 0x0000042B System.Void VRCameraDataRecorder::Start()
extern void VRCameraDataRecorder_Start_mF24923C2AD9CA96029AEC0FF34B53148968582D2 (void);
// 0x0000042C System.Void VRCameraDataRecorder::Update()
extern void VRCameraDataRecorder_Update_m6DE26A04BDC3BE933264C665338586257D96D4A9 (void);
// 0x0000042D System.Void VRCameraDataRecorder::InitializeRecorder()
extern void VRCameraDataRecorder_InitializeRecorder_m2298020648653D6862C38B603ACAB9FBA7AED0B9 (void);
// 0x0000042E System.Void VRCameraDataRecorder::GetUsernameFromSceneManager()
extern void VRCameraDataRecorder_GetUsernameFromSceneManager_m161DB94254B61448572C6FBF5C15C86284DC45AB (void);
// 0x0000042F System.Void VRCameraDataRecorder::SetUsername(System.String)
extern void VRCameraDataRecorder_SetUsername_mC48C8A05A3D31A9A38BCDA6F54200DB978E96231 (void);
// 0x00000430 System.String VRCameraDataRecorder::GetCurrentUsername()
extern void VRCameraDataRecorder_GetCurrentUsername_mF2A5EF75D463578E11367D602F8E4C57290DDAD6 (void);
// 0x00000431 System.Void VRCameraDataRecorder::FindVRCamera()
extern void VRCameraDataRecorder_FindVRCamera_mDA1D490BB45959B9D44C708FBE9210C81C2DE313 (void);
// 0x00000432 System.Void VRCameraDataRecorder::EnsureOutputDirectory()
extern void VRCameraDataRecorder_EnsureOutputDirectory_m63CBDA7B5C46E800F8DEC12AEFA387401ED5DB02 (void);
// 0x00000433 System.String VRCameraDataRecorder::GetOutputPath()
extern void VRCameraDataRecorder_GetOutputPath_mCFC5D0FAB6276C8C328299A2CFC4007CFA83002E (void);
// 0x00000434 System.String VRCameraDataRecorder::GetAndroidOptimizedPath(System.String)
extern void VRCameraDataRecorder_GetAndroidOptimizedPath_mE90C23B679113ADF43242366B2849EB2328AADD4 (void);
// 0x00000435 System.String VRCameraDataRecorder::GetExternalStoragePath()
extern void VRCameraDataRecorder_GetExternalStoragePath_mF191C0C4740224B30FA66131605A4D6A60D780C5 (void);
// 0x00000436 System.Void VRCameraDataRecorder::LogControlInstructions()
extern void VRCameraDataRecorder_LogControlInstructions_mDD06BF25681A1DB0CE83DB45D5C0B1043650D638 (void);
// 0x00000437 System.Void VRCameraDataRecorder::HandleInput()
extern void VRCameraDataRecorder_HandleInput_m4B3D8DC40CBBE0854AB1FCFD26926A519D7AC479 (void);
// 0x00000438 System.Void VRCameraDataRecorder::UpdateDebugDisplay()
extern void VRCameraDataRecorder_UpdateDebugDisplay_m5A21C45C3DE4D877227DBAA2B29BF389175D5D38 (void);
// 0x00000439 System.Void VRCameraDataRecorder::StartRecording()
extern void VRCameraDataRecorder_StartRecording_m13B219BC52D784938946886FFEF5194AE2B11AB0 (void);
// 0x0000043A System.Void VRCameraDataRecorder::StopRecording()
extern void VRCameraDataRecorder_StopRecording_m955D6E3137D2C5C8B3D1D7979A3C98D1078C7DCE (void);
// 0x0000043B System.Collections.IEnumerator VRCameraDataRecorder::RecordingLoop()
extern void VRCameraDataRecorder_RecordingLoop_m81D6CE2D669993A9659DBB07A89575AD0A67A732 (void);
// 0x0000043C System.Void VRCameraDataRecorder::RecordCurrentCameraData()
extern void VRCameraDataRecorder_RecordCurrentCameraData_mCF4E911DAF09ED423DE93315D1F0453E48636676 (void);
// 0x0000043D System.String VRCameraDataRecorder::GetDeviceInfo()
extern void VRCameraDataRecorder_GetDeviceInfo_m43FC6CFD4BA80C7A3772363598EE7E2750110620 (void);
// 0x0000043E System.Void VRCameraDataRecorder::SaveDataToJson()
extern void VRCameraDataRecorder_SaveDataToJson_mE2E6CFF367E73C9988490DD5715A780DBD3D4D60 (void);
// 0x0000043F SimpleJSON.JSONNode VRCameraDataRecorder::CreateJsonData()
extern void VRCameraDataRecorder_CreateJsonData_mBF1B146B906B5C0C24AA8A540C638475A2777CAA (void);
// 0x00000440 System.Void VRCameraDataRecorder::LogDataSummary()
extern void VRCameraDataRecorder_LogDataSummary_m9FCC77B7E3363C677D7E2F7B775076814ED8CAEB (void);
// 0x00000441 System.Void VRCameraDataRecorder::LogFileAccessInstructions(System.String)
extern void VRCameraDataRecorder_LogFileAccessInstructions_mB954EDFA17A5DF61F97CF6FB394AE841E718F22F (void);
// 0x00000442 System.Void VRCameraDataRecorder::ClearRecordedData()
extern void VRCameraDataRecorder_ClearRecordedData_mDD7B2FE51B33B8EC3426AEE167E373B9D4584CDC (void);
// 0x00000443 System.Boolean VRCameraDataRecorder::IsRecording()
extern void VRCameraDataRecorder_IsRecording_m394D9D6532AF68F5252EF9AFE797A68945A8CE04 (void);
// 0x00000444 System.Int32 VRCameraDataRecorder::GetRecordCount()
extern void VRCameraDataRecorder_GetRecordCount_mFFF238F9E2AAFA292BB40C216C025087CD786A7C (void);
// 0x00000445 System.Void VRCameraDataRecorder::RecordOnce()
extern void VRCameraDataRecorder_RecordOnce_m7B393D5F3E5A59566D6D29A7C1C4FD5FB5E260A5 (void);
// 0x00000446 System.Void VRCameraDataRecorder::OnGUI()
extern void VRCameraDataRecorder_OnGUI_mB8DE8B381909D6A5503F8792B9268068963464E0 (void);
// 0x00000447 System.Void VRCameraDataRecorder::.ctor()
extern void VRCameraDataRecorder__ctor_m79DE51ED2F241690ED22F45BDBD8F44AB13134AA (void);
// 0x00000448 System.Void VRCameraDataRecorder/CameraDataRecord::.ctor()
extern void CameraDataRecord__ctor_m22A26133AE0CB1C1615C874F9FCFA1B6995CD7BB (void);
// 0x00000449 System.Void VRCameraDataRecorder/Vector3Data::.ctor(UnityEngine.Vector3)
extern void Vector3Data__ctor_mD2FBC60CD38FB04CD858E9B024F474C903BF91AB (void);
// 0x0000044A System.Void VRCameraDataRecorder/QuaternionData::.ctor(UnityEngine.Quaternion)
extern void QuaternionData__ctor_mC73DC8A138E357ECBA52DC7F53C36C676D739C74 (void);
// 0x0000044B System.Void VRCameraDataRecorder/Matrix4x4Data::.ctor(UnityEngine.Matrix4x4)
extern void Matrix4x4Data__ctor_mC02B0B3875BE7ABF29596859872FE66806101648 (void);
// 0x0000044C System.Void VRCameraDataRecorder/<RecordingLoop>d__50::.ctor(System.Int32)
extern void U3CRecordingLoopU3Ed__50__ctor_m12EC069DD13FF4A1650BC0F2E15F95A41DAE8349 (void);
// 0x0000044D System.Void VRCameraDataRecorder/<RecordingLoop>d__50::System.IDisposable.Dispose()
extern void U3CRecordingLoopU3Ed__50_System_IDisposable_Dispose_m2F8B3C312B3FE201EAFDCA78FDB4D2C844EFF7D4 (void);
// 0x0000044E System.Boolean VRCameraDataRecorder/<RecordingLoop>d__50::MoveNext()
extern void U3CRecordingLoopU3Ed__50_MoveNext_mEC03D266D2EFED3FA11876347C6CD8B879C9081C (void);
// 0x0000044F System.Object VRCameraDataRecorder/<RecordingLoop>d__50::System.Collections.Generic.IEnumerator<System.Object>.get_Current()
extern void U3CRecordingLoopU3Ed__50_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m653DC0081E985BE8CDCD874D0F4A25AEBFDF6429 (void);
// 0x00000450 System.Void VRCameraDataRecorder/<RecordingLoop>d__50::System.Collections.IEnumerator.Reset()
extern void U3CRecordingLoopU3Ed__50_System_Collections_IEnumerator_Reset_mA4F9CCA4AA286FA56085136117DB34E56FDB032A (void);
// 0x00000451 System.Object VRCameraDataRecorder/<RecordingLoop>d__50::System.Collections.IEnumerator.get_Current()
extern void U3CRecordingLoopU3Ed__50_System_Collections_IEnumerator_get_Current_m468FB1568AD09BB117168DEBF37DDA2F1F5BB9A6 (void);
// 0x00000452 System.Void VRCameraDataRecorderExample::Start()
extern void VRCameraDataRecorderExample_Start_mEE43A6AD7D6FFA495DAA9FF429255ACDA1381C6C (void);
// 0x00000453 System.Void VRCameraDataRecorderExample::Update()
extern void VRCameraDataRecorderExample_Update_mA951608F0024802933EADE8C26D1605643B5188F (void);
// 0x00000454 System.Void VRCameraDataRecorderExample::CreateUI()
extern void VRCameraDataRecorderExample_CreateUI_m92345E8D0FA664C40EFFCF2C1F56B77B66AB26B2 (void);
// 0x00000455 UnityEngine.UI.Text VRCameraDataRecorderExample::CreateLabel(System.String,UnityEngine.Vector2,UnityEngine.Vector2,UnityEngine.Transform,System.Int32)
extern void VRCameraDataRecorderExample_CreateLabel_mD622842267EF1DA13AF9C04AFEA11071D4169611 (void);
// 0x00000456 UnityEngine.UI.Button VRCameraDataRecorderExample::CreateButton(System.String,UnityEngine.Vector2,UnityEngine.Vector2,UnityEngine.Transform)
extern void VRCameraDataRecorderExample_CreateButton_m640BBFEB0A767262E5BCFA6240FADA282BAA1B3C (void);
// 0x00000457 UnityEngine.UI.Slider VRCameraDataRecorderExample::CreateSlider(UnityEngine.Vector2,UnityEngine.Vector2,UnityEngine.Transform)
extern void VRCameraDataRecorderExample_CreateSlider_mBD99CC6FAC7F0EEBE6D96FCF4EEC563217F1A19D (void);
// 0x00000458 System.Void VRCameraDataRecorderExample::SetupButtonEvents()
extern void VRCameraDataRecorderExample_SetupButtonEvents_m4296BD05665C09FB4B5CE36E18F1D410EA8E5187 (void);
// 0x00000459 System.Void VRCameraDataRecorderExample::UpdateUI()
extern void VRCameraDataRecorderExample_UpdateUI_m4649B36DE06E8950BE7B9BC11B774AA9BE50A061 (void);
// 0x0000045A System.Void VRCameraDataRecorderExample::OnIntervalChanged(System.Single)
extern void VRCameraDataRecorderExample_OnIntervalChanged_m4DF2786A5D02845650D367958286E254F78ED14B (void);
// 0x0000045B System.Void VRCameraDataRecorderExample::.ctor()
extern void VRCameraDataRecorderExample__ctor_m4BB78F56199A6736A8AA561776746D78CEEADB0E (void);
// 0x0000045C System.Void VRCameraDataRecorderExample::<SetupButtonEvents>b__17_0()
extern void VRCameraDataRecorderExample_U3CSetupButtonEventsU3Eb__17_0_mBB4716351AA0A9E88B205FCFBDEAA408776D8C41 (void);
// 0x0000045D System.Void VRCameraDataRecorderExample::<SetupButtonEvents>b__17_1()
extern void VRCameraDataRecorderExample_U3CSetupButtonEventsU3Eb__17_1_m16D21A6A368940B7D80E62FB7F36F2F24A386C17 (void);
// 0x0000045E System.Void VRCameraDataRecorderExample::<SetupButtonEvents>b__17_2()
extern void VRCameraDataRecorderExample_U3CSetupButtonEventsU3Eb__17_2_mBA7CE0B6ED68AEB12E6D1DD8764BB988BDC116D2 (void);
// 0x0000045F System.Void VRCameraDataRecorderExample::<SetupButtonEvents>b__17_3()
extern void VRCameraDataRecorderExample_U3CSetupButtonEventsU3Eb__17_3_m19BD054CACC515D3E6D706267BCD1E67CEAD2AA1 (void);
// 0x00000460 System.Void VRInputManager::add_OnPartSelected(System.Action`1<AssemblyPart>)
extern void VRInputManager_add_OnPartSelected_m5C3070EA0886F30681B28F729759E44945503631 (void);
// 0x00000461 System.Void VRInputManager::remove_OnPartSelected(System.Action`1<AssemblyPart>)
extern void VRInputManager_remove_OnPartSelected_mE5B70AD10CA05140511B584946DAFFF157DCD00D (void);
// 0x00000462 System.Void VRInputManager::add_OnNextStepRequested(System.Action)
extern void VRInputManager_add_OnNextStepRequested_m2AED815A84BB90AED60FA2ADBDB9BE01850C5128 (void);
// 0x00000463 System.Void VRInputManager::remove_OnNextStepRequested(System.Action)
extern void VRInputManager_remove_OnNextStepRequested_mDC65B60380B65586D84B0AE6486F1D699B3CCA4D (void);
// 0x00000464 System.Void VRInputManager::add_OnResetRequested(System.Action)
extern void VRInputManager_add_OnResetRequested_m1C1B61023DB588C405AB2FDE0C8D89E0358BFB36 (void);
// 0x00000465 System.Void VRInputManager::remove_OnResetRequested(System.Action)
extern void VRInputManager_remove_OnResetRequested_mF0F568735A599F592A11B3E3F8BA09AD20DFE065 (void);
// 0x00000466 System.Void VRInputManager::add_OnReplayRequested(System.Action)
extern void VRInputManager_add_OnReplayRequested_m35F9442BEBC3708327BB67E82AC1AD933A6FBC44 (void);
// 0x00000467 System.Void VRInputManager::remove_OnReplayRequested(System.Action)
extern void VRInputManager_remove_OnReplayRequested_mC7C89A4ED9CEB8D3AAE425FE22981108BFAC25FB (void);
// 0x00000468 System.Void VRInputManager::Start()
extern void VRInputManager_Start_mEDCE09DC068C2A7676C78F6BC85EC184FA2F8CBA (void);
// 0x00000469 System.Void VRInputManager::Update()
extern void VRInputManager_Update_m7292786F9775C1AD6BE6BFFEADDE8C0779FE6607 (void);
// 0x0000046A System.Void VRInputManager::InitializeVRInput()
extern void VRInputManager_InitializeVRInput_m014D294AD01E88C13A296A9320ACB776A9DC9344 (void);
// 0x0000046B System.Void VRInputManager::HandleVRInput()
extern void VRInputManager_HandleVRInput_m2945939741DC28FDAC21E86422F0B328B023B04C (void);
// 0x0000046C System.Void VRInputManager::HandleFallbackInput()
extern void VRInputManager_HandleFallbackInput_m78EE683656B46278F07043FF6E01BE34654F7C7D (void);
// 0x0000046D System.Void VRInputManager::HandleControllerInput(System.Object,System.Object)
extern void VRInputManager_HandleControllerInput_m9A3A2BC6C842ED3485AE7F633C8A8E78C25B190B (void);
// 0x0000046E System.Void VRInputManager::HandlePartSelection(System.Object)
extern void VRInputManager_HandlePartSelection_m31C91C31BE9209E526B636D4994BDB56852DA231 (void);
// 0x0000046F System.Void VRInputManager::SelectPart(AssemblyPart,System.Object)
extern void VRInputManager_SelectPart_m89687CF7C30B69579DB6ABB6E87502C2F5DC3A84 (void);
// 0x00000470 System.Void VRInputManager::HandleMenuInput()
extern void VRInputManager_HandleMenuInput_mB767C0F4AE4A0A081A0052ED5A3FCDCE13CFC4AE (void);
// 0x00000471 System.Void VRInputManager::TriggerHapticFeedback(System.Object)
extern void VRInputManager_TriggerHapticFeedback_m914B5A1B27AD7FF6AF0C69D147C977C5726379DD (void);
// 0x00000472 AssemblyPart VRInputManager::GetSelectedPart()
extern void VRInputManager_GetSelectedPart_mBC791B9E65C1B02EEDE52A970D2F851CFE3DF8D9 (void);
// 0x00000473 System.Object VRInputManager::GetActiveInteractor()
extern void VRInputManager_GetActiveInteractor_m881063E8E4974B5F28149A27A909EB288536E84E (void);
// 0x00000474 System.Void VRInputManager::SetInteractionLayerMask(UnityEngine.LayerMask)
extern void VRInputManager_SetInteractionLayerMask_m40C012C138859D2A930F2F5F2E07F89E110A5FB3 (void);
// 0x00000475 System.Void VRInputManager::SetHapticFeedback(System.Boolean)
extern void VRInputManager_SetHapticFeedback_m84F62D86924CB54B7922FE04A2494933842313C0 (void);
// 0x00000476 System.Void VRInputManager::TriggerNextStep()
extern void VRInputManager_TriggerNextStep_m9D4087D1FAEDD1B31D3FDFF307777D43E3D32B6A (void);
// 0x00000477 System.Void VRInputManager::TriggerReset()
extern void VRInputManager_TriggerReset_m6078607B188F8C35D0D12E816ED32BA5784C4491 (void);
// 0x00000478 System.Void VRInputManager::TriggerReplay()
extern void VRInputManager_TriggerReplay_m0E79D4ECB60ACD5797AA0702C8E7215EFFB8E19D (void);
// 0x00000479 System.Void VRInputManager::OnDestroy()
extern void VRInputManager_OnDestroy_m83E19ABE0AEB47324FE2F3526117192E9ADEBC1E (void);
// 0x0000047A System.Void VRInputManager::.ctor()
extern void VRInputManager__ctor_m1A0FBE8ECE1AEDE907F35442F5634CBAD5A9A546 (void);
// 0x0000047B VRSceneManager VRSceneManager::get_Instance()
extern void VRSceneManager_get_Instance_m118157146B4BE9795183E6C81BBDBBE9D5DB79E9 (void);
// 0x0000047C System.Void VRSceneManager::Awake()
extern void VRSceneManager_Awake_m23BE811D09BD7942CFFCC61D5804A7639CEC83F7 (void);
// 0x0000047D System.Void VRSceneManager::InitializeSceneManager()
extern void VRSceneManager_InitializeSceneManager_m0E5D51FADF3206FE1C88118C5075B58FE0DDE8C5 (void);
// 0x0000047E System.Void VRSceneManager::InitializeSceneList()
extern void VRSceneManager_InitializeSceneList_mEE86EA0D4BE3326E5E5A64948543161DD7D67FAC (void);
// 0x0000047F System.Void VRSceneManager::CreateFadeCanvas()
extern void VRSceneManager_CreateFadeCanvas_mEA3B2E07FAF72CB5233D710FAF6641DAF454E010 (void);
// 0x00000480 System.Void VRSceneManager::LoadScene(System.String)
extern void VRSceneManager_LoadScene_m6A7FE983E0235F39127036CC0F9EE80E2BE034B9 (void);
// 0x00000481 System.Collections.IEnumerator VRSceneManager::LoadSceneCoroutine(System.String)
extern void VRSceneManager_LoadSceneCoroutine_m9A025797332D8CBEC8E5114F6DC4CBFAC711FE82 (void);
// 0x00000482 System.Boolean VRSceneManager::IsSceneValid(System.String)
extern void VRSceneManager_IsSceneValid_m41A68FD4C62EB0733A7A709D58F2938B77A6474B (void);
// 0x00000483 System.Collections.IEnumerator VRSceneManager::FadeOut()
extern void VRSceneManager_FadeOut_m8665058FB9990D64344328DADB4C957E8130806D (void);
// 0x00000484 System.Collections.IEnumerator VRSceneManager::FadeIn()
extern void VRSceneManager_FadeIn_m2522426C2D28DF9005BC65A93DA6B2FE13F71A31 (void);
// 0x00000485 System.Void VRSceneManager::PlaySound(UnityEngine.AudioClip)
extern void VRSceneManager_PlaySound_m1A214E7B0D35E2EFB6DFADCF83770029E9E093D9 (void);
// 0x00000486 System.Void VRSceneManager::SetSceneData(System.String,System.Object)
extern void VRSceneManager_SetSceneData_m949C377A36DC622AE27ECF9E0D9782B57AEE3458 (void);
// 0x00000487 T VRSceneManager::GetSceneData(System.String,T)
// 0x00000488 VRSceneManager/SceneInfo VRSceneManager::GetSceneInfo(System.String)
extern void VRSceneManager_GetSceneInfo_mA666C5990B054CD86C278616A27AA762B33B0F73 (void);
// 0x00000489 System.Collections.Generic.List`1<VRSceneManager/SceneInfo> VRSceneManager::GetAvailableScenes()
extern void VRSceneManager_GetAvailableScenes_mF3A7C8E6767A04528FB68B7E1E30B1910592E26B (void);
// 0x0000048A System.Void VRSceneManager::ReloadCurrentScene()
extern void VRSceneManager_ReloadCurrentScene_m70D6F41B4BFD8686C8686E53C89991D5873B386F (void);
// 0x0000048B System.Void VRSceneManager::QuitApplication()
extern void VRSceneManager_QuitApplication_mA4D25E6C6EFA5C932FFE2C568090FFF8F97DCE24 (void);
// 0x0000048C System.Boolean VRSceneManager::IsTransitioning()
extern void VRSceneManager_IsTransitioning_mAADF4F2517D845B8C490F55090963D4F9D553406 (void);
// 0x0000048D System.Void VRSceneManager::.ctor()
extern void VRSceneManager__ctor_mF88F82349A1608FC6EA485777D9E977600E9D2AC (void);
// 0x0000048E System.Void VRSceneManager/SceneInfo::.ctor()
extern void SceneInfo__ctor_m347D14C60B440061693A6F359F277666AD6E6DB1 (void);
// 0x0000048F System.Void VRSceneManager/<LoadSceneCoroutine>d__30::.ctor(System.Int32)
extern void U3CLoadSceneCoroutineU3Ed__30__ctor_mE2AB13257713E200833201192B8ED7A6F0DF31E5 (void);
// 0x00000490 System.Void VRSceneManager/<LoadSceneCoroutine>d__30::System.IDisposable.Dispose()
extern void U3CLoadSceneCoroutineU3Ed__30_System_IDisposable_Dispose_m63081A76DC7EA6B1C534C054CFF05F9341F7901B (void);
// 0x00000491 System.Boolean VRSceneManager/<LoadSceneCoroutine>d__30::MoveNext()
extern void U3CLoadSceneCoroutineU3Ed__30_MoveNext_m83F2172F53BFC5631F3DF080B7F32F94A549F131 (void);
// 0x00000492 System.Object VRSceneManager/<LoadSceneCoroutine>d__30::System.Collections.Generic.IEnumerator<System.Object>.get_Current()
extern void U3CLoadSceneCoroutineU3Ed__30_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m964ECD1D1FB832F49E7A8886CA24050E31194789 (void);
// 0x00000493 System.Void VRSceneManager/<LoadSceneCoroutine>d__30::System.Collections.IEnumerator.Reset()
extern void U3CLoadSceneCoroutineU3Ed__30_System_Collections_IEnumerator_Reset_mE7DFEC452A4A585A6343A16A0EC896879B55B8F3 (void);
// 0x00000494 System.Object VRSceneManager/<LoadSceneCoroutine>d__30::System.Collections.IEnumerator.get_Current()
extern void U3CLoadSceneCoroutineU3Ed__30_System_Collections_IEnumerator_get_Current_m538A409A0E3EEDEE1232F8BEF260797BA5C241F9 (void);
// 0x00000495 System.Void VRSceneManager/<FadeOut>d__32::.ctor(System.Int32)
extern void U3CFadeOutU3Ed__32__ctor_m2461220C7BEEC939D00D86F37369F693C18BB860 (void);
// 0x00000496 System.Void VRSceneManager/<FadeOut>d__32::System.IDisposable.Dispose()
extern void U3CFadeOutU3Ed__32_System_IDisposable_Dispose_m30E2A09F3983DD268365E9FB75A6106E72F92FE9 (void);
// 0x00000497 System.Boolean VRSceneManager/<FadeOut>d__32::MoveNext()
extern void U3CFadeOutU3Ed__32_MoveNext_mFB345B2962DFE6F79792548EC451AF0D426C66FE (void);
// 0x00000498 System.Object VRSceneManager/<FadeOut>d__32::System.Collections.Generic.IEnumerator<System.Object>.get_Current()
extern void U3CFadeOutU3Ed__32_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m3F25FDD024FE371828928F7606B379E4A9D3A89F (void);
// 0x00000499 System.Void VRSceneManager/<FadeOut>d__32::System.Collections.IEnumerator.Reset()
extern void U3CFadeOutU3Ed__32_System_Collections_IEnumerator_Reset_m0BE4EEBA7B18A6A5C485751FC5CFBCD6CE59E8F6 (void);
// 0x0000049A System.Object VRSceneManager/<FadeOut>d__32::System.Collections.IEnumerator.get_Current()
extern void U3CFadeOutU3Ed__32_System_Collections_IEnumerator_get_Current_m9E9E7B082756DDA0647653D8D8B2170986EBAD1A (void);
// 0x0000049B System.Void VRSceneManager/<FadeIn>d__33::.ctor(System.Int32)
extern void U3CFadeInU3Ed__33__ctor_mA055E33DC493B3C9A5D4BAB3C113D53B226EA066 (void);
// 0x0000049C System.Void VRSceneManager/<FadeIn>d__33::System.IDisposable.Dispose()
extern void U3CFadeInU3Ed__33_System_IDisposable_Dispose_m43B47C9123149A7BBF3A39B24D105DEC1BBF1DD1 (void);
// 0x0000049D System.Boolean VRSceneManager/<FadeIn>d__33::MoveNext()
extern void U3CFadeInU3Ed__33_MoveNext_mBFB2CC9F1D62AF9EACD3018B3009957A46E4F4CB (void);
// 0x0000049E System.Object VRSceneManager/<FadeIn>d__33::System.Collections.Generic.IEnumerator<System.Object>.get_Current()
extern void U3CFadeInU3Ed__33_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m72AF3D9CDB5AF9FA225531D4097530F5C663AE7C (void);
// 0x0000049F System.Void VRSceneManager/<FadeIn>d__33::System.Collections.IEnumerator.Reset()
extern void U3CFadeInU3Ed__33_System_Collections_IEnumerator_Reset_mE39AEF2D791156BC29F524D9F38F6660B7088CB6 (void);
// 0x000004A0 System.Object VRSceneManager/<FadeIn>d__33::System.Collections.IEnumerator.get_Current()
extern void U3CFadeInU3Ed__33_System_Collections_IEnumerator_get_Current_m1676468DBFCD000E564B2652AE03AF484760FD0D (void);
// 0x000004A1 System.Void VRSceneManager/<>c__DisplayClass37_0::.ctor()
extern void U3CU3Ec__DisplayClass37_0__ctor_m27519FE6A99A84898DEEBADCAE0431BF7324C797 (void);
// 0x000004A2 System.Boolean VRSceneManager/<>c__DisplayClass37_0::<GetSceneInfo>b__0(VRSceneManager/SceneInfo)
extern void U3CU3Ec__DisplayClass37_0_U3CGetSceneInfoU3Eb__0_m09B4BE3FE2109882D2D0FAE06E664BF316B5A094 (void);
// 0x000004A3 System.Void VRSimulator::Start()
extern void VRSimulator_Start_mD3E50DCE68A0531D10D9B0890923C8AD19E2E42C (void);
// 0x000004A4 System.Void VRSimulator::Update()
extern void VRSimulator_Update_m9327341FF575FC0397A7F26DEFC504F1461B811C (void);
// 0x000004A5 System.Void VRSimulator::InitializeVRSimulator()
extern void VRSimulator_InitializeVRSimulator_m08012E382E2E65F7682B95B68FE09F3962808B6D (void);
// 0x000004A6 System.Void VRSimulator::CreateControllerSimulators()
extern void VRSimulator_CreateControllerSimulators_m9515B3DF2549CD6DDAE872426520560CFA1CEB72 (void);
// 0x000004A7 System.Void VRSimulator::CreateControllerVisual(UnityEngine.Transform,UnityEngine.Color)
extern void VRSimulator_CreateControllerVisual_mBAC43F3090A61B7A16382DE351F9D71F0D24A0AC (void);
// 0x000004A8 System.Void VRSimulator::HandleVRSimulation()
extern void VRSimulator_HandleVRSimulation_mD04E9283442A2697F29376F31694486BF0EB0CCB (void);
// 0x000004A9 System.Void VRSimulator::HandleHeadMovement()
extern void VRSimulator_HandleHeadMovement_m89D0144BFCC732DFD5F8B431B5E3F405D888BAC6 (void);
// 0x000004AA System.Void VRSimulator::HandleHeadRotation()
extern void VRSimulator_HandleHeadRotation_m0BAB512A2F654EC70E7E3CCC1E1AA85EC1FC8386 (void);
// 0x000004AB System.Void VRSimulator::HandleControllerSimulation()
extern void VRSimulator_HandleControllerSimulation_m491E50B6FA22C44B18B5B46C8E54DF3D364402D7 (void);
// 0x000004AC System.Void VRSimulator::ClampToVRBounds()
extern void VRSimulator_ClampToVRBounds_m346D4C4FAFE0D69D220BC73482F7A0A41850FE21 (void);
// 0x000004AD UnityEngine.Camera VRSimulator::GetVRCamera()
extern void VRSimulator_GetVRCamera_m3DFB20A1FE02C03F6739D3946B00529BBDE6B986 (void);
// 0x000004AE UnityEngine.Transform VRSimulator::GetControllerTransform(System.Boolean)
extern void VRSimulator_GetControllerTransform_m1C1EA8393D526442039540E5A31909D6F980738B (void);
// 0x000004AF System.Void VRSimulator::ResetVRPosition()
extern void VRSimulator_ResetVRPosition_m62A6C813FFAAF87F3CE95C74B625011322F2B72A (void);
// 0x000004B0 System.Void VRSimulator::ToggleVRSimulation()
extern void VRSimulator_ToggleVRSimulation_mE3CB63194F7580CF49D2D50207B6D93767B2F87D (void);
// 0x000004B1 System.Void VRSimulator::SetUserHeight(System.Single)
extern void VRSimulator_SetUserHeight_m5EFF0E65925D0C9D487C436847E13B11075A9AF2 (void);
// 0x000004B2 System.Void VRSimulator::OnDrawGizmos()
extern void VRSimulator_OnDrawGizmos_m68FA100330BD0AD428DCBC8DF436445AAC857061 (void);
// 0x000004B3 System.Void VRSimulator::OnGUI()
extern void VRSimulator_OnGUI_m7DD4CA686C0C1D92F4ADD9CFAB89EF3DF332486D (void);
// 0x000004B4 UnityEngine.GUIStyle VRSimulator::EditorGUIStyle()
extern void VRSimulator_EditorGUIStyle_m678D64F4A00ABC95B7E5EC7074CCD8FF99135800 (void);
// 0x000004B5 System.Void VRSimulator::.ctor()
extern void VRSimulator__ctor_m7D11F1D5F20D30258E8D21FC8DC28D626C180C31 (void);
// 0x000004B6 System.Void VRSystemDebugger::Start()
extern void VRSystemDebugger_Start_mF4D9B8DF58E6FD69789E8744925FE6ED5D5F5CA5 (void);
// 0x000004B7 System.Void VRSystemDebugger::Update()
extern void VRSystemDebugger_Update_m6FC02EC2526241D70C8CDD5450C225273B55CAFF (void);
// 0x000004B8 System.Void VRSystemDebugger::InitializeDebugger()
extern void VRSystemDebugger_InitializeDebugger_m00E152AC644E455ED18EB8DB4775542780A1EDBB (void);
// 0x000004B9 System.Void VRSystemDebugger::CheckVRSystemStatus()
extern void VRSystemDebugger_CheckVRSystemStatus_m1DA318C9E0EE0C15FF2EF29267550B7ECD8AA38E (void);
// 0x000004BA System.Void VRSystemDebugger::TestPositionAdjustment()
extern void VRSystemDebugger_TestPositionAdjustment_mFC5A3F99CFF09B56C21BEBBA3628982E72CE988E (void);
// 0x000004BB System.Void VRSystemDebugger::TestPreviewFunction()
extern void VRSystemDebugger_TestPreviewFunction_mD94D3AB447BE71F612E62305D67F31E55F28697C (void);
// 0x000004BC System.Void VRSystemDebugger::TestGuidanceFunction()
extern void VRSystemDebugger_TestGuidanceFunction_m4AB64ED31A52BF0BD75AD70E226A915951FE55EC (void);
// 0x000004BD System.Void VRSystemDebugger::ResetVRFunctions()
extern void VRSystemDebugger_ResetVRFunctions_m1D0D51958DBE77D2F4AC514BE83FCEF6F771E8EA (void);
// 0x000004BE System.Void VRSystemDebugger::ToggleDebugMode()
extern void VRSystemDebugger_ToggleDebugMode_m3A95E2806E7651F21F95183F1C151B0465F6DC07 (void);
// 0x000004BF System.Void VRSystemDebugger::UpdateDebugInfo()
extern void VRSystemDebugger_UpdateDebugInfo_m4C02BAD48D1BB8392EB8760B9B95FF82FF8AE3C6 (void);
// 0x000004C0 System.Void VRSystemDebugger::UpdateStatusText()
extern void VRSystemDebugger_UpdateStatusText_m6A59DD8A2FB1777300016A3ACDF79E8C237B45F4 (void);
// 0x000004C1 System.Void VRSystemDebugger::CreateDebugUI()
extern void VRSystemDebugger_CreateDebugUI_m0892B8F0361D6914BCF045FBACB4D6CEE94969BA (void);
// 0x000004C2 System.Void VRSystemDebugger::CreateDebugButton(System.String,UnityEngine.Vector2,System.Action,UnityEngine.Transform)
extern void VRSystemDebugger_CreateDebugButton_mD9CD27AF600971B2A1605A75897024DDA83B7E3B (void);
// 0x000004C3 System.Void VRSystemDebugger::ShowHelp()
extern void VRSystemDebugger_ShowHelp_m51B9B9CF53B8D9C5477FC883F2F8E3EAA211F581 (void);
// 0x000004C4 System.Void VRSystemDebugger::OnGUI()
extern void VRSystemDebugger_OnGUI_m6406E1978F3D72C836069F62E7483C38D821E569 (void);
// 0x000004C5 System.Void VRSystemDebugger::.ctor()
extern void VRSystemDebugger__ctor_m13E74A955BFAC048E5CF671518C3BAD1287A56F1 (void);
// 0x000004C6 System.Void VRSystemDebugger/<>c__DisplayClass28_0::.ctor()
extern void U3CU3Ec__DisplayClass28_0__ctor_mF171D5F5DC51408281799B284EFE62F2752CA5B2 (void);
// 0x000004C7 System.Void VRSystemDebugger/<>c__DisplayClass28_0::<CreateDebugButton>b__0()
extern void U3CU3Ec__DisplayClass28_0_U3CCreateDebugButtonU3Eb__0_mBBC5C967B55B62451B75696B24EA7C15F61D6F82 (void);
// 0x000004C8 System.Void VRUIInteractor::Start()
extern void VRUIInteractor_Start_m5DDB15AD5C89152807A4626FC0DC0E05A0854E87 (void);
// 0x000004C9 System.Void VRUIInteractor::Update()
extern void VRUIInteractor_Update_m4238876624D3FC1E1E289106CFA51CAE4B30E123 (void);
// 0x000004CA System.Void VRUIInteractor::InitializeInteractor()
extern void VRUIInteractor_InitializeInteractor_m7F9538DB3D178313DC15DBC5447BFB47AF86B9A9 (void);
// 0x000004CB System.Void VRUIInteractor::HandleVRUIInteraction()
extern void VRUIInteractor_HandleVRUIInteraction_m40AB0E5072B873763EF688177E65AEBB46A8CDB2 (void);
// 0x000004CC System.Void VRUIInteractor::HandleUIRaycastHit(UnityEngine.EventSystems.RaycastResult,System.Object)
extern void VRUIInteractor_HandleUIRaycastHit_mCBEFD5F375E9A6326A32A1DCB517AC59E4AAB0BB (void);
// 0x000004CD System.Void VRUIInteractor::HandleControllerInput(System.Object)
extern void VRUIInteractor_HandleControllerInput_m226F17E5C3871C815DE8B548CFD178106206D7CD (void);
// 0x000004CE System.Void VRUIInteractor::SetHoverState(UnityEngine.GameObject)
extern void VRUIInteractor_SetHoverState_m40C5E00F14F42868B3556551BE9732550995DD21 (void);
// 0x000004CF System.Void VRUIInteractor::ClearHoverState()
extern void VRUIInteractor_ClearHoverState_m756EBD48FD4F8E89A5B3A2378B9D8CF37768DB0A (void);
// 0x000004D0 System.Void VRUIInteractor::HandleUIClick(UnityEngine.GameObject,System.Object)
extern void VRUIInteractor_HandleUIClick_mA06E584725B92F2152EFF1A94B981FB1DDFEAC86 (void);
// 0x000004D1 System.Void VRUIInteractor::ClearPressState()
extern void VRUIInteractor_ClearPressState_m70CFDB6772C0E24F2A84B1612DDC640509C292C9 (void);
// 0x000004D2 System.Void VRUIInteractor::ExecuteUIClick(UnityEngine.GameObject)
extern void VRUIInteractor_ExecuteUIClick_m81DA429F8B06C346453C48EE77B5ACCE0709D986 (void);
// 0x000004D3 VRUIInteractor/UIElementState VRUIInteractor::GetOrCreateElementState(UnityEngine.GameObject)
extern void VRUIInteractor_GetOrCreateElementState_m73C764698601C1B25B0C3C3CD699EC5032F6C353 (void);
// 0x000004D4 System.Void VRUIInteractor::PlaySound(UnityEngine.AudioClip)
extern void VRUIInteractor_PlaySound_mF359A1D99C91A7E1C868BE7871510CE7F03DF3D8 (void);
// 0x000004D5 System.Void VRUIInteractor::TriggerHapticFeedback(System.Single,System.Object)
extern void VRUIInteractor_TriggerHapticFeedback_m6BEE684DEFDA0859FC4F0E5ED510BA8F5C011B2F (void);
// 0x000004D6 System.Void VRUIInteractor::OnDestroy()
extern void VRUIInteractor_OnDestroy_mB0461015BD2B85F2DC34241E74256DA5DF3BDB5B (void);
// 0x000004D7 System.Void VRUIInteractor::.ctor()
extern void VRUIInteractor__ctor_m0A50D0BDB266E08F7F1BF5F4D7544BAF0DD30C1E (void);
// 0x000004D8 System.Void VRUIInteractor/UIElementState::.ctor(UnityEngine.GameObject)
extern void UIElementState__ctor_mFDCC2A1F061B62DD6C4E8451A81453D387CDB360 (void);
// 0x000004D9 System.Void VRUILayoutOptimizer::Start()
extern void VRUILayoutOptimizer_Start_m153DB2196F565DDD58E7FF507B6E664B2EA27284 (void);
// 0x000004DA System.Void VRUILayoutOptimizer::OptimizeAllUIElements()
extern void VRUILayoutOptimizer_OptimizeAllUIElements_mE7A302EADE061B79F63669A291C7191AB2087E52 (void);
// 0x000004DB System.Void VRUILayoutOptimizer::OptimizeTexts(UnityEngine.Transform)
extern void VRUILayoutOptimizer_OptimizeTexts_mEFEA6AACA05D72A1285DCA9CDB67CE56E457D166 (void);
// 0x000004DC System.Void VRUILayoutOptimizer::OptimizeText(UnityEngine.UI.Text)
extern void VRUILayoutOptimizer_OptimizeText_m36C34CC01771CD683A36BDEDCC094644026DA74C (void);
// 0x000004DD System.Void VRUILayoutOptimizer::OptimizeButtons(UnityEngine.Transform)
extern void VRUILayoutOptimizer_OptimizeButtons_m7B58CB001FC7A5C23D872022BAA8AAABDC7784AE (void);
// 0x000004DE System.Void VRUILayoutOptimizer::OptimizeButton(UnityEngine.UI.Button)
extern void VRUILayoutOptimizer_OptimizeButton_m2386A26766D1DC205C9C14763F78638F615E34B6 (void);
// 0x000004DF System.Void VRUILayoutOptimizer::OptimizeSliders(UnityEngine.Transform)
extern void VRUILayoutOptimizer_OptimizeSliders_mC7654D0D6FAF68C4FB37D2E9BD4A581533FF2953 (void);
// 0x000004E0 System.Void VRUILayoutOptimizer::OptimizeSlider(UnityEngine.UI.Slider)
extern void VRUILayoutOptimizer_OptimizeSlider_m58A55B95F325662DE8C6A4D93318A1BCB1043D36 (void);
// 0x000004E1 System.Void VRUILayoutOptimizer::OptimizeImages(UnityEngine.Transform)
extern void VRUILayoutOptimizer_OptimizeImages_mC168737595F33D8B01D274A8C5E20AD41C477EFD (void);
// 0x000004E2 System.Void VRUILayoutOptimizer::OptimizeImage(UnityEngine.UI.Image)
extern void VRUILayoutOptimizer_OptimizeImage_m6803B237D36F7FAE7358FC75A8CC2B30F395F85B (void);
// 0x000004E3 System.Void VRUILayoutOptimizer::OptimizeLayoutGroups(UnityEngine.Transform)
extern void VRUILayoutOptimizer_OptimizeLayoutGroups_m4201C600A3D0E5F14299A9718E4F7240D6403EDA (void);
// 0x000004E4 System.Void VRUILayoutOptimizer::OptimizeHorizontalLayoutGroup(UnityEngine.UI.HorizontalLayoutGroup)
extern void VRUILayoutOptimizer_OptimizeHorizontalLayoutGroup_mA3DBD35C63F6E2460819979CD5B49667772EF480 (void);
// 0x000004E5 System.Void VRUILayoutOptimizer::OptimizeVerticalLayoutGroup(UnityEngine.UI.VerticalLayoutGroup)
extern void VRUILayoutOptimizer_OptimizeVerticalLayoutGroup_mB394A46F02D40F3802CA692D85767EDF9A81C020 (void);
// 0x000004E6 System.Void VRUILayoutOptimizer::OptimizeGridLayoutGroup(UnityEngine.UI.GridLayoutGroup)
extern void VRUILayoutOptimizer_OptimizeGridLayoutGroup_m9F7012D3C8D7F947461C42D3212171479A6EDC3A (void);
// 0x000004E7 UnityEngine.Color VRUILayoutOptimizer::OptimizeTextColor(UnityEngine.Color)
extern void VRUILayoutOptimizer_OptimizeTextColor_mAEC87DDD47923F54C5CB5F9FB20FB882648D8D79 (void);
// 0x000004E8 UnityEngine.Color VRUILayoutOptimizer::OptimizeImageColor(UnityEngine.Color)
extern void VRUILayoutOptimizer_OptimizeImageColor_m084376AF520DA6465BE9C879A40D8817D71BF84A (void);
// 0x000004E9 System.Void VRUILayoutOptimizer::RestoreOriginalLayout()
extern void VRUILayoutOptimizer_RestoreOriginalLayout_m4870911CA9B1DC8FEDBE101EF786AE624570E4AE (void);
// 0x000004EA System.Void VRUILayoutOptimizer::RestoreElement(VRUILayoutOptimizer/OptimizationRecord)
extern void VRUILayoutOptimizer_RestoreElement_mE090AE86F57BB8DB8D071640CFAE2252E2C33EA2 (void);
// 0x000004EB System.Void VRUILayoutOptimizer::RestoreText(VRUILayoutOptimizer/OptimizationRecord)
extern void VRUILayoutOptimizer_RestoreText_m80997831094B239579375A769456425A6B02249D (void);
// 0x000004EC System.Void VRUILayoutOptimizer::RestoreButton(VRUILayoutOptimizer/OptimizationRecord)
extern void VRUILayoutOptimizer_RestoreButton_m2A02F4F183EFD1AE6478ECE2420ECB9DC6039901 (void);
// 0x000004ED System.Void VRUILayoutOptimizer::RestoreSlider(VRUILayoutOptimizer/OptimizationRecord)
extern void VRUILayoutOptimizer_RestoreSlider_m23C99C0BFDDF16CD2FB279BF6C940FCC82D8CCFB (void);
// 0x000004EE System.Void VRUILayoutOptimizer::RestoreImage(VRUILayoutOptimizer/OptimizationRecord)
extern void VRUILayoutOptimizer_RestoreImage_m3474557DC90E689B42CF51D866FF2BF028D18E1A (void);
// 0x000004EF System.Void VRUILayoutOptimizer::RestoreLayoutGroup(VRUILayoutOptimizer/OptimizationRecord)
extern void VRUILayoutOptimizer_RestoreLayoutGroup_m3EBBC2981CE7CBAE632F17135FB743303D8E5C21 (void);
// 0x000004F0 System.Void VRUILayoutOptimizer::.ctor()
extern void VRUILayoutOptimizer__ctor_m2561E67A988D883339AE1C8DF391FD033E613A1E (void);
// 0x000004F1 System.Void VRUILayoutOptimizer/OptimizationRecord::.ctor(UnityEngine.GameObject,System.String)
extern void OptimizationRecord__ctor_mBF9772C0B6E93699E1B017273E55607445042DE3 (void);
// 0x000004F2 System.Void VRUIManager::Start()
extern void VRUIManager_Start_m04D2D04C9DA59AA7D5BB3241865F88AEC1578F87 (void);
// 0x000004F3 System.Void VRUIManager::Update()
extern void VRUIManager_Update_m3B8BC3A450E6DD2FAEB1C5AA2B3D79E536253FD0 (void);
// 0x000004F4 System.Void VRUIManager::InitializeVRUI()
extern void VRUIManager_InitializeVRUI_m404C3AC545D813E4C4E7335F7327739BDF2DF7CE (void);
// 0x000004F5 System.Void VRUIManager::FindRequiredComponents()
extern void VRUIManager_FindRequiredComponents_mA6F5D53DC305DA17C05161C5E35EE403D5F3BCDA (void);
// 0x000004F6 System.Boolean VRUIManager::ValidateComponents()
extern void VRUIManager_ValidateComponents_m9C339C67423B1F600A6B28D0ABA555AB0BB64A65 (void);
// 0x000004F7 System.Void VRUIManager::ConfigureCanvas()
extern void VRUIManager_ConfigureCanvas_m92C1363C3CFA70D39A7B883E4E7F5118822BA654 (void);
// 0x000004F8 System.Void VRUIManager::ConfigureEventSystem()
extern void VRUIManager_ConfigureEventSystem_mA4229D01EE54479758572D163EDDC74EBE4AB50F (void);
// 0x000004F9 System.Void VRUIManager::SetInitialUIPosition()
extern void VRUIManager_SetInitialUIPosition_m1D1D8BD430CA8EA6CFC5BE15182D85A998C984FA (void);
// 0x000004FA System.Void VRUIManager::PositionUI()
extern void VRUIManager_PositionUI_m4E4F26379F3A352AADFD59DDE3F8334C170878AF (void);
// 0x000004FB System.Void VRUIManager::UpdateUIPosition()
extern void VRUIManager_UpdateUIPosition_m25322D067DC8AA1513485A2703347D7883DE3251 (void);
// 0x000004FC System.Void VRUIManager::HandleVRInteraction()
extern void VRUIManager_HandleVRInteraction_mC49E19F38920360E2C2CBE9B8359494D2A5241F4 (void);
// 0x000004FD System.Void VRUIManager::RepositionUIToUser()
extern void VRUIManager_RepositionUIToUser_m21CD2DB7AF62040244BFA9F9F194D1F7FB3430F7 (void);
// 0x000004FE System.Void VRUIManager::ResetUIPosition()
extern void VRUIManager_ResetUIPosition_m596AF0D47DC0A5FD88EF29D730565ECAEB897EE5 (void);
// 0x000004FF System.Void VRUIManager::SetFollowMode(System.Boolean)
extern void VRUIManager_SetFollowMode_mECC53B484A4F495A64505E04E6A89D4ABEBA21B2 (void);
// 0x00000500 System.Void VRUIManager::SetUIDistance(System.Single)
extern void VRUIManager_SetUIDistance_m0E02D07045FB937B1FE86CDEA241484D96593D0C (void);
// 0x00000501 System.Void VRUIManager::SetUIScale(System.Single)
extern void VRUIManager_SetUIScale_mC90CB002CE43399EF2496291D67F9E43FB637984 (void);
// 0x00000502 System.Void VRUIManager::.ctor()
extern void VRUIManager__ctor_mA0E0CAF90A4BBB9F6AB114920D3196477A74AD81 (void);
// 0x00000503 System.Void VRUISetupHelper::Start()
extern void VRUISetupHelper_Start_mBC1600985350628411B7E8A7777EA3D16AB19791 (void);
// 0x00000504 System.Void VRUISetupHelper::SetupVRUI()
extern void VRUISetupHelper_SetupVRUI_m95E4059DF585A9179FD7C8A359C98DB82B989F32 (void);
// 0x00000505 UnityEngine.Canvas VRUISetupHelper::SetupCanvas()
extern void VRUISetupHelper_SetupCanvas_m0FD070D92F95A997C3C1F2EF6E579986253F8DB6 (void);
// 0x00000506 System.Void VRUISetupHelper::SetupEventSystem()
extern void VRUISetupHelper_SetupEventSystem_mE259722DC3179C10C4E3B8D4F4394C76D0E572D1 (void);
// 0x00000507 System.Void VRUISetupHelper::SetupVRUIComponents(UnityEngine.Canvas)
extern void VRUISetupHelper_SetupVRUIComponents_m1ECF523B2A08558C0653DD3F8C76DF1DD540D686 (void);
// 0x00000508 System.Void VRUISetupHelper::CreateSampleUI(UnityEngine.Canvas)
extern void VRUISetupHelper_CreateSampleUI_m1614FEE2A064C758138B27CE23F499BB5311F513 (void);
// 0x00000509 UnityEngine.GameObject VRUISetupHelper::CreateUIPanel(UnityEngine.Transform,System.String,UnityEngine.Vector2)
extern void VRUISetupHelper_CreateUIPanel_m691A1C886CE361B6358F3476D103B040215C0B55 (void);
// 0x0000050A UnityEngine.GameObject VRUISetupHelper::CreateUIText(UnityEngine.Transform,System.String,System.String,UnityEngine.Vector2,System.Int32)
extern void VRUISetupHelper_CreateUIText_m47469CA5CCD4B353C4F66D91C0C961124A075353 (void);
// 0x0000050B UnityEngine.GameObject VRUISetupHelper::CreateUIButton(UnityEngine.Transform,System.String,System.String,UnityEngine.Vector2,UnityEngine.Vector2)
extern void VRUISetupHelper_CreateUIButton_m50A37CD89B198B7608924630BA086DBFA9A76D86 (void);
// 0x0000050C UnityEngine.GameObject VRUISetupHelper::CreateUIToggle(UnityEngine.Transform,System.String,System.String,UnityEngine.Vector2)
extern void VRUISetupHelper_CreateUIToggle_m5B0245319F0F065F63D613FF0998B57F08E16A23 (void);
// 0x0000050D UnityEngine.GameObject VRUISetupHelper::CreateUISlider(UnityEngine.Transform,System.String,UnityEngine.Vector2,UnityEngine.Vector2)
extern void VRUISetupHelper_CreateUISlider_m5ECD6ACB9A05D80EEDD4B68109A32E90FD12957D (void);
// 0x0000050E System.Void VRUISetupHelper::OptimizeForVR(UnityEngine.Canvas)
extern void VRUISetupHelper_OptimizeForVR_m891B33AE1CE27A392F7E38BE911258318AAF1EDC (void);
// 0x0000050F System.Void VRUISetupHelper::.ctor()
extern void VRUISetupHelper__ctor_m0A6698FB0021BC243231BF0343361AC73279BED7 (void);
// 0x00000510 System.Void VRUserGuidance::Start()
extern void VRUserGuidance_Start_m2C26A5944C138C6EB9D103D0240709E0409E0DA2 (void);
// 0x00000511 System.Void VRUserGuidance::InitializeGuidance()
extern void VRUserGuidance_InitializeGuidance_m111726F47FA961DDD0D503F7BEF48608DAFA63BE (void);
// 0x00000512 System.Void VRUserGuidance::CreateDefaultGuidanceElements()
extern void VRUserGuidance_CreateDefaultGuidanceElements_m0AFE2F40630B4E4C06B0A43CAEBCC18278A63355 (void);
// 0x00000513 UnityEngine.GameObject VRUserGuidance::CreateDefaultArrow()
extern void VRUserGuidance_CreateDefaultArrow_mB91943240AA44802B4DE5AAC6BDF2608DDAB7F1A (void);
// 0x00000514 UnityEngine.GameObject VRUserGuidance::CreateDefaultFloorIndicator()
extern void VRUserGuidance_CreateDefaultFloorIndicator_mA45E47D528074ADD517DB3183F94D65E07BCF562 (void);
// 0x00000515 UnityEngine.Material VRUserGuidance::CreateGlowMaterial()
extern void VRUserGuidance_CreateGlowMaterial_m495FF8571A8E0BDEDE67C09F3DEFEE7E4E63E3B5 (void);
// 0x00000516 System.Collections.IEnumerator VRUserGuidance::GuideUserToOptimalPosition(UnityEngine.Vector3,System.String)
extern void VRUserGuidance_GuideUserToOptimalPosition_mC79A19264F09BC6BCEF153E0CC101D8126AD4F79 (void);
// 0x00000517 UnityEngine.GameObject VRUserGuidance::CreateFloorIndicator(UnityEngine.Vector3)
extern void VRUserGuidance_CreateFloorIndicator_m49DCB1913C2AE2108BAE7A25987E44FA61C959AF (void);
// 0x00000518 UnityEngine.GameObject VRUserGuidance::CreateDirectionArrow(UnityEngine.Vector3)
extern void VRUserGuidance_CreateDirectionArrow_m49DDE0E2C40D72DA7D005ABC9FF2056B175AAEE9 (void);
// 0x00000519 System.Collections.IEnumerator VRUserGuidance::GuidanceAnimationCoroutine()
extern void VRUserGuidance_GuidanceAnimationCoroutine_m3B8D4FD6BF51349E6E5987D0C6D7886C4F5E7E96 (void);
// 0x0000051A System.Collections.IEnumerator VRUserGuidance::WaitForUserPosition(UnityEngine.Vector3,System.Single)
extern void VRUserGuidance_WaitForUserPosition_m25A0AD600F78E2A12D386122D5318A9D40137B3D (void);
// 0x0000051B System.Void VRUserGuidance::HighlightAssemblyArea(UnityEngine.Transform,System.Single)
extern void VRUserGuidance_HighlightAssemblyArea_m8F23D0E3987831C7C6E06FA181BB7786A9F02894 (void);
// 0x0000051C System.Collections.IEnumerator VRUserGuidance::HighlightCoroutine(UnityEngine.Transform,System.Single)
extern void VRUserGuidance_HighlightCoroutine_m266744DF7580311A49F5D1B52BF4068ACBDD071C (void);
// 0x0000051D System.Void VRUserGuidance::ShowGuidanceMessage(System.String)
extern void VRUserGuidance_ShowGuidanceMessage_mC6CD298B1CF141D06FAC053F87B89F593B523BD6 (void);
// 0x0000051E System.Void VRUserGuidance::HideGuidanceMessage()
extern void VRUserGuidance_HideGuidanceMessage_mA90A93C1FC49799AE7431E38E0AE7BED37A8D988 (void);
// 0x0000051F System.Void VRUserGuidance::PlayVoiceGuidance(System.String)
extern void VRUserGuidance_PlayVoiceGuidance_mF07DE289690B3B6D0D078DF1852F39F7601022E6 (void);
// 0x00000520 System.Void VRUserGuidance::StopCurrentGuidance()
extern void VRUserGuidance_StopCurrentGuidance_mBD5A25783D8D2B21DBE870B5C3F799BF2BF3CCE0 (void);
// 0x00000521 System.Void VRUserGuidance::GuideForAssemblyStep(UnityEngine.Vector3,System.String)
extern void VRUserGuidance_GuideForAssemblyStep_mE182EFF483C7A344834439E154DCF1DBD145356D (void);
// 0x00000522 System.Void VRUserGuidance::GuideToBackView(UnityEngine.Transform)
extern void VRUserGuidance_GuideToBackView_mCA2A1CF9AA944EACA2EC5D7C50E05A92ACD8CCFC (void);
// 0x00000523 System.Void VRUserGuidance::OnDestroy()
extern void VRUserGuidance_OnDestroy_m7BF288A0E12B51E4B715EA2C23E9E3A46FC00E68 (void);
// 0x00000524 System.Void VRUserGuidance::.ctor()
extern void VRUserGuidance__ctor_m9F6E3AFCB34E45E926F7ABAA32999A0B63591C9A (void);
// 0x00000525 System.Void VRUserGuidance/<GuideUserToOptimalPosition>d__24::.ctor(System.Int32)
extern void U3CGuideUserToOptimalPositionU3Ed__24__ctor_m1383C15052E14C47D77A90AAE7423BAF6E590D0D (void);
// 0x00000526 System.Void VRUserGuidance/<GuideUserToOptimalPosition>d__24::System.IDisposable.Dispose()
extern void U3CGuideUserToOptimalPositionU3Ed__24_System_IDisposable_Dispose_mEB8F8FF06B92E65B2167F12F36A9A618A6CD0F04 (void);
// 0x00000527 System.Boolean VRUserGuidance/<GuideUserToOptimalPosition>d__24::MoveNext()
extern void U3CGuideUserToOptimalPositionU3Ed__24_MoveNext_m81B10167D70F0B0245058FFBEFB3F5883B2DE8EC (void);
// 0x00000528 System.Object VRUserGuidance/<GuideUserToOptimalPosition>d__24::System.Collections.Generic.IEnumerator<System.Object>.get_Current()
extern void U3CGuideUserToOptimalPositionU3Ed__24_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mB81D6CD4EF4AAF55F4E784DFC0C52997104AB727 (void);
// 0x00000529 System.Void VRUserGuidance/<GuideUserToOptimalPosition>d__24::System.Collections.IEnumerator.Reset()
extern void U3CGuideUserToOptimalPositionU3Ed__24_System_Collections_IEnumerator_Reset_m6AE49C806C145AF9AF743741D6813856FB20E436 (void);
// 0x0000052A System.Object VRUserGuidance/<GuideUserToOptimalPosition>d__24::System.Collections.IEnumerator.get_Current()
extern void U3CGuideUserToOptimalPositionU3Ed__24_System_Collections_IEnumerator_get_Current_m05DA6C906386DC256EF371B3DC563E92E1BCF2D3 (void);
// 0x0000052B System.Void VRUserGuidance/<GuidanceAnimationCoroutine>d__27::.ctor(System.Int32)
extern void U3CGuidanceAnimationCoroutineU3Ed__27__ctor_m41C3F2CB62ADDF6131E88E9D478201915A12D72B (void);
// 0x0000052C System.Void VRUserGuidance/<GuidanceAnimationCoroutine>d__27::System.IDisposable.Dispose()
extern void U3CGuidanceAnimationCoroutineU3Ed__27_System_IDisposable_Dispose_m3E698714563A7AB7FE0851759B017FFBE7571D8E (void);
// 0x0000052D System.Boolean VRUserGuidance/<GuidanceAnimationCoroutine>d__27::MoveNext()
extern void U3CGuidanceAnimationCoroutineU3Ed__27_MoveNext_mE7639A38359A58563755BBA6746487EF891FB5C3 (void);
// 0x0000052E System.Object VRUserGuidance/<GuidanceAnimationCoroutine>d__27::System.Collections.Generic.IEnumerator<System.Object>.get_Current()
extern void U3CGuidanceAnimationCoroutineU3Ed__27_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mCB8CA2A7022D7B8157A2FFBEF84D936A7F2D1218 (void);
// 0x0000052F System.Void VRUserGuidance/<GuidanceAnimationCoroutine>d__27::System.Collections.IEnumerator.Reset()
extern void U3CGuidanceAnimationCoroutineU3Ed__27_System_Collections_IEnumerator_Reset_m22B7737FA1DE2913391936766502E491C30B285A (void);
// 0x00000530 System.Object VRUserGuidance/<GuidanceAnimationCoroutine>d__27::System.Collections.IEnumerator.get_Current()
extern void U3CGuidanceAnimationCoroutineU3Ed__27_System_Collections_IEnumerator_get_Current_m398E02A923F8C6F2634C18740BF345F5E32A34AF (void);
// 0x00000531 System.Void VRUserGuidance/<WaitForUserPosition>d__28::.ctor(System.Int32)
extern void U3CWaitForUserPositionU3Ed__28__ctor_m0BECD0EF5CF852EADD90DF2D5232960ACDB06907 (void);
// 0x00000532 System.Void VRUserGuidance/<WaitForUserPosition>d__28::System.IDisposable.Dispose()
extern void U3CWaitForUserPositionU3Ed__28_System_IDisposable_Dispose_m945E3580AB7FA758B5E6D66ACFFEEDDA3B144734 (void);
// 0x00000533 System.Boolean VRUserGuidance/<WaitForUserPosition>d__28::MoveNext()
extern void U3CWaitForUserPositionU3Ed__28_MoveNext_m69132C50DDCD695621E34947CA71BCC29A972CAC (void);
// 0x00000534 System.Object VRUserGuidance/<WaitForUserPosition>d__28::System.Collections.Generic.IEnumerator<System.Object>.get_Current()
extern void U3CWaitForUserPositionU3Ed__28_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m1D1F574D4CBF44FC28EFCDBD7EFE663E1C7F8B3C (void);
// 0x00000535 System.Void VRUserGuidance/<WaitForUserPosition>d__28::System.Collections.IEnumerator.Reset()
extern void U3CWaitForUserPositionU3Ed__28_System_Collections_IEnumerator_Reset_mE174ADE025D5BDE79204F7F552D442729054DD6D (void);
// 0x00000536 System.Object VRUserGuidance/<WaitForUserPosition>d__28::System.Collections.IEnumerator.get_Current()
extern void U3CWaitForUserPositionU3Ed__28_System_Collections_IEnumerator_get_Current_mA076668FDC8A7D8845AD524FFEEC97CB07220914 (void);
// 0x00000537 System.Void VRUserGuidance/<HighlightCoroutine>d__30::.ctor(System.Int32)
extern void U3CHighlightCoroutineU3Ed__30__ctor_m4801E771114AF06F0F1DF0AD483BF36DF6F73DFA (void);
// 0x00000538 System.Void VRUserGuidance/<HighlightCoroutine>d__30::System.IDisposable.Dispose()
extern void U3CHighlightCoroutineU3Ed__30_System_IDisposable_Dispose_m1355F4D82BABB77482C78A8B8D47CB9F54A4E42D (void);
// 0x00000539 System.Boolean VRUserGuidance/<HighlightCoroutine>d__30::MoveNext()
extern void U3CHighlightCoroutineU3Ed__30_MoveNext_mA4F4F16B04D4A5CA97C46D94E1C65F499625AA85 (void);
// 0x0000053A System.Object VRUserGuidance/<HighlightCoroutine>d__30::System.Collections.Generic.IEnumerator<System.Object>.get_Current()
extern void U3CHighlightCoroutineU3Ed__30_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m58CAB82258849F24804D018BC8BDAFD7891FD733 (void);
// 0x0000053B System.Void VRUserGuidance/<HighlightCoroutine>d__30::System.Collections.IEnumerator.Reset()
extern void U3CHighlightCoroutineU3Ed__30_System_Collections_IEnumerator_Reset_m4541F0BC302E8F10FFF049C61292FB3086C8E04D (void);
// 0x0000053C System.Object VRUserGuidance/<HighlightCoroutine>d__30::System.Collections.IEnumerator.get_Current()
extern void U3CHighlightCoroutineU3Ed__30_System_Collections_IEnumerator_get_Current_m7B462A3F8AD8BF9C023937252F26709B026C22CC (void);
// 0x0000053D SimpleJSON.JSONNodeType SimpleJSON.JSONNode::get_Tag()
// 0x0000053E SimpleJSON.JSONNode SimpleJSON.JSONNode::get_Item(System.Int32)
extern void JSONNode_get_Item_m77F15891BEC7ED659BFBC392555178B558747AD8 (void);
// 0x0000053F System.Void SimpleJSON.JSONNode::set_Item(System.Int32,SimpleJSON.JSONNode)
extern void JSONNode_set_Item_mC6F47073D979B943286B2EAB1A6D0380AFE58A09 (void);
// 0x00000540 SimpleJSON.JSONNode SimpleJSON.JSONNode::get_Item(System.String)
extern void JSONNode_get_Item_m466B08DF2E30B20606697EC7AE043C2791DC6768 (void);
// 0x00000541 System.Void SimpleJSON.JSONNode::set_Item(System.String,SimpleJSON.JSONNode)
extern void JSONNode_set_Item_m045530804B67FC5E2E57E497219F27ED70CE437E (void);
// 0x00000542 System.String SimpleJSON.JSONNode::get_Value()
extern void JSONNode_get_Value_m2A9961ACC3D4BCBB028012CD79B619DCBD82A839 (void);
// 0x00000543 System.Void SimpleJSON.JSONNode::set_Value(System.String)
extern void JSONNode_set_Value_mE8CD0E68E0E2B0A716F56B0FE9B988EC2BAD773A (void);
// 0x00000544 System.Int32 SimpleJSON.JSONNode::get_Count()
extern void JSONNode_get_Count_m260DDA50B8AFB98F5946E54B9EADD05891A82C8B (void);
// 0x00000545 System.Boolean SimpleJSON.JSONNode::get_IsNumber()
extern void JSONNode_get_IsNumber_m6B495FE576572E9FC7999740C63980BCB65AD768 (void);
// 0x00000546 System.Boolean SimpleJSON.JSONNode::get_IsString()
extern void JSONNode_get_IsString_mBDE2CAF25E51CDA450074BE9DC81D834903BA392 (void);
// 0x00000547 System.Boolean SimpleJSON.JSONNode::get_IsBoolean()
extern void JSONNode_get_IsBoolean_m13F16853C0F6D76D0AB6B7E866923A0632C108A2 (void);
// 0x00000548 System.Boolean SimpleJSON.JSONNode::get_IsNull()
extern void JSONNode_get_IsNull_m6443A7B3540D725ED3ACA0038A74CE0346A31F8D (void);
// 0x00000549 System.Boolean SimpleJSON.JSONNode::get_IsArray()
extern void JSONNode_get_IsArray_m52DCDB47E4CB2673FDCECCD3BE9DD2D90B5C948F (void);
// 0x0000054A System.Boolean SimpleJSON.JSONNode::get_IsObject()
extern void JSONNode_get_IsObject_m237FE2EA3382DD9762ED426B49F46183F5EF39AB (void);
// 0x0000054B System.Boolean SimpleJSON.JSONNode::get_Inline()
extern void JSONNode_get_Inline_m7A5B6C07F44EFEEDD80FD72580C32C0579041F4C (void);
// 0x0000054C System.Void SimpleJSON.JSONNode::set_Inline(System.Boolean)
extern void JSONNode_set_Inline_m18362F10F03DDCD1FF29B4868C3EA793D39AE7F6 (void);
// 0x0000054D System.Void SimpleJSON.JSONNode::Add(System.String,SimpleJSON.JSONNode)
extern void JSONNode_Add_mB05F1A32B54A9A1223F9AC6A6A737836FA1F4E7E (void);
// 0x0000054E System.Void SimpleJSON.JSONNode::Add(SimpleJSON.JSONNode)
extern void JSONNode_Add_mDAF96580EAF3B9FF23888A8549BED7A98439075D (void);
// 0x0000054F SimpleJSON.JSONNode SimpleJSON.JSONNode::Remove(System.String)
extern void JSONNode_Remove_mF56C4223700DF4F1D5AE12BCD69C492C2487FA59 (void);
// 0x00000550 SimpleJSON.JSONNode SimpleJSON.JSONNode::Remove(System.Int32)
extern void JSONNode_Remove_m7B5E0BC0A29C35857D7B10857A8C52C0E3DFB615 (void);
// 0x00000551 SimpleJSON.JSONNode SimpleJSON.JSONNode::Remove(SimpleJSON.JSONNode)
extern void JSONNode_Remove_mE2CFD05512C25BD11EA4160CAAF88B8154D9DBE5 (void);
// 0x00000552 System.Void SimpleJSON.JSONNode::Clear()
extern void JSONNode_Clear_mD9B59BDE8D07A352AB775FD4A8FB262D406EB848 (void);
// 0x00000553 SimpleJSON.JSONNode SimpleJSON.JSONNode::Clone()
extern void JSONNode_Clone_mE7849A4FBD98462D93E715826B0D01DE7FC822C3 (void);
// 0x00000554 System.Collections.Generic.IEnumerable`1<SimpleJSON.JSONNode> SimpleJSON.JSONNode::get_Children()
extern void JSONNode_get_Children_m3E2D70DBCA2C8311F65A47B766668728392B1F89 (void);
// 0x00000555 System.Collections.Generic.IEnumerable`1<SimpleJSON.JSONNode> SimpleJSON.JSONNode::get_DeepChildren()
extern void JSONNode_get_DeepChildren_m891CB892AEA834980686ED760B952A86DC1E8725 (void);
// 0x00000556 System.Boolean SimpleJSON.JSONNode::HasKey(System.String)
extern void JSONNode_HasKey_mBEF13E4AC99F2F0D76D4CD87405BB71586C4486B (void);
// 0x00000557 SimpleJSON.JSONNode SimpleJSON.JSONNode::GetValueOrDefault(System.String,SimpleJSON.JSONNode)
extern void JSONNode_GetValueOrDefault_m751E871953EBA8094B4DE73CC261C884720811F6 (void);
// 0x00000558 System.String SimpleJSON.JSONNode::ToString()
extern void JSONNode_ToString_m4CC464630B0AEEDD82AEB6B069690949AF569345 (void);
// 0x00000559 System.String SimpleJSON.JSONNode::ToString(System.Int32)
extern void JSONNode_ToString_m1F607CB90F49115510B7CF5228733578E9AD41F2 (void);
// 0x0000055A System.Void SimpleJSON.JSONNode::WriteToStringBuilder(System.Text.StringBuilder,System.Int32,System.Int32,SimpleJSON.JSONTextMode)
// 0x0000055B SimpleJSON.JSONNode/Enumerator SimpleJSON.JSONNode::GetEnumerator()
// 0x0000055C System.Collections.Generic.IEnumerable`1<System.Collections.Generic.KeyValuePair`2<System.String,SimpleJSON.JSONNode>> SimpleJSON.JSONNode::get_Linq()
extern void JSONNode_get_Linq_m8569DB478533504290D9A09ECA0DF12F116122DA (void);
// 0x0000055D SimpleJSON.JSONNode/KeyEnumerator SimpleJSON.JSONNode::get_Keys()
extern void JSONNode_get_Keys_mC3401CC91BBD9D1166EF8EFC0C87A820FC543D1B (void);
// 0x0000055E SimpleJSON.JSONNode/ValueEnumerator SimpleJSON.JSONNode::get_Values()
extern void JSONNode_get_Values_mF8FB164A48A169146D00EBA3F51D4C8E380C1930 (void);
// 0x0000055F System.Double SimpleJSON.JSONNode::get_AsDouble()
extern void JSONNode_get_AsDouble_m9A8E3EC46E4545BCBFA26B99C0F013067D2F0AE4 (void);
// 0x00000560 System.Void SimpleJSON.JSONNode::set_AsDouble(System.Double)
extern void JSONNode_set_AsDouble_mCDBB05BD0AE82EEF0C4842F5A9205B8F4C858015 (void);
// 0x00000561 System.Int32 SimpleJSON.JSONNode::get_AsInt()
extern void JSONNode_get_AsInt_mE4A3FCC1D91362D077C2ACF418ACAB43771B1FE6 (void);
// 0x00000562 System.Void SimpleJSON.JSONNode::set_AsInt(System.Int32)
extern void JSONNode_set_AsInt_m12FCF0B7E45E17EA0456AE44EFEF0C8731603F50 (void);
// 0x00000563 System.Single SimpleJSON.JSONNode::get_AsFloat()
extern void JSONNode_get_AsFloat_m0D044C1F3FC35086783A4BAF506EA96DC997D050 (void);
// 0x00000564 System.Void SimpleJSON.JSONNode::set_AsFloat(System.Single)
extern void JSONNode_set_AsFloat_m55FCE24DF60B37724DACCCF0A759522B2561DE92 (void);
// 0x00000565 System.Boolean SimpleJSON.JSONNode::get_AsBool()
extern void JSONNode_get_AsBool_m902380F5939671ACBBB7EFA01A48F1A082B1FD9C (void);
// 0x00000566 System.Void SimpleJSON.JSONNode::set_AsBool(System.Boolean)
extern void JSONNode_set_AsBool_m6097FD196A8C7BB156125363D1C1D3EF0EB67CD3 (void);
// 0x00000567 System.Int64 SimpleJSON.JSONNode::get_AsLong()
extern void JSONNode_get_AsLong_m31250905C6F4BED9B1059009E064D07D609C4C18 (void);
// 0x00000568 System.Void SimpleJSON.JSONNode::set_AsLong(System.Int64)
extern void JSONNode_set_AsLong_m8D29780DEA1458A2F9C33805432DB1554950ECF4 (void);
// 0x00000569 System.UInt64 SimpleJSON.JSONNode::get_AsULong()
extern void JSONNode_get_AsULong_mA34C3D1DA0D3339D0B63F386867ADE3E460909DD (void);
// 0x0000056A System.Void SimpleJSON.JSONNode::set_AsULong(System.UInt64)
extern void JSONNode_set_AsULong_m2BC120C5B1842E17BC0E6E5714511391DD504091 (void);
// 0x0000056B SimpleJSON.JSONArray SimpleJSON.JSONNode::get_AsArray()
extern void JSONNode_get_AsArray_m2D0890FDDA140528CAB44B1B6B3E34B26383ACC7 (void);
// 0x0000056C SimpleJSON.JSONObject SimpleJSON.JSONNode::get_AsObject()
extern void JSONNode_get_AsObject_m72F6D406BECA2FB0A24B20E0A353FDB8E409CA1B (void);
// 0x0000056D SimpleJSON.JSONNode SimpleJSON.JSONNode::op_Implicit(System.String)
extern void JSONNode_op_Implicit_m71A2D2EECDD79DC3A3DAF6510BB2F8ED57DE6AAC (void);
// 0x0000056E System.String SimpleJSON.JSONNode::op_Implicit(SimpleJSON.JSONNode)
extern void JSONNode_op_Implicit_m6019D30B60A2033906907488CB6236EC9A7B7B6B (void);
// 0x0000056F SimpleJSON.JSONNode SimpleJSON.JSONNode::op_Implicit(System.Double)
extern void JSONNode_op_Implicit_m098A31323C3D89615E0EBD709D83AB4F684453CF (void);
// 0x00000570 System.Double SimpleJSON.JSONNode::op_Implicit(SimpleJSON.JSONNode)
extern void JSONNode_op_Implicit_m17B71B28DE136B73EF2B97DA87BB4A4BB27332E9 (void);
// 0x00000571 SimpleJSON.JSONNode SimpleJSON.JSONNode::op_Implicit(System.Single)
extern void JSONNode_op_Implicit_m4A9267CC71FC4FDD39ABAE262B7B2D334EB0FFBD (void);
// 0x00000572 System.Single SimpleJSON.JSONNode::op_Implicit(SimpleJSON.JSONNode)
extern void JSONNode_op_Implicit_m808991B4DFA11ECBF7080226CFC3069A7E6673E8 (void);
// 0x00000573 SimpleJSON.JSONNode SimpleJSON.JSONNode::op_Implicit(System.Int32)
extern void JSONNode_op_Implicit_m6AC3F8DDC02644CA8D85EC90758373D1B7EC4322 (void);
// 0x00000574 System.Int32 SimpleJSON.JSONNode::op_Implicit(SimpleJSON.JSONNode)
extern void JSONNode_op_Implicit_mD9A5824FE62046D01AD966EC503DEB775B2C7482 (void);
// 0x00000575 SimpleJSON.JSONNode SimpleJSON.JSONNode::op_Implicit(System.Int64)
extern void JSONNode_op_Implicit_m94EF6FC36942EA4A49ABFCA42FC12BCE914990FA (void);
// 0x00000576 System.Int64 SimpleJSON.JSONNode::op_Implicit(SimpleJSON.JSONNode)
extern void JSONNode_op_Implicit_mD25BDDE21767954AE9D36F16948B6F77173EC2F6 (void);
// 0x00000577 SimpleJSON.JSONNode SimpleJSON.JSONNode::op_Implicit(System.UInt64)
extern void JSONNode_op_Implicit_m64294932E998EC6A35EF99F1CD4D36BFB9A8FB1E (void);
// 0x00000578 System.UInt64 SimpleJSON.JSONNode::op_Implicit(SimpleJSON.JSONNode)
extern void JSONNode_op_Implicit_mD5974501A6FBBD60D6E7331940440498D67A7A05 (void);
// 0x00000579 SimpleJSON.JSONNode SimpleJSON.JSONNode::op_Implicit(System.Boolean)
extern void JSONNode_op_Implicit_mCE1B7A6233218E114687A876F778B4A1CBF22B74 (void);
// 0x0000057A System.Boolean SimpleJSON.JSONNode::op_Implicit(SimpleJSON.JSONNode)
extern void JSONNode_op_Implicit_m29CA9621387E0DDDECCCAAB240A140A854567FDF (void);
// 0x0000057B SimpleJSON.JSONNode SimpleJSON.JSONNode::op_Implicit(System.Collections.Generic.KeyValuePair`2<System.String,SimpleJSON.JSONNode>)
extern void JSONNode_op_Implicit_mB9C34D74CF1854E402B4AD106AB2084169287E1E (void);
// 0x0000057C System.Boolean SimpleJSON.JSONNode::op_Equality(SimpleJSON.JSONNode,System.Object)
extern void JSONNode_op_Equality_mD30EBFA5F9398107FCC5CE51B05CE4CFFBCC6A8E (void);
// 0x0000057D System.Boolean SimpleJSON.JSONNode::op_Inequality(SimpleJSON.JSONNode,System.Object)
extern void JSONNode_op_Inequality_m91693B2A4AC881F8703CC1D1050371B8EC552CF7 (void);
// 0x0000057E System.Boolean SimpleJSON.JSONNode::Equals(System.Object)
extern void JSONNode_Equals_mE1B8A846783529B1E54786975A6A2396089A88DE (void);
// 0x0000057F System.Int32 SimpleJSON.JSONNode::GetHashCode()
extern void JSONNode_GetHashCode_m0A263555D1F0E6766A61692A7E1BC3546B2BC984 (void);
// 0x00000580 System.Text.StringBuilder SimpleJSON.JSONNode::get_EscapeBuilder()
extern void JSONNode_get_EscapeBuilder_mA695C85FBFBCF3863E2AC3B63821B469CC632DB1 (void);
// 0x00000581 System.String SimpleJSON.JSONNode::Escape(System.String)
extern void JSONNode_Escape_m5C811748A36C7258315C1D2036712855F184ADDD (void);
// 0x00000582 SimpleJSON.JSONNode SimpleJSON.JSONNode::ParseElement(System.String,System.Boolean)
extern void JSONNode_ParseElement_m3478B79AC164A87E0B2088067EDEC6DE146DAA56 (void);
// 0x00000583 SimpleJSON.JSONNode SimpleJSON.JSONNode::Parse(System.String)
extern void JSONNode_Parse_m7198C73C509B06CD8A96576D7D2A5A125DC7D0B4 (void);
// 0x00000584 System.Void SimpleJSON.JSONNode::.ctor()
extern void JSONNode__ctor_mF8F2893483161D3B7B9877B63C69063D26A5C353 (void);
// 0x00000585 System.Void SimpleJSON.JSONNode::.cctor()
extern void JSONNode__cctor_m00855C5266A7EF25B6EBE62476F1FAD5C7046065 (void);
// 0x00000586 System.Boolean SimpleJSON.JSONNode/Enumerator::get_IsValid()
extern void Enumerator_get_IsValid_mBC273331DC1699FF46BD3621AE5059A54AD98BA6 (void);
// 0x00000587 System.Void SimpleJSON.JSONNode/Enumerator::.ctor(System.Collections.Generic.List`1/Enumerator<SimpleJSON.JSONNode>)
extern void Enumerator__ctor_mF21239C69620D815F8CD34F022BE18E9DAF9CB10 (void);
// 0x00000588 System.Void SimpleJSON.JSONNode/Enumerator::.ctor(System.Collections.Generic.Dictionary`2/Enumerator<System.String,SimpleJSON.JSONNode>)
extern void Enumerator__ctor_mAC4ED0FA4B083E2652E865A41EA5C74A49478EFE (void);
// 0x00000589 System.Collections.Generic.KeyValuePair`2<System.String,SimpleJSON.JSONNode> SimpleJSON.JSONNode/Enumerator::get_Current()
extern void Enumerator_get_Current_mDE6750203413E1069D0520793D6AA0B2527CB20E (void);
// 0x0000058A System.Boolean SimpleJSON.JSONNode/Enumerator::MoveNext()
extern void Enumerator_MoveNext_m238CF072385A1106BEDEFCE33BA2B0DBE999758A (void);
// 0x0000058B System.Void SimpleJSON.JSONNode/ValueEnumerator::.ctor(System.Collections.Generic.List`1/Enumerator<SimpleJSON.JSONNode>)
extern void ValueEnumerator__ctor_mCC61CE3EDCF1AC94A84E031F2E89F8054C94A015 (void);
// 0x0000058C System.Void SimpleJSON.JSONNode/ValueEnumerator::.ctor(System.Collections.Generic.Dictionary`2/Enumerator<System.String,SimpleJSON.JSONNode>)
extern void ValueEnumerator__ctor_m122732DF448B45E8E82956E07AC8314C60E28C29 (void);
// 0x0000058D System.Void SimpleJSON.JSONNode/ValueEnumerator::.ctor(SimpleJSON.JSONNode/Enumerator)
extern void ValueEnumerator__ctor_m7BA4BAD5FEBAC4054F71575B728DC27EC4080F0A (void);
// 0x0000058E SimpleJSON.JSONNode SimpleJSON.JSONNode/ValueEnumerator::get_Current()
extern void ValueEnumerator_get_Current_mAA24A52FDEB7160BD268193175388EACB41B7CE2 (void);
// 0x0000058F System.Boolean SimpleJSON.JSONNode/ValueEnumerator::MoveNext()
extern void ValueEnumerator_MoveNext_m5B596A2EF2FF395EDA8F5CAB97C0789498D250C9 (void);
// 0x00000590 SimpleJSON.JSONNode/ValueEnumerator SimpleJSON.JSONNode/ValueEnumerator::GetEnumerator()
extern void ValueEnumerator_GetEnumerator_m765261287A2C0AEF757B94994826F43951387E4C (void);
// 0x00000591 System.Void SimpleJSON.JSONNode/KeyEnumerator::.ctor(System.Collections.Generic.List`1/Enumerator<SimpleJSON.JSONNode>)
extern void KeyEnumerator__ctor_m6EA81E2BED4CA5194A7306D8B324F7356E37F80A (void);
// 0x00000592 System.Void SimpleJSON.JSONNode/KeyEnumerator::.ctor(System.Collections.Generic.Dictionary`2/Enumerator<System.String,SimpleJSON.JSONNode>)
extern void KeyEnumerator__ctor_mA6338E82A9F8AA19A1744352B4FE54103AD70405 (void);
// 0x00000593 System.Void SimpleJSON.JSONNode/KeyEnumerator::.ctor(SimpleJSON.JSONNode/Enumerator)
extern void KeyEnumerator__ctor_m526EA1364C367B83C931F4208CDD816BD02810EA (void);
// 0x00000594 System.String SimpleJSON.JSONNode/KeyEnumerator::get_Current()
extern void KeyEnumerator_get_Current_mB4E0F33D7E23A7F365D12B3530DE7FB6B7A1F7E3 (void);
// 0x00000595 System.Boolean SimpleJSON.JSONNode/KeyEnumerator::MoveNext()
extern void KeyEnumerator_MoveNext_m42FE2CEE808A7E065895BA333B7FBD2F3AEE032F (void);
// 0x00000596 SimpleJSON.JSONNode/KeyEnumerator SimpleJSON.JSONNode/KeyEnumerator::GetEnumerator()
extern void KeyEnumerator_GetEnumerator_mD4687B4D6D10E4D6870CBBECC680689A62A95C0B (void);
// 0x00000597 System.Void SimpleJSON.JSONNode/LinqEnumerator::.ctor(SimpleJSON.JSONNode)
extern void LinqEnumerator__ctor_m9FD8AB1580F3D94C5C36D070DBE85E023ED36E30 (void);
// 0x00000598 System.Collections.Generic.KeyValuePair`2<System.String,SimpleJSON.JSONNode> SimpleJSON.JSONNode/LinqEnumerator::get_Current()
extern void LinqEnumerator_get_Current_m28F0BE4D9B5736F5BD79197C1895EAC1592EBAAF (void);
// 0x00000599 System.Object SimpleJSON.JSONNode/LinqEnumerator::System.Collections.IEnumerator.get_Current()
extern void LinqEnumerator_System_Collections_IEnumerator_get_Current_m6B6C12C7E8CD21DF513FCDCB4E88E454790B6FF0 (void);
// 0x0000059A System.Boolean SimpleJSON.JSONNode/LinqEnumerator::MoveNext()
extern void LinqEnumerator_MoveNext_mCA8604B6E8D857CF16003E674048C05E29447819 (void);
// 0x0000059B System.Void SimpleJSON.JSONNode/LinqEnumerator::Dispose()
extern void LinqEnumerator_Dispose_m5D6A54C4B712D138739726323D5BEA50A4E12E32 (void);
// 0x0000059C System.Collections.Generic.IEnumerator`1<System.Collections.Generic.KeyValuePair`2<System.String,SimpleJSON.JSONNode>> SimpleJSON.JSONNode/LinqEnumerator::GetEnumerator()
extern void LinqEnumerator_GetEnumerator_m4A9F0720F0C0964F91032AB8B8776F09DC70A90B (void);
// 0x0000059D System.Void SimpleJSON.JSONNode/LinqEnumerator::Reset()
extern void LinqEnumerator_Reset_m56B65E398518EF57070307FDC48069DFE37BC57B (void);
// 0x0000059E System.Collections.IEnumerator SimpleJSON.JSONNode/LinqEnumerator::System.Collections.IEnumerable.GetEnumerator()
extern void LinqEnumerator_System_Collections_IEnumerable_GetEnumerator_mB63F02D713868ABF87DAB18ABFD5D832F4D805A4 (void);
// 0x0000059F System.Void SimpleJSON.JSONNode/<get_Children>d__43::.ctor(System.Int32)
extern void U3Cget_ChildrenU3Ed__43__ctor_mA2E1AC1211A03DAFF45B69AF872ED71E58F4D458 (void);
// 0x000005A0 System.Void SimpleJSON.JSONNode/<get_Children>d__43::System.IDisposable.Dispose()
extern void U3Cget_ChildrenU3Ed__43_System_IDisposable_Dispose_m0C7490DE49A53AB049729E66293845681AB08395 (void);
// 0x000005A1 System.Boolean SimpleJSON.JSONNode/<get_Children>d__43::MoveNext()
extern void U3Cget_ChildrenU3Ed__43_MoveNext_m33A56DB8F47EADE4EB91E3FBFF4D01F1CF255839 (void);
// 0x000005A2 SimpleJSON.JSONNode SimpleJSON.JSONNode/<get_Children>d__43::System.Collections.Generic.IEnumerator<SimpleJSON.JSONNode>.get_Current()
extern void U3Cget_ChildrenU3Ed__43_System_Collections_Generic_IEnumeratorU3CSimpleJSON_JSONNodeU3E_get_Current_m85EB3E729C5EE85E2103FED7453D79C1D132C2EB (void);
// 0x000005A3 System.Void SimpleJSON.JSONNode/<get_Children>d__43::System.Collections.IEnumerator.Reset()
extern void U3Cget_ChildrenU3Ed__43_System_Collections_IEnumerator_Reset_m755BAC68C65681AA8266C6AC37D2308771D54067 (void);
// 0x000005A4 System.Object SimpleJSON.JSONNode/<get_Children>d__43::System.Collections.IEnumerator.get_Current()
extern void U3Cget_ChildrenU3Ed__43_System_Collections_IEnumerator_get_Current_m04BDDA2EB2EC20489BB50BDDB46313F624F90CF9 (void);
// 0x000005A5 System.Collections.Generic.IEnumerator`1<SimpleJSON.JSONNode> SimpleJSON.JSONNode/<get_Children>d__43::System.Collections.Generic.IEnumerable<SimpleJSON.JSONNode>.GetEnumerator()
extern void U3Cget_ChildrenU3Ed__43_System_Collections_Generic_IEnumerableU3CSimpleJSON_JSONNodeU3E_GetEnumerator_m96326AFEFC6998DB0E90D15633CFE23661C21916 (void);
// 0x000005A6 System.Collections.IEnumerator SimpleJSON.JSONNode/<get_Children>d__43::System.Collections.IEnumerable.GetEnumerator()
extern void U3Cget_ChildrenU3Ed__43_System_Collections_IEnumerable_GetEnumerator_m39BF4FF795523B96CA4FA6383244D82117D96C46 (void);
// 0x000005A7 System.Void SimpleJSON.JSONNode/<get_DeepChildren>d__45::.ctor(System.Int32)
extern void U3Cget_DeepChildrenU3Ed__45__ctor_m89830CB6F115E0AD956EF880354CAFBAD7AF9E5A (void);
// 0x000005A8 System.Void SimpleJSON.JSONNode/<get_DeepChildren>d__45::System.IDisposable.Dispose()
extern void U3Cget_DeepChildrenU3Ed__45_System_IDisposable_Dispose_mCE52C471742B7A6DA19AF43E9096545012D560DD (void);
// 0x000005A9 System.Boolean SimpleJSON.JSONNode/<get_DeepChildren>d__45::MoveNext()
extern void U3Cget_DeepChildrenU3Ed__45_MoveNext_m644F556E82CCF23C7B91E0B0266F4716E18C2F5E (void);
// 0x000005AA System.Void SimpleJSON.JSONNode/<get_DeepChildren>d__45::<>m__Finally1()
extern void U3Cget_DeepChildrenU3Ed__45_U3CU3Em__Finally1_mBA31C43EB8ACB72C8A163B470D786ACB361CF740 (void);
// 0x000005AB System.Void SimpleJSON.JSONNode/<get_DeepChildren>d__45::<>m__Finally2()
extern void U3Cget_DeepChildrenU3Ed__45_U3CU3Em__Finally2_mC829190BED7A6B48F2F4C64848495925A3C58EEE (void);
// 0x000005AC SimpleJSON.JSONNode SimpleJSON.JSONNode/<get_DeepChildren>d__45::System.Collections.Generic.IEnumerator<SimpleJSON.JSONNode>.get_Current()
extern void U3Cget_DeepChildrenU3Ed__45_System_Collections_Generic_IEnumeratorU3CSimpleJSON_JSONNodeU3E_get_Current_m6E1A05C1C6A7BF9748F1768E2B2AB1B140F49983 (void);
// 0x000005AD System.Void SimpleJSON.JSONNode/<get_DeepChildren>d__45::System.Collections.IEnumerator.Reset()
extern void U3Cget_DeepChildrenU3Ed__45_System_Collections_IEnumerator_Reset_mB10807E87C7440A590E9580E6A5B329ACCAD49E4 (void);
// 0x000005AE System.Object SimpleJSON.JSONNode/<get_DeepChildren>d__45::System.Collections.IEnumerator.get_Current()
extern void U3Cget_DeepChildrenU3Ed__45_System_Collections_IEnumerator_get_Current_m2A8CD7D70A8ACF8A362378B75EAF7B41BC9FCEF6 (void);
// 0x000005AF System.Collections.Generic.IEnumerator`1<SimpleJSON.JSONNode> SimpleJSON.JSONNode/<get_DeepChildren>d__45::System.Collections.Generic.IEnumerable<SimpleJSON.JSONNode>.GetEnumerator()
extern void U3Cget_DeepChildrenU3Ed__45_System_Collections_Generic_IEnumerableU3CSimpleJSON_JSONNodeU3E_GetEnumerator_mAD2929E624663DCA925B762F05FCF8CDDE1FC6C8 (void);
// 0x000005B0 System.Collections.IEnumerator SimpleJSON.JSONNode/<get_DeepChildren>d__45::System.Collections.IEnumerable.GetEnumerator()
extern void U3Cget_DeepChildrenU3Ed__45_System_Collections_IEnumerable_GetEnumerator_m8BB12003DCC4402BDA35F5B5AE1B82EF7C1A4856 (void);
// 0x000005B1 System.Boolean SimpleJSON.JSONArray::get_Inline()
extern void JSONArray_get_Inline_mBA0C9AEBB7420DBDFD977C0F54CC237E8F2BE3E5 (void);
// 0x000005B2 System.Void SimpleJSON.JSONArray::set_Inline(System.Boolean)
extern void JSONArray_set_Inline_m731089F5D0FA649ED210518DC299635A8D86A1DC (void);
// 0x000005B3 SimpleJSON.JSONNodeType SimpleJSON.JSONArray::get_Tag()
extern void JSONArray_get_Tag_m360EB078D7897D6D52783B8CDA6B736D014E97BC (void);
// 0x000005B4 System.Boolean SimpleJSON.JSONArray::get_IsArray()
extern void JSONArray_get_IsArray_mA7B4EF5B0128FB64ACEB7EAC66FA3522991980AF (void);
// 0x000005B5 SimpleJSON.JSONNode/Enumerator SimpleJSON.JSONArray::GetEnumerator()
extern void JSONArray_GetEnumerator_m6AF64AE0DD2A5AAB8C0E271BF0CAB8AA1FD32E17 (void);
// 0x000005B6 SimpleJSON.JSONNode SimpleJSON.JSONArray::get_Item(System.Int32)
extern void JSONArray_get_Item_m8BE9047FC512840E6A4594560EDF86BB4E0FF657 (void);
// 0x000005B7 System.Void SimpleJSON.JSONArray::set_Item(System.Int32,SimpleJSON.JSONNode)
extern void JSONArray_set_Item_mBCD05590C34BC589B786E753B9FE796EBA3F6725 (void);
// 0x000005B8 SimpleJSON.JSONNode SimpleJSON.JSONArray::get_Item(System.String)
extern void JSONArray_get_Item_mE18312128B02B505BA656D7F444B05A6769710AE (void);
// 0x000005B9 System.Void SimpleJSON.JSONArray::set_Item(System.String,SimpleJSON.JSONNode)
extern void JSONArray_set_Item_mE4E0DE5133E60AF49E46FEDAD00D2A04349C0855 (void);
// 0x000005BA System.Int32 SimpleJSON.JSONArray::get_Count()
extern void JSONArray_get_Count_mB71218A2D8288D0665C467844F7351D301FDAFDD (void);
// 0x000005BB System.Void SimpleJSON.JSONArray::Add(System.String,SimpleJSON.JSONNode)
extern void JSONArray_Add_mD1FBE0F0FC20E7415014B7FF21939592EBB0C9A1 (void);
// 0x000005BC SimpleJSON.JSONNode SimpleJSON.JSONArray::Remove(System.Int32)
extern void JSONArray_Remove_m79500DBD9751A04C02756470A4D22DDCF9C97FEC (void);
// 0x000005BD SimpleJSON.JSONNode SimpleJSON.JSONArray::Remove(SimpleJSON.JSONNode)
extern void JSONArray_Remove_m64C3EBFE3DB5BE130232769DC43000E84589E674 (void);
// 0x000005BE System.Void SimpleJSON.JSONArray::Clear()
extern void JSONArray_Clear_m86E2E8BE6493C5C555525B9935AFF9E53BB72C2B (void);
// 0x000005BF SimpleJSON.JSONNode SimpleJSON.JSONArray::Clone()
extern void JSONArray_Clone_mA05BA59E71672A88208218DF12C4E5F7A8773502 (void);
// 0x000005C0 System.Collections.Generic.IEnumerable`1<SimpleJSON.JSONNode> SimpleJSON.JSONArray::get_Children()
extern void JSONArray_get_Children_m733AE4C5816E51E6F86441110606489A0406AA91 (void);
// 0x000005C1 System.Void SimpleJSON.JSONArray::WriteToStringBuilder(System.Text.StringBuilder,System.Int32,System.Int32,SimpleJSON.JSONTextMode)
extern void JSONArray_WriteToStringBuilder_m9F23115433028794DCAC019F82EEFD946990D994 (void);
// 0x000005C2 System.Void SimpleJSON.JSONArray::.ctor()
extern void JSONArray__ctor_m92FFF2DC8E1425398814F50D4B253EB459B8477F (void);
// 0x000005C3 System.Void SimpleJSON.JSONArray/<get_Children>d__24::.ctor(System.Int32)
extern void U3Cget_ChildrenU3Ed__24__ctor_m4FA6CFA96B1189496D9E219499A0C05F713A6D28 (void);
// 0x000005C4 System.Void SimpleJSON.JSONArray/<get_Children>d__24::System.IDisposable.Dispose()
extern void U3Cget_ChildrenU3Ed__24_System_IDisposable_Dispose_m91E6F93E3940835795BCA9BFD783592E29BDEE5A (void);
// 0x000005C5 System.Boolean SimpleJSON.JSONArray/<get_Children>d__24::MoveNext()
extern void U3Cget_ChildrenU3Ed__24_MoveNext_m9C8F57C9E0722A9D843A2BA0259E7EE30778CF6B (void);
// 0x000005C6 System.Void SimpleJSON.JSONArray/<get_Children>d__24::<>m__Finally1()
extern void U3Cget_ChildrenU3Ed__24_U3CU3Em__Finally1_m8E8730694C83B14CFFB30D810166D12563C1DFF2 (void);
// 0x000005C7 SimpleJSON.JSONNode SimpleJSON.JSONArray/<get_Children>d__24::System.Collections.Generic.IEnumerator<SimpleJSON.JSONNode>.get_Current()
extern void U3Cget_ChildrenU3Ed__24_System_Collections_Generic_IEnumeratorU3CSimpleJSON_JSONNodeU3E_get_Current_m6958E538A455210191F2E06BA531D4AE5F0E97F0 (void);
// 0x000005C8 System.Void SimpleJSON.JSONArray/<get_Children>d__24::System.Collections.IEnumerator.Reset()
extern void U3Cget_ChildrenU3Ed__24_System_Collections_IEnumerator_Reset_mE122AA2BA93A72C8C8733C4F7EC6A7B8CFB42FCD (void);
// 0x000005C9 System.Object SimpleJSON.JSONArray/<get_Children>d__24::System.Collections.IEnumerator.get_Current()
extern void U3Cget_ChildrenU3Ed__24_System_Collections_IEnumerator_get_Current_m508CF18DF3857321EA1CFDC62E0406DBEF6FDF7F (void);
// 0x000005CA System.Collections.Generic.IEnumerator`1<SimpleJSON.JSONNode> SimpleJSON.JSONArray/<get_Children>d__24::System.Collections.Generic.IEnumerable<SimpleJSON.JSONNode>.GetEnumerator()
extern void U3Cget_ChildrenU3Ed__24_System_Collections_Generic_IEnumerableU3CSimpleJSON_JSONNodeU3E_GetEnumerator_m7679E5F774E9512FC2DA58B2D0236A66983BC632 (void);
// 0x000005CB System.Collections.IEnumerator SimpleJSON.JSONArray/<get_Children>d__24::System.Collections.IEnumerable.GetEnumerator()
extern void U3Cget_ChildrenU3Ed__24_System_Collections_IEnumerable_GetEnumerator_m7593480F6CC6218E2EA7CD84ED3A56FF6274AB32 (void);
// 0x000005CC System.Boolean SimpleJSON.JSONObject::get_Inline()
extern void JSONObject_get_Inline_mCDF2154366BEFF9E547918F999E7F3C7C4865F84 (void);
// 0x000005CD System.Void SimpleJSON.JSONObject::set_Inline(System.Boolean)
extern void JSONObject_set_Inline_m7F048A7565E5A53FDB610D44B7CA75A314CB7A7A (void);
// 0x000005CE SimpleJSON.JSONNodeType SimpleJSON.JSONObject::get_Tag()
extern void JSONObject_get_Tag_mD57D6BCAD1C677B88693FD508129CFAD661F4FBD (void);
// 0x000005CF System.Boolean SimpleJSON.JSONObject::get_IsObject()
extern void JSONObject_get_IsObject_m9F72861BE5A0DB2888AA3CBEC82718E08DD71E93 (void);
// 0x000005D0 SimpleJSON.JSONNode/Enumerator SimpleJSON.JSONObject::GetEnumerator()
extern void JSONObject_GetEnumerator_m8912E3D1EA302655BB5701B53EB19437238BABDA (void);
// 0x000005D1 SimpleJSON.JSONNode SimpleJSON.JSONObject::get_Item(System.String)
extern void JSONObject_get_Item_m219B9BA37D800A5DFEAA14E4EECA375B3565BF96 (void);
// 0x000005D2 System.Void SimpleJSON.JSONObject::set_Item(System.String,SimpleJSON.JSONNode)
extern void JSONObject_set_Item_m1AC7334DBA67D0CB6C9549B83B3FFA75CF226AEF (void);
// 0x000005D3 SimpleJSON.JSONNode SimpleJSON.JSONObject::get_Item(System.Int32)
extern void JSONObject_get_Item_m5C2EDBE7B154A3FC1CC43616C4C40255B4D95652 (void);
// 0x000005D4 System.Void SimpleJSON.JSONObject::set_Item(System.Int32,SimpleJSON.JSONNode)
extern void JSONObject_set_Item_mFB6E61E3FA394B7D2CA01CC957A6A253642D109B (void);
// 0x000005D5 System.Int32 SimpleJSON.JSONObject::get_Count()
extern void JSONObject_get_Count_m9109E9A81559A9006EE160CA6A0F3291C71F2D08 (void);
// 0x000005D6 System.Void SimpleJSON.JSONObject::Add(System.String,SimpleJSON.JSONNode)
extern void JSONObject_Add_m25BD208A0AC0F0223FD93FBCB42785B12A6E1A18 (void);
// 0x000005D7 SimpleJSON.JSONNode SimpleJSON.JSONObject::Remove(System.String)
extern void JSONObject_Remove_m34280FDB4512E61F42781475E492BE98514830C9 (void);
// 0x000005D8 SimpleJSON.JSONNode SimpleJSON.JSONObject::Remove(System.Int32)
extern void JSONObject_Remove_mD1B01E22A9C1FEE83A00ECDFD8E0D8A422F8E4C2 (void);
// 0x000005D9 SimpleJSON.JSONNode SimpleJSON.JSONObject::Remove(SimpleJSON.JSONNode)
extern void JSONObject_Remove_m51B998A7997D184A1A20359D512C6B5A1B825404 (void);
// 0x000005DA System.Void SimpleJSON.JSONObject::Clear()
extern void JSONObject_Clear_m74686B9AF4B75949F959B81AAF8DE5076C60B3FE (void);
// 0x000005DB SimpleJSON.JSONNode SimpleJSON.JSONObject::Clone()
extern void JSONObject_Clone_mF3146F5687820508FD22051B23EFA20430B811C1 (void);
// 0x000005DC System.Boolean SimpleJSON.JSONObject::HasKey(System.String)
extern void JSONObject_HasKey_m79E034D14422C265C62C6C50C8E6F8337749457E (void);
// 0x000005DD SimpleJSON.JSONNode SimpleJSON.JSONObject::GetValueOrDefault(System.String,SimpleJSON.JSONNode)
extern void JSONObject_GetValueOrDefault_m969ABBC8049DB2DF4EC53968CDF7DF45666873BC (void);
// 0x000005DE System.Collections.Generic.IEnumerable`1<SimpleJSON.JSONNode> SimpleJSON.JSONObject::get_Children()
extern void JSONObject_get_Children_m03D7227DE57F0BE2977FC0436C0DE48858650B7C (void);
// 0x000005DF System.Void SimpleJSON.JSONObject::WriteToStringBuilder(System.Text.StringBuilder,System.Int32,System.Int32,SimpleJSON.JSONTextMode)
extern void JSONObject_WriteToStringBuilder_m931DC8805C6B8F09617958EFDAEA957751EB2EAE (void);
// 0x000005E0 System.Void SimpleJSON.JSONObject::.ctor()
extern void JSONObject__ctor_m8007967452F5257DC9F5DF2B78B411BFD4B6D6AB (void);
// 0x000005E1 System.Void SimpleJSON.JSONObject/<>c__DisplayClass21_0::.ctor()
extern void U3CU3Ec__DisplayClass21_0__ctor_m6976B4CF7F93E28364B390F81E55DAD60BB141C1 (void);
// 0x000005E2 System.Boolean SimpleJSON.JSONObject/<>c__DisplayClass21_0::<Remove>b__0(System.Collections.Generic.KeyValuePair`2<System.String,SimpleJSON.JSONNode>)
extern void U3CU3Ec__DisplayClass21_0_U3CRemoveU3Eb__0_m8B35D441B276B749481FF797FC51A256A7A56105 (void);
// 0x000005E3 System.Void SimpleJSON.JSONObject/<get_Children>d__27::.ctor(System.Int32)
extern void U3Cget_ChildrenU3Ed__27__ctor_mC18696B4562A62E4AA0969D6399C8C0631E35DC8 (void);
// 0x000005E4 System.Void SimpleJSON.JSONObject/<get_Children>d__27::System.IDisposable.Dispose()
extern void U3Cget_ChildrenU3Ed__27_System_IDisposable_Dispose_mC5CC72D1E22DD570C8E2EB525332F70406CDB9AA (void);
// 0x000005E5 System.Boolean SimpleJSON.JSONObject/<get_Children>d__27::MoveNext()
extern void U3Cget_ChildrenU3Ed__27_MoveNext_mF000F683CB97030C47BF22BD34472814A0C7630C (void);
// 0x000005E6 System.Void SimpleJSON.JSONObject/<get_Children>d__27::<>m__Finally1()
extern void U3Cget_ChildrenU3Ed__27_U3CU3Em__Finally1_mF5ECB5874D716A4939B7F1DB00D93DC58CEA824D (void);
// 0x000005E7 SimpleJSON.JSONNode SimpleJSON.JSONObject/<get_Children>d__27::System.Collections.Generic.IEnumerator<SimpleJSON.JSONNode>.get_Current()
extern void U3Cget_ChildrenU3Ed__27_System_Collections_Generic_IEnumeratorU3CSimpleJSON_JSONNodeU3E_get_Current_mD5BCAEE8B6A2ADEAF8EC61432A9619287942CD66 (void);
// 0x000005E8 System.Void SimpleJSON.JSONObject/<get_Children>d__27::System.Collections.IEnumerator.Reset()
extern void U3Cget_ChildrenU3Ed__27_System_Collections_IEnumerator_Reset_m7F54C4A2495814DE04F74FB9E9296EA2B68BFF6D (void);
// 0x000005E9 System.Object SimpleJSON.JSONObject/<get_Children>d__27::System.Collections.IEnumerator.get_Current()
extern void U3Cget_ChildrenU3Ed__27_System_Collections_IEnumerator_get_Current_mF24C3141BA1436A87068A46004816112F281FF9E (void);
// 0x000005EA System.Collections.Generic.IEnumerator`1<SimpleJSON.JSONNode> SimpleJSON.JSONObject/<get_Children>d__27::System.Collections.Generic.IEnumerable<SimpleJSON.JSONNode>.GetEnumerator()
extern void U3Cget_ChildrenU3Ed__27_System_Collections_Generic_IEnumerableU3CSimpleJSON_JSONNodeU3E_GetEnumerator_mB7F1824F0A6AD34C4EFEB913F04662B64CEF262C (void);
// 0x000005EB System.Collections.IEnumerator SimpleJSON.JSONObject/<get_Children>d__27::System.Collections.IEnumerable.GetEnumerator()
extern void U3Cget_ChildrenU3Ed__27_System_Collections_IEnumerable_GetEnumerator_m02800F9D77652D9E15E570729565FE79BCC2B3F8 (void);
// 0x000005EC SimpleJSON.JSONNodeType SimpleJSON.JSONString::get_Tag()
extern void JSONString_get_Tag_m68B0FF9ADDC3E203E5D60BB10639AEABACA34D44 (void);
// 0x000005ED System.Boolean SimpleJSON.JSONString::get_IsString()
extern void JSONString_get_IsString_m933985E37AE8A887A2039A9BAC7698F083BCD6E3 (void);
// 0x000005EE SimpleJSON.JSONNode/Enumerator SimpleJSON.JSONString::GetEnumerator()
extern void JSONString_GetEnumerator_m1CB9E437FC8622F3FE05D0AC12024D144747E0B8 (void);
// 0x000005EF System.String SimpleJSON.JSONString::get_Value()
extern void JSONString_get_Value_mEAD2BD372A2C517E83233BA5F6E309745AA5E9B4 (void);
// 0x000005F0 System.Void SimpleJSON.JSONString::set_Value(System.String)
extern void JSONString_set_Value_mB974D9B82AB8F9FAB84DCA99B8BD4B7C1C08ED00 (void);
// 0x000005F1 System.Void SimpleJSON.JSONString::.ctor(System.String)
extern void JSONString__ctor_m1DD5FB9A4147F72A0ED5F773FF82FA269241AD19 (void);
// 0x000005F2 SimpleJSON.JSONNode SimpleJSON.JSONString::Clone()
extern void JSONString_Clone_m59FCBC159496A334397171CF5127205C82C30A73 (void);
// 0x000005F3 System.Void SimpleJSON.JSONString::WriteToStringBuilder(System.Text.StringBuilder,System.Int32,System.Int32,SimpleJSON.JSONTextMode)
extern void JSONString_WriteToStringBuilder_mDF24D860FBF8E71F6F04799DD70F7700CE41D818 (void);
// 0x000005F4 System.Boolean SimpleJSON.JSONString::Equals(System.Object)
extern void JSONString_Equals_m1C60B537E558E6DF85ACF3EF9FF43BF9A3CF5435 (void);
// 0x000005F5 System.Int32 SimpleJSON.JSONString::GetHashCode()
extern void JSONString_GetHashCode_m979A74F84B4C0F45BF63D75DE1146490F743EE00 (void);
// 0x000005F6 System.Void SimpleJSON.JSONString::Clear()
extern void JSONString_Clear_m3E9CBF4AB37C6FD0011E19CA99E074FEA129FED7 (void);
// 0x000005F7 SimpleJSON.JSONNodeType SimpleJSON.JSONNumber::get_Tag()
extern void JSONNumber_get_Tag_m7C6E217E85B6161812496B63E5D371B910AAC856 (void);
// 0x000005F8 System.Boolean SimpleJSON.JSONNumber::get_IsNumber()
extern void JSONNumber_get_IsNumber_mFABFD0C9C4905CFB34A62700A1BD335F53E4214E (void);
// 0x000005F9 SimpleJSON.JSONNode/Enumerator SimpleJSON.JSONNumber::GetEnumerator()
extern void JSONNumber_GetEnumerator_m4D13E84756AEED9FCD7EFEEE4D01187DD049C596 (void);
// 0x000005FA System.String SimpleJSON.JSONNumber::get_Value()
extern void JSONNumber_get_Value_mBC5AB046D134B1E54C228C9C1C2231F8448CD56D (void);
// 0x000005FB System.Void SimpleJSON.JSONNumber::set_Value(System.String)
extern void JSONNumber_set_Value_m2264762BBD76F39DDC5DF3160910A44FBEFDE54C (void);
// 0x000005FC System.Double SimpleJSON.JSONNumber::get_AsDouble()
extern void JSONNumber_get_AsDouble_m8C004121700A7E7EB2B77ED223187227E33DE60B (void);
// 0x000005FD System.Void SimpleJSON.JSONNumber::set_AsDouble(System.Double)
extern void JSONNumber_set_AsDouble_m8E17AF8C0E9AE0EF6E25D86CB1B119904ADC0558 (void);
// 0x000005FE System.Int64 SimpleJSON.JSONNumber::get_AsLong()
extern void JSONNumber_get_AsLong_mF96069F806F51121CBFE8847D9E0D312F05986BB (void);
// 0x000005FF System.Void SimpleJSON.JSONNumber::set_AsLong(System.Int64)
extern void JSONNumber_set_AsLong_m541EF4E20CD8683CA860E0B969CECF7B71E2A357 (void);
// 0x00000600 System.UInt64 SimpleJSON.JSONNumber::get_AsULong()
extern void JSONNumber_get_AsULong_mD1EB0D23B9143C4CC1AA4BF75F17E326C08785CA (void);
// 0x00000601 System.Void SimpleJSON.JSONNumber::set_AsULong(System.UInt64)
extern void JSONNumber_set_AsULong_m320EA0ACC4B63183B5223CFCF0B25B8DA383C0DA (void);
// 0x00000602 System.Void SimpleJSON.JSONNumber::.ctor(System.Double)
extern void JSONNumber__ctor_m1CE3527102D15EBC3A183E3519895E291CAC1D90 (void);
// 0x00000603 System.Void SimpleJSON.JSONNumber::.ctor(System.String)
extern void JSONNumber__ctor_m39FDDE1A9EFEE9C4F2498E531D12B97AA49A1BA5 (void);
// 0x00000604 SimpleJSON.JSONNode SimpleJSON.JSONNumber::Clone()
extern void JSONNumber_Clone_m1C9DD94EB3011E55E840B55B4D4F3EAB63AF8A52 (void);
// 0x00000605 System.Void SimpleJSON.JSONNumber::WriteToStringBuilder(System.Text.StringBuilder,System.Int32,System.Int32,SimpleJSON.JSONTextMode)
extern void JSONNumber_WriteToStringBuilder_mD311BC3C1EE3E159C43801EB214F084E567367F2 (void);
// 0x00000606 System.Boolean SimpleJSON.JSONNumber::IsNumeric(System.Object)
extern void JSONNumber_IsNumeric_m9039F8DA776517548A2A6BEA7377B419C0525887 (void);
// 0x00000607 System.Boolean SimpleJSON.JSONNumber::Equals(System.Object)
extern void JSONNumber_Equals_mC04BB811CCAF20E70AE696AE74ECFDF5DA888688 (void);
// 0x00000608 System.Int32 SimpleJSON.JSONNumber::GetHashCode()
extern void JSONNumber_GetHashCode_m976ADFE41037830524798C7E6AFE08006B5F77AD (void);
// 0x00000609 System.Void SimpleJSON.JSONNumber::Clear()
extern void JSONNumber_Clear_mEB7835A2B2D433CE017CFD91CAE974ADB27CE72C (void);
// 0x0000060A SimpleJSON.JSONNodeType SimpleJSON.JSONBool::get_Tag()
extern void JSONBool_get_Tag_m82CE84C4C89E157D4DB036B9F0745343C005C338 (void);
// 0x0000060B System.Boolean SimpleJSON.JSONBool::get_IsBoolean()
extern void JSONBool_get_IsBoolean_m2671AE98710859611DF47E6BC58E6582C3A5B445 (void);
// 0x0000060C SimpleJSON.JSONNode/Enumerator SimpleJSON.JSONBool::GetEnumerator()
extern void JSONBool_GetEnumerator_mA07A10A6111713F7AD09FF03D09A6028556094D9 (void);
// 0x0000060D System.String SimpleJSON.JSONBool::get_Value()
extern void JSONBool_get_Value_mBEA89869448B0B597758D5BF2A3B576CA0BB64E3 (void);
// 0x0000060E System.Void SimpleJSON.JSONBool::set_Value(System.String)
extern void JSONBool_set_Value_mC960EE4083CA91D0059BE24661AFC06E131E2CFC (void);
// 0x0000060F System.Boolean SimpleJSON.JSONBool::get_AsBool()
extern void JSONBool_get_AsBool_mE04224144EAD0A9AD2F3B14BC0C68557A3BF22AC (void);
// 0x00000610 System.Void SimpleJSON.JSONBool::set_AsBool(System.Boolean)
extern void JSONBool_set_AsBool_m88EDF61A5ABBFF3ECF723312852E14F3C60AE365 (void);
// 0x00000611 System.Void SimpleJSON.JSONBool::.ctor(System.Boolean)
extern void JSONBool__ctor_mBB02E388CFB96B99E84561FCFF68147F00391C58 (void);
// 0x00000612 System.Void SimpleJSON.JSONBool::.ctor(System.String)
extern void JSONBool__ctor_m8CFB6AA78095EA003AB9B5EDD8932E8E0B01A1B9 (void);
// 0x00000613 SimpleJSON.JSONNode SimpleJSON.JSONBool::Clone()
extern void JSONBool_Clone_m0B98A17130A9A6FCEC5A92408F551E344CB80274 (void);
// 0x00000614 System.Void SimpleJSON.JSONBool::WriteToStringBuilder(System.Text.StringBuilder,System.Int32,System.Int32,SimpleJSON.JSONTextMode)
extern void JSONBool_WriteToStringBuilder_m82C70C80863730E8A22EE7A5B099C765F2E1D91E (void);
// 0x00000615 System.Boolean SimpleJSON.JSONBool::Equals(System.Object)
extern void JSONBool_Equals_m2671F40DA8F1128BA1451FE7066515C6E0C50D45 (void);
// 0x00000616 System.Int32 SimpleJSON.JSONBool::GetHashCode()
extern void JSONBool_GetHashCode_mC5B59375A9EE9978A5ADD1A24ECEE3FC920836DB (void);
// 0x00000617 System.Void SimpleJSON.JSONBool::Clear()
extern void JSONBool_Clear_m7841012AB307EA72DCFA23305AF45E45ACF7B7DE (void);
// 0x00000618 SimpleJSON.JSONNull SimpleJSON.JSONNull::CreateOrGet()
extern void JSONNull_CreateOrGet_mDC16038413CE71B027A7F9AB1546AF8666D3D3BD (void);
// 0x00000619 System.Void SimpleJSON.JSONNull::.ctor()
extern void JSONNull__ctor_m909243259F39D10FA6FEB176474DEF9C9972D76B (void);
// 0x0000061A SimpleJSON.JSONNodeType SimpleJSON.JSONNull::get_Tag()
extern void JSONNull_get_Tag_m89A7F368EA6269874235F85E43AE82254AAFD41E (void);
// 0x0000061B System.Boolean SimpleJSON.JSONNull::get_IsNull()
extern void JSONNull_get_IsNull_m1174212D6379871AC361EF06FA05DD510FC55595 (void);
// 0x0000061C SimpleJSON.JSONNode/Enumerator SimpleJSON.JSONNull::GetEnumerator()
extern void JSONNull_GetEnumerator_m16D254C74386D1A0AB2EFD1DE0EAF409C73B7686 (void);
// 0x0000061D System.String SimpleJSON.JSONNull::get_Value()
extern void JSONNull_get_Value_mB15431220D7D0B45CE002A204DF9E070CF78DBE0 (void);
// 0x0000061E System.Void SimpleJSON.JSONNull::set_Value(System.String)
extern void JSONNull_set_Value_mAF0CD2E912EF772E0892EB4ABB77294F689CF20A (void);
// 0x0000061F System.Boolean SimpleJSON.JSONNull::get_AsBool()
extern void JSONNull_get_AsBool_m6F3817CD49ED7CC10C180D31D84ED4B0151C78CE (void);
// 0x00000620 System.Void SimpleJSON.JSONNull::set_AsBool(System.Boolean)
extern void JSONNull_set_AsBool_m5717BC3921B7DE0683E9160B3816628B5CBC663D (void);
// 0x00000621 SimpleJSON.JSONNode SimpleJSON.JSONNull::Clone()
extern void JSONNull_Clone_m103493F0850508FB95CCA260491BAA283658289F (void);
// 0x00000622 System.Boolean SimpleJSON.JSONNull::Equals(System.Object)
extern void JSONNull_Equals_m8A39CAD3A41E9584C434B90A1360C62B3E158DE6 (void);
// 0x00000623 System.Int32 SimpleJSON.JSONNull::GetHashCode()
extern void JSONNull_GetHashCode_m74BE6286F06C6E7D5E35381E8BD27215117D9061 (void);
// 0x00000624 System.Void SimpleJSON.JSONNull::WriteToStringBuilder(System.Text.StringBuilder,System.Int32,System.Int32,SimpleJSON.JSONTextMode)
extern void JSONNull_WriteToStringBuilder_mB5B78BFA6A4943319926C1B2AE93F68C7B9B5FFD (void);
// 0x00000625 System.Void SimpleJSON.JSONNull::.cctor()
extern void JSONNull__cctor_m00A365175E9F31A2842DA242EE490783F0EAC483 (void);
// 0x00000626 SimpleJSON.JSONNodeType SimpleJSON.JSONLazyCreator::get_Tag()
extern void JSONLazyCreator_get_Tag_m1CB86FEA25328F1BE9CC01F6D020C9450E9F466E (void);
// 0x00000627 SimpleJSON.JSONNode/Enumerator SimpleJSON.JSONLazyCreator::GetEnumerator()
extern void JSONLazyCreator_GetEnumerator_m720BF0642A079A8BD44F6D650CF4D833DEF67757 (void);
// 0x00000628 System.Void SimpleJSON.JSONLazyCreator::.ctor(SimpleJSON.JSONNode)
extern void JSONLazyCreator__ctor_m0B3625D19DDD8DBDBB45822FAABCE266FA4EE694 (void);
// 0x00000629 System.Void SimpleJSON.JSONLazyCreator::.ctor(SimpleJSON.JSONNode,System.String)
extern void JSONLazyCreator__ctor_m02E2D630C60045F25A3AC001B7A17DF2D5D197B4 (void);
// 0x0000062A T SimpleJSON.JSONLazyCreator::Set(T)
// 0x0000062B SimpleJSON.JSONNode SimpleJSON.JSONLazyCreator::get_Item(System.Int32)
extern void JSONLazyCreator_get_Item_m562D16AE7F1F0CACA5ED050B390B63F98EBC77B1 (void);
// 0x0000062C System.Void SimpleJSON.JSONLazyCreator::set_Item(System.Int32,SimpleJSON.JSONNode)
extern void JSONLazyCreator_set_Item_m42894F9D00193BC7138C5D451E1B0BBD1BFE1084 (void);
// 0x0000062D SimpleJSON.JSONNode SimpleJSON.JSONLazyCreator::get_Item(System.String)
extern void JSONLazyCreator_get_Item_mF7AE3ADFBE062BF3B83FECCE0EF10F10996DE0CD (void);
// 0x0000062E System.Void SimpleJSON.JSONLazyCreator::set_Item(System.String,SimpleJSON.JSONNode)
extern void JSONLazyCreator_set_Item_m0107997E3B3CB75FACD86FB487C5D9416171CBEC (void);
// 0x0000062F System.Void SimpleJSON.JSONLazyCreator::Add(SimpleJSON.JSONNode)
extern void JSONLazyCreator_Add_mA8451EE34FEA0205B6BD6527AB46E5926451F49F (void);
// 0x00000630 System.Void SimpleJSON.JSONLazyCreator::Add(System.String,SimpleJSON.JSONNode)
extern void JSONLazyCreator_Add_mDC69A4E203B73054072D1575EC4CF20D95064F61 (void);
// 0x00000631 System.Boolean SimpleJSON.JSONLazyCreator::op_Equality(SimpleJSON.JSONLazyCreator,System.Object)
extern void JSONLazyCreator_op_Equality_m46508F81FB60FE9DCA683335676093A23D59D799 (void);
// 0x00000632 System.Boolean SimpleJSON.JSONLazyCreator::op_Inequality(SimpleJSON.JSONLazyCreator,System.Object)
extern void JSONLazyCreator_op_Inequality_m06C76EEC055AE314ED6E4FE7A49719AC7ACA397D (void);
// 0x00000633 System.Boolean SimpleJSON.JSONLazyCreator::Equals(System.Object)
extern void JSONLazyCreator_Equals_m753939907CFDB1548B0DAAB38E4737EF17B50066 (void);
// 0x00000634 System.Int32 SimpleJSON.JSONLazyCreator::GetHashCode()
extern void JSONLazyCreator_GetHashCode_m878E7AFF42AE5C43F4F643B6AEB25662491316F9 (void);
// 0x00000635 System.Int32 SimpleJSON.JSONLazyCreator::get_AsInt()
extern void JSONLazyCreator_get_AsInt_mE1404FBC99CE4E8EF4ABBE0BDF661206BAC2C44D (void);
// 0x00000636 System.Void SimpleJSON.JSONLazyCreator::set_AsInt(System.Int32)
extern void JSONLazyCreator_set_AsInt_m13146E53FD6A2F7573B752BFF079E0AF6A5FAE74 (void);
// 0x00000637 System.Single SimpleJSON.JSONLazyCreator::get_AsFloat()
extern void JSONLazyCreator_get_AsFloat_m2600D4B0E1179583EFE268070C66EAC11D380E04 (void);
// 0x00000638 System.Void SimpleJSON.JSONLazyCreator::set_AsFloat(System.Single)
extern void JSONLazyCreator_set_AsFloat_m9DCF79C70D4ED3728C12B709A6D95A0F0A057DE0 (void);
// 0x00000639 System.Double SimpleJSON.JSONLazyCreator::get_AsDouble()
extern void JSONLazyCreator_get_AsDouble_m41D6DF89CD7CEC00F36962068EE072D391EC0B38 (void);
// 0x0000063A System.Void SimpleJSON.JSONLazyCreator::set_AsDouble(System.Double)
extern void JSONLazyCreator_set_AsDouble_mB7ABE38136DBEDA7CC9AC12A381322D6C49ADED9 (void);
// 0x0000063B System.Int64 SimpleJSON.JSONLazyCreator::get_AsLong()
extern void JSONLazyCreator_get_AsLong_mFBA0000985629FA20509FA45A6A8B751C9CAC2B8 (void);
// 0x0000063C System.Void SimpleJSON.JSONLazyCreator::set_AsLong(System.Int64)
extern void JSONLazyCreator_set_AsLong_mBD4640D2F347DEF793A631A44026A03D3D5D73A4 (void);
// 0x0000063D System.UInt64 SimpleJSON.JSONLazyCreator::get_AsULong()
extern void JSONLazyCreator_get_AsULong_m09F6B8D28F383D9A0F857339A6663B24D6AB97A2 (void);
// 0x0000063E System.Void SimpleJSON.JSONLazyCreator::set_AsULong(System.UInt64)
extern void JSONLazyCreator_set_AsULong_m5514AFD97B29BBA5D1A4EC80F7086929DE977A7D (void);
// 0x0000063F System.Boolean SimpleJSON.JSONLazyCreator::get_AsBool()
extern void JSONLazyCreator_get_AsBool_m7D8AF5879C2C8036916AA6B15E22CB4B80412CF4 (void);
// 0x00000640 System.Void SimpleJSON.JSONLazyCreator::set_AsBool(System.Boolean)
extern void JSONLazyCreator_set_AsBool_m4DB409DB959182CAA610147A51A2ECDBAFEA6092 (void);
// 0x00000641 SimpleJSON.JSONArray SimpleJSON.JSONLazyCreator::get_AsArray()
extern void JSONLazyCreator_get_AsArray_m493C069A3624597885A7B6E00C82E829A84B47C4 (void);
// 0x00000642 SimpleJSON.JSONObject SimpleJSON.JSONLazyCreator::get_AsObject()
extern void JSONLazyCreator_get_AsObject_mE01B43B261A6A56F4FCE40AB11F3AAF90B7C292D (void);
// 0x00000643 System.Void SimpleJSON.JSONLazyCreator::WriteToStringBuilder(System.Text.StringBuilder,System.Int32,System.Int32,SimpleJSON.JSONTextMode)
extern void JSONLazyCreator_WriteToStringBuilder_mC9975859B1C42C9F5E507E604121D10B2FB2D93D (void);
// 0x00000644 SimpleJSON.JSONNode SimpleJSON.JSON::Parse(System.String)
extern void JSON_Parse_mEE6C962A58074E33C05C49D74221F1852E7963CE (void);
// 0x00000645 System.UInt32 <PrivateImplementationDetails>::ComputeStringHash(System.String)
extern void U3CPrivateImplementationDetailsU3E_ComputeStringHash_m6EA1F233618497AEFF8902A5EDFA24C74E2F2876 (void);
static Il2CppMethodPointer s_methodPointers[1605] = 
{
	AlignmentTest_Start_m2992385C4EC00DF4CBD504AFE7C4733AB74EA27C,
	AlignmentTest_Update_mC549F80E002E8792FC35718DB75096B24417796C,
	AlignmentTest_ValidateReferencesPoints_m204A4E87E164E0B9A7BF3EF65CF1A40DF03C9FD7,
	AlignmentTest_PrintReferencePointAxes_mC8B51A9797C0BAB6F31DF8B4856DBCCFD6DF07E5,
	AlignmentTest_TestAlignment_m0FE6AF80AE013ABF0311E74A818A1739C362664B,
	AlignmentTest_GetReferencePointIndex_mD953C570EF17CBE25C6A6FCEEAC7F098213346B6,
	AlignmentTest_ResetSourcePart_mD9BD350634284BC34A214C90A9097D928BA02877,
	AlignmentTest_AnalyzeRotationConstraints_m6F5468E3CD3F9C5FF79861F198486D2FCF442C06,
	AlignmentTest_OnDrawGizmos_m3B6D469A036F4294CCB23D0FC7ED12B4370CD139,
	AlignmentTest__ctor_m03EB6CA5E2A81935E8B7633CF50EC4512D82D9E4,
	AssemblyAnimation_Start_m6B156AEC545BABFC9E83268A7242AB3E6AAD57A0,
	AssemblyAnimation_AssembleAnimation_mD23C831759F0A86521787D49EB39059B229A87DF,
	AssemblyAnimation_CalculateTargetRotation_mA6508A3B3E9CD605EDDF12699318800E66B5A0D6,
	AssemblyAnimation_GetAxisDirection_m8AFF54066643C0EC9792A5D5060431F772F5F983,
	AssemblyAnimation_MoveAndRotate_mBF7B193EA8886BD53D4AF584C7CC0226BFF99D49,
	AssemblyAnimation_UpdateMountPointPosition_mEF4CC16237F1B31ECDC5989FCAC099D13AE9F060,
	AssemblyAnimation_Move_mDD30F014C6D6EB3049C99A57EEDA225B59E675FA,
	AssemblyAnimation_RotateAndMoveNut_m2A5A259FCF9DB2A7C975DC329853C3C237218F2A,
	AssemblyAnimation_RotateAroundPivot_m11F4F892FE211459F0A8FE6E7E26A3D16020ED9C,
	AssemblyAnimation_OnDrawGizmos_m9C46EA1309C3833F2A60966C729161043E990B0C,
	AssemblyAnimation_PrintDebugInfo_m886797F456E00CE82212CA80B857BED824C4382E,
	AssemblyAnimation_Update_m4775FEC5063A69CB7B4062F471A710822952F9FF,
	AssemblyAnimation_MoveScrewImproved_m34A71C209750820105B716048F82429A90D11EA8,
	AssemblyAnimation_RotateInPlace_mC016BA5F2E9EFCF628B4E57E3299B095D0087D82,
	AssemblyAnimation_MoveNutImproved_m3787B1F890FBC2A73697F7FC99833FFC5360C68E,
	AssemblyAnimation_MoveAndRotateByMountPoint_m0490CD567207CBB769708FEC8C33887C1F0EDA3D,
	AssemblyAnimation_MoveByMountPoint_m3F9E1AB39B0010533A6D51206BA700BC14168F57,
	AssemblyAnimation_AlignTransformPosition_m3D151DC8759C7E823C727DFD49ED7E446ADDF9F7,
	AssemblyAnimation__ctor_m3B6ADFF0903CB9927E995FE63DC4FEDCE5AEE408,
	U3CAssembleAnimationU3Ed__21__ctor_m486944FD17AD41EF0EB65721E434EDD0C859FBC0,
	U3CAssembleAnimationU3Ed__21_System_IDisposable_Dispose_mC982219E4D12769F0121C6B9F5BC0A547268F74C,
	U3CAssembleAnimationU3Ed__21_MoveNext_m9195D7E71EFAA2CD414BCE34293DD023C25543BA,
	U3CAssembleAnimationU3Ed__21_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mDCA1BD8238361DF92C2206969BA75929D5886379,
	U3CAssembleAnimationU3Ed__21_System_Collections_IEnumerator_Reset_m6E8DD4B28AB37EE167F18FB88D936E3295AAF610,
	U3CAssembleAnimationU3Ed__21_System_Collections_IEnumerator_get_Current_m0C9027DEDA977EF87AD0476301DAC9FAA27D0B67,
	U3CMoveAndRotateU3Ed__24__ctor_m14BA0EF9D087AA07D1FA44644D8D76EE7B3C1EAC,
	U3CMoveAndRotateU3Ed__24_System_IDisposable_Dispose_m122DFE0E8B6098A97535CBDE8823AB03ECE2275F,
	U3CMoveAndRotateU3Ed__24_MoveNext_mFA6F675DE65A4AA8970D97C434AEBA3849B55BEE,
	U3CMoveAndRotateU3Ed__24_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m252FE712A52F09C50A074FB2D672EEB600BFB317,
	U3CMoveAndRotateU3Ed__24_System_Collections_IEnumerator_Reset_mEE4599C8344317F907FAFE052180C8CAC745C1E8,
	U3CMoveAndRotateU3Ed__24_System_Collections_IEnumerator_get_Current_mEB6C6624554E112D7CB1C745504DC9ECC5D01589,
	U3CMoveU3Ed__26__ctor_m9716B47FCF49AC1DB55C0D018EBC2A9E888E1351,
	U3CMoveU3Ed__26_System_IDisposable_Dispose_m13AAE265F854DD6C74470BA76FB235FC2A3AE5BD,
	U3CMoveU3Ed__26_MoveNext_m09017BC9C35757AEB0E89872C7623A4ED9CA3205,
	U3CMoveU3Ed__26_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m184E395C74F4E6536C76E8F549D0102279BD21EA,
	U3CMoveU3Ed__26_System_Collections_IEnumerator_Reset_m48E2711DAA629D40F110620E7C00F3DF1F28234E,
	U3CMoveU3Ed__26_System_Collections_IEnumerator_get_Current_m971AC46F3CF6750BBE7F0C586BD91ACC97DCD68E,
	U3CRotateAndMoveNutU3Ed__27__ctor_m0AA3BFBE304E14564FA817E68A63F9E529D8C6DB,
	U3CRotateAndMoveNutU3Ed__27_System_IDisposable_Dispose_m9CCAE6E643EA4FC7C0EADAF67D1357BC131FAE0A,
	U3CRotateAndMoveNutU3Ed__27_MoveNext_mB1AAAF5FEBAF1D2B290AB05349BF2683CB52FA17,
	U3CRotateAndMoveNutU3Ed__27_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m7417189A45F030516B044C8A83724507E296718C,
	U3CRotateAndMoveNutU3Ed__27_System_Collections_IEnumerator_Reset_m8368D49D750EF526B25970EDAF45917ADC48FA54,
	U3CRotateAndMoveNutU3Ed__27_System_Collections_IEnumerator_get_Current_mC164F69E0B2620D4B773E91FDD95E76EDE2183D5,
	U3CRotateAroundPivotU3Ed__28__ctor_m7514BDD06AE5CBE0394539FF5B388116E252AF59,
	U3CRotateAroundPivotU3Ed__28_System_IDisposable_Dispose_mFFC5549CC8E641BDC1D8D67D1899ED2DF9F577C9,
	U3CRotateAroundPivotU3Ed__28_MoveNext_m7049D492430B44C01E36B6966FCBF05AE1CA7487,
	U3CRotateAroundPivotU3Ed__28_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mD87D0B599C83DB3030C042558E7BC11DF134029F,
	U3CRotateAroundPivotU3Ed__28_System_Collections_IEnumerator_Reset_m8F009E68606B9434FF94AEA9025D3D0220F95A0D,
	U3CRotateAroundPivotU3Ed__28_System_Collections_IEnumerator_get_Current_m995BEB903FD7837F6FECCE90B73058479F3E57F3,
	U3CMoveScrewImprovedU3Ed__32__ctor_mCDE0CDA7508BE39FB54800AF3DB2A2F399815EC6,
	U3CMoveScrewImprovedU3Ed__32_System_IDisposable_Dispose_mC1BD0D23CC393EBECFCEB68B629D2BB8900C11D2,
	U3CMoveScrewImprovedU3Ed__32_MoveNext_mE0EFAD28F37BFBBD90687BAA2F449DC7136C5459,
	U3CMoveScrewImprovedU3Ed__32_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mE6549B0292EA7BBD8E33A7CA1AA62171C351CC4A,
	U3CMoveScrewImprovedU3Ed__32_System_Collections_IEnumerator_Reset_mCC6F396AE06E5BCA37EBB1F5F43EDDB5EF0AFFA7,
	U3CMoveScrewImprovedU3Ed__32_System_Collections_IEnumerator_get_Current_m822E2E6F2C3DC8D60CDFA7D759B9588A8B793FDE,
	U3CRotateInPlaceU3Ed__33__ctor_mC8C4126A76A5AFA2BED0922A9A560AEBA42FD4C4,
	U3CRotateInPlaceU3Ed__33_System_IDisposable_Dispose_m4D19E780BD89A5BE3FD6033C757793FAAED1EB0C,
	U3CRotateInPlaceU3Ed__33_MoveNext_mAFB49C696CAC83C5682FBBEDD560A018337E4A1F,
	U3CRotateInPlaceU3Ed__33_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m966453D359708348AD2877B2B65FC557002CC921,
	U3CRotateInPlaceU3Ed__33_System_Collections_IEnumerator_Reset_mA9E151254C98D3129A5608CF72644B7A44523D7F,
	U3CRotateInPlaceU3Ed__33_System_Collections_IEnumerator_get_Current_mA2EF39F00D69519A69EB032BBAB65571E639FA92,
	U3CMoveNutImprovedU3Ed__34__ctor_mAE734DEEF29803125421859097231EB174A0A816,
	U3CMoveNutImprovedU3Ed__34_System_IDisposable_Dispose_m81E68AF574B8DD98DCECC368AE6EDCDF4239C945,
	U3CMoveNutImprovedU3Ed__34_MoveNext_m3D73BBEA683BC5404B7865852255F53FBC907B94,
	U3CMoveNutImprovedU3Ed__34_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m118403148DDF0A023CDF1CFC10FD389EE84E0285,
	U3CMoveNutImprovedU3Ed__34_System_Collections_IEnumerator_Reset_m6766341120AA0057CA9A4A2D67B3FFB6F6950613,
	U3CMoveNutImprovedU3Ed__34_System_Collections_IEnumerator_get_Current_m3D758031FBF1166270AB8B33D8ECE2BD134C9802,
	U3CMoveAndRotateByMountPointU3Ed__35__ctor_mBA636007EAF50870880909CB73BE2FB864AD4914,
	U3CMoveAndRotateByMountPointU3Ed__35_System_IDisposable_Dispose_mF8426BDEA58C677F89E48D79F73EFA8CBFCB524A,
	U3CMoveAndRotateByMountPointU3Ed__35_MoveNext_m04B0FA0693C400947042F490CFF4F4B06C920270,
	U3CMoveAndRotateByMountPointU3Ed__35_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m9321065DDE127745BAFF0090A1529BE7A4D10A3E,
	U3CMoveAndRotateByMountPointU3Ed__35_System_Collections_IEnumerator_Reset_mE4D4C4AA40D64F22503CEC2EBEF702B06A4593B7,
	U3CMoveAndRotateByMountPointU3Ed__35_System_Collections_IEnumerator_get_Current_mC27596FEB950ECF7C6BD79B33462B221947244EF,
	U3CMoveByMountPointU3Ed__36__ctor_m1F54AE1061A0248056DF5DD5999A009334899B3B,
	U3CMoveByMountPointU3Ed__36_System_IDisposable_Dispose_m97D03CFD8318D2EA71F3DA9ADAA0243214A851D1,
	U3CMoveByMountPointU3Ed__36_MoveNext_mA51856E916803374B00A52F35F194BBCA0356FA2,
	U3CMoveByMountPointU3Ed__36_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mF9266A75C9C4C9A8B0B0F64E4F7AD3C5E478D2D7,
	U3CMoveByMountPointU3Ed__36_System_Collections_IEnumerator_Reset_mAFA5F0C0C5A727A53F1189E6195C432A9890B47E,
	U3CMoveByMountPointU3Ed__36_System_Collections_IEnumerator_get_Current_mC81D9F1CB442057CF329B637CF00BACF54CA3CE4,
	AssemblyAnimationManager_get_AnimationSpeedRate_mDDBE70FD2FAD13DD46C69E376953215CF3B33E68,
	AssemblyAnimationManager_set_AnimationSpeedRate_m40A5C4BD1F9B4DF2AB78FE5932B160E32FE67292,
	AssemblyAnimationManager_ValidatePart_m46F20A6F8EC1C83A4BA17E29EEE6938FA86433DA,
	AssemblyAnimationManager_AnimateOverTime_m81DD5756B67A920512A58FF3DE976E2B1512249F,
	AssemblyAnimationManager_MovePart_m09C69C191E8C81AA6DAC05CD7EE6A7509B2ABA29,
	AssemblyAnimationManager_RotatePartInPlace_m9347E51B925042884D46A5C1908FD9E911D59D46,
	AssemblyAnimationManager_RotatePartAroundAxis_m63484611346ED485115C86577179D0DF3B21470A,
	AssemblyAnimationManager_MoveAndRotateByMountPoint_m8DD0338BCBBD7B54E03416BE6B2532177419EA73,
	AssemblyAnimationManager_MoveByMountPoint_m81F522832892F3795A0E865113CAD3800B7DCC25,
	AssemblyAnimationManager_AlignTransformPosition_m9D6C3792968DBDB142757118D3C0298A1E2C9391,
	AssemblyAnimationManager_AlignParts_m7A6EA927C278B2855C41C4AA9534EAB306868E78,
	AssemblyAnimationManager_CalculateAlignmentRotation_m11CBD762346D5454230FD794143B9994E37F4CA9,
	AssemblyAnimationManager_GetAxisDirection_m4D43AF8864F81864ACCC4EC249DB55B8B9A82DB4,
	AssemblyAnimationManager_InstallScrew_mD607DC25321427D9DF79BD546E1EEE269D5CC6E1,
	AssemblyAnimationManager_InstallNut_m402E3A46E8C657C86E6C3B7465E58B09F682D35F,
	AssemblyAnimationManager_AlignPartsAsync_m5E585B7D17D642E4074BFD40D17713910AB09CBE,
	AssemblyAnimationManager_AlignPartsSync_m688E20F88A62998DAF49DDB8D7EBFD2A62F982E4,
	AssemblyAnimationManager_CompleteAssemblySequence_m2D88E4AFFF901634E3B1D159D3AC9C12CB12C03D,
	AssemblyAnimationManager__ctor_mBEC5D5A7F30D782D77777D225EC4EB2BE97E1531,
	U3CAnimateOverTimeU3Ed__10__ctor_m6FCD3B0A5AF03A24AF7855090ECB2A3599AD626F,
	U3CAnimateOverTimeU3Ed__10_System_IDisposable_Dispose_mF4C77CC94ED80E76C76F571E6811ADB7ADE3454F,
	U3CAnimateOverTimeU3Ed__10_MoveNext_m195A1FC6B97364310709D23CE85B1D4F86854370,
	U3CAnimateOverTimeU3Ed__10_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m0B993B2148D70AAD993C1717D53A5784D27ECACA,
	U3CAnimateOverTimeU3Ed__10_System_Collections_IEnumerator_Reset_mB732BBA347180FD8B94FBEA662120F00D8786111,
	U3CAnimateOverTimeU3Ed__10_System_Collections_IEnumerator_get_Current_m4F0F9A98AAEFDEEB272405E86F8B02A5335C4F60,
	U3CU3Ec__DisplayClass11_0__ctor_m967251DBE7B3B10F213ADD91883755FADA548AB4,
	U3CU3Ec__DisplayClass11_0_U3CMovePartU3Eb__0_mFE9AE39CD908349C0EAEE49C2A8FFF428D94C85F,
	U3CU3Ec__DisplayClass11_0_U3CMovePartU3Eb__1_m17628096485283FE54E09C9F938CD11ED9CC6E06,
	U3CMovePartU3Ed__11__ctor_mFA3733646E1A3F848FE6AF0D2FCEEC8D68E50693,
	U3CMovePartU3Ed__11_System_IDisposable_Dispose_mA079DA89C6672151C2CD1314F5765C125AF96C57,
	U3CMovePartU3Ed__11_MoveNext_m21218F444A2D0047B188088AB460646A93D9305B,
	U3CMovePartU3Ed__11_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m55FC00A208A5DB3DD89DA1674D16386029538640,
	U3CMovePartU3Ed__11_System_Collections_IEnumerator_Reset_m5F3B4BD4B900E1216395E69DF7DA981CA1A1B549,
	U3CMovePartU3Ed__11_System_Collections_IEnumerator_get_Current_m69793D09EC560C4DD5F3515011D0D4A2D5D06386,
	U3CU3Ec__DisplayClass12_0__ctor_mB6F29EF3938BE271918C03B9E60F474FCB91F1A0,
	U3CU3Ec__DisplayClass12_0_U3CRotatePartInPlaceU3Eb__0_m72522C877F6F4AEFE8203DADB1C07D2BBFC61115,
	U3CU3Ec__DisplayClass12_0_U3CRotatePartInPlaceU3Eb__1_m44ACA0310BCB59F931DCB5B64F963C9D23CA1C85,
	U3CRotatePartInPlaceU3Ed__12__ctor_mBEAA0D6912A77EF3CA7345E3F8536984DFA9BD7E,
	U3CRotatePartInPlaceU3Ed__12_System_IDisposable_Dispose_m447EE95281F6777373242F2B98710E144AEFEC9F,
	U3CRotatePartInPlaceU3Ed__12_MoveNext_mA8B419B80BF09EF8959DCF95FEE2B073EB740F1C,
	U3CRotatePartInPlaceU3Ed__12_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mFB47B3D54C6AB9FF50DE010DAB96D2C8AEA412ED,
	U3CRotatePartInPlaceU3Ed__12_System_Collections_IEnumerator_Reset_m02A1EA77E16E756AC9EC6DC86F8B7E856065BE46,
	U3CRotatePartInPlaceU3Ed__12_System_Collections_IEnumerator_get_Current_m06B320F837DFD7E7B8CDCA391D2A97C3CE263DB7,
	U3CU3Ec__DisplayClass13_0__ctor_mBCC63144A53BF221E89F58AF6381A540E801EB51,
	U3CU3Ec__DisplayClass13_0_U3CRotatePartAroundAxisU3Eb__0_mA262C9AAA7259FB504A9C0603F0FAC70549F5C6F,
	U3CU3Ec__DisplayClass13_0_U3CRotatePartAroundAxisU3Eb__1_mE14982D28A9AA313AE0092D73E6FAAC5B6950B25,
	U3CRotatePartAroundAxisU3Ed__13__ctor_mBB1D7E0F7826D2919AA3B89AB3BE3A8D739A92D7,
	U3CRotatePartAroundAxisU3Ed__13_System_IDisposable_Dispose_m2AE5F0A9BE26DA938FBC3E7D2CDE3CBCA540ADC4,
	U3CRotatePartAroundAxisU3Ed__13_MoveNext_m850999FBC7D938BAB9B0DD1819C0F7600F56ADE9,
	U3CRotatePartAroundAxisU3Ed__13_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mFC1A4041D2B788A31244F310786491D89C1ADAFE,
	U3CRotatePartAroundAxisU3Ed__13_System_Collections_IEnumerator_Reset_mA646940B486F8DF2ABDC54D9BB4A522D6DF474D2,
	U3CRotatePartAroundAxisU3Ed__13_System_Collections_IEnumerator_get_Current_mC12DCAF15357B258D61791DDD82B51736D590D71,
	U3CU3Ec__DisplayClass14_0__ctor_m5B7FEE20A5C07A27340298EAE5D539571DF7A66C,
	U3CU3Ec__DisplayClass14_0_U3CMoveAndRotateByMountPointU3Eb__0_m7EC2C1E485309DC5260F9CE9562BEF09F7B54D85,
	U3CU3Ec__DisplayClass14_0_U3CMoveAndRotateByMountPointU3Eb__1_mC71660A82CF4BC0BDE3B78AE5DF10942D7F105EA,
	U3CMoveAndRotateByMountPointU3Ed__14__ctor_m9A080D9627117975CA8C32F3604679177706FDD0,
	U3CMoveAndRotateByMountPointU3Ed__14_System_IDisposable_Dispose_m3D9FCEC1E9F76AA3208DE678E6DF2DC0DB8A6D91,
	U3CMoveAndRotateByMountPointU3Ed__14_MoveNext_m9BF3CBA940C3AE5D8149D70FD9B41515E2C3274A,
	U3CMoveAndRotateByMountPointU3Ed__14_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mB8382E78AF30CFC27ACF7CE8D7C003BF4BB807D4,
	U3CMoveAndRotateByMountPointU3Ed__14_System_Collections_IEnumerator_Reset_m0910B6ADAB14DD9159A00C7F31AB2DED22F786F9,
	U3CMoveAndRotateByMountPointU3Ed__14_System_Collections_IEnumerator_get_Current_m1487B11E1F693248CEC406CA5AC9C2DB0D325389,
	U3CU3Ec__DisplayClass15_0__ctor_mF6E8D22BC5FFBFB118E420CC73A0C72A976B8107,
	U3CU3Ec__DisplayClass15_0_U3CMoveByMountPointU3Eb__0_mB05EF60579F08183A9971542487DC38E3615E699,
	U3CU3Ec__DisplayClass15_0_U3CMoveByMountPointU3Eb__1_m0F56C3D3B73ACFAE4A74FAF6945D2277689980D7,
	U3CMoveByMountPointU3Ed__15__ctor_m982295494133219B3C01226A883BEA8EC569A4D6,
	U3CMoveByMountPointU3Ed__15_System_IDisposable_Dispose_m8EEC45AECB0F1B2462BD6F6476952424DA8F3A6E,
	U3CMoveByMountPointU3Ed__15_MoveNext_mCDBA24FC2646E2FCD400F35F974795C9F1E71FC7,
	U3CMoveByMountPointU3Ed__15_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m1BC767D8A33C5A375BDF4ECE75E0C38D3E1B2B95,
	U3CMoveByMountPointU3Ed__15_System_Collections_IEnumerator_Reset_mD64E62DB133113EFD531A989ECB1EF4731F19BEB,
	U3CMoveByMountPointU3Ed__15_System_Collections_IEnumerator_get_Current_m1581E82FD713A836C717677202408056D67DDAEC,
	U3CAlignPartsU3Ed__17__ctor_mB5D8DDBCF1971CBDF22EF7C5809AE214E88DE8ED,
	U3CAlignPartsU3Ed__17_System_IDisposable_Dispose_m40C91ED59A35B8A76984C5CD07D3FCB2788BDA6C,
	U3CAlignPartsU3Ed__17_MoveNext_mA1B59E17C37E16B8B82BA8BA15F105A0B08FF6FD,
	U3CAlignPartsU3Ed__17_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mF518E7555FF4B2DCF918B67E313FDC7CAC4AA2CC,
	U3CAlignPartsU3Ed__17_System_Collections_IEnumerator_Reset_m65CC8619FC89A9B803813020F70F0C87A6B1C3EB,
	U3CAlignPartsU3Ed__17_System_Collections_IEnumerator_get_Current_m215BB3886B1F0545F172FBEC19FA6DE0E8BF0C6E,
	U3CInstallScrewU3Ed__20__ctor_mFFF5D2B23896C8D28E7DE9404D1B6FEC58AB0DAF,
	U3CInstallScrewU3Ed__20_System_IDisposable_Dispose_mD805219881F7C390067A14B1E9613DE9E44DE154,
	U3CInstallScrewU3Ed__20_MoveNext_m11E25DE6C83D14583BB5BF047D814D128578A261,
	U3CInstallScrewU3Ed__20_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mADFCEB5C97976EDD36BFE11DD4F52875C8A3298F,
	U3CInstallScrewU3Ed__20_System_Collections_IEnumerator_Reset_m002E0B711EBA27F381052CD9A368205A8793FCDE,
	U3CInstallScrewU3Ed__20_System_Collections_IEnumerator_get_Current_m34CF5377FB92312DC437E444E2F20043D48BAEB4,
	U3CInstallNutU3Ed__21__ctor_m33D0919297A18444AA527119F6AF425631EE8F58,
	U3CInstallNutU3Ed__21_System_IDisposable_Dispose_m442604572C4481A33B63F08FDDD8231B86AE8803,
	U3CInstallNutU3Ed__21_MoveNext_m7B3938DCEF64AA43B11A0AE6C0528729F57A6FD1,
	U3CInstallNutU3Ed__21_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m574645F338EBBD73499835737B97CBC00F9D0582,
	U3CInstallNutU3Ed__21_System_Collections_IEnumerator_Reset_m3B4DBFF0D24901B7C680AB35FB771F4AF80422DF,
	U3CInstallNutU3Ed__21_System_Collections_IEnumerator_get_Current_m4F217576A9159CEA37515A906EED62555AF6979C,
	U3CAlignPartsSyncU3Ed__23__ctor_m89125A48D794F5FCE14384806D911DA878A8E1D9,
	U3CAlignPartsSyncU3Ed__23_System_IDisposable_Dispose_m44C3F9883751EFD57D16BD3A4344FC439C9F5054,
	U3CAlignPartsSyncU3Ed__23_MoveNext_m702695B033F90E38B6C660E6300DA10F66B81D19,
	U3CAlignPartsSyncU3Ed__23_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mF1984E7DCA1A253A1BE2FE00D871366DFA7B9C87,
	U3CAlignPartsSyncU3Ed__23_System_Collections_IEnumerator_Reset_mDDFD01B78693121FD30943BD678F0738412C2892,
	U3CAlignPartsSyncU3Ed__23_System_Collections_IEnumerator_get_Current_m930CCA9D1CFD7BFB03B5F5A32E3EF53C5980D745,
	U3CCompleteAssemblySequenceU3Ed__24__ctor_m6873ECA5E7A1B716F0EBC7343F2352A0BFEA116B,
	U3CCompleteAssemblySequenceU3Ed__24_System_IDisposable_Dispose_mBFA49DF4E24E707B41CB3E9103F535A909F97718,
	U3CCompleteAssemblySequenceU3Ed__24_MoveNext_mF07AF18D2B7FE9D8FDAEC0CB35E06D72BEA061CE,
	U3CCompleteAssemblySequenceU3Ed__24_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m6A0F91B1BA366E512665D752B47AB37752D22CBF,
	U3CCompleteAssemblySequenceU3Ed__24_System_Collections_IEnumerator_Reset_m4E60FE5DEFC1708BF1B59877D3C882F2689A7F10,
	U3CCompleteAssemblySequenceU3Ed__24_System_Collections_IEnumerator_get_Current_mE1E14AAB8061094B63E88C56F85DB287B36BFF7D,
	AssemblyAnimationService_get_AnimationManager_m2390D3B47AAE7E2447E4C67572CB7C27DC1B3AC5,
	AssemblyAnimationService_Awake_mB7E5899A0B2D0BAFC2C5CB6A6A0A47FE12DAAC93,
	AssemblyAnimationService_Start_m2F6680EF6EF0EF87313B5852350D6CD2AA65A9E4,
	AssemblyAnimationService_AlignPartsPublic_mA8042FD975498F9879E139EE96089CBA5CADB774,
	AssemblyAnimationService_MoveScrewImprovedPublic_m60D285E03B83A1FB2FD0A5D91BCCFECBC55EFB9B,
	AssemblyAnimationService_MoveNutImprovedPublic_mDE246AD0253F5EB806E42CB521680F08C3C06AE4,
	AssemblyAnimationService_GetAxisDirection_m16F7FB7DF40731F203B4972B7013BEC0AABEB1D5,
	AssemblyAnimationService_MoveScrewImproved_m84B33DE65006D0CC6399AFFAC3DD9DC7333C1E35,
	AssemblyAnimationService_MoveNutImproved_m6DF3B64C6188156C89239B005BBC0AECB6EFCCB8,
	AssemblyAnimationService__ctor_mFE7BB99BDB3CC7DA6229F2F87F925E7C8378C41B,
	U3CMoveScrewImprovedU3Ed__14__ctor_mF7CAD27EE50C47CE7D9B100CA08BBD1818CDFA17,
	U3CMoveScrewImprovedU3Ed__14_System_IDisposable_Dispose_m8C2780039594A2BA7CABE0108A0BDDA2738D6EE6,
	U3CMoveScrewImprovedU3Ed__14_MoveNext_mA0D4AA02C62875E2B30B4B68AE49BD83FEDDB258,
	U3CMoveScrewImprovedU3Ed__14_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m5E42E26C3E4B6EB3606DC959E3B2ED1F247538F9,
	U3CMoveScrewImprovedU3Ed__14_System_Collections_IEnumerator_Reset_mB3378F9CAB2D4FBD28B57B19AB2E4A09552342EA,
	U3CMoveScrewImprovedU3Ed__14_System_Collections_IEnumerator_get_Current_mCA66715AE837146DF72DEEA7DDA288A029C70BFD,
	U3CMoveNutImprovedU3Ed__15__ctor_mDD0643FFE94EA0DE1C955C8BF1A4E3C6A5A40355,
	U3CMoveNutImprovedU3Ed__15_System_IDisposable_Dispose_mDADC295F6FF625100DC91523F1B1D93D9A05CD28,
	U3CMoveNutImprovedU3Ed__15_MoveNext_m7ACA9DC3B5514A5366E06476CA100C79F95070CF,
	U3CMoveNutImprovedU3Ed__15_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m81FDD57950948E47486F944A75DB02644169422C,
	U3CMoveNutImprovedU3Ed__15_System_Collections_IEnumerator_Reset_mDB3E2043E8BEF95FF79784AA22A37105DF1CB65B,
	U3CMoveNutImprovedU3Ed__15_System_Collections_IEnumerator_get_Current_m532C2F3BA34DD38F05EBF7C256BABA6533E37531,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	AssemblyPart_get_PartTransform_m4DBAD54DBC01EA8AA0B9CE3F213F16BF39F4185B,
	AssemblyPart_get_PartName_m6214ABEAAF046BDED82DEF234113EC5242D85403,
	AssemblyPart_get_Type_m7F1E8F177BCDAF6F36A237322F53A5B0923E4461,
	AssemblyPart_get_ReferencePoints_m78FB08E4A8A88DB9C21CE9D33063B7186F578282,
	AssemblyPart_get_HasReferencePoints_m4101AA83A26052106AD881D2DC804625891542F5,
	AssemblyPart_get_ReferencePointCount_mF33DFB60760BFAF332ABD9E985F0CD490B7C9B3B,
	AssemblyPart_get_MountPoint_mD5D437BBFF699DBD34FB735B8A5C2A9B873CDBEC,
	AssemblyPart_GetReferencePoint_mA201236E27083AF2890401B12D8DD8EAE59A319A,
	AssemblyPart_GetReferencePointByName_mD7F8DDB6004E03A542114CC8A0741B3516BC5237,
	AssemblyPart_GetMountPoint_m2222F2090704F0CDC758B755808AEEC450942A74,
	AssemblyPart_OnValidate_m38D200EFECAD3B6FF377AC38D18AAC318BDE752E,
	AssemblyPart_ValidateReferencePoints_m89909ADC56847528A6D64C919908BA4011F9D627,
	AssemblyPart_OnDrawGizmosSelected_mA4CDB6064D0D756A45BA6A6803030B952DCABBF9,
	AssemblyPart_DrawReferencePointGizmo_m1CCA44422B01AFC722B02C7A9032345CC8264F44,
	AssemblyPart__ctor_m3A868BA0F7EC280E2368E4F589546FDD3695782A,
	AssemblyStep_CreateDirect_m12C41BB8FF5AC02BEF0B44BD7CDFBD717F12E0E8,
	AssemblyStep_CreateWithScrew_m34C4D028BD6F7243EA8B441F9A66089668760BA4,
	AssemblyStep_Create_m6DB78A29CAB7CEA6B3615B08A2B35907A4165D7A,
	AssemblyStep_AddMountPoint_m194EA8814100C8F34D6BB078603A47010B7B707D,
	AssemblyStep_ToString_m320E7A2E2902A63202043DA88272743C043F82EB,
	AssemblyStep_IsValid_mF9FCF7AB253203EA0B1C0D39E09B71CE913F6A41,
	AssemblyStep_RequiresFastener_mFD703603DDAD3E14B20E48D70E9EE9A5E3988974,
	AssemblyStep_HasAdditionalMountPoints_m5341C821D4FE304D28E15E715126EF409BEA18E3,
	FastenerInfo_get_Empty_mB6E6BDF922B577CDD26EF4EAFE797397DB6D0F09,
	FastenerInfo_CreateScrew_m81C9F9116D2CFC59BA2634D6D3365B9927E530C1,
	FastenerInfo_GetScrewPrefabName_mF8FF0A3357E0D93D0E92BEAB58C43B420954CC6F,
	FastenerInfo_GetNutPrefabName_mE1B530F590FD507BC8E6580B643F4362D2853EAE,
	AdditionalMountPoint_Create_m599AEDDB62836E0EE9104830BF83B4AE2D3D96C0,
	NULL,
	NULL,
	NULL,
	NULL,
	AssemblyStepData_ToString_mE8C413BE6001E1478285B8AA28545AB3EBC65360,
	ExternalSystemDataProvider_get_DataSourceType_m14CDFE335CB35D4AD1AC1DE3BBB0B2E25D4977DA,
	ExternalSystemDataProvider_get_IsConnected_m428A4A6807AE65917050F1D4BD1FDE5384743B94,
	ExternalSystemDataProvider_set_IsConnected_m6D819239718C5DCC0C40836B3077E444E3EC2178,
	ExternalSystemDataProvider_Start_mC5926556AE446A1DC315041738E77D2C3977BF05,
	ExternalSystemDataProvider_LoadAssemblySteps_m1472E0480FA42AFF7360021D4F824FB8C3D5A452,
	ExternalSystemDataProvider_TestConnection_mDB39828320D3F62AB5BCBC56EB6EF8248B5CE976,
	ExternalSystemDataProvider_RequestAssemblyDataFromExternalSystem_mC61C99CC5DB66CBE28CB449494EAF4F4E8AB73EE,
	ExternalSystemDataProvider_ParseAssemblyDataFromJson_m8DCB25C7EAC9468A6EE2E03D4C0250320A667893,
	ExternalSystemDataProvider_LoadFallbackData_m1B98A53A671D82EA503AE632E1B5415C5DDE1AC9,
	ExternalSystemDataProvider_SetAssemblyData_m3B14BDC51384FD61843D7F9DC1BF8CF9F7B8A166,
	ExternalSystemDataProvider_ClearCache_m9D6121B0F4F95CBF9C21A89CB25B173D131E83C7,
	ExternalSystemDataProvider__ctor_m9590CBEF733E2D7E55508B2FB9B25C5DBFD152BD,
	ExternalSystemDataProvider_U3CStartU3Eb__11_0_mA2C973F8D30BE3B3D3D798DCCC3516D0BCFDDCCB,
	U3CU3Ec__DisplayClass12_0__ctor_mC73E03CEA17878AD793BE79F4E4B3CAE982D06D2,
	U3CU3Ec__DisplayClass12_0_U3CLoadAssemblyStepsU3Eb__0_m16AF587FC247F8F9393586C243453E0879517F8D,
	U3CLoadAssemblyStepsU3Ed__12__ctor_m3C4467A00F1A52FA235CC197F8154C46821806AF,
	U3CLoadAssemblyStepsU3Ed__12_System_IDisposable_Dispose_m823495F27CE6FA0DDB5A212DE42F0B86008943C8,
	U3CLoadAssemblyStepsU3Ed__12_MoveNext_mE99C821ED07EFE7AF1BBB874E479E5033CABF493,
	U3CLoadAssemblyStepsU3Ed__12_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m0E4CE5851A7B106D55ADAD5F7E9F1C6D597A441D,
	U3CLoadAssemblyStepsU3Ed__12_System_Collections_IEnumerator_Reset_mAD9121A9CE9C097EE3A7F42FD4D274661A6BC2A3,
	U3CLoadAssemblyStepsU3Ed__12_System_Collections_IEnumerator_get_Current_m04E3677891C88CDBD5394C91BD4E70518891BCC8,
	U3CTestConnectionU3Ed__13__ctor_mED859D27EA19C44ED202C9A522933A077C6B2122,
	U3CTestConnectionU3Ed__13_System_IDisposable_Dispose_m64E944147C46A76E1472CEE05499759C04CF0925,
	U3CTestConnectionU3Ed__13_MoveNext_m17B3C0EFCABCFD6194690E8D5E1EA25D24900CDC,
	U3CTestConnectionU3Ed__13_U3CU3Em__Finally1_mF77D5135A870834B987AED604F7F619AD1DC645B,
	U3CTestConnectionU3Ed__13_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m57C0DAED84E9128B87049D4DB24DC7FFEF2FF4CF,
	U3CTestConnectionU3Ed__13_System_Collections_IEnumerator_Reset_m0109DE10A1A3A1B19A8D19D94B86B6CA750FF7F7,
	U3CTestConnectionU3Ed__13_System_Collections_IEnumerator_get_Current_m0A71964B7E2DB3173D0C01DCF06CE10A79F61A59,
	U3CRequestAssemblyDataFromExternalSystemU3Ed__14__ctor_m646BB5DCB05AFE7A08F5EA4D5666CF4E426C0FC4,
	U3CRequestAssemblyDataFromExternalSystemU3Ed__14_System_IDisposable_Dispose_mD38B1871DF3B73A3E411F2E513100C439DA7CBF2,
	U3CRequestAssemblyDataFromExternalSystemU3Ed__14_MoveNext_m9011DA563EDA71964BD5306F8F12A8549F6E7956,
	U3CRequestAssemblyDataFromExternalSystemU3Ed__14_U3CU3Em__Finally1_mDBFECEC7CE9D75CCBE4A55DABA1B94ADF841E02D,
	U3CRequestAssemblyDataFromExternalSystemU3Ed__14_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m7CF71093660F98A17DFCDB1CF90366D9783825BC,
	U3CRequestAssemblyDataFromExternalSystemU3Ed__14_System_Collections_IEnumerator_Reset_m5A7B8010C71A615ED9E3C3B6F59146359ECEBC9D,
	U3CRequestAssemblyDataFromExternalSystemU3Ed__14_System_Collections_IEnumerator_get_Current_m73D5DF270A82D9EBDCAD575060CFED95C809EF74,
	VRStartMenuDebugger_Start_mCA11FD2E79A9F2411E1F92AEA1D4D4FE598EFCA2,
	VRStartMenuDebugger_InitializeDebugger_m50DCD85046ED23A21922790766C2C5BF58598AF2,
	VRStartMenuDebugger_FindComponents_m195F2B12B6D65A57C1C5EDD4E541ABD55341DE64,
	VRStartMenuDebugger_CheckComponentStatus_m887ADEF2C22CC1903D56E36E2174340C4FB57EC1,
	VRStartMenuDebugger_AutoFixIssues_mFD6C6EAA452662400EE66E4ACFB0E62EEBD8A2E0,
	VRStartMenuDebugger_FixInputFieldColors_m0B7EA43D6125FE83F7EC30C0EE5C4B6248A25D7C,
	VRStartMenuDebugger_EnsureEventListeners_mB6B8DDD8F3557834CC84593804D604D718C21961,
	VRStartMenuDebugger_SetupDebugListeners_m0F67D6CD44B6E69A5CC77EDE6B059DCB74123EDB,
	VRStartMenuDebugger_OnDebugUsernameChanged_m3783811BCAA798FE77F5AF503A2DAF6DF55A6EC1,
	VRStartMenuDebugger_OnDebugStartButtonClicked_mFD69A072CC76586FE7569FB6D4436CD4B20F7659,
	VRStartMenuDebugger_OnUsernameChangedDebug_m4660AE06B8591298F6CBAD1BEA86100E3F7F04C5,
	VRStartMenuDebugger_OnStartButtonClickedDebug_mD085B289A5C05AC0B77C482C7A045D531545817B,
	VRStartMenuDebugger_Update_m109186679A78C4613687189ABAAF04284FFA0DE0,
	VRStartMenuDebugger_TestUsernameValidation_mE81B392B607AFE5B27637FD9517ABE15673476C5,
	VRStartMenuDebugger_SimulateButtonClick_m7D76FCB9BAD45F0604B456776E03EBC46A9D7B04,
	VRStartMenuDebugger_OnDestroy_m47501387F1037E4067C9D35EA88571E2952BDBA8,
	VRStartMenuDebugger__ctor_m7BE0E184B4DCD358DFE8598CDA06134701DF1941,
	U3CInitializeDebuggerU3Ed__6__ctor_mF601DC61C87C6320172FFD4DD28B30F13B8AC96D,
	U3CInitializeDebuggerU3Ed__6_System_IDisposable_Dispose_m5EB40F4521B70B4D9DE8A1E350604A8672CA5C0E,
	U3CInitializeDebuggerU3Ed__6_MoveNext_mF5F5CA347F139A978FAA9CCF71246754F38DE723,
	U3CInitializeDebuggerU3Ed__6_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m05E97CBD35B96A1EBE2F44EC9102D2ED3AD3C97B,
	U3CInitializeDebuggerU3Ed__6_System_Collections_IEnumerator_Reset_m70984B78FEA26B0E8266FCB0AF86AA0E0EB7B96A,
	U3CInitializeDebuggerU3Ed__6_System_Collections_IEnumerator_get_Current_m26C5B45A5E24AAD3AAE213B7ECC10882B1BA2D17,
	VRStartMenuInputHandler_Start_m137CD96555FE4F55446BFD50245245BFA03969BF,
	VRStartMenuInputHandler_Update_mF1FB12C8E91A65089D2DE2182931F759FC6FD334,
	VRStartMenuInputHandler_InitializeInputHandler_mFCF368DFE40DB98269297E9241AA74887F07F773,
	VRStartMenuInputHandler_InitializeVRControllers_mED30E8BBEE14FD4BBDFBBD7475AA9C30E8D9DCB5,
	VRStartMenuInputHandler_SetupInputFieldEvents_mB71CAB34CB1B25C9086FDBC6A3E91A03F3EE366D,
	VRStartMenuInputHandler_HandleVRInput_mBF6807512C22D0483E70E8A6EB24C8836F191516,
	VRStartMenuInputHandler_OnInputFieldSelected_m7AEB482780B962F80F36DFC5A0EBFEEDF1B4FA1E,
	VRStartMenuInputHandler_OnInputFieldDeselected_m57C1A10FCB11AF304502C7399A5FA2C04DEC4DE3,
	VRStartMenuInputHandler_OnInputValueChanged_m1E0C4D176DDCF4D060C783B0735B6E9CC5782928,
	VRStartMenuInputHandler_ValidateInput_mDF07421BFAEFFD767FFA9AA4C36ECFD53BFCB20A,
	VRStartMenuInputHandler_ShowVirtualKeyboard_m6658344E465C06E44B7ECF1CE71FF16BAE562EA7,
	VRStartMenuInputHandler_HideVirtualKeyboard_m0DACE19F455726E6559CBAAD034896F8DBAC9C3A,
	VRStartMenuInputHandler_PositionVirtualKeyboard_m3A7124F39BB7EF5848EB1CECBB10ED93B98FF22E,
	VRStartMenuInputHandler_CreateVirtualKeyboard_m6DFCDF81906C0BA1D7C957DB6A48744E98D52920,
	VRStartMenuInputHandler_CreateSimpleKeyboard_mE607E49D01315D6BFD795D6080DFE911AB15CDA3,
	VRStartMenuInputHandler_CreateKeyboardButton_m6BB7DC28C3DCA398CF1E5111786C32064E7269EA,
	VRStartMenuInputHandler_ConfirmInput_mAA6FF7DD95A7626C4A63AF49EEDD2D3AAFE86435,
	VRStartMenuInputHandler_DeleteLastCharacter_m655E23F3537BA2E3C17E840CE6558DD5435A298A,
	VRStartMenuInputHandler_CancelInput_m81A357F1B947E35EC2E6D730361295A00AAB45AD,
	VRStartMenuInputHandler_TriggerHapticFeedback_mFB1075B4A1A79ED8FD6B10FB783B6EA33915E503,
	VRStartMenuInputHandler_PlaySound_mA2D4E417DDC3D6F8F07759659D13F7FA77A1AE22,
	VRStartMenuInputHandler_SetTargetInputField_m17B5E22D37C983BDB24E012E9F0FF227FA0575C9,
	VRStartMenuInputHandler_SetStartButton_m6C4809C34295CFEA5076390A05172AB46AECE738,
	VRStartMenuInputHandler_OnDestroy_m3F6822CD6DE09F9AA4413E5B56D29AFCB5EB6312,
	VRStartMenuInputHandler__ctor_m4692B8956B477837C04B0A7F332CAF987CFEC6F0,
	U3CU3Ec__DisplayClass41_0__ctor_m499D9EA61ECC2731D1395ABAA2215DCB62466B1C,
	U3CU3Ec__DisplayClass41_0_U3CCreateKeyboardButtonU3Eb__0_m7BF9B61A50A8B7E38BB86766226D87687BAFF3B7,
	VRStartMenuManager_Start_m9F886633AC413F63DBDE41E7E222EE5F56E67DFF,
	VRStartMenuManager_InitializeStartMenu_m04C795CD3CA78AA19228ABFFD9643A0FDFC2973A,
	VRStartMenuManager_SetupComponents_m2DEAF59F164ABFB7EBFC878307013E3D21FB7E13,
	VRStartMenuManager_EnsureUIReferences_mA6ACD2FC19B8289E3A341E43286A7164D3BA6783,
	VRStartMenuManager_SetupVRUI_m7EDB14F93B2BD9EEBA68CAD61E49C7CBC9AD347C,
	VRStartMenuManager_PositionUIForVR_mD6DCC7F14DB8A9E4C8B0532F304EB0C8D0E7FE48,
	VRStartMenuManager_SetupUIElements_m67BE95F818BB4CEA7242B1FB31495986523C7ACA,
	VRStartMenuManager_SetupEventListeners_m2906A0BC9B1589C20053F36A4311D43F330D8C6B,
	VRStartMenuManager_OnUsernameInputChanged_m6EA9A6A6FAAFBFC0B29B9E55F69D6920816392D4,
	VRStartMenuManager_ValidateUsername_m9D7AA04294D2029192A994968BBACD87573A740C,
	VRStartMenuManager_OnStartButtonPressed_mC9C370BE0AEEE182B836745018DFB5DC151E8097,
	VRStartMenuManager_TransitionToNextScene_mA7E052CDD2051BB40382F29938340448D42859C0,
	VRStartMenuManager_ShowUsernameEmptyWarning_mB03BFC06B721F52AF75A5094F5529ACF081BC6D8,
	VRStartMenuManager_ShowTemporaryWarning_m17C50A332A89F46B83B1EE53AA54D4A090702DB4,
	VRStartMenuManager_PlaySound_m7D90BFA6B6751A6528C69413FE3E8020F6A425BC,
	VRStartMenuManager_GetCurrentUsername_m7CE4CF30AE7FFC1511D50D52224D425ECDA91214,
	VRStartMenuManager_SetNextSceneName_mAFEABA75AB99D71A60CB31CD75931FE12835CA72,
	VRStartMenuManager_RepositionUI_m9F928B3131C92D4C94A5E88A41678BDA74444BFA,
	VRStartMenuManager_OnDestroy_m15A4AC7DC8A6973085C5E7750F33DB3901D8793E,
	VRStartMenuManager__ctor_m4A4A9EB001BD35898F06BFE63B5A3FFB73D1143D,
	U3CInitializeStartMenuU3Ed__24__ctor_m437041FE0499D7785CB9D91A232CBB3A4EAB5CC1,
	U3CInitializeStartMenuU3Ed__24_System_IDisposable_Dispose_mE32D883D9F5F72EB7D43339577E3C2D09B6DDFBC,
	U3CInitializeStartMenuU3Ed__24_MoveNext_m936FAC86DD24ECE91E0DFC919CAE158E2702FC58,
	U3CInitializeStartMenuU3Ed__24_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mED8EE2B49708FE33CFAE34E993E6F34D8B9991BC,
	U3CInitializeStartMenuU3Ed__24_System_Collections_IEnumerator_Reset_m1C5131F125A503B0C61288BA2046401EC7ADE6DD,
	U3CInitializeStartMenuU3Ed__24_System_Collections_IEnumerator_get_Current_m0F4F5C8508EAB01CD23A77923615CD8AFBC1938B,
	U3CTransitionToNextSceneU3Ed__34__ctor_m5E47491BF11C1DA429A0D9EA87F87CFD087F050B,
	U3CTransitionToNextSceneU3Ed__34_System_IDisposable_Dispose_mFCE1E18878E683311B0CAA4C0ACE5E43EA95A800,
	U3CTransitionToNextSceneU3Ed__34_MoveNext_m6B537140FDC6FA1D94A964827AEA30D2A506246A,
	U3CTransitionToNextSceneU3Ed__34_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m1BD547156554140B100A0C5478482173B5FAE1D5,
	U3CTransitionToNextSceneU3Ed__34_System_Collections_IEnumerator_Reset_mF1B8A22D6F272DFE16BA19705CB6F41E48E74DD1,
	U3CTransitionToNextSceneU3Ed__34_System_Collections_IEnumerator_get_Current_m19A156A6A14955E57DBBF11AAFE000F3B2F80896,
	U3CShowTemporaryWarningU3Ed__36__ctor_m27F57359E19E5CFD4B26954401A3D12D22AAF0F7,
	U3CShowTemporaryWarningU3Ed__36_System_IDisposable_Dispose_mC401D53FA967D9A0D6F8BE627B642AC722DE8816,
	U3CShowTemporaryWarningU3Ed__36_MoveNext_m85AB959FDD14873C823CA5060DADD1CFFFE0E2C2,
	U3CShowTemporaryWarningU3Ed__36_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m100151301AA8DDCDF651527EB5F49B71C1BB8F1F,
	U3CShowTemporaryWarningU3Ed__36_System_Collections_IEnumerator_Reset_m7DDCBE8B54528677BEE4D10993A450182F40DCB5,
	U3CShowTemporaryWarningU3Ed__36_System_Collections_IEnumerator_get_Current_mD149B075A0BF1318C8B796F78F2974598F377BA7,
	Neo4jAssemblyController_add_OnPartSelected_m4D16A619571CE65FD08CFEDDBE34D00E3469E194,
	Neo4jAssemblyController_remove_OnPartSelected_m35A3259406D73B2A30DCA1699D81B0D376B7426F,
	Neo4jAssemblyController_add_OnAssemblyStepsLoaded_mEF161A9592C509DEC5BEE31A2DAB43AA2FE177E3,
	Neo4jAssemblyController_remove_OnAssemblyStepsLoaded_mB8CAA0AEC52E6FFED644C55D5B8FC8FF6A513C2C,
	Neo4jAssemblyController_add_OnStepExecuted_mA0F70E2031DA9BE90755EEB10F1CC206475F157F,
	Neo4jAssemblyController_remove_OnStepExecuted_mFF08276D8FE610DB524C365FD2D26D3DA6D2293E,
	Neo4jAssemblyController_add_OnAssemblyCompleted_m2BAC78501536C56C9D59EBD9AD15360EE42BE25C,
	Neo4jAssemblyController_remove_OnAssemblyCompleted_m6124236E0195C0B5275A79A7094326324CE87755,
	Neo4jAssemblyController_get_AnimationDuration_mD65987078E3F5628F74EDBC6FA7FB8D1AC9A7FA6,
	Neo4jAssemblyController_HasRemainingSteps_mA3C07C4AA8D348AF02248CDFCA02C4B860395570,
	Neo4jAssemblyController_get_AnimationSpeedRate_m4F8403F7507EF50FFC1AF78331AAD835FBCE46FC,
	Neo4jAssemblyController_set_AnimationSpeedRate_mEC2065E1CE70858518AACC29AF215DAD353C069F,
	Neo4jAssemblyController_Start_m3AECDA8E796F8C997D6FCBF00A3854965D62EE8D,
	Neo4jAssemblyController_InitializeDataSource_m4A02EBD71B98D99A666978180F80837642FB5544,
	Neo4jAssemblyController_InitializeVRInput_mF5AAAE73C17E0A6C8AE2B105BDE77F59AE76DB23,
	Neo4jAssemblyController_Update_m55852F29A8A74946F9E1C94B8F7772DDBAECA3A5,
	Neo4jAssemblyController_HandleVRPartSelection_m8D20EC8D130555634039D85720CC60A9C2500E88,
	Neo4jAssemblyController_LoadAssemblyStepsFromExternalSource_m220FD7EEB23B401993043C74AAB99DD339DB643E,
	Neo4jAssemblyController_ReceiveExternalAssemblyData_mADA684E475D76B1FB3BDD135696FAA1AB1F5E521,
	Neo4jAssemblyController_ParseExternalAssemblyData_m539B7AC68C9A8C7C0F0DC85ECCC0E54AD54500D1,
	Neo4jAssemblyController_ProcessExternalAssemblySteps_m9E85D256E06AB803A13D72A63B39F08DEB1BE0CF,
	Neo4jAssemblyController_RegisterEvents_m25E701F38AB55F43F3C27000F68194C07E2A5296,
	Neo4jAssemblyController_UnregisterEvents_mFAA72A59582464448EC861E7F37EA49AD36B925E,
	Neo4jAssemblyController_InitializeVRSystem_mAC7B055F97C12134DB0841F1A985BE7D00FA3AC8,
	Neo4jAssemblyController_WaitForVRPreparation_m54E9E7B24E841DD3B151F5CD649A72FE3D5C1BDA,
	Neo4jAssemblyController_OnDestroy_mAEAF19F1505C989BC014E45A134099F7BAD6C13A,
	Neo4jAssemblyController_InitializePartMapping_m682509B14C0F5C5E53EC3D2C7AE87D9BF4988C1D,
	Neo4jAssemblyController_SaveAllPartsInitialState_mF8E0AE3F5A1EF7EB7D69D072BEAD9583623F8FD2,
	Neo4jAssemblyController_SaveAllFastenersInitialState_m76BBA96BD40D615670BF2C04E489AEA4811E8582,
	Neo4jAssemblyController_RestoreAllPartsInitialState_mF422227C574FAE5053B7A1D338846B44E6208405,
	Neo4jAssemblyController_RestoreAllFastenersInitialState_mAA05BEFC1C4A70827ECEA501FE997AC0D8C4D959,
	Neo4jAssemblyController_SaveStepPartsInitialState_mC896D7D191AD4AF4378380D20610E7740D4AD93A,
	Neo4jAssemblyController_RestoreStepPartsInitialState_m4E32E569376ECCA5FA209A3CDC143684E4FD7FB1,
	Neo4jAssemblyController_SelectPartWithRaycast_m81F9DEE660CCC6206D273753209E8A25D69F0AAA,
	Neo4jAssemblyController_ResetAssemblySteps_mC66B1EE8CE40B790B643BAB96909241CC8B03381,
	Neo4jAssemblyController_QueryAssemblyRelationships_m510B66812C23BBC224DA63FF670E640873B240A7,
	Neo4jAssemblyController_ProcessAssemblyRelationships_mFF290E89F175D17B687084ACDE6CA8EDAF88641F,
	Neo4jAssemblyController_ExecuteNextStep_mACFB3484B07642D7E1AB5DD2FCEF0BF28EFD0C5A,
	Neo4jAssemblyController_ReplayLastStep_mE733ACCC650508A829BD8BC1B4B6583E4D03195D,
	Neo4jAssemblyController_ExecuteNextAssemblyStepCoroutine_m3CF8AF855E6F76DF59E55F173D5D9A09B96035EA,
	Neo4jAssemblyController_ReplayAssemblyStepCoroutine_m3297ABAA72F89163A828E1D6124F9FC1A330AF5E,
	Neo4jAssemblyController_ResetAssembly_mD59C8122A2FB6FB3EFEC64F7CD6269AC2E6EFE23,
	Neo4jAssemblyController_InstallFastener_m9359B647D3FE9CA58FA7E9178FEACEF6EEC6B353,
	Neo4jAssemblyController_GetScrewPrefabName_m11120109918806CCD7A9C806AEA1B0FDCF201DC3,
	Neo4jAssemblyController_GetNutPrefabName_m5E19A285FC2691F9273945519D3D4AF0ECA55D25,
	Neo4jAssemblyController_GetOrCreateFastener_m5668C21E677AD73F08DB03CD0BB4A446D65B32D1,
	Neo4jAssemblyController_GetFastenerFromScene_m71CCB551C77E2F8132E2178C1E00F39E20ABB059,
	Neo4jAssemblyController_CleanupFasteners_m2082E46B24B9678AB1A28D93A0BECE9EA2BA2C9C,
	Neo4jAssemblyController_ValidateReferencePointMappings_mD89452E7D5999153B80C25E96FC5F98824180A0A,
	Neo4jAssemblyController_GetReferencePointIndex_m0D83B603692A1137C3BC03DC391D1CC85EE8B728,
	Neo4jAssemblyController__ctor_m4ADC9376EA623CE58D22058DA0C60245D51BBA9F,
	AssemblyStep__ctor_m40EF831184A0632EF7E7D625F994BB6378CEE04E,
	AssemblyStep__ctor_m9E10466D3C2D06685921F7B5B8A0C782FF812EFE,
	AssemblyStep_ToString_mF97FA0F6A8BF46F54FA596AB0A2C5C7F43719B1B,
	U3CU3Ec__DisplayClass59_0__ctor_m674F3B8084624F71F29620390EBB50983E196BA8,
	U3CU3Ec__DisplayClass59_0_U3CLoadAssemblyStepsFromExternalSourceU3Eb__0_m4F0C3DBE566EB3831FD70B0AEAEB01AC61D340A5,
	U3CLoadAssemblyStepsFromExternalSourceU3Ed__59__ctor_m2C1F4EFC774BF14272FF4C1DE89DE90681AD6C55,
	U3CLoadAssemblyStepsFromExternalSourceU3Ed__59_System_IDisposable_Dispose_m319F5BB430D02162EF3D195381B7E0DE3470B642,
	U3CLoadAssemblyStepsFromExternalSourceU3Ed__59_MoveNext_m4C664DDEE23B0F7DB2C62D6A1D9AD7D437EF41EF,
	U3CLoadAssemblyStepsFromExternalSourceU3Ed__59_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m9E75C1167D32508D6D41500462CB256C3E65D4F5,
	U3CLoadAssemblyStepsFromExternalSourceU3Ed__59_System_Collections_IEnumerator_Reset_m08F35C22B001B0C70C07A90DAFD20092C2B8B704,
	U3CLoadAssemblyStepsFromExternalSourceU3Ed__59_System_Collections_IEnumerator_get_Current_m6300756178A711F4FDAA4DD544522FEEC89132A3,
	U3CWaitForVRPreparationU3Ed__66__ctor_mA26F8D438CFBCC764A3973DF91BB079E5FC86820,
	U3CWaitForVRPreparationU3Ed__66_System_IDisposable_Dispose_m5490BA5AB8662E4178645EA3F755E64B9B077266,
	U3CWaitForVRPreparationU3Ed__66_MoveNext_mCA451A1C7C47A420465B31D6CB316F6958D32F42,
	U3CWaitForVRPreparationU3Ed__66_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mD036D6150E98DC02DE3A8F510FDC7A8FDAFD89F0,
	U3CWaitForVRPreparationU3Ed__66_System_Collections_IEnumerator_Reset_m5F772293B065B07DF676A82107C0BBB0A5F92428,
	U3CWaitForVRPreparationU3Ed__66_System_Collections_IEnumerator_get_Current_m52AC34BA7D5DFF18B219A7BD2CD4EBE0BDF352B3,
	U3CQueryAssemblyRelationshipsU3Ed__77__ctor_m63FF621B2605D1CE12644713FAD955EF8AF70741,
	U3CQueryAssemblyRelationshipsU3Ed__77_System_IDisposable_Dispose_mF250A1D07A074FFBD2179196CB4C4A9A5D39F144,
	U3CQueryAssemblyRelationshipsU3Ed__77_MoveNext_m68B7BEE9C9852EC055960456DEB832C85417BDA4,
	U3CQueryAssemblyRelationshipsU3Ed__77_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m14ACC263AD0586D408DAF65D36ECC65EC0301CBA,
	U3CQueryAssemblyRelationshipsU3Ed__77_System_Collections_IEnumerator_Reset_m37782519971FBCD9FBD71AF51DF5E8441868B9B2,
	U3CQueryAssemblyRelationshipsU3Ed__77_System_Collections_IEnumerator_get_Current_m6A7ABA42EC552ED61B2FA04C827210B6B6B1887D,
	U3CExecuteNextAssemblyStepCoroutineU3Ed__81__ctor_m5316EDF961735308B4F4DE6657C8705AE634486C,
	U3CExecuteNextAssemblyStepCoroutineU3Ed__81_System_IDisposable_Dispose_mFA8525A8DD6ABC5567010D9ABD1A101F54B7A5FD,
	U3CExecuteNextAssemblyStepCoroutineU3Ed__81_MoveNext_m75E73CA60ABC40F906CA9BD24676B7375D36AE37,
	U3CExecuteNextAssemblyStepCoroutineU3Ed__81_U3CU3Em__Finally1_m0AC89C812FC7EBC2D1CE5369314AE31F4AEFB13A,
	U3CExecuteNextAssemblyStepCoroutineU3Ed__81_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m795F6DC87ECFB5D7634AB43CA5B8A81967CED80B,
	U3CExecuteNextAssemblyStepCoroutineU3Ed__81_System_Collections_IEnumerator_Reset_m90271590611B26E1C589AA1B03B223B7125CFD80,
	U3CExecuteNextAssemblyStepCoroutineU3Ed__81_System_Collections_IEnumerator_get_Current_m5AA9A1AA8D9E4AE14CECA949F637D68E2690EE69,
	U3CReplayAssemblyStepCoroutineU3Ed__82__ctor_m683C4A2977ACE95F8A4B3212D8B74DCDFDF9985F,
	U3CReplayAssemblyStepCoroutineU3Ed__82_System_IDisposable_Dispose_m5C94E4437FC7601FD1A3EB687903BC868F3D84FB,
	U3CReplayAssemblyStepCoroutineU3Ed__82_MoveNext_mEEF55F7F8BE7B275D426DB98664325A9C0750FFA,
	U3CReplayAssemblyStepCoroutineU3Ed__82_U3CU3Em__Finally1_m4D496C152DE3BBA6D1A114155AB7325053288EE7,
	U3CReplayAssemblyStepCoroutineU3Ed__82_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m0D0A92E990BE7D4B853008F555BF55C1C0DA9914,
	U3CReplayAssemblyStepCoroutineU3Ed__82_System_Collections_IEnumerator_Reset_mE6F070F38D905A98889F0349BD79FADA66F63596,
	U3CReplayAssemblyStepCoroutineU3Ed__82_System_Collections_IEnumerator_get_Current_mCEBE7A15C471402777371677167DE1FC168AD000,
	U3CInstallFastenerU3Ed__84__ctor_m4FD9BF41A9BFE32F2D0B379420BF7D3B47AC6532,
	U3CInstallFastenerU3Ed__84_System_IDisposable_Dispose_m415B97167EC0304AE3FAEA6F4A8F5EC96B942B9E,
	U3CInstallFastenerU3Ed__84_MoveNext_m83532E4BE67AD72FF1A99FF40711360D039B7E40,
	U3CInstallFastenerU3Ed__84_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m86C08B0FEEBD5B379113B97BD31D19E8D1184862,
	U3CInstallFastenerU3Ed__84_System_Collections_IEnumerator_Reset_m6B5303B72F2DA1B747CE3A057C9BE9D5E73A69EF,
	U3CInstallFastenerU3Ed__84_System_Collections_IEnumerator_get_Current_mB6CB41312E67EFED9789A3AABB5CD51B452A4B45,
	Neo4jAssemblyUI_Start_mCDBF12C4FCBB62A6578482CB3130B39E429DB8B1,
	Neo4jAssemblyUI_InitializeUI_m05FCB4604C7F9AFC19E8C254447704E0A83F8158,
	Neo4jAssemblyUI_UpdateStatusText_m073B99500339696C19FF9D540DFD383B45EFD509,
	Neo4jAssemblyUI_UpdateSelectedPartText_m42F4B327A9CDE05D64706788A964637867EE1C9F,
	Neo4jAssemblyUI_UpdateStepsCountText_m767BFA50F60CDD8717A5CB624DA986A4C484D3F5,
	Neo4jAssemblyUI_OnNextStepButtonClicked_m89A46D40A824A84DB2D5BCED0694AF59297F74C9,
	Neo4jAssemblyUI_OnResetButtonClicked_mC352298942248A3EAB6C13E668BDD2A640DEF32E,
	Neo4jAssemblyUI_OnAutoPlayToggleChanged_mD6E10ECFDD2336CE79F14D364972F22ECEE14BD5,
	Neo4jAssemblyUI_StartAutoPlay_m14DC99B95C55D759A7C8C9F09391D105DB4E4161,
	Neo4jAssemblyUI_StopAutoPlay_mEA3969A97E2AE15517BDDCF3CABA44AAD0B221B8,
	Neo4jAssemblyUI_AutoPlayCoroutine_mEC3062D30DE1321E94E76FB8A224816EB264C6B1,
	Neo4jAssemblyUI_EnableNextStepButton_m5F22F1526B195F1CACBF8E394D3D4E706C77E18F,
	Neo4jAssemblyUI_EnableReplayButton_mE8BAB509106ECCB7533C84A7402E6BF5A4E25846,
	Neo4jAssemblyUI_OnReplayButtonClicked_m59F31CA63E639C3FDD53F42CCAA3E3CA030769A8,
	Neo4jAssemblyUI_OnSpeedSliderChanged_m92803AE875B80617418DF329FB59CC4816F9AF59,
	Neo4jAssemblyUI_UpdateSpeedValueText_m204D5414571DCA126E0FC211D05C7309FF849FA8,
	Neo4jAssemblyUI_OnPartSelected_m24FF709F553104F87BF3472EEBD5E7B4BC484A21,
	Neo4jAssemblyUI_OnAssemblyStepsLoaded_mF3A4DDE5C3A0C4605A57BBFF65655881EA4EA879,
	Neo4jAssemblyUI_OnStepExecuted_m5B708537395770CEA2BA9E44A16EA6F625EB5CF3,
	Neo4jAssemblyUI_OnAssemblyCompleted_m5C7B8529034EF917149CDA74A7F93D781A7FBA88,
	Neo4jAssemblyUI_DetectAndConfigureVRMode_m78FFD5A1A55259CA0F1C02927E236847AE6E88B2,
	Neo4jAssemblyUI_DetectVREnvironment_m732348695CF92B7A2BAFA44E81BD4ACEDD166F0B,
	Neo4jAssemblyUI_ConfigureVRUI_m8948C893F1758447C1261DD4420B805B19BC46A5,
	Neo4jAssemblyUI_ConfigureCanvasForVR_m91AD432342AD7207153A2B3E7B07FB22E4522D90,
	Neo4jAssemblyUI_PositionVRUI_m4232E667A9CBD0671EC8737C21E65113B53CB0EC,
	Neo4jAssemblyUI_UpdateVRUIPosition_mB06EB09924C3F77E2749787054D5A154CDED7861,
	Neo4jAssemblyUI__ctor_m6F3E7FB649ADDE8690BC38471688F5EBBA39A66C,
	U3CAutoPlayCoroutineU3Ed__33__ctor_m894E972DB222B6440DC956DBFE118C917518E8B6,
	U3CAutoPlayCoroutineU3Ed__33_System_IDisposable_Dispose_m5EC2DB12AE6973E7279E6B0C2AB768C7916B928B,
	U3CAutoPlayCoroutineU3Ed__33_MoveNext_m9B1141BF801C1A5FDF5495E4D6A4309474B893C2,
	U3CAutoPlayCoroutineU3Ed__33_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mA69BADFC70ADCE26A2110E9CBC66F1442312306D,
	U3CAutoPlayCoroutineU3Ed__33_System_Collections_IEnumerator_Reset_m4C3050A99DA581ECE711B4BFF1F6F883B5C171AB,
	U3CAutoPlayCoroutineU3Ed__33_System_Collections_IEnumerator_get_Current_mFB2351BF4ED4DF4F0BAB8B18ED888603AFA5E4BA,
	Neo4jConnector_ExecuteQuery_mEAAA9076CCEACB71D39D0CB2B2C435F2482E902A,
	Neo4jConnector_get_DatabaseUrl_m818FFB7AB8FBCCBF2B20198871C4BAF4F3A87406,
	Neo4jConnector_get_DatabaseUsername_m3A33077D70412DDA80A989E4ED3A80EA0988AADC,
	Neo4jConnector_get_DatabasePassword_m5FDE24B2B8E1ACF7487AFCEC1B3C77EE026C0AE9,
	Neo4jConnector_TestConnection_m35B27310317D2B22ADA87DE18797F6680A670975,
	Neo4jConnector_QueryConnections_m20F0AB449482A817B28427DA4182C5AF63780ADA,
	Neo4jConnector__ctor_m3A334A82D94E2CCDF167E43D7A2F762E6BCC9934,
	U3CExecuteQueryU3Ed__3__ctor_mF382C62277E30FCD9C5529C0F149ACDBD2ED51B3,
	U3CExecuteQueryU3Ed__3_System_IDisposable_Dispose_m987DD58EE3222A4AF70C7FE9CC9D6EA214487C59,
	U3CExecuteQueryU3Ed__3_MoveNext_m6C2962BA6BFD5B1A97CA394F19EF50DC6C6F71AE,
	U3CExecuteQueryU3Ed__3_U3CU3Em__Finally1_mCDA4EE30E34C7960A59D93912BDF64C1EABE8927,
	U3CExecuteQueryU3Ed__3_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mA0A3756E3BDFC5451D48651394FD8F5B729CD492,
	U3CExecuteQueryU3Ed__3_System_Collections_IEnumerator_Reset_m20E488C05406BAFE351959C407556F69D5EE09D5,
	U3CExecuteQueryU3Ed__3_System_Collections_IEnumerator_get_Current_m40360A3B3B6ADB0982FB0CCB874DB747A142F5D3,
	U3CU3Ec__DisplayClass10_0__ctor_m2D6F0E70743D756A75C9B0210FDF2B01DCEA797C,
	U3CU3Ec__DisplayClass10_0_U3CTestConnectionU3Eb__0_mD98388D4B0828C0E8454B39CE13470D8E20D9B3D,
	U3CTestConnectionU3Ed__10__ctor_m04FE15BC317A8298369BEAD645E40BFD404767D4,
	U3CTestConnectionU3Ed__10_System_IDisposable_Dispose_mCA99EF51488D04A2E93023FCC08B2DA489A05EE7,
	U3CTestConnectionU3Ed__10_MoveNext_m96A27E3B5C8CB1E4819A172195EB3FA0F8607C4A,
	U3CTestConnectionU3Ed__10_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mB6722C108C3DF5120114EF33DABBB98028A477A0,
	U3CTestConnectionU3Ed__10_System_Collections_IEnumerator_Reset_m47CA6D10F214B3C95F0C47E3385A560B639FE44C,
	U3CTestConnectionU3Ed__10_System_Collections_IEnumerator_get_Current_mA687283225AABB53C7277F963BE5FF3EC2F763E4,
	U3CU3Ec__DisplayClass11_0__ctor_m71B8992D0FB0926ACE415E53A4684A2DF6714732,
	U3CU3Ec__DisplayClass11_0_U3CQueryConnectionsU3Eb__0_mB885CD288657C90B46973ED4A83370E2EB43F1E1,
	U3CQueryConnectionsU3Ed__11__ctor_m4901A0BADFEAAD19E68A799040D9219B08A784B8,
	U3CQueryConnectionsU3Ed__11_System_IDisposable_Dispose_m9B6A9EAFA1DF81B82CA4736BF8C5CDDAEDEE53C4,
	U3CQueryConnectionsU3Ed__11_MoveNext_m9C9706538C675FFC6BBAFF18A15E02B14F0F87B0,
	U3CQueryConnectionsU3Ed__11_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m2AE7E242201CF1333C7E094F5478EF48E1FF84FE,
	U3CQueryConnectionsU3Ed__11_System_Collections_IEnumerator_Reset_mEC7096B0DC3ECFCBD13C0E0172E69C12B2AA13B1,
	U3CQueryConnectionsU3Ed__11_System_Collections_IEnumerator_get_Current_mF188E3532A258CACD997C4E0CEDA25586E84D3CA,
	PICOActionBasedInputHandler_Start_m5283276783CEB7F35A612223A226FF975708ABC4,
	PICOActionBasedInputHandler_Update_mFCE96BE462AF65AFDD4CB9726AD0B065619BEEA3,
	PICOActionBasedInputHandler_InitializeComponents_mE8F9A747698CF519F19F16E6ACB4E80D9975C075,
	PICOActionBasedInputHandler_LogInitializationStatus_mBC9F0A470D8A398A0D7442D9E27FF63C8F8E169E,
	PICOActionBasedInputHandler_LogCurrentStatus_mEF634A96468D6034ACA5F57F8D18896788F7596A,
	PICOActionBasedInputHandler_LogActionStatus_mFB08C4828F36409A7DBDF030AA5C336476293784,
	PICOActionBasedInputHandler_HandleInput_m24C7B04C2DA52B90C5A12D794FFEDD19CAEC966B,
	PICOActionBasedInputHandler_HandleControllerInput_mA220D0C9EA4D19845DD7BA8AF7DDCE3859D80DEE,
	PICOActionBasedInputHandler_IsActionPressed_m3155B73DCCE30E9E19FE85CC5B9EDDE898C9E0FD,
	PICOActionBasedInputHandler_OnActivatePressed_m7AE24EB01CCA071648CEF003F9E560851898C3B8,
	PICOActionBasedInputHandler_OnSelectPressed_m3F32F937F7083102736C5C01AEF8ADC5B9DF1CBC,
	PICOActionBasedInputHandler_OnResetPressed_mACA5F4619F25C6DD52D4D8AD59E58B82BB11122F,
	PICOActionBasedInputHandler_OnDebugTogglePressed_mACB69A49A4B556FF347ED7444F59CD4FD361E4BB,
	PICOActionBasedInputHandler_TestAllFunctions_mE7DC4B29F48BE120AF163829198CEA367CC444AF,
	PICOActionBasedInputHandler_TestSequence_m2439BAA1E2CF92615F5E7824F9FF9ED691CEDB16,
	PICOActionBasedInputHandler_ShowUsageInstructions_m428E6AB3B279034D9D47FE2E001D0BA4ECB738BB,
	PICOActionBasedInputHandler__ctor_mD0592C42B59D6D4A72DB777A70A4E0AA6D375ABB,
	U3CTestSequenceU3Ed__24__ctor_m63E784D465340FA1C25FD3DD2C216FB9B056DF78,
	U3CTestSequenceU3Ed__24_System_IDisposable_Dispose_m43FD54F0F3A9654496327DB209EC2BDB705495F6,
	U3CTestSequenceU3Ed__24_MoveNext_mD08BA0C261532540EEF62EEDEF7795FF0D742FA5,
	U3CTestSequenceU3Ed__24_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m23690C82448D5379FF16AE261106A9DCF15978CD,
	U3CTestSequenceU3Ed__24_System_Collections_IEnumerator_Reset_m1BB26E9A1E2EBA6C7185E542B721215770A5F551,
	U3CTestSequenceU3Ed__24_System_Collections_IEnumerator_get_Current_m4297DB5CA68EAC52EA07F7F203AA2FF51288D9C1,
	PICOControllerSetup_Start_m5F241870CB9B80BC017272322883503AC49BE051,
	PICOControllerSetup_SetupControllers_m72805A02CEBE60DEAA4D0E07B4C7095A84032425,
	PICOControllerSetup_SetupSingleController_m74AE3179F36E9CC86FDA2E2C89B8AF6E27AEBF06,
	PICOControllerSetup_SetupLineRenderer_m33DC79CAF616908E90EC738D905B1D2BCE1E56F1,
	PICOControllerSetup_CheckControllerStatus_mC841641D357AE21EF8F113B28F95955B2FB04ED7,
	PICOControllerSetup_ListControllerObjects_m4CBE0C35E608A1D638C73488570176E02DBF359F,
	PICOControllerSetup_ForceReconfigure_m132EB69610160E6C96E214AD15D5DE0FDD4EBC58,
	PICOControllerSetup__ctor_m374CC7BC77C813A3447D2169DEA6673EE82A51B4,
	PICODeploymentManager_Awake_m398A9B609C4E8098A44BA316F9B6F9F4892CF687,
	PICODeploymentManager_Start_m513829B8D2BF62E541D3FE0AF705EFCB2F5E488D,
	PICODeploymentManager_ConfigureEnvironment_mC5E0B52F460FB39BAD2CC61A7755B3D764B2BBF5,
	PICODeploymentManager_DetectPlatform_m4BCBE611C15930811EC55D0CA3898BB30BA2AD25,
	PICODeploymentManager_FindComponents_m2AC3FB0DA8784A5D6C521CA5D0B039EB3432E492,
	PICODeploymentManager_ConfigureForPICO_m756046194F9532F301AE2B2855F2C7F73B88F6E8,
	PICODeploymentManager_ConfigureForDevelopment_mCC48CBBFF4897FCFF273D76E47EB9086CE94E527,
	PICODeploymentManager_OptimizeForPICO_m590101ECBEA7FA00F93B945F73D89115837764F1,
	PICODeploymentManager_SetPrivateField_m782293148798ED51D338230F031D468CA3A51006,
	PICODeploymentManager_ValidateConfiguration_mA06D72971650E2CF927865E2D89F9DB4CD379578,
	PICODeploymentManager_LogCurrentConfiguration_m4654CFA8462DDCE883E072EA523387DD7490DB03,
	PICODeploymentManager_SwitchToPICOMode_mB6A30668470129DBAB3854E37345B0191D21710E,
	PICODeploymentManager_SwitchToDevelopmentMode_mE31EA7C035E616FB8C204469B7448EA4AB2B2C6A,
	PICODeploymentManager_get_IsPICODeployment_m499D3B221CB2EBFE1FD38A93A875C6EDA71F9859,
	PICODeploymentManager_GetConfigurationInfo_m991D723DCB734ECFB9CD217B0DA8F099D88B31E7,
	PICODeploymentManager__ctor_m96ABD81A1A0A99CA26934178D14E45362E75D80F,
	PICODirectInputAdapter_Start_m2A5C87BB9F216B129FA843A4734167C8B0724B1A,
	PICODirectInputAdapter_Update_m53D03CB5A4777E9CE93230AE27AF649FFDDF2469,
	PICODirectInputAdapter_InitializeControllers_mDA453B95268607367503AC070B4C58996183E871,
	PICODirectInputAdapter_HandleVRInput_m75DF374F763D76534563DDE63C35617326D5FE06,
	PICODirectInputAdapter_CheckControllerInput_m79F7B68788B0B88991A44D3370E19F65DC208A88,
	PICODirectInputAdapter_HandleKeyboardInput_mA07CEE79E4DB0D3F459A89BA1943A78FCE235C84,
	PICODirectInputAdapter_CanProcessInput_m6423337FAB7AA30B8AABD625FDAE7C99EB135138,
	PICODirectInputAdapter_OnTriggerPressed_m61E64A311BBBEB5E0AC7C10EA2A075E5B0C536EE,
	PICODirectInputAdapter_OnPrimaryButtonPressed_m701C225A85724F67079D76B624A00F6FC864A4F5,
	PICODirectInputAdapter_OnSecondaryButtonPressed_mA08085BAE1FCDE4B30EA445F48427CFA18680E13,
	PICODirectInputAdapter_OnMenuButtonPressed_mB2F3F1874919E309CD97FC304D49BAD939BBD6E3,
	PICODirectInputAdapter_ExecuteFirstPartFunction_mBAB619E77578BCD65F5313823BBBA6B6F211225F,
	PICODirectInputAdapter_ExecuteSecondPartFunction_mE2EE88871A087E61B6B2C184092C38A041901C50,
	PICODirectInputAdapter_ExecuteResetFunction_mB73A2CF2ADD761D62F7A2C8DC96CC422154D1EF5,
	PICODirectInputAdapter_ExecuteToggleDebugFunction_m3FC5101094C5BFDA0929092548C3106AC3CE7831,
	PICODirectInputAdapter_ShowInputMapping_mAD8FBC4112115DC41086B5E0855477251034961D,
	PICODirectInputAdapter_TestControllerStatus_m97FC164A3583F27048BAA60F0BAACF519B304E83,
	PICODirectInputAdapter__ctor_m3E8D206240305C09F89FC996ED7D851C830071D0,
	PICODirectInputHandler_Start_mFA57C80FF4DD17A223A4FAAB857964C93EB9C077,
	PICODirectInputHandler_Update_mB2C5A8308C90BE2312F054D7DEF955D61567DDDC,
	PICODirectInputHandler_HandleInput_m8ECCDF65B9B809B6638155D49D80EE5FE7960465,
	PICODirectInputHandler_HandleVRInput_m6F7326C95D586B11B55273BCE7C7EEFEC63063A4,
	PICODirectInputHandler_TryUnityInputSystem_m9610CC50C9330F7604859757A9E59C9DAC06896E,
	PICODirectInputHandler_TryPICOSDKInput_m1D5069857220BD2873882E5D51E0490E253AA280,
	PICODirectInputHandler_CheckPICOControllerInput_m442CE8CD224B60F1C5D184A2F52EE8536E7CBCCC,
	PICODirectInputHandler_CheckGenericVRInput_mA97072C7EBEF0620A7332BCE6C64DF7A5E1D7C2B,
	PICODirectInputHandler_OnTriggerPressed_m8DDD5FFAB2F3A3EF8B9CE6810540B6636E5343C6,
	PICODirectInputHandler_OnGripPressed_m90F3D8ED65734A08E5260F0827D324FBC0DBB79D,
	PICODirectInputHandler_OnResetPressed_mB2FD40429877E2DD142F46436C4A2DCB85AC8F79,
	PICODirectInputHandler_TestAllInputMethods_mB2FD465BA2EAF2367DB3EE361173FDEA7EA803D7,
	PICODirectInputHandler_DelayedTest_m9613806AB31CF36E449D1BF410D02DEF7830D047,
	PICODirectInputHandler_ShowInputStatus_m21D8C7097304A8AA2EEF0A772FE29CD2FE320394,
	PICODirectInputHandler__ctor_mEB43796546A76A87159B11D9647C0BA00E28BFEA,
	U3CDelayedTestU3Ed__19__ctor_m22D837F7EC054246C6355B1D45C3C2C9B9028024,
	U3CDelayedTestU3Ed__19_System_IDisposable_Dispose_m61C957866B42E0D17C39AA5168B7587B9CCCFF9B,
	U3CDelayedTestU3Ed__19_MoveNext_mFBE1886C895FAC7E907CA4999156AEC87BE92394,
	U3CDelayedTestU3Ed__19_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mA677564398654DD24ED6479643B1A44486DDC193,
	U3CDelayedTestU3Ed__19_System_Collections_IEnumerator_Reset_m65D495376BC721741112BF5908EB1B13577919F0,
	U3CDelayedTestU3Ed__19_System_Collections_IEnumerator_get_Current_mC07BD713285BC3EDD0B897CE3405B2ED4C9B3CC8,
	PICOInputActionAdapter_Start_mFEC27B900794FDBC51E077338A32857BB3C49D58,
	PICOInputActionAdapter_Update_m4B21C7C5439C480AD4201092F4139B8A9D3452BA,
	PICOInputActionAdapter_SetupInputActions_mE70DC8336068BB2FBC092500AFDD4395E3040B42,
	PICOInputActionAdapter_CreateDefaultInputActions_m7482E0CA4BF18FB85A87F47DCE5BC702C8F09841,
	PICOInputActionAdapter_EnableInputAction_mFD90517072CBD0770C7495692B4AFDE394D58937,
	PICOInputActionAdapter_OnTriggerPressed_m1849F3BC4F4CCB77E1103D06A1BFF02648066A24,
	PICOInputActionAdapter_OnPrimaryButtonPressed_m0FCB5C5F95D7D6F39A5FC54B3B6EE9132D0F5890,
	PICOInputActionAdapter_OnSecondaryButtonPressed_m8AB9EBE1E4186CE3B46CC42AD8E14F62D6DEAA6A,
	PICOInputActionAdapter_OnMenuButtonPressed_m319615EA8A0A25FAF97FBD6FCB600CCD01A68EE8,
	PICOInputActionAdapter_HandleKeyboardInput_m5C1FEC4F016F9BE4C662334D83DBC35EFFBD1AF7,
	PICOInputActionAdapter_CanProcessInput_m8C3DDBB9ECE1BA6C4A3BB3994863BACD8EE912B8,
	PICOInputActionAdapter_ExecuteFirstPartFunction_mB4A3F1E28C8AA3A206BF4D65BA0C4D0FC4583508,
	PICOInputActionAdapter_ExecuteSecondPartFunction_m3B67E965A4F601858216A3688F20BAE3D7441BFF,
	PICOInputActionAdapter_ExecuteResetFunction_mB6CF2C0B10B463C9976D1AB0B475D978C7119856,
	PICOInputActionAdapter_ExecuteToggleDebugFunction_m70421F91327638B3B6557C76B017EAC35F948E84,
	PICOInputActionAdapter_ShowInputMapping_mDF347EBB94EE12A360E8768B182C96ED22A38089,
	PICOInputActionAdapter_TestAllFunctions_m92CEF00FF6ED66D2E00A1D1F945532EBF3E573CF,
	PICOInputActionAdapter_TestSequence_m473100F307494B3A880FB273588A413025A449D4,
	PICOInputActionAdapter_OnDestroy_m8D918E4B75748E507C0D18DDC371F1ED155B9BCC,
	PICOInputActionAdapter_DisableInputAction_m6AD74DB2931357C77AC8EB085E36C416F8C8604C,
	PICOInputActionAdapter__ctor_m4CCE4C65359A357DB4BE1593D372CEBC14562723,
	U3CTestSequenceU3Ed__31__ctor_m3BDC0345881C884010DB9F4AB4A77C716A0BF75A,
	U3CTestSequenceU3Ed__31_System_IDisposable_Dispose_m6603F96B83077892E9959F9FE27C292F4C80C426,
	U3CTestSequenceU3Ed__31_MoveNext_mB9F2DDCAD0CBA6E9B97F419623ABE5C2DA40F1B0,
	U3CTestSequenceU3Ed__31_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mF9E5243783320DBDC7C1AD34EB7DEAD500CB9C9B,
	U3CTestSequenceU3Ed__31_System_Collections_IEnumerator_Reset_m17C2E78A6796DA3243E2AD3A3CA92D29E5E7D6C4,
	U3CTestSequenceU3Ed__31_System_Collections_IEnumerator_get_Current_m0FA97FB4F51EC3D5958A3F4751DB285E2BB7EE95,
	PICOInputDebugger_Start_mED0B9109DDF286712F33A5816BBD3847D9248FF3,
	PICOInputDebugger_Update_m71252699CD44C4ECD91C5985457DA373767DDD8D,
	PICOInputDebugger_InitializeControllers_m9EE9976021D71E0487CE3E4FD5BDFDA6ABD218B6,
	PICOInputDebugger_CheckControllerInput_mA88B8ED1A9DB62D1ADA2290D2A14B01997BE5A59,
	PICOInputDebugger_CheckSingleControllerInput_mAD64D79BCBF450D23F16BB6AA41F32EA09724664,
	PICOInputDebugger_CanProcessInput_m14315930B9BD4842600F8FBA96183734E74DF9E6,
	PICOInputDebugger_OnTriggerPressed_m62BF0C3DEFE2D5CEB2165992BA62436541392E6E,
	PICOInputDebugger_OnPrimaryButtonPressed_m6787EA7385B9DBBA01AB4DF2BCB3594C4F8F0870,
	PICOInputDebugger_OnSecondaryButtonPressed_m1FA1F3FDCD22AC5E5FCC0EE2273CF5C74D7E1C52,
	PICOInputDebugger_OnMenuButtonPressed_m22D79F630907E8AFAC94B5C83FBC3A38CE892DE3,
	PICOInputDebugger_ExecuteFirstPartFunction_m8B3F8F75228CA7DB2E46B768A306BD8F7E2AAE64,
	PICOInputDebugger_ExecuteSecondPartFunction_mC4ACE39CFF09C9640EE82CC9D7AD8E18531F24CF,
	PICOInputDebugger_ExecuteResetFunction_m846321D60499460E318444698A61CB8CF00B7053,
	PICOInputDebugger_CreateDebugUI_m4F5B4264B4509FD49DD3517DD4286963D94514E1,
	PICOInputDebugger_CreateDebugText_m6228F6ADE47D963C8705244CDA6F5D6DCCE7BF87,
	PICOInputDebugger_UpdateDebugUI_m9D6DB340C65E3B1AC435C2CB143391D40F46084C,
	PICOInputDebugger_UpdateStatusInfo_m8EE31D24EAECB4E97D1C61522EF9D3AD94760CCC,
	PICOInputDebugger_UpdateFunctionInfo_m1102E26D73645C9A0EFF783CC44F7B9FE796B975,
	PICOInputDebugger_ToggleDebugUI_m7E0A289813700F9028679EE5F8E5C7F0783CC1E5,
	PICOInputDebugger__ctor_m4DF1BD100C5257BF139FFB5E21B2CE39E7EAD3F4,
	PICOVRButtonFix_Start_m863E3D5ED8A30D027933CF2F043753AFCC04E55A,
	PICOVRButtonFix_InitializeVRButtonFix_mE84E183E7A2C421946BB79CA6C845FDB180C0EBC,
	PICOVRButtonFix_FindTargetCanvas_mE672F7057FF83F93D86D68A72DC2CACD3C4A56C1,
	PICOVRButtonFix_SetupEventSystem_mEC0D203EF6A83C49CBCBCCB34622D940A552F7E3,
	PICOVRButtonFix_SetupTrackedDeviceRaycaster_m30B52569843DE9BB416C8CF78E70D058896719C2,
	PICOVRButtonFix_FindAllButtons_m7F8AF0C7963BF9052A72B12ED18E34483AD0CE7C,
	PICOVRButtonFix_SetupButtonClickHandlers_m83ADE23C18D19C2B55E4F3FCE18748A9A416B0A2,
	PICOVRButtonFix_VerifySetup_mF4F5C1E6AD7AC9A5DD53D4E4CB566A88AFD034C2,
	PICOVRButtonFix_TestButtonClicks_m845830A8F2616F95EEFAA0E39FE821D13DFBDF62,
	PICOVRButtonFix_Update_mD5F25898AFCE2083C51B535E1C820EB28175D76B,
	PICOVRButtonFix_CheckRaycastHits_m4D8D382FCB487F00836BF48449912E6888E8EC4C,
	PICOVRButtonFix__ctor_mDBC32389DB6A751F70D9071F413E361206BDDF8E,
	U3CInitializeVRButtonFixU3Ed__9__ctor_mBC83A0BE871EC38F3496C42FA108BA7140BC9F18,
	U3CInitializeVRButtonFixU3Ed__9_System_IDisposable_Dispose_m10516B8928FA30A71EFCA756CE607A7BEA92535A,
	U3CInitializeVRButtonFixU3Ed__9_MoveNext_mBBE50AAC0295DC33DF499A5598D71636E2D3FAA7,
	U3CInitializeVRButtonFixU3Ed__9_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m7E51F56247E552C9CB9DFB00E8C2560C20CEA3F5,
	U3CInitializeVRButtonFixU3Ed__9_System_Collections_IEnumerator_Reset_m1009D198068D05E302196CCE9B19E6F9987BFDDE,
	U3CInitializeVRButtonFixU3Ed__9_System_Collections_IEnumerator_get_Current_mA380C41067C5E93A5BF8E6413B4695D93E1BE5DE,
	U3CU3Ec__DisplayClass14_0__ctor_m3B9616EC977A54C02786F41DE88DBE7A5BE9B64A,
	U3CU3Ec__DisplayClass14_0_U3CSetupButtonClickHandlersU3Eb__0_m99AAD07E456DAEF5C523DD2E920F39E9CC32F052,
	U3CU3Ec__DisplayClass14_0_U3CSetupButtonClickHandlersU3Eb__1_mECE202021E314136C21FE4FD5A50B942EA806D20,
	PICOVRInputAdapter_Start_m871A5B4EFAE0F6D3DF7460A683D070FAE7D3FD17,
	PICOVRInputAdapter_Update_m6BB22EC20ED960A5D818DF991517B25085452F68,
	PICOVRInputAdapter_DetectXRToolkit_mECE5EC00D1EEF0C477E6DC7884640E3402E981CB,
	PICOVRInputAdapter_InitializePICOInput_m9A4DCCE6BDE9CAD0161EDA915C8376B99B1AA324,
	PICOVRInputAdapter_HandlePICOInput_mF3940DD2E6CBE4CAB8899C35BD271933AADF8AB6,
	PICOVRInputAdapter_HandleActionBasedControllerInput_m5D3404D6628BF88659B371101B0F29592BB87B89,
	PICOVRInputAdapter_GetActionButtonState_m1C65E51590AACC2CAE5776CEE4BF33E509E0EAF6,
	PICOVRInputAdapter_OnTriggerPressed_mE549CF40673C509C9D9E9A81D05FCA1ABF6D7C26,
	PICOVRInputAdapter_OnPrimaryButtonPressed_m5B191F23E444682348C83342EE77E5F7733CBED5,
	PICOVRInputAdapter_OnSecondaryButtonPressed_mBE6FE162441EBEF8CBC7258253688BDC42639150,
	PICOVRInputAdapter_OnMenuButtonPressed_m07CD6C45894DC25455589BFB78C4C75BEB70A195,
	PICOVRInputAdapter_HandleFallbackInput_m725E2F706B69D03843CCECD9CACE628846D07366,
	PICOVRInputAdapter_ProvideTactileFeedback_mA106D6E843A8B64A4B8F1A1713D176EADB2F9B00,
	PICOVRInputAdapter_ShowInputMapping_mD80922B53B9A1B8A9923F45FB7350B8F658E45B4,
	PICOVRInputAdapter_TestAllVRFunctions_m422DC00A07F0828D9B4715651B75700B74E40E08,
	PICOVRInputAdapter_TestSequence_m1F1934F4900216A04B1000AB2543D2B8A6A6DE48,
	PICOVRInputAdapter_SetPICOInputEnabled_m3997B8537C9BDB14CE2F8835E6FAABDC20DC34C5,
	PICOVRInputAdapter_OnDestroy_mC342C42272011A4436B1CF27A9119CDB0CC2EF8B,
	PICOVRInputAdapter__ctor_m7A5E3D1662C082337811679E8795E15FB3B0202F,
	U3CTestSequenceU3Ed__30__ctor_m11C7FEDBED6C0F71E70CB6E5D390B4E4EA66474D,
	U3CTestSequenceU3Ed__30_System_IDisposable_Dispose_m7ECB7389819098C0B04C39F19232040DAC259AAB,
	U3CTestSequenceU3Ed__30_MoveNext_mD5EEA90FBCB09F77542CC10B4BD0FEF2C6538336,
	U3CTestSequenceU3Ed__30_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mDAF53EFACFE8B126E6591229FE67ECF5E2C26E95,
	U3CTestSequenceU3Ed__30_System_Collections_IEnumerator_Reset_mB35907C0426BAFAC88BC3CA4695585834F57CBE4,
	U3CTestSequenceU3Ed__30_System_Collections_IEnumerator_get_Current_m7507C329E70FB9E08412B645C57EE1C04F77898F,
	PICOVRInputHandler_Start_m040FD6075E73153DEDD3315F6C3476739C77AFD9,
	PICOVRInputHandler_Update_m3A120BCFDCF1C2B42C985E13048CD32A454A4A39,
	PICOVRInputHandler_InitializeComponents_m53757037F6AB72E5146CC91603B6638C0B51E36B,
	PICOVRInputHandler_LogInitializationStatus_m6FF7C0513BB4188C8CAD136D2A122BC8DD6F0DE5,
	PICOVRInputHandler_HandleInput_m33C53CE04B4F943981EED2BC6762483DBF06689E,
	PICOVRInputHandler_HandleControllerInput_mF6C3E09F22D3860BF14FC25ED30589E8BC2EFC8D,
	PICOVRInputHandler_IsActionPressed_m868FCCE0FDCE4F0F8AEE912FFAEEF0E647915372,
	PICOVRInputHandler_CheckAndHandleButtonPress_m85D2F84C0C71891829283FEFE5F66C6C10ECE1BE,
	PICOVRInputHandler_OnTriggerPressed_m96E981ED765712ED65751D598F61C389EB273E79,
	PICOVRInputHandler_OnPrimaryPressed_m2ABD8130AF22D9A47B8703AFB9408B92ECD1C700,
	PICOVRInputHandler_OnSecondaryPressed_m7B04E81B614BB222F5CCF4E2B0557738C5C968CA,
	PICOVRInputHandler_OnMenuPressed_m717A6F52AF2EA6E53D72094BC205585C347E2F0D,
	PICOVRInputHandler_HandleKeyboardFallback_mE2ACE1AA53696A22C7F7D4BCC972C262C8B54A39,
	PICOVRInputHandler_TestAllFunctions_mC0DFED38901ED358C4359274C59EEA7BDD2CCC2D,
	PICOVRInputHandler_TestSequence_mA7F235A3619845B15F9FB8F24D79627642208F31,
	PICOVRInputHandler_ShowUsageInstructions_m29ED5CFD4C5FDEFA350FF0A57ED239413958FECE,
	PICOVRInputHandler__ctor_m1767D45B151903DF3735AC430E4A2359DE38500F,
	U3CU3Ec__DisplayClass18_0__ctor_m499730C506EDA8F4B0428AD941FDE43D2891CDA5,
	U3CU3Ec__DisplayClass18_0_U3CHandleControllerInputU3Eb__0_m1054D905ED79E90B5887023E8AA4F061806C2420,
	U3CU3Ec__DisplayClass18_0_U3CHandleControllerInputU3Eb__1_mFE5AABC3D2A7CCD66C07B5B5C36F777CF2A12C3D,
	U3CU3Ec__DisplayClass18_0_U3CHandleControllerInputU3Eb__2_m76726ABD87662B0D4ADDF651594731C5AEA3D9DE,
	U3CU3Ec__DisplayClass18_0_U3CHandleControllerInputU3Eb__3_mDDABDD2DC74231131DE3FD5E38C3B1A2E736DA6F,
	U3CTestSequenceU3Ed__27__ctor_mC616EFEDA99D028A468A9825316DB1F69A092121,
	U3CTestSequenceU3Ed__27_System_IDisposable_Dispose_m10362276E247EB7B3A3C9500C6719389CCE74095,
	U3CTestSequenceU3Ed__27_MoveNext_m015A6CFDFEE15927FF40E2D1B4FE175AC71EE316,
	U3CTestSequenceU3Ed__27_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mF3E8793DA39E562916284E74E2166B83401C8AA9,
	U3CTestSequenceU3Ed__27_System_Collections_IEnumerator_Reset_mA10DFBD13E14E0C2EA4D071B3F966AE52F8DBB53,
	U3CTestSequenceU3Ed__27_System_Collections_IEnumerator_get_Current_m65EA1B1AC3F0E1D0A41FECD3106BF52E3A7DA32D,
	SimpleAssemblyDataReceiver_Start_mB44AE545C79D458750DBEE53F78418DC34163055,
	SimpleAssemblyDataReceiver_ReceiveSimpleAssemblyStep_m751135B0629BE3E50283B250FE3E9D6BA623B337,
	SimpleAssemblyDataReceiver_ReceiveAssemblyStepWithScrew_m87F12585CCFD4136F3B9DE8A367A6B051BD831B7,
	SimpleAssemblyDataReceiver_TryAddStepToController_m89E596EAEFA6294F5C076400486EDBB1B423C77B,
	SimpleAssemblyDataReceiver_ClearAllSteps_m817241E6B6F8B229EF35A686EBDD8283CDA50F9D,
	SimpleAssemblyDataReceiver_StartAssemblyAnimation_mD334AECAA1620754F45521B94BCE7D035CD8F42E,
	SimpleAssemblyDataReceiver_GetSystemStatus_mE12B7F1DC369CC97FF75AA09133188F5E2548636,
	SimpleAssemblyDataReceiver_ReceiveAssemblyDataJSON_m67F383928F0E66CA5A0DD51CB8C534C62DA78C8F,
	SimpleAssemblyDataReceiver_ReceiveAssemblyDataArray_m5E561D92167351D9352518004CAA3C5008D735F1,
	SimpleAssemblyDataReceiver_ParseAndReceiveStep_m0372DAFEAEAD2350244888876E8FBA058EEA1B0A,
	SimpleAssemblyDataReceiver_TestSimpleAssembly_mE5581D0D3574FD6F73497D42A0EF5614434C4B7B,
	SimpleAssemblyDataReceiver_TestScrewAssembly_m259B3BD72BE9EBAF72CE540C8D0D482021D92726,
	SimpleAssemblyDataReceiver_TestMultipleAssembly_m6243B7111800784F8D077E28386A821F4744ED16,
	SimpleAssemblyDataReceiver_TestClearSteps_mC8257389399356339D993907E42103EA4D4731DA,
	SimpleAssemblyDataReceiver_TestStartAnimation_m5023F5E501A2FE7524A3FDF4E5B9C11656E91A5B,
	SimpleAssemblyDataReceiver__ctor_mCA7A0DCFEE31FF8AE677EF481BD4960C1B118BF6,
	SimpleExternalDataReceiver_Start_mECA24017984741ED3C4CF74C3C90DFDE417E2417,
	SimpleExternalDataReceiver_ReceiveAssemblyData_m7AF8687829EA89619C310A2227EBE18FB25688CF,
	SimpleExternalDataReceiver_ReceiveSimpleAssemblyStep_mE520344B7689530EB470781513F8A325343BE3D4,
	SimpleExternalDataReceiver_ReceiveAssemblyStepWithFastener_m1984DFABC245B69573A8A4A7E2032CD82CA99F9D,
	SimpleExternalDataReceiver_LoadTestData_m0FC21159F061F075440F99AAF799A2649056FCD6,
	SimpleExternalDataReceiver_CreateSampleAssemblyData_m2108FA0D0233FBA46269FE7F1124445B43A90E3D,
	SimpleExternalDataReceiver_TestSimpleAssemblyStep_m63592CCDBFE2ABD91516C20F4458EE3B5F03C1B2,
	SimpleExternalDataReceiver_TestScrewAssemblyStep_m09B2C7220E1CEA8C5CD5C6C5C394B59291B06166,
	SimpleExternalDataReceiver_ClearAssemblySteps_m05F35556FEC69C68310A9F7240ABA243154F2B88,
	SimpleExternalDataReceiver_IsReadyToReceiveData_m5B34E4D6E78899F1FA1686BE5716E45FDFAB7821,
	SimpleExternalDataReceiver_GetAssemblyStatus_m8BF8F36E42416F776DE765767DD5BBC2A3A7F508,
	SimpleExternalDataReceiver_Update_mABB95B818A82D5CBA18DEFB12504B29CB06F99CB,
	SimpleExternalDataReceiver__ctor_m430FD562BDA65E920723B815DF9F5E4F8992225D,
	SimpleInputTest_Start_m17B5289622AD00616A837197FE2E354B5C9B02AC,
	SimpleInputTest_Update_m40448C64D41AE6A8E8046F9F46C988DEB58EBAA8,
	SimpleInputTest_TestVRInput_mBC8781020E96405DE9100985B0743C50B1444930,
	SimpleInputTest_TestControllerInput_m1F8394486B18C0961AF9318078B5E6AB054ED2B0,
	SimpleInputTest_TestFunction1_m5A15960E8E4BB342A760DD24DE3374A44069A771,
	SimpleInputTest_TestFunction2_m227FE2856E933C6D0E2D092FECD1C509F52EF021,
	SimpleInputTest_ManualTestFunction1_m2DD2FF7422D331117A98D0A45606ECA0056A03C9,
	SimpleInputTest_ManualTestFunction2_m76EA3FE9A4DCA31519FDD9B5BDA1C5322BBF8E28,
	SimpleInputTest_LogDetailedStatus_m25B3CDC2FCFFC40FB1129BA5BE4FFEFBA92ADC1A,
	SimpleInputTest__ctor_mF47AC1968882E5C6AC8E7F58533A4E22C7D5E370,
	Test_Start_mA902842AF55C0A063D71F22B280F28BF0FB01497,
	Test_Update_m68AE52707CAA861E0DBDC29971974A5BF62D82D1,
	Test_OnDrawGizmos_m6481F8828A0F683CCAE82CCB2C6CB8A94F05E6E8,
	Test__ctor_mB84DF4A3888723C395E76E3879FDFB8AA1EFEDCB,
	VRSettingsUI_Start_m30BE28F2E1966ACE15BDAAF844B86A58DBA33285,
	VRSettingsUI_Update_m8B10435EEB34CCB961FB9121A6EA01BCCA9234E4,
	VRSettingsUI_InitializeVRSettingsUI_m8896DB93E4A7759E966DB63FDC865D121450DCF1,
	VRSettingsUI_FindVRCamera_m04A785B2FCCE82D2EFDA6AB65C84D2F73F2895B7,
	VRSettingsUI_SetupUIComponents_mB247929990339F171ED94BC69D5C7F4321ED79C4,
	VRSettingsUI_SetupEventListeners_m63A480EAEBF617337C9398B208CB1D991A98939A,
	VRSettingsUI_UpdateUIPositions_m8AB9678BAC7E439CCE1DD099C5B3D5B537410804,
	VRSettingsUI_UpdateSettingsButtonPosition_m896F7DFEA3F92420DC25667B030AE2D6B5ED659D,
	VRSettingsUI_UpdateSettingsPanelPosition_m9C7457CE93B1AE5B070DC7A7302F3D5AD136A9F7,
	VRSettingsUI_OnSettingsButtonClicked_m950F8E15EBB2DE76B69385B091FFB084439936AF,
	VRSettingsUI_OnCloseButtonClicked_m7B08C422C384B040914C81382389729844FB8D62,
	VRSettingsUI_OpenSettingsPanel_m689F5D2613DA04C5BD94EAB49F04BA4A4B68FF9D,
	VRSettingsUI_CloseSettingsPanel_m03F55AB561F7F44A935034B5067228B44C27B8D5,
	VRSettingsUI_AnimatePanel_m6E567DD5B75D303F82BC840F3ED79E58EC6B3C4B,
	VRSettingsUI_PlaySound_m0B2549385BCCB3FDEE807B2BD08EE4C769E630FC,
	VRSettingsUI_ToggleSettingsPanel_m950B852B6D4BFEEA844FEF2DD4572DE7FCC7E1D5,
	VRSettingsUI_IsSettingsPanelOpen_m6F92428A01E88BF983F26B7EE840D204D3DEA50C,
	VRSettingsUI_OnDestroy_mC01C9302B355759B16E86FCFEA28BBE3664F9E49,
	VRSettingsUI__ctor_m0C165A9F8A137ACB24C59B97C5AE5278269F7650,
	U3CAnimatePanelU3Ed__35__ctor_mB7BCAEA220978C80A503C1E1546E9E133F835019,
	U3CAnimatePanelU3Ed__35_System_IDisposable_Dispose_m23915BDCA288098FB04FDBDA3F8EC90F87482EC0,
	U3CAnimatePanelU3Ed__35_MoveNext_mF54258248E9478A7E9A462A5D15D34E85B964425,
	U3CAnimatePanelU3Ed__35_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m98AD8560F99A98052798B70E31DA293AA3341B93,
	U3CAnimatePanelU3Ed__35_System_Collections_IEnumerator_Reset_mC549C4CA1369482D512B455DED0B30E6FADF4DC7,
	U3CAnimatePanelU3Ed__35_System_Collections_IEnumerator_get_Current_mE4B13B80E0CDE07BABF8CCB559FBDACE35FB51E0,
	VRAssemblyDebugger_Start_m88057DC24FD49105662F4E7E745676D3B7362827,
	VRAssemblyDebugger_Update_mEE80CE3E4E82FFB2C7D1BBD866F3561C6A3C577F,
	VRAssemblyDebugger_FindComponents_m9609506EED6196374D2A6551B8361EF71DBBE20E,
	VRAssemblyDebugger_ValidateSetup_m1200B28D916E56DA6D6A94CD18B92BDA862F7CF5,
	VRAssemblyDebugger_LogSystemStatus_m67504E1AA45FE48557C8AEE8E518A2B406845D26,
	VRAssemblyDebugger_HandleDebugInput_m95DC4AEC014F5083F4C6B3667F9181BB3D68087A,
	VRAssemblyDebugger_TestPositioning_mE4BE9287A0C26F70480A633E0AA84F01AD8EF095,
	VRAssemblyDebugger_TestCameraBasedPositioning_m8805EAF07380827CDF88D5AAF4C1A997FCED85AD,
	VRAssemblyDebugger_GetVRSimulatorCamera_m42AE5CF74DEF231BDA5DFC445E0C78DD7894017F,
	VRAssemblyDebugger_TestOrientation_m8F0A58E8070694652BF410D17CD1ECAC13FFB120,
	VRAssemblyDebugger_ShowCurrentCameraInfo_mACE249A98EA7F9D20B8A794EF3EAFB5A9E85DBBF,
	VRAssemblyDebugger_ShowTargetAssemblyPointInfo_mDA6C48E266723BDE26C51399073099CF693FCAF4,
	VRAssemblyDebugger_TestOnlyOrientationStep_m8B8B5D222D95300345C45D99402858AD873CAD66,
	VRAssemblyDebugger_TestSpecificAssemblyPointOrientation_m5CF4EB0F31A75F6F7B1B4D25A1D628F0C74760B4,
	VRAssemblyDebugger_VerifyAssemblyPointOrientation_mA1EF2FC7FA25374B28C61701E6D87DA824571E65,
	VRAssemblyDebugger_DiagnoseOrientationIssue_mAFFF4A3D6D5A704B1940C563634D21D0A245B0FC,
	VRAssemblyDebugger_ResetPosition_m1DEA543BFBA73DA26F0BAE298036516BB32A3A33,
	VRAssemblyDebugger_ToggleDebugMode_mF30A6483F7D73F8DE00EA69AD570FEE3CA1AC22B,
	VRAssemblyDebugger_ForceInitializePositioner_mA640224DBD231DE04B6D914DF72CF66D2705AA1C,
	VRAssemblyDebugger_GetPrivateField_m25929B441736549ED4A599156FF67034D765AEA4,
	VRAssemblyDebugger_SetRelativePosition_m708C810BBC71BCD7A7CB44F87BC5916D45BD4B4E,
	VRAssemblyDebugger_EditorGUIStyle_m35445EEFA87748810503E4CB9FEE18A444015057,
	VRAssemblyDebugger__ctor_m846C941FB4DC221B0C49AE40AEA08D325BDF0136,
	U3CTestOnlyOrientationStepU3Ed__28__ctor_m9B662B94B759AC3B9F552E18AB7AB013101B1AB1,
	U3CTestOnlyOrientationStepU3Ed__28_System_IDisposable_Dispose_m3E2C3C2CD915EEF1D58B5DB881A5002DD759BF0B,
	U3CTestOnlyOrientationStepU3Ed__28_MoveNext_m78AF374949E36ABCB83706E8894069978470B4B5,
	U3CTestOnlyOrientationStepU3Ed__28_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m44B4CCBEEF7749175E732B612ABBD49F0C0CFEFC,
	U3CTestOnlyOrientationStepU3Ed__28_System_Collections_IEnumerator_Reset_m3590A923984CE409A45AF837D1B56CDCA672A6C8,
	U3CTestOnlyOrientationStepU3Ed__28_System_Collections_IEnumerator_get_Current_m230BEBBF16972C25F2A2FA0D950A3C34AD46C0D5,
	U3CTestSpecificAssemblyPointOrientationU3Ed__29__ctor_m5A6F84491B8F67D391E2BFA2D4B975587423C7DA,
	U3CTestSpecificAssemblyPointOrientationU3Ed__29_System_IDisposable_Dispose_m9F953536DBFB57E85D98A21E9440BEBB7535B17E,
	U3CTestSpecificAssemblyPointOrientationU3Ed__29_MoveNext_m68332CED94D7FA13E233246364AEFF987BB7F35F,
	U3CTestSpecificAssemblyPointOrientationU3Ed__29_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m8D12D60712C0259F9A0F546EA7BD12623ECA146F,
	U3CTestSpecificAssemblyPointOrientationU3Ed__29_System_Collections_IEnumerator_Reset_mF04B89F437DADBE1B555DF45F4471373FE045AFF,
	U3CTestSpecificAssemblyPointOrientationU3Ed__29_System_Collections_IEnumerator_get_Current_m70C8B298768F8D3616C6993EADE553B9C97E9A7D,
	VRAssemblyInputManager_Start_mA071D9867E841360986E8C25E0B0689565C15B51,
	VRAssemblyInputManager_Update_m5F5EFC6D39C57E239CEFA69E578E9789DBEB4A5D,
	VRAssemblyInputManager_HandleUserInput_mD95622AC11C59557AAD9693685AC4E4AF0B9F9B6,
	VRAssemblyInputManager_TriggerViewAdjustment_mCF5B8656FFFF2F4313D81F47D48EF3D824D3AEEA,
	VRAssemblyInputManager_SetInputEnabled_mBAFD23791634AB7C79B70274AC68511F8DB47B56,
	VRAssemblyInputManager_SetAdjustViewKey_m4CD9D688CC342CB065496781B0263907C71D3415,
	VRAssemblyInputManager_IsInputEnabled_mB36B4B78547223AC46C3B29212BBAD8AD8EDFDA6,
	VRAssemblyInputManager_GetAdjustViewKey_m1F671ABA14E0F0F762D8DF3431A60F6ECE65FB0A,
	VRAssemblyInputManager_ShowConfigInfo_mAE157D5244A6CAA8CB37FD237DABCD39A11815F8,
	VRAssemblyInputManager_ManualTriggerViewAdjustment_mBA2B0E3CB91C06DF5386F52DEF8259F0E30B2699,
	VRAssemblyInputManager__ctor_mE112945EA6B078E0A6C7847779FC55D210937E1C,
	VRAssemblyManager_Start_mDA576639DB8A0362297780138D4F3BC36920A994,
	VRAssemblyManager_InitializeVRAssembly_m61B42336F65A0C38B9B418AC8570C935F1DA2467,
	VRAssemblyManager_ValidateComponents_m0F445CC8D29914F78350C91C215AC8A0702AFE15,
	VRAssemblyManager_RegisterEvents_m2955263D52A07D862808B447F67DB13A10D1CC3A,
	VRAssemblyManager_StartVRAssembly_mDD774F0335B0D92CC78ECD48E28FE47DF484BFE2,
	VRAssemblyManager_VRAssemblySequence_m4F60D5FAF4CA6701136808FE51BD120DB956FBCF,
	VRAssemblyManager_OnAssemblyStepStart_m68CEC5F770F53F8920BC948047D761031F8C6D56,
	VRAssemblyManager_HandleVRAssemblyStep_m0230321D5CC43CA35079CDA265EE3A58A81F0DFB,
	VRAssemblyManager_get_IsVRPreparationComplete_mB97875686DCB650689742F75CD81C51FD8346F74,
	VRAssemblyManager_WaitForVRPreparationComplete_mDB066982391FD0FFCF4B8D5740CA3D5A23705A48,
	VRAssemblyManager_OnAssemblyStepEnd_mAB514E9B1D4F5C641513091A2E6EA1B88EE73021,
	VRAssemblyManager_ShouldProvideGuidance_m31AA3A4E040B7D4DDD7282F100EE48617A63248B,
	VRAssemblyManager_CalculateOptimalViewingPosition_m60BD83EDB1B475046C19BB370054F21C9EB514DD,
	VRAssemblyManager_WaitForUserConfirmation_mE0967F620CDABE033D93E2E06F8235D0CF79EE9D,
	VRAssemblyManager_SetVRMode_m569524A5A1C7422D5C9B7186A742C0AD971D1016,
	VRAssemblyManager_SetAutoPositioning_mBE16B86A3539819DDBE0A4C6B7CC7E923714E3F8,
	VRAssemblyManager_SetPreviewEnabled_m74C5E6007111F93F5D1AEAD155144F1A35815495,
	VRAssemblyManager_SetGuidanceEnabled_m8EF37827F65350446E0B3A23699BB276AECF6E1E,
	VRAssemblyManager_EmergencyStop_m81E3F4576F8183D2094D9B11DBFB734B9E2E9614,
	VRAssemblyManager_get_IsVRActive_m7DAF896FB9B21197C36955BD5E67E4700AFD03DB,
	VRAssemblyManager_Update_m7BD02B95DCF357D82EFFB9FC7EE883C46CA96266,
	VRAssemblyManager_OnDestroy_mC7E461C5726252E563A6CB849A40DF1AA8D2C26D,
	VRAssemblyManager__ctor_mB8DFC25C0B600A60B3AE7F237FFAEF2A280163CB,
	U3CVRAssemblySequenceU3Ed__20__ctor_m15C1BA205A65889F3B1AA4687FA21CF3CBDB15E5,
	U3CVRAssemblySequenceU3Ed__20_System_IDisposable_Dispose_m4E450CF9D5A7DAA93754195466037BBA4332AC7A,
	U3CVRAssemblySequenceU3Ed__20_MoveNext_m94504F439833F8C405887E9B22DCA8D74C74F110,
	U3CVRAssemblySequenceU3Ed__20_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m7C9ADD12540525BB4E1E21A8EA0298FA27BDD0EE,
	U3CVRAssemblySequenceU3Ed__20_System_Collections_IEnumerator_Reset_m25E4812F67F15F87525A091CFC2800258B5EC185,
	U3CVRAssemblySequenceU3Ed__20_System_Collections_IEnumerator_get_Current_m37BD81E157F96F2B0D238706E54D60333BA4FF8D,
	U3CHandleVRAssemblyStepU3Ed__22__ctor_m5B8BD705CB5456122031EAC852D509B48AFC7BDC,
	U3CHandleVRAssemblyStepU3Ed__22_System_IDisposable_Dispose_mC252729B34F64E9BE86FD0F29859A4342EEFFFC3,
	U3CHandleVRAssemblyStepU3Ed__22_MoveNext_mC4148A804FBEEDFE908BBDC1A5E0812BC93B3A44,
	U3CHandleVRAssemblyStepU3Ed__22_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m91CB3B7FBE56222FB39BB88518933448D3CE5638,
	U3CHandleVRAssemblyStepU3Ed__22_System_Collections_IEnumerator_Reset_m616ED41E4B560E64832BE9DAC1166B4A548B9A79,
	U3CHandleVRAssemblyStepU3Ed__22_System_Collections_IEnumerator_get_Current_mFAF826A344407DA9BFFA9CFF231756CF4BDF86D6,
	U3CWaitForVRPreparationCompleteU3Ed__25__ctor_mB6081A3F963A624E483D6E2CB20EB43B42D6D259,
	U3CWaitForVRPreparationCompleteU3Ed__25_System_IDisposable_Dispose_m14D28E34281E8A0D815BD510A4BE3EEDC50BD334,
	U3CWaitForVRPreparationCompleteU3Ed__25_MoveNext_m974547382090787B2AC4948000C97135A3D19717,
	U3CWaitForVRPreparationCompleteU3Ed__25_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m87E49B1C17012F82B835A84F695CCE93669C23C9,
	U3CWaitForVRPreparationCompleteU3Ed__25_System_Collections_IEnumerator_Reset_m0DE129C8A70A9F4C2EC4EE4010E1BDE0D735630F,
	U3CWaitForVRPreparationCompleteU3Ed__25_System_Collections_IEnumerator_get_Current_m9AF6C3D510AEDD059A65F4808C80017060DA52A6,
	U3CWaitForUserConfirmationU3Ed__29__ctor_m828B8C111734364C413EEA80DBAE7818B6693AAB,
	U3CWaitForUserConfirmationU3Ed__29_System_IDisposable_Dispose_m8B19E83C5666C051EB8ABA41181E537E72D7852C,
	U3CWaitForUserConfirmationU3Ed__29_MoveNext_m9A99851260094FB73B76C0B38B120393B41E223D,
	U3CWaitForUserConfirmationU3Ed__29_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mE2C104580BEDF91066B06522A1CC39F5FF5DE8B9,
	U3CWaitForUserConfirmationU3Ed__29_System_Collections_IEnumerator_Reset_m20CAE4230AA07AF2942969D9850D2EF181B074DA,
	U3CWaitForUserConfirmationU3Ed__29_System_Collections_IEnumerator_get_Current_m9F68820E433A8DAA48D59E06547A2A4C41734743,
	VRAssemblyOrientationHelper_CalculateOrientationToCamera_mF47564ED969D38B30467970E816E9B3173192493,
	VRAssemblyOrientationHelper_CalculateOptimalOrientationToCamera_m049E4AF65711CD58D2666CFFCCBD2AB9CBAA8642,
	VRAssemblyOrientationHelper_TestAssemblyFaceOrientation_mEC996FE985F0125881DC009D65528AA547AC7695,
	VRAssemblyOrientationHelper_ApplyOrientationToAssemblyRoot_mFB1217FF274605584C302693443A8DE70B8395AD,
	VRAssemblyOrientationHelper__ctor_mE949CDCE7B99F7F2DA680CBF98BE4940A8722DAF,
	VRAssemblyPositioner_Start_mFFF6952551522C27AD0B0E56278CFC8B8941FD26,
	VRAssemblyPositioner_InitializePositioner_m9124E7DB33BAAF7E4A553CB33D0D19AACAFFA47A,
	VRAssemblyPositioner_FindAssemblyRoot_m76F73E3280D092430590A3C5935EA6906082FC54,
	VRAssemblyPositioner_FindCommonParent_mFD993971AE6B55D04A2F1BB88F3BFF82BE71DBF5,
	VRAssemblyPositioner_FindCommonAncestor_m8BF526F0357F659DBA58046175FF095B320458B7,
	VRAssemblyPositioner_CalculateOptimalAssemblyPosition_mCCCA0B80E9FB0D9CBC24BA71FAE44CDC2E9C39C0,
	VRAssemblyPositioner_CalculateOptimalPositionForAssemblyPoint_mF76630F9A037F2F91553F2827D908FA293BDDBEB,
	VRAssemblyPositioner_CalculateOptimalAssemblyRotation_m1B4D78A138191B6394B7C26C3C2D0A7E4FF01FF2,
	VRAssemblyPositioner_CalculateSimpleOrientationToCamera_mB325A81E6266A102E64FD0399CA9E1600FFE81F3,
	VRAssemblyPositioner_AlignMountPointToCameraByRotatingAssemblyRoot_m4C5C316E1F8245C13EC6254EA57E8036216077DB,
	VRAssemblyPositioner_AdjustViewForUser_mC79C505C188EA75655EABB206EF0FA4C2E151118,
	VRAssemblyPositioner_AdjustOrientationForAssemblyStep_m7CB1A2538FBC14BE0B4DAFCF6F0279783B6B0D6F,
	VRAssemblyPositioner_FindAssemblyPartByName_mFC89B480A960886DB31C7EE3AF53E4813A11E33A,
	VRAssemblyPositioner_GetCurrentCamera_mCFCABE3ADCC01A83A7D5AA9293E9268E4383E727,
	VRAssemblyPositioner_CalculateConfigurableOrientationToCamera_mA0A6EE3AFD791D09A5C27FF83B58706DC252B755,
	VRAssemblyPositioner_MoveAssemblyToOptimalPosition_m64DD6F7BC72200DCB2B5D1495437C304CF1BF1FA,
	VRAssemblyPositioner_AdjustOrientationForAssemblyStep_m893501C12F225D2ADF3A2AA5A69285FBB35B833E,
	VRAssemblyPositioner_AdjustOrientationForAssemblyStepSimple_m45A85A5F4AEE0991EED1A32389FA0BC4C9C5C4A5,
	VRAssemblyPositioner_RotateAssemblyToOrientation_m903DDB21A7FB97B74763E9CD47A81045A9985342,
	VRAssemblyPositioner_RestoreOriginalPosition_m229244D2F6DC89EE58101C4E71A089FF5CC5EBAB,
	VRAssemblyPositioner_MoveAssemblyToPosition_m3265AEF75C780D7AF67AF754063FC623CFD9D381,
	VRAssemblyPositioner_OnAssemblyStart_m1CC06174648066EB18079253DAEC0CF93F6CDCE4,
	VRAssemblyPositioner_OnAssemblyStepStart_mB2FDD80F3FF3212DA2D1DAB197AACFAC04C4DC54,
	VRAssemblyPositioner_OnAssemblyEnd_m05C00CAB697658AA0CA5145709B5828DD36B3807,
	VRAssemblyPositioner_OnDrawGizmos_mD45B6543C4D4D7B39E5FB7C31F5FEF7B7D814AFE,
	VRAssemblyPositioner__ctor_mBDA40D1EDDAD06C5DF5C442F6384186127C9F720,
	U3CAlignMountPointToCameraByRotatingAssemblyRootU3Ed__21__ctor_m0D0B0C195FCC2C55AA072ECC3862628B281A9D11,
	U3CAlignMountPointToCameraByRotatingAssemblyRootU3Ed__21_System_IDisposable_Dispose_mD275EFBB76EF555B83760CB9046C83B871785FBB,
	U3CAlignMountPointToCameraByRotatingAssemblyRootU3Ed__21_MoveNext_mC27FF8AD8F88F10E3E52A291E183C5A0CA66D378,
	U3CAlignMountPointToCameraByRotatingAssemblyRootU3Ed__21_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m72870BED84F078D5D9CEF398B90449A8118FB154,
	U3CAlignMountPointToCameraByRotatingAssemblyRootU3Ed__21_System_Collections_IEnumerator_Reset_mBD2FD28D6BB83701D6B287AF9D6FD9EBDB1744FE,
	U3CAlignMountPointToCameraByRotatingAssemblyRootU3Ed__21_System_Collections_IEnumerator_get_Current_m6D3579E96F47F1CA0C3D4FADF6932540CE116777,
	U3CAdjustViewForUserU3Ed__22__ctor_m006678A83626B4118E9B48C8834C283A399CF69A,
	U3CAdjustViewForUserU3Ed__22_System_IDisposable_Dispose_mB593F54BDCD8AC0E1CC9500E2D7D43EE916CCA8E,
	U3CAdjustViewForUserU3Ed__22_MoveNext_mF5D76945924B72DD55643866906B3FAAC8665913,
	U3CAdjustViewForUserU3Ed__22_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m9525B598EB0788482F6E660F0DD6398E62D3A450,
	U3CAdjustViewForUserU3Ed__22_System_Collections_IEnumerator_Reset_m17778E32EB826F2FBC94DD0D4442326D189DACAE,
	U3CAdjustViewForUserU3Ed__22_System_Collections_IEnumerator_get_Current_m1A8EEC0A5344E81D4FB3DABC780690563123AFC5,
	U3CAdjustOrientationForAssemblyStepU3Ed__23__ctor_m75AC51E639A3509D82202FAF8ECCEAC579740E26,
	U3CAdjustOrientationForAssemblyStepU3Ed__23_System_IDisposable_Dispose_mB6CBC1270D197E10FCCB502F48CBCDE46D7EE528,
	U3CAdjustOrientationForAssemblyStepU3Ed__23_MoveNext_m2A0113A7AD49513801B6DC2923BA555882E62C7E,
	U3CAdjustOrientationForAssemblyStepU3Ed__23_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m68E2F9F06421CDDC956139C23A66C6226F60D96A,
	U3CAdjustOrientationForAssemblyStepU3Ed__23_System_Collections_IEnumerator_Reset_m27AF98F6C2E32EA7C3DB046B287D21D4BD916D91,
	U3CAdjustOrientationForAssemblyStepU3Ed__23_System_Collections_IEnumerator_get_Current_mC92DDC079FD5FA257BE79FF918079A209B053A5F,
	U3CMoveAssemblyToOptimalPositionU3Ed__27__ctor_mF2989CE76E89429FECCFD292F46546426A0BD51D,
	U3CMoveAssemblyToOptimalPositionU3Ed__27_System_IDisposable_Dispose_m040CC09EF1C1C55E0A1429C1C40AA6D4ACD18992,
	U3CMoveAssemblyToOptimalPositionU3Ed__27_MoveNext_m5C84A6F53876BCAAB8462568964A8CD5B9959E1B,
	U3CMoveAssemblyToOptimalPositionU3Ed__27_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mC25F303E68D8FD213AD91A8AB6A1664B9F46AC1F,
	U3CMoveAssemblyToOptimalPositionU3Ed__27_System_Collections_IEnumerator_Reset_m65FF3DC73365597E0EF08CB82AB70FE8FBCF9E2E,
	U3CMoveAssemblyToOptimalPositionU3Ed__27_System_Collections_IEnumerator_get_Current_m029D021F3BBF7BAFD5E3F6877BF280E47EE6737D,
	U3CAdjustOrientationForAssemblyStepU3Ed__28__ctor_m478B426DA67564F70769C7A8DCC77DE206C9296B,
	U3CAdjustOrientationForAssemblyStepU3Ed__28_System_IDisposable_Dispose_mCF1920AC9A0761E522D9FFBE517B830A0E6E09EA,
	U3CAdjustOrientationForAssemblyStepU3Ed__28_MoveNext_m7F684594D13601CDB11CD33A337A026CCFCBE4EF,
	U3CAdjustOrientationForAssemblyStepU3Ed__28_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mA6FDFBF40E74D2CF45C6C8AA05D54FE498D0CA7D,
	U3CAdjustOrientationForAssemblyStepU3Ed__28_System_Collections_IEnumerator_Reset_m08D72A9C82C45E47C81AD2A5F18EECE757317490,
	U3CAdjustOrientationForAssemblyStepU3Ed__28_System_Collections_IEnumerator_get_Current_m7D7EC06E9FAFA81FF34F504DD9D2F105C59CCF5F,
	U3CAdjustOrientationForAssemblyStepSimpleU3Ed__29__ctor_mADF6F2936523932BC402AD3721A098844C8419B5,
	U3CAdjustOrientationForAssemblyStepSimpleU3Ed__29_System_IDisposable_Dispose_mAD796BF9E9853F1DC3634FABD1C54A8918968F96,
	U3CAdjustOrientationForAssemblyStepSimpleU3Ed__29_MoveNext_mDC6ACD100243A12AC9AD74433CA7052F8C161322,
	U3CAdjustOrientationForAssemblyStepSimpleU3Ed__29_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m686C03651D73FBCE812B60EBC0AA79BB8C76707B,
	U3CAdjustOrientationForAssemblyStepSimpleU3Ed__29_System_Collections_IEnumerator_Reset_m45A1412C3E752EC244BA907D757D5351F56AB467,
	U3CAdjustOrientationForAssemblyStepSimpleU3Ed__29_System_Collections_IEnumerator_get_Current_m88D91229D28C156D57D2358A3771A1C0DDFEA2DD,
	U3CRotateAssemblyToOrientationU3Ed__30__ctor_m7B8DAFB80E21B1B53DEBFBD384F9BF86AAA0F147,
	U3CRotateAssemblyToOrientationU3Ed__30_System_IDisposable_Dispose_m939C4A22BD5B47EDB386FCB36DB15CA9103F11BF,
	U3CRotateAssemblyToOrientationU3Ed__30_MoveNext_m89840F02335F25724577553BB0F98938DC5396A7,
	U3CRotateAssemblyToOrientationU3Ed__30_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m9957A50D7FFE8FCBF787F150F08B29E8C9A12C3E,
	U3CRotateAssemblyToOrientationU3Ed__30_System_Collections_IEnumerator_Reset_mA3B3F7E80BA89970F06B335F2DEF61DC5382F552,
	U3CRotateAssemblyToOrientationU3Ed__30_System_Collections_IEnumerator_get_Current_mAF40BAC1D918D12C264ED6A163C0D7FE04467025,
	U3CRestoreOriginalPositionU3Ed__31__ctor_m06EFB24057F46B530E96E42095C5E2362D416F07,
	U3CRestoreOriginalPositionU3Ed__31_System_IDisposable_Dispose_m2A69E59EC0674E66CE7BEDA074798713FF27CD37,
	U3CRestoreOriginalPositionU3Ed__31_MoveNext_m80D2FA6C72C39A0A2F83614CCBF460D87D756AF0,
	U3CRestoreOriginalPositionU3Ed__31_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m85C10E5BD35459696CD125F61ABB83EDF4E4FCD4,
	U3CRestoreOriginalPositionU3Ed__31_System_Collections_IEnumerator_Reset_m32B03686DB82B27D976FD318D1F224ED9B619BAA,
	U3CRestoreOriginalPositionU3Ed__31_System_Collections_IEnumerator_get_Current_m84B7DA2C6FBE436E8E986594089D5AF24D864FBB,
	U3CMoveAssemblyToPositionU3Ed__32__ctor_mF47F7D46AF4B998252FC4221BD01E1F5A76D08DE,
	U3CMoveAssemblyToPositionU3Ed__32_System_IDisposable_Dispose_mFAD0EF442D75C85F4B3AB09EC97831B6A2A7A86D,
	U3CMoveAssemblyToPositionU3Ed__32_MoveNext_m92BD3A350F9DBD14408C6194EF811A8929866C41,
	U3CMoveAssemblyToPositionU3Ed__32_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m7ED8174506B440633059D41488C7870D6F75D53D,
	U3CMoveAssemblyToPositionU3Ed__32_System_Collections_IEnumerator_Reset_mE6810E007FA9A72966ABA8F23CC76723DABE127B,
	U3CMoveAssemblyToPositionU3Ed__32_System_Collections_IEnumerator_get_Current_m8E567E0EFA0301F933E85C3639F4E49431E7D0A0,
	VRAssemblyPreview_Start_mED668E216CD1BF3238274F38FB189F86749D67D4,
	VRAssemblyPreview_InitializePreview_mDFF4DA69A92C3C640A66CE0485E05AB370ACF703,
	VRAssemblyPreview_FindVRControllerTransform_mAFFDB44E12C416D7C144012FDDFF63D731B7CA0E,
	VRAssemblyPreview_CreateTransparentPreviewMaterial_mE497510E391F2D45B8B5792CB5CDBEC2F665AAB4,
	VRAssemblyPreview_CreateAssemblyPreview_mD5A7DCD6F4AF1B208E0BF28B8723C3D1FA161067,
	VRAssemblyPreview_CreatePreviewPart_m4CFA31DC394DC9C74857A26BB7A4B0AC5C4803CE,
	VRAssemblyPreview_RemoveUnnecessaryComponents_m563B40629121E9F57F2F9371B41AF992EDFC9260,
	VRAssemblyPreview_ApplyPreviewMaterial_m3384BA6F9D4268A6D2E2F156094CB725B3729724,
	VRAssemblyPreview_CalculatePreviewPosition_m82C1750BB96AB66FB89ABFCE5AB1B9040D9F3061,
	VRAssemblyPreview_StartAutoRotation_m94C67A95F47BAE5E3A0E4592258CBF29F5FE9E7E,
	VRAssemblyPreview_AutoRotatePreview_m36965A4615E1D102B093C8ADF3BC80401625343F,
	VRAssemblyPreview_StopAutoRotation_m997BA0A1A20DF95CD892A377F0BAF4DFCC6AA4D7,
	VRAssemblyPreview_HighlightPart_mED7B531F23C5CA7259EA2981C38FD8BCF70EBE26,
	VRAssemblyPreview_ApplyHighlightMaterial_mF7D0CB782613B0A798605598ACBD2A84D4AFC6D7,
	VRAssemblyPreview_UpdatePreviewLabel_mDDACD5208B82FAA0FB2E138005D6F656C62C7709,
	VRAssemblyPreview_ClearPreview_m4C94B8C33901A3F0ACF93AC9431C3D0C97BC840D,
	VRAssemblyPreview_SetPreviewVisible_m748E67C64D66E4A7E23E38DAE1FC705D6C0937A7,
	VRAssemblyPreview_Update_m08B0D97AC62AB87B51812F77552F9325944AA62B,
	VRAssemblyPreview_CreateStepPreview_m0C8F9ACC6A716360C2C05840DE6C831A88F7107F,
	VRAssemblyPreview_OnAssemblyStepEnd_mEDDD449732FEF79B41FB9C25EC54F56ABEE5BCA9,
	VRAssemblyPreview_OnDestroy_mF713FB4825A4B3722D6293AE530BD67592A58C42,
	VRAssemblyPreview__ctor_m64135A87EB8C83AEEAE0361EDA4D9B23F679ED71,
	U3CAutoRotatePreviewU3Ed__27__ctor_mA2DA3759648FF5E918D549B13DF0350BBCE2697D,
	U3CAutoRotatePreviewU3Ed__27_System_IDisposable_Dispose_m677C9339AED9A4AE57E1E5B145D36F20BE8D224D,
	U3CAutoRotatePreviewU3Ed__27_MoveNext_m1D3C137D39CCB4D977B7264816433D6110B35BA6,
	U3CAutoRotatePreviewU3Ed__27_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m8F92CD0884F8448279C86F50188C9BE659B93638,
	U3CAutoRotatePreviewU3Ed__27_System_Collections_IEnumerator_Reset_m80E579BC4C4F965234CD610773047C40FF719F5B,
	U3CAutoRotatePreviewU3Ed__27_System_Collections_IEnumerator_get_Current_mC019D63C87CE3E229D18D6E48A62D08E6295B529,
	VRAssemblyStepManager_Start_m72D6B27C41F36AFE890F7B37056B3ABE02FB78DF,
	VRAssemblyStepManager_PrepareAssemblyStep_mC750835B08545AACBDA36F421C977E18BAAD81B3,
	VRAssemblyStepManager_PrepareAssemblyStep_m28CA76769B5FF321AC452C60258EF6280381AD09,
	VRAssemblyStepManager_SetAutoAdjustOrientation_m1C11B0B943C64F84C28303644EB74763F19EB50D,
	VRAssemblyStepManager_SetDefaultOrientationAxis_mCAA7E4A6207F9682DE1C53B60B6CD085A602DDC8,
	VRAssemblyStepManager_SetDelayBeforeAnimation_m35708D0C4A5C72490B86021A7A3D43CD8A9C2ED2,
	VRAssemblyStepManager_GetCurrentConfig_m441D5C2FFB1F9895ECC368223B9A20EB199CF5B4,
	VRAssemblyStepManager_ShowConfigInfo_m1DA49B5FF6DAAF830F30F97ADB163D06F47BFA53,
	VRAssemblyStepManager_TestPrepareAssemblyStep_m7A7F21A20B8C077A72E132692860D1F721C252BA,
	VRAssemblyStepManager__ctor_m1653B6CD8E821AE2C032729D4A23943C122C08D6,
	U3CPrepareAssemblyStepU3Ed__6__ctor_mE7075AE756C42E03383ABE6827CB451A2D02007E,
	U3CPrepareAssemblyStepU3Ed__6_System_IDisposable_Dispose_m02AC0D6BD3E36876681CE177F5F02859A0965580,
	U3CPrepareAssemblyStepU3Ed__6_MoveNext_m39E448C266CCAEC4CA68E7F237C5BE944CC5D689,
	U3CPrepareAssemblyStepU3Ed__6_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mC153B0CD5B76ABE23D9B62949080B20BF56F057A,
	U3CPrepareAssemblyStepU3Ed__6_System_Collections_IEnumerator_Reset_m982FF1DAFB00EAC4B78A875EB23D6EF877AF136A,
	U3CPrepareAssemblyStepU3Ed__6_System_Collections_IEnumerator_get_Current_mE2B078D3C6BF8242FF514A3A11A69C6B77DA7044,
	U3CPrepareAssemblyStepU3Ed__7__ctor_m4CA782BAFF0957AC85A3C7BE7DD61D4DCDC43B1C,
	U3CPrepareAssemblyStepU3Ed__7_System_IDisposable_Dispose_mC4B289C29A307D5B6CB38523CED148B3B53D0929,
	U3CPrepareAssemblyStepU3Ed__7_MoveNext_m12D236973EB066E62EDC93CCF70130E0D2F6A039,
	U3CPrepareAssemblyStepU3Ed__7_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m28B749622E1767C8347A58468F1438DD18C21ED8,
	U3CPrepareAssemblyStepU3Ed__7_System_Collections_IEnumerator_Reset_mD1C68AF192BFAEF82C17C4654A2C796FEE9207FF,
	U3CPrepareAssemblyStepU3Ed__7_System_Collections_IEnumerator_get_Current_m688E40F03DE06B7464051366E169A2447EC14E95,
	VRAssemblyUIIntegrator_Start_m2858EE5A73A57506632DB9C52006A563EAD5C587,
	VRAssemblyUIIntegrator_OnDestroy_mA2A9C954FDC1B1DB493A3C66E3745C7D7E449EA8,
	VRAssemblyUIIntegrator_InitializeVRUI_m58B2BC1016D85CFFEB615525DA485053019052F6,
	VRAssemblyUIIntegrator_FindRequiredComponents_m61BB39388E6109BC46E9BE4E2894F55EA65E2F51,
	VRAssemblyUIIntegrator_ValidateComponents_m48917C090134589A8FF18ECD6007AF63879EBFDC,
	VRAssemblyUIIntegrator_ConfigureVRMode_mC85A5412619C8CAF67618BF54EBD11D9995E37A3,
	VRAssemblyUIIntegrator_SetupEventListeners_m1BB80FD0277F914B0C3470CCA2B078CAE5F48F9B,
	VRAssemblyUIIntegrator_ConfigureUIPanels_mBD95B6D6EF0F5D63DFED14659B6E90BE9E963DA7,
	VRAssemblyUIIntegrator_UpdateVRModeDisplay_mA60ACCD40821975FE3A0DF7DB3EAAB246813D08A,
	VRAssemblyUIIntegrator_UpdateStatusCoroutine_m156D938C6424126227D22A74952B553C71507B35,
	VRAssemblyUIIntegrator_UpdateStatusDisplay_m808206FFF757FEB6F1D44914E35C09AE4DCF4643,
	VRAssemblyUIIntegrator_OnVRPartSelected_mEC74F2DE2C749B1AD6B1774364E106921A4F4E3F,
	VRAssemblyUIIntegrator_OnVRNextStepRequested_mA61225F282739DB1AD76065087C7A71519BACF99,
	VRAssemblyUIIntegrator_OnVRResetRequested_m00066F50130A0DD3A756316EC1D9BCAE9942940B,
	VRAssemblyUIIntegrator_OnVRReplayRequested_m1332ECBF324F73B8669EDAD40D25A560F0FCF412,
	VRAssemblyUIIntegrator_OnVRRepositionButtonClicked_mA043FC7122548A1DCCBB500B07BC72580E3DCA83,
	VRAssemblyUIIntegrator_OnVRResetViewButtonClicked_m057772BDB12E14E096914CE0F5BDDA316165856F,
	VRAssemblyUIIntegrator_OnVRFollowModeToggleChanged_mEB6E4326B62570B6990BFC198130639B488641DB,
	VRAssemblyUIIntegrator_OnVRUIDistanceSliderChanged_mA032ED9671D87F112C99D50F6E085C0342DDBB3E,
	VRAssemblyUIIntegrator_OnVRUIScaleSliderChanged_m9A434A2A174CFDF4725ABD14816983B5A2AC95D5,
	VRAssemblyUIIntegrator_SetVRMode_mA80559E71BAB9B017C68E8AD512E389F5ACDF807,
	VRAssemblyUIIntegrator_SetDebugMode_mA11F154628D379365A26EF29768245228CF9CC49,
	VRAssemblyUIIntegrator_IsVRModeActive_m1019A7EFE2C9C8F796DF8D5503363B9B3E3A8309,
	VRAssemblyUIIntegrator_IsInitialized_m077C708DBA137957916F719F21E2782D95999576,
	VRAssemblyUIIntegrator__ctor_m84002463FA3CA1230ED8E5597D5D4B14AB47E3DA,
	U3CUpdateStatusCoroutineU3Ed__32__ctor_m18A39C340BD15FAF604FA4613F6C3249682A812F,
	U3CUpdateStatusCoroutineU3Ed__32_System_IDisposable_Dispose_m68233148CE7B6B381FA20B3E87983ADE8392254F,
	U3CUpdateStatusCoroutineU3Ed__32_MoveNext_m4AE101EE3A5E86958838EECD951C267730C7E332,
	U3CUpdateStatusCoroutineU3Ed__32_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mF137B78CDEE29643F99FC0D9EBC2DC044030F7C8,
	U3CUpdateStatusCoroutineU3Ed__32_System_Collections_IEnumerator_Reset_m93D76977FBB483D15EA0AE1FA782D32A7C10ED52,
	U3CUpdateStatusCoroutineU3Ed__32_System_Collections_IEnumerator_get_Current_mCA2982354EB2AC4F43D03904F7C898ADCEE8DE90,
	VRCameraDataRecorder_Start_mF24923C2AD9CA96029AEC0FF34B53148968582D2,
	VRCameraDataRecorder_Update_m6DE26A04BDC3BE933264C665338586257D96D4A9,
	VRCameraDataRecorder_InitializeRecorder_m2298020648653D6862C38B603ACAB9FBA7AED0B9,
	VRCameraDataRecorder_GetUsernameFromSceneManager_m161DB94254B61448572C6FBF5C15C86284DC45AB,
	VRCameraDataRecorder_SetUsername_mC48C8A05A3D31A9A38BCDA6F54200DB978E96231,
	VRCameraDataRecorder_GetCurrentUsername_mF2A5EF75D463578E11367D602F8E4C57290DDAD6,
	VRCameraDataRecorder_FindVRCamera_mDA1D490BB45959B9D44C708FBE9210C81C2DE313,
	VRCameraDataRecorder_EnsureOutputDirectory_m63CBDA7B5C46E800F8DEC12AEFA387401ED5DB02,
	VRCameraDataRecorder_GetOutputPath_mCFC5D0FAB6276C8C328299A2CFC4007CFA83002E,
	VRCameraDataRecorder_GetAndroidOptimizedPath_mE90C23B679113ADF43242366B2849EB2328AADD4,
	VRCameraDataRecorder_GetExternalStoragePath_mF191C0C4740224B30FA66131605A4D6A60D780C5,
	VRCameraDataRecorder_LogControlInstructions_mDD06BF25681A1DB0CE83DB45D5C0B1043650D638,
	VRCameraDataRecorder_HandleInput_m4B3D8DC40CBBE0854AB1FCFD26926A519D7AC479,
	VRCameraDataRecorder_UpdateDebugDisplay_m5A21C45C3DE4D877227DBAA2B29BF389175D5D38,
	VRCameraDataRecorder_StartRecording_m13B219BC52D784938946886FFEF5194AE2B11AB0,
	VRCameraDataRecorder_StopRecording_m955D6E3137D2C5C8B3D1D7979A3C98D1078C7DCE,
	VRCameraDataRecorder_RecordingLoop_m81D6CE2D669993A9659DBB07A89575AD0A67A732,
	VRCameraDataRecorder_RecordCurrentCameraData_mCF4E911DAF09ED423DE93315D1F0453E48636676,
	VRCameraDataRecorder_GetDeviceInfo_m43FC6CFD4BA80C7A3772363598EE7E2750110620,
	VRCameraDataRecorder_SaveDataToJson_mE2E6CFF367E73C9988490DD5715A780DBD3D4D60,
	VRCameraDataRecorder_CreateJsonData_mBF1B146B906B5C0C24AA8A540C638475A2777CAA,
	VRCameraDataRecorder_LogDataSummary_m9FCC77B7E3363C677D7E2F7B775076814ED8CAEB,
	VRCameraDataRecorder_LogFileAccessInstructions_mB954EDFA17A5DF61F97CF6FB394AE841E718F22F,
	VRCameraDataRecorder_ClearRecordedData_mDD7B2FE51B33B8EC3426AEE167E373B9D4584CDC,
	VRCameraDataRecorder_IsRecording_m394D9D6532AF68F5252EF9AFE797A68945A8CE04,
	VRCameraDataRecorder_GetRecordCount_mFFF238F9E2AAFA292BB40C216C025087CD786A7C,
	VRCameraDataRecorder_RecordOnce_m7B393D5F3E5A59566D6D29A7C1C4FD5FB5E260A5,
	VRCameraDataRecorder_OnGUI_mB8DE8B381909D6A5503F8792B9268068963464E0,
	VRCameraDataRecorder__ctor_m79DE51ED2F241690ED22F45BDBD8F44AB13134AA,
	CameraDataRecord__ctor_m22A26133AE0CB1C1615C874F9FCFA1B6995CD7BB,
	Vector3Data__ctor_mD2FBC60CD38FB04CD858E9B024F474C903BF91AB,
	QuaternionData__ctor_mC73DC8A138E357ECBA52DC7F53C36C676D739C74,
	Matrix4x4Data__ctor_mC02B0B3875BE7ABF29596859872FE66806101648,
	U3CRecordingLoopU3Ed__50__ctor_m12EC069DD13FF4A1650BC0F2E15F95A41DAE8349,
	U3CRecordingLoopU3Ed__50_System_IDisposable_Dispose_m2F8B3C312B3FE201EAFDCA78FDB4D2C844EFF7D4,
	U3CRecordingLoopU3Ed__50_MoveNext_mEC03D266D2EFED3FA11876347C6CD8B879C9081C,
	U3CRecordingLoopU3Ed__50_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m653DC0081E985BE8CDCD874D0F4A25AEBFDF6429,
	U3CRecordingLoopU3Ed__50_System_Collections_IEnumerator_Reset_mA4F9CCA4AA286FA56085136117DB34E56FDB032A,
	U3CRecordingLoopU3Ed__50_System_Collections_IEnumerator_get_Current_m468FB1568AD09BB117168DEBF37DDA2F1F5BB9A6,
	VRCameraDataRecorderExample_Start_mEE43A6AD7D6FFA495DAA9FF429255ACDA1381C6C,
	VRCameraDataRecorderExample_Update_mA951608F0024802933EADE8C26D1605643B5188F,
	VRCameraDataRecorderExample_CreateUI_m92345E8D0FA664C40EFFCF2C1F56B77B66AB26B2,
	VRCameraDataRecorderExample_CreateLabel_mD622842267EF1DA13AF9C04AFEA11071D4169611,
	VRCameraDataRecorderExample_CreateButton_m640BBFEB0A767262E5BCFA6240FADA282BAA1B3C,
	VRCameraDataRecorderExample_CreateSlider_mBD99CC6FAC7F0EEBE6D96FCF4EEC563217F1A19D,
	VRCameraDataRecorderExample_SetupButtonEvents_m4296BD05665C09FB4B5CE36E18F1D410EA8E5187,
	VRCameraDataRecorderExample_UpdateUI_m4649B36DE06E8950BE7B9BC11B774AA9BE50A061,
	VRCameraDataRecorderExample_OnIntervalChanged_m4DF2786A5D02845650D367958286E254F78ED14B,
	VRCameraDataRecorderExample__ctor_m4BB78F56199A6736A8AA561776746D78CEEADB0E,
	VRCameraDataRecorderExample_U3CSetupButtonEventsU3Eb__17_0_mBB4716351AA0A9E88B205FCFBDEAA408776D8C41,
	VRCameraDataRecorderExample_U3CSetupButtonEventsU3Eb__17_1_m16D21A6A368940B7D80E62FB7F36F2F24A386C17,
	VRCameraDataRecorderExample_U3CSetupButtonEventsU3Eb__17_2_mBA7CE0B6ED68AEB12E6D1DD8764BB988BDC116D2,
	VRCameraDataRecorderExample_U3CSetupButtonEventsU3Eb__17_3_m19BD054CACC515D3E6D706267BCD1E67CEAD2AA1,
	VRInputManager_add_OnPartSelected_m5C3070EA0886F30681B28F729759E44945503631,
	VRInputManager_remove_OnPartSelected_mE5B70AD10CA05140511B584946DAFFF157DCD00D,
	VRInputManager_add_OnNextStepRequested_m2AED815A84BB90AED60FA2ADBDB9BE01850C5128,
	VRInputManager_remove_OnNextStepRequested_mDC65B60380B65586D84B0AE6486F1D699B3CCA4D,
	VRInputManager_add_OnResetRequested_m1C1B61023DB588C405AB2FDE0C8D89E0358BFB36,
	VRInputManager_remove_OnResetRequested_mF0F568735A599F592A11B3E3F8BA09AD20DFE065,
	VRInputManager_add_OnReplayRequested_m35F9442BEBC3708327BB67E82AC1AD933A6FBC44,
	VRInputManager_remove_OnReplayRequested_mC7C89A4ED9CEB8D3AAE425FE22981108BFAC25FB,
	VRInputManager_Start_mEDCE09DC068C2A7676C78F6BC85EC184FA2F8CBA,
	VRInputManager_Update_m7292786F9775C1AD6BE6BFFEADDE8C0779FE6607,
	VRInputManager_InitializeVRInput_m014D294AD01E88C13A296A9320ACB776A9DC9344,
	VRInputManager_HandleVRInput_m2945939741DC28FDAC21E86422F0B328B023B04C,
	VRInputManager_HandleFallbackInput_m78EE683656B46278F07043FF6E01BE34654F7C7D,
	VRInputManager_HandleControllerInput_m9A3A2BC6C842ED3485AE7F633C8A8E78C25B190B,
	VRInputManager_HandlePartSelection_m31C91C31BE9209E526B636D4994BDB56852DA231,
	VRInputManager_SelectPart_m89687CF7C30B69579DB6ABB6E87502C2F5DC3A84,
	VRInputManager_HandleMenuInput_mB767C0F4AE4A0A081A0052ED5A3FCDCE13CFC4AE,
	VRInputManager_TriggerHapticFeedback_m914B5A1B27AD7FF6AF0C69D147C977C5726379DD,
	VRInputManager_GetSelectedPart_mBC791B9E65C1B02EEDE52A970D2F851CFE3DF8D9,
	VRInputManager_GetActiveInteractor_m881063E8E4974B5F28149A27A909EB288536E84E,
	VRInputManager_SetInteractionLayerMask_m40C012C138859D2A930F2F5F2E07F89E110A5FB3,
	VRInputManager_SetHapticFeedback_m84F62D86924CB54B7922FE04A2494933842313C0,
	VRInputManager_TriggerNextStep_m9D4087D1FAEDD1B31D3FDFF307777D43E3D32B6A,
	VRInputManager_TriggerReset_m6078607B188F8C35D0D12E816ED32BA5784C4491,
	VRInputManager_TriggerReplay_m0E79D4ECB60ACD5797AA0702C8E7215EFFB8E19D,
	VRInputManager_OnDestroy_m83E19ABE0AEB47324FE2F3526117192E9ADEBC1E,
	VRInputManager__ctor_m1A0FBE8ECE1AEDE907F35442F5634CBAD5A9A546,
	VRSceneManager_get_Instance_m118157146B4BE9795183E6C81BBDBBE9D5DB79E9,
	VRSceneManager_Awake_m23BE811D09BD7942CFFCC61D5804A7639CEC83F7,
	VRSceneManager_InitializeSceneManager_m0E5D51FADF3206FE1C88118C5075B58FE0DDE8C5,
	VRSceneManager_InitializeSceneList_mEE86EA0D4BE3326E5E5A64948543161DD7D67FAC,
	VRSceneManager_CreateFadeCanvas_mEA3B2E07FAF72CB5233D710FAF6641DAF454E010,
	VRSceneManager_LoadScene_m6A7FE983E0235F39127036CC0F9EE80E2BE034B9,
	VRSceneManager_LoadSceneCoroutine_m9A025797332D8CBEC8E5114F6DC4CBFAC711FE82,
	VRSceneManager_IsSceneValid_m41A68FD4C62EB0733A7A709D58F2938B77A6474B,
	VRSceneManager_FadeOut_m8665058FB9990D64344328DADB4C957E8130806D,
	VRSceneManager_FadeIn_m2522426C2D28DF9005BC65A93DA6B2FE13F71A31,
	VRSceneManager_PlaySound_m1A214E7B0D35E2EFB6DFADCF83770029E9E093D9,
	VRSceneManager_SetSceneData_m949C377A36DC622AE27ECF9E0D9782B57AEE3458,
	NULL,
	VRSceneManager_GetSceneInfo_mA666C5990B054CD86C278616A27AA762B33B0F73,
	VRSceneManager_GetAvailableScenes_mF3A7C8E6767A04528FB68B7E1E30B1910592E26B,
	VRSceneManager_ReloadCurrentScene_m70D6F41B4BFD8686C8686E53C89991D5873B386F,
	VRSceneManager_QuitApplication_mA4D25E6C6EFA5C932FFE2C568090FFF8F97DCE24,
	VRSceneManager_IsTransitioning_mAADF4F2517D845B8C490F55090963D4F9D553406,
	VRSceneManager__ctor_mF88F82349A1608FC6EA485777D9E977600E9D2AC,
	SceneInfo__ctor_m347D14C60B440061693A6F359F277666AD6E6DB1,
	U3CLoadSceneCoroutineU3Ed__30__ctor_mE2AB13257713E200833201192B8ED7A6F0DF31E5,
	U3CLoadSceneCoroutineU3Ed__30_System_IDisposable_Dispose_m63081A76DC7EA6B1C534C054CFF05F9341F7901B,
	U3CLoadSceneCoroutineU3Ed__30_MoveNext_m83F2172F53BFC5631F3DF080B7F32F94A549F131,
	U3CLoadSceneCoroutineU3Ed__30_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m964ECD1D1FB832F49E7A8886CA24050E31194789,
	U3CLoadSceneCoroutineU3Ed__30_System_Collections_IEnumerator_Reset_mE7DFEC452A4A585A6343A16A0EC896879B55B8F3,
	U3CLoadSceneCoroutineU3Ed__30_System_Collections_IEnumerator_get_Current_m538A409A0E3EEDEE1232F8BEF260797BA5C241F9,
	U3CFadeOutU3Ed__32__ctor_m2461220C7BEEC939D00D86F37369F693C18BB860,
	U3CFadeOutU3Ed__32_System_IDisposable_Dispose_m30E2A09F3983DD268365E9FB75A6106E72F92FE9,
	U3CFadeOutU3Ed__32_MoveNext_mFB345B2962DFE6F79792548EC451AF0D426C66FE,
	U3CFadeOutU3Ed__32_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m3F25FDD024FE371828928F7606B379E4A9D3A89F,
	U3CFadeOutU3Ed__32_System_Collections_IEnumerator_Reset_m0BE4EEBA7B18A6A5C485751FC5CFBCD6CE59E8F6,
	U3CFadeOutU3Ed__32_System_Collections_IEnumerator_get_Current_m9E9E7B082756DDA0647653D8D8B2170986EBAD1A,
	U3CFadeInU3Ed__33__ctor_mA055E33DC493B3C9A5D4BAB3C113D53B226EA066,
	U3CFadeInU3Ed__33_System_IDisposable_Dispose_m43B47C9123149A7BBF3A39B24D105DEC1BBF1DD1,
	U3CFadeInU3Ed__33_MoveNext_mBFB2CC9F1D62AF9EACD3018B3009957A46E4F4CB,
	U3CFadeInU3Ed__33_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m72AF3D9CDB5AF9FA225531D4097530F5C663AE7C,
	U3CFadeInU3Ed__33_System_Collections_IEnumerator_Reset_mE39AEF2D791156BC29F524D9F38F6660B7088CB6,
	U3CFadeInU3Ed__33_System_Collections_IEnumerator_get_Current_m1676468DBFCD000E564B2652AE03AF484760FD0D,
	U3CU3Ec__DisplayClass37_0__ctor_m27519FE6A99A84898DEEBADCAE0431BF7324C797,
	U3CU3Ec__DisplayClass37_0_U3CGetSceneInfoU3Eb__0_m09B4BE3FE2109882D2D0FAE06E664BF316B5A094,
	VRSimulator_Start_mD3E50DCE68A0531D10D9B0890923C8AD19E2E42C,
	VRSimulator_Update_m9327341FF575FC0397A7F26DEFC504F1461B811C,
	VRSimulator_InitializeVRSimulator_m08012E382E2E65F7682B95B68FE09F3962808B6D,
	VRSimulator_CreateControllerSimulators_m9515B3DF2549CD6DDAE872426520560CFA1CEB72,
	VRSimulator_CreateControllerVisual_mBAC43F3090A61B7A16382DE351F9D71F0D24A0AC,
	VRSimulator_HandleVRSimulation_mD04E9283442A2697F29376F31694486BF0EB0CCB,
	VRSimulator_HandleHeadMovement_m89D0144BFCC732DFD5F8B431B5E3F405D888BAC6,
	VRSimulator_HandleHeadRotation_m0BAB512A2F654EC70E7E3CCC1E1AA85EC1FC8386,
	VRSimulator_HandleControllerSimulation_m491E50B6FA22C44B18B5B46C8E54DF3D364402D7,
	VRSimulator_ClampToVRBounds_m346D4C4FAFE0D69D220BC73482F7A0A41850FE21,
	VRSimulator_GetVRCamera_m3DFB20A1FE02C03F6739D3946B00529BBDE6B986,
	VRSimulator_GetControllerTransform_m1C1EA8393D526442039540E5A31909D6F980738B,
	VRSimulator_ResetVRPosition_m62A6C813FFAAF87F3CE95C74B625011322F2B72A,
	VRSimulator_ToggleVRSimulation_mE3CB63194F7580CF49D2D50207B6D93767B2F87D,
	VRSimulator_SetUserHeight_m5EFF0E65925D0C9D487C436847E13B11075A9AF2,
	VRSimulator_OnDrawGizmos_m68FA100330BD0AD428DCBC8DF436445AAC857061,
	VRSimulator_OnGUI_m7DD4CA686C0C1D92F4ADD9CFAB89EF3DF332486D,
	VRSimulator_EditorGUIStyle_m678D64F4A00ABC95B7E5EC7074CCD8FF99135800,
	VRSimulator__ctor_m7D11F1D5F20D30258E8D21FC8DC28D626C180C31,
	VRSystemDebugger_Start_mF4D9B8DF58E6FD69789E8744925FE6ED5D5F5CA5,
	VRSystemDebugger_Update_m6FC02EC2526241D70C8CDD5450C225273B55CAFF,
	VRSystemDebugger_InitializeDebugger_m00E152AC644E455ED18EB8DB4775542780A1EDBB,
	VRSystemDebugger_CheckVRSystemStatus_m1DA318C9E0EE0C15FF2EF29267550B7ECD8AA38E,
	VRSystemDebugger_TestPositionAdjustment_mFC5A3F99CFF09B56C21BEBBA3628982E72CE988E,
	VRSystemDebugger_TestPreviewFunction_mD94D3AB447BE71F612E62305D67F31E55F28697C,
	VRSystemDebugger_TestGuidanceFunction_m4AB64ED31A52BF0BD75AD70E226A915951FE55EC,
	VRSystemDebugger_ResetVRFunctions_m1D0D51958DBE77D2F4AC514BE83FCEF6F771E8EA,
	VRSystemDebugger_ToggleDebugMode_m3A95E2806E7651F21F95183F1C151B0465F6DC07,
	VRSystemDebugger_UpdateDebugInfo_m4C02BAD48D1BB8392EB8760B9B95FF82FF8AE3C6,
	VRSystemDebugger_UpdateStatusText_m6A59DD8A2FB1777300016A3ACDF79E8C237B45F4,
	VRSystemDebugger_CreateDebugUI_m0892B8F0361D6914BCF045FBACB4D6CEE94969BA,
	VRSystemDebugger_CreateDebugButton_mD9CD27AF600971B2A1605A75897024DDA83B7E3B,
	VRSystemDebugger_ShowHelp_m51B9B9CF53B8D9C5477FC883F2F8E3EAA211F581,
	VRSystemDebugger_OnGUI_m6406E1978F3D72C836069F62E7483C38D821E569,
	VRSystemDebugger__ctor_m13E74A955BFAC048E5CF671518C3BAD1287A56F1,
	U3CU3Ec__DisplayClass28_0__ctor_mF171D5F5DC51408281799B284EFE62F2752CA5B2,
	U3CU3Ec__DisplayClass28_0_U3CCreateDebugButtonU3Eb__0_mBBC5C967B55B62451B75696B24EA7C15F61D6F82,
	VRUIInteractor_Start_m5DDB15AD5C89152807A4626FC0DC0E05A0854E87,
	VRUIInteractor_Update_m4238876624D3FC1E1E289106CFA51CAE4B30E123,
	VRUIInteractor_InitializeInteractor_m7F9538DB3D178313DC15DBC5447BFB47AF86B9A9,
	VRUIInteractor_HandleVRUIInteraction_m40AB0E5072B873763EF688177E65AEBB46A8CDB2,
	VRUIInteractor_HandleUIRaycastHit_mCBEFD5F375E9A6326A32A1DCB517AC59E4AAB0BB,
	VRUIInteractor_HandleControllerInput_m226F17E5C3871C815DE8B548CFD178106206D7CD,
	VRUIInteractor_SetHoverState_m40C5E00F14F42868B3556551BE9732550995DD21,
	VRUIInteractor_ClearHoverState_m756EBD48FD4F8E89A5B3A2378B9D8CF37768DB0A,
	VRUIInteractor_HandleUIClick_mA06E584725B92F2152EFF1A94B981FB1DDFEAC86,
	VRUIInteractor_ClearPressState_m70CFDB6772C0E24F2A84B1612DDC640509C292C9,
	VRUIInteractor_ExecuteUIClick_m81DA429F8B06C346453C48EE77B5ACCE0709D986,
	VRUIInteractor_GetOrCreateElementState_m73C764698601C1B25B0C3C3CD699EC5032F6C353,
	VRUIInteractor_PlaySound_mF359A1D99C91A7E1C868BE7871510CE7F03DF3D8,
	VRUIInteractor_TriggerHapticFeedback_m6BEE684DEFDA0859FC4F0E5ED510BA8F5C011B2F,
	VRUIInteractor_OnDestroy_mB0461015BD2B85F2DC34241E74256DA5DF3BDB5B,
	VRUIInteractor__ctor_m0A50D0BDB266E08F7F1BF5F4D7544BAF0DD30C1E,
	UIElementState__ctor_mFDCC2A1F061B62DD6C4E8451A81453D387CDB360,
	VRUILayoutOptimizer_Start_m153DB2196F565DDD58E7FF507B6E664B2EA27284,
	VRUILayoutOptimizer_OptimizeAllUIElements_mE7A302EADE061B79F63669A291C7191AB2087E52,
	VRUILayoutOptimizer_OptimizeTexts_mEFEA6AACA05D72A1285DCA9CDB67CE56E457D166,
	VRUILayoutOptimizer_OptimizeText_m36C34CC01771CD683A36BDEDCC094644026DA74C,
	VRUILayoutOptimizer_OptimizeButtons_m7B58CB001FC7A5C23D872022BAA8AAABDC7784AE,
	VRUILayoutOptimizer_OptimizeButton_m2386A26766D1DC205C9C14763F78638F615E34B6,
	VRUILayoutOptimizer_OptimizeSliders_mC7654D0D6FAF68C4FB37D2E9BD4A581533FF2953,
	VRUILayoutOptimizer_OptimizeSlider_m58A55B95F325662DE8C6A4D93318A1BCB1043D36,
	VRUILayoutOptimizer_OptimizeImages_mC168737595F33D8B01D274A8C5E20AD41C477EFD,
	VRUILayoutOptimizer_OptimizeImage_m6803B237D36F7FAE7358FC75A8CC2B30F395F85B,
	VRUILayoutOptimizer_OptimizeLayoutGroups_m4201C600A3D0E5F14299A9718E4F7240D6403EDA,
	VRUILayoutOptimizer_OptimizeHorizontalLayoutGroup_mA3DBD35C63F6E2460819979CD5B49667772EF480,
	VRUILayoutOptimizer_OptimizeVerticalLayoutGroup_mB394A46F02D40F3802CA692D85767EDF9A81C020,
	VRUILayoutOptimizer_OptimizeGridLayoutGroup_m9F7012D3C8D7F947461C42D3212171479A6EDC3A,
	VRUILayoutOptimizer_OptimizeTextColor_mAEC87DDD47923F54C5CB5F9FB20FB882648D8D79,
	VRUILayoutOptimizer_OptimizeImageColor_m084376AF520DA6465BE9C879A40D8817D71BF84A,
	VRUILayoutOptimizer_RestoreOriginalLayout_m4870911CA9B1DC8FEDBE101EF786AE624570E4AE,
	VRUILayoutOptimizer_RestoreElement_mE090AE86F57BB8DB8D071640CFAE2252E2C33EA2,
	VRUILayoutOptimizer_RestoreText_m80997831094B239579375A769456425A6B02249D,
	VRUILayoutOptimizer_RestoreButton_m2A02F4F183EFD1AE6478ECE2420ECB9DC6039901,
	VRUILayoutOptimizer_RestoreSlider_m23C99C0BFDDF16CD2FB279BF6C940FCC82D8CCFB,
	VRUILayoutOptimizer_RestoreImage_m3474557DC90E689B42CF51D866FF2BF028D18E1A,
	VRUILayoutOptimizer_RestoreLayoutGroup_m3EBBC2981CE7CBAE632F17135FB743303D8E5C21,
	VRUILayoutOptimizer__ctor_m2561E67A988D883339AE1C8DF391FD033E613A1E,
	OptimizationRecord__ctor_mBF9772C0B6E93699E1B017273E55607445042DE3,
	VRUIManager_Start_m04D2D04C9DA59AA7D5BB3241865F88AEC1578F87,
	VRUIManager_Update_m3B8BC3A450E6DD2FAEB1C5AA2B3D79E536253FD0,
	VRUIManager_InitializeVRUI_m404C3AC545D813E4C4E7335F7327739BDF2DF7CE,
	VRUIManager_FindRequiredComponents_mA6F5D53DC305DA17C05161C5E35EE403D5F3BCDA,
	VRUIManager_ValidateComponents_m9C339C67423B1F600A6B28D0ABA555AB0BB64A65,
	VRUIManager_ConfigureCanvas_m92C1363C3CFA70D39A7B883E4E7F5118822BA654,
	VRUIManager_ConfigureEventSystem_mA4229D01EE54479758572D163EDDC74EBE4AB50F,
	VRUIManager_SetInitialUIPosition_m1D1D8BD430CA8EA6CFC5BE15182D85A998C984FA,
	VRUIManager_PositionUI_m4E4F26379F3A352AADFD59DDE3F8334C170878AF,
	VRUIManager_UpdateUIPosition_m25322D067DC8AA1513485A2703347D7883DE3251,
	VRUIManager_HandleVRInteraction_mC49E19F38920360E2C2CBE9B8359494D2A5241F4,
	VRUIManager_RepositionUIToUser_m21CD2DB7AF62040244BFA9F9F194D1F7FB3430F7,
	VRUIManager_ResetUIPosition_m596AF0D47DC0A5FD88EF29D730565ECAEB897EE5,
	VRUIManager_SetFollowMode_mECC53B484A4F495A64505E04E6A89D4ABEBA21B2,
	VRUIManager_SetUIDistance_m0E02D07045FB937B1FE86CDEA241484D96593D0C,
	VRUIManager_SetUIScale_mC90CB002CE43399EF2496291D67F9E43FB637984,
	VRUIManager__ctor_mA0E0CAF90A4BBB9F6AB114920D3196477A74AD81,
	VRUISetupHelper_Start_mBC1600985350628411B7E8A7777EA3D16AB19791,
	VRUISetupHelper_SetupVRUI_m95E4059DF585A9179FD7C8A359C98DB82B989F32,
	VRUISetupHelper_SetupCanvas_m0FD070D92F95A997C3C1F2EF6E579986253F8DB6,
	VRUISetupHelper_SetupEventSystem_mE259722DC3179C10C4E3B8D4F4394C76D0E572D1,
	VRUISetupHelper_SetupVRUIComponents_m1ECF523B2A08558C0653DD3F8C76DF1DD540D686,
	VRUISetupHelper_CreateSampleUI_m1614FEE2A064C758138B27CE23F499BB5311F513,
	VRUISetupHelper_CreateUIPanel_m691A1C886CE361B6358F3476D103B040215C0B55,
	VRUISetupHelper_CreateUIText_m47469CA5CCD4B353C4F66D91C0C961124A075353,
	VRUISetupHelper_CreateUIButton_m50A37CD89B198B7608924630BA086DBFA9A76D86,
	VRUISetupHelper_CreateUIToggle_m5B0245319F0F065F63D613FF0998B57F08E16A23,
	VRUISetupHelper_CreateUISlider_m5ECD6ACB9A05D80EEDD4B68109A32E90FD12957D,
	VRUISetupHelper_OptimizeForVR_m891B33AE1CE27A392F7E38BE911258318AAF1EDC,
	VRUISetupHelper__ctor_m0A6698FB0021BC243231BF0343361AC73279BED7,
	VRUserGuidance_Start_m2C26A5944C138C6EB9D103D0240709E0409E0DA2,
	VRUserGuidance_InitializeGuidance_m111726F47FA961DDD0D503F7BEF48608DAFA63BE,
	VRUserGuidance_CreateDefaultGuidanceElements_m0AFE2F40630B4E4C06B0A43CAEBCC18278A63355,
	VRUserGuidance_CreateDefaultArrow_mB91943240AA44802B4DE5AAC6BDF2608DDAB7F1A,
	VRUserGuidance_CreateDefaultFloorIndicator_mA45E47D528074ADD517DB3183F94D65E07BCF562,
	VRUserGuidance_CreateGlowMaterial_m495FF8571A8E0BDEDE67C09F3DEFEE7E4E63E3B5,
	VRUserGuidance_GuideUserToOptimalPosition_mC79A19264F09BC6BCEF153E0CC101D8126AD4F79,
	VRUserGuidance_CreateFloorIndicator_m49DCB1913C2AE2108BAE7A25987E44FA61C959AF,
	VRUserGuidance_CreateDirectionArrow_m49DDE0E2C40D72DA7D005ABC9FF2056B175AAEE9,
	VRUserGuidance_GuidanceAnimationCoroutine_m3B8D4FD6BF51349E6E5987D0C6D7886C4F5E7E96,
	VRUserGuidance_WaitForUserPosition_m25A0AD600F78E2A12D386122D5318A9D40137B3D,
	VRUserGuidance_HighlightAssemblyArea_m8F23D0E3987831C7C6E06FA181BB7786A9F02894,
	VRUserGuidance_HighlightCoroutine_m266744DF7580311A49F5D1B52BF4068ACBDD071C,
	VRUserGuidance_ShowGuidanceMessage_mC6CD298B1CF141D06FAC053F87B89F593B523BD6,
	VRUserGuidance_HideGuidanceMessage_mA90A93C1FC49799AE7431E38E0AE7BED37A8D988,
	VRUserGuidance_PlayVoiceGuidance_mF07DE289690B3B6D0D078DF1852F39F7601022E6,
	VRUserGuidance_StopCurrentGuidance_mBD5A25783D8D2B21DBE870B5C3F799BF2BF3CCE0,
	VRUserGuidance_GuideForAssemblyStep_mE182EFF483C7A344834439E154DCF1DBD145356D,
	VRUserGuidance_GuideToBackView_mCA2A1CF9AA944EACA2EC5D7C50E05A92ACD8CCFC,
	VRUserGuidance_OnDestroy_m7BF288A0E12B51E4B715EA2C23E9E3A46FC00E68,
	VRUserGuidance__ctor_m9F6E3AFCB34E45E926F7ABAA32999A0B63591C9A,
	U3CGuideUserToOptimalPositionU3Ed__24__ctor_m1383C15052E14C47D77A90AAE7423BAF6E590D0D,
	U3CGuideUserToOptimalPositionU3Ed__24_System_IDisposable_Dispose_mEB8F8FF06B92E65B2167F12F36A9A618A6CD0F04,
	U3CGuideUserToOptimalPositionU3Ed__24_MoveNext_m81B10167D70F0B0245058FFBEFB3F5883B2DE8EC,
	U3CGuideUserToOptimalPositionU3Ed__24_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mB81D6CD4EF4AAF55F4E784DFC0C52997104AB727,
	U3CGuideUserToOptimalPositionU3Ed__24_System_Collections_IEnumerator_Reset_m6AE49C806C145AF9AF743741D6813856FB20E436,
	U3CGuideUserToOptimalPositionU3Ed__24_System_Collections_IEnumerator_get_Current_m05DA6C906386DC256EF371B3DC563E92E1BCF2D3,
	U3CGuidanceAnimationCoroutineU3Ed__27__ctor_m41C3F2CB62ADDF6131E88E9D478201915A12D72B,
	U3CGuidanceAnimationCoroutineU3Ed__27_System_IDisposable_Dispose_m3E698714563A7AB7FE0851759B017FFBE7571D8E,
	U3CGuidanceAnimationCoroutineU3Ed__27_MoveNext_mE7639A38359A58563755BBA6746487EF891FB5C3,
	U3CGuidanceAnimationCoroutineU3Ed__27_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mCB8CA2A7022D7B8157A2FFBEF84D936A7F2D1218,
	U3CGuidanceAnimationCoroutineU3Ed__27_System_Collections_IEnumerator_Reset_m22B7737FA1DE2913391936766502E491C30B285A,
	U3CGuidanceAnimationCoroutineU3Ed__27_System_Collections_IEnumerator_get_Current_m398E02A923F8C6F2634C18740BF345F5E32A34AF,
	U3CWaitForUserPositionU3Ed__28__ctor_m0BECD0EF5CF852EADD90DF2D5232960ACDB06907,
	U3CWaitForUserPositionU3Ed__28_System_IDisposable_Dispose_m945E3580AB7FA758B5E6D66ACFFEEDDA3B144734,
	U3CWaitForUserPositionU3Ed__28_MoveNext_m69132C50DDCD695621E34947CA71BCC29A972CAC,
	U3CWaitForUserPositionU3Ed__28_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m1D1F574D4CBF44FC28EFCDBD7EFE663E1C7F8B3C,
	U3CWaitForUserPositionU3Ed__28_System_Collections_IEnumerator_Reset_mE174ADE025D5BDE79204F7F552D442729054DD6D,
	U3CWaitForUserPositionU3Ed__28_System_Collections_IEnumerator_get_Current_mA076668FDC8A7D8845AD524FFEEC97CB07220914,
	U3CHighlightCoroutineU3Ed__30__ctor_m4801E771114AF06F0F1DF0AD483BF36DF6F73DFA,
	U3CHighlightCoroutineU3Ed__30_System_IDisposable_Dispose_m1355F4D82BABB77482C78A8B8D47CB9F54A4E42D,
	U3CHighlightCoroutineU3Ed__30_MoveNext_mA4F4F16B04D4A5CA97C46D94E1C65F499625AA85,
	U3CHighlightCoroutineU3Ed__30_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m58CAB82258849F24804D018BC8BDAFD7891FD733,
	U3CHighlightCoroutineU3Ed__30_System_Collections_IEnumerator_Reset_m4541F0BC302E8F10FFF049C61292FB3086C8E04D,
	U3CHighlightCoroutineU3Ed__30_System_Collections_IEnumerator_get_Current_m7B462A3F8AD8BF9C023937252F26709B026C22CC,
	NULL,
	JSONNode_get_Item_m77F15891BEC7ED659BFBC392555178B558747AD8,
	JSONNode_set_Item_mC6F47073D979B943286B2EAB1A6D0380AFE58A09,
	JSONNode_get_Item_m466B08DF2E30B20606697EC7AE043C2791DC6768,
	JSONNode_set_Item_m045530804B67FC5E2E57E497219F27ED70CE437E,
	JSONNode_get_Value_m2A9961ACC3D4BCBB028012CD79B619DCBD82A839,
	JSONNode_set_Value_mE8CD0E68E0E2B0A716F56B0FE9B988EC2BAD773A,
	JSONNode_get_Count_m260DDA50B8AFB98F5946E54B9EADD05891A82C8B,
	JSONNode_get_IsNumber_m6B495FE576572E9FC7999740C63980BCB65AD768,
	JSONNode_get_IsString_mBDE2CAF25E51CDA450074BE9DC81D834903BA392,
	JSONNode_get_IsBoolean_m13F16853C0F6D76D0AB6B7E866923A0632C108A2,
	JSONNode_get_IsNull_m6443A7B3540D725ED3ACA0038A74CE0346A31F8D,
	JSONNode_get_IsArray_m52DCDB47E4CB2673FDCECCD3BE9DD2D90B5C948F,
	JSONNode_get_IsObject_m237FE2EA3382DD9762ED426B49F46183F5EF39AB,
	JSONNode_get_Inline_m7A5B6C07F44EFEEDD80FD72580C32C0579041F4C,
	JSONNode_set_Inline_m18362F10F03DDCD1FF29B4868C3EA793D39AE7F6,
	JSONNode_Add_mB05F1A32B54A9A1223F9AC6A6A737836FA1F4E7E,
	JSONNode_Add_mDAF96580EAF3B9FF23888A8549BED7A98439075D,
	JSONNode_Remove_mF56C4223700DF4F1D5AE12BCD69C492C2487FA59,
	JSONNode_Remove_m7B5E0BC0A29C35857D7B10857A8C52C0E3DFB615,
	JSONNode_Remove_mE2CFD05512C25BD11EA4160CAAF88B8154D9DBE5,
	JSONNode_Clear_mD9B59BDE8D07A352AB775FD4A8FB262D406EB848,
	JSONNode_Clone_mE7849A4FBD98462D93E715826B0D01DE7FC822C3,
	JSONNode_get_Children_m3E2D70DBCA2C8311F65A47B766668728392B1F89,
	JSONNode_get_DeepChildren_m891CB892AEA834980686ED760B952A86DC1E8725,
	JSONNode_HasKey_mBEF13E4AC99F2F0D76D4CD87405BB71586C4486B,
	JSONNode_GetValueOrDefault_m751E871953EBA8094B4DE73CC261C884720811F6,
	JSONNode_ToString_m4CC464630B0AEEDD82AEB6B069690949AF569345,
	JSONNode_ToString_m1F607CB90F49115510B7CF5228733578E9AD41F2,
	NULL,
	NULL,
	JSONNode_get_Linq_m8569DB478533504290D9A09ECA0DF12F116122DA,
	JSONNode_get_Keys_mC3401CC91BBD9D1166EF8EFC0C87A820FC543D1B,
	JSONNode_get_Values_mF8FB164A48A169146D00EBA3F51D4C8E380C1930,
	JSONNode_get_AsDouble_m9A8E3EC46E4545BCBFA26B99C0F013067D2F0AE4,
	JSONNode_set_AsDouble_mCDBB05BD0AE82EEF0C4842F5A9205B8F4C858015,
	JSONNode_get_AsInt_mE4A3FCC1D91362D077C2ACF418ACAB43771B1FE6,
	JSONNode_set_AsInt_m12FCF0B7E45E17EA0456AE44EFEF0C8731603F50,
	JSONNode_get_AsFloat_m0D044C1F3FC35086783A4BAF506EA96DC997D050,
	JSONNode_set_AsFloat_m55FCE24DF60B37724DACCCF0A759522B2561DE92,
	JSONNode_get_AsBool_m902380F5939671ACBBB7EFA01A48F1A082B1FD9C,
	JSONNode_set_AsBool_m6097FD196A8C7BB156125363D1C1D3EF0EB67CD3,
	JSONNode_get_AsLong_m31250905C6F4BED9B1059009E064D07D609C4C18,
	JSONNode_set_AsLong_m8D29780DEA1458A2F9C33805432DB1554950ECF4,
	JSONNode_get_AsULong_mA34C3D1DA0D3339D0B63F386867ADE3E460909DD,
	JSONNode_set_AsULong_m2BC120C5B1842E17BC0E6E5714511391DD504091,
	JSONNode_get_AsArray_m2D0890FDDA140528CAB44B1B6B3E34B26383ACC7,
	JSONNode_get_AsObject_m72F6D406BECA2FB0A24B20E0A353FDB8E409CA1B,
	JSONNode_op_Implicit_m71A2D2EECDD79DC3A3DAF6510BB2F8ED57DE6AAC,
	JSONNode_op_Implicit_m6019D30B60A2033906907488CB6236EC9A7B7B6B,
	JSONNode_op_Implicit_m098A31323C3D89615E0EBD709D83AB4F684453CF,
	JSONNode_op_Implicit_m17B71B28DE136B73EF2B97DA87BB4A4BB27332E9,
	JSONNode_op_Implicit_m4A9267CC71FC4FDD39ABAE262B7B2D334EB0FFBD,
	JSONNode_op_Implicit_m808991B4DFA11ECBF7080226CFC3069A7E6673E8,
	JSONNode_op_Implicit_m6AC3F8DDC02644CA8D85EC90758373D1B7EC4322,
	JSONNode_op_Implicit_mD9A5824FE62046D01AD966EC503DEB775B2C7482,
	JSONNode_op_Implicit_m94EF6FC36942EA4A49ABFCA42FC12BCE914990FA,
	JSONNode_op_Implicit_mD25BDDE21767954AE9D36F16948B6F77173EC2F6,
	JSONNode_op_Implicit_m64294932E998EC6A35EF99F1CD4D36BFB9A8FB1E,
	JSONNode_op_Implicit_mD5974501A6FBBD60D6E7331940440498D67A7A05,
	JSONNode_op_Implicit_mCE1B7A6233218E114687A876F778B4A1CBF22B74,
	JSONNode_op_Implicit_m29CA9621387E0DDDECCCAAB240A140A854567FDF,
	JSONNode_op_Implicit_mB9C34D74CF1854E402B4AD106AB2084169287E1E,
	JSONNode_op_Equality_mD30EBFA5F9398107FCC5CE51B05CE4CFFBCC6A8E,
	JSONNode_op_Inequality_m91693B2A4AC881F8703CC1D1050371B8EC552CF7,
	JSONNode_Equals_mE1B8A846783529B1E54786975A6A2396089A88DE,
	JSONNode_GetHashCode_m0A263555D1F0E6766A61692A7E1BC3546B2BC984,
	JSONNode_get_EscapeBuilder_mA695C85FBFBCF3863E2AC3B63821B469CC632DB1,
	JSONNode_Escape_m5C811748A36C7258315C1D2036712855F184ADDD,
	JSONNode_ParseElement_m3478B79AC164A87E0B2088067EDEC6DE146DAA56,
	JSONNode_Parse_m7198C73C509B06CD8A96576D7D2A5A125DC7D0B4,
	JSONNode__ctor_mF8F2893483161D3B7B9877B63C69063D26A5C353,
	JSONNode__cctor_m00855C5266A7EF25B6EBE62476F1FAD5C7046065,
	Enumerator_get_IsValid_mBC273331DC1699FF46BD3621AE5059A54AD98BA6,
	Enumerator__ctor_mF21239C69620D815F8CD34F022BE18E9DAF9CB10,
	Enumerator__ctor_mAC4ED0FA4B083E2652E865A41EA5C74A49478EFE,
	Enumerator_get_Current_mDE6750203413E1069D0520793D6AA0B2527CB20E,
	Enumerator_MoveNext_m238CF072385A1106BEDEFCE33BA2B0DBE999758A,
	ValueEnumerator__ctor_mCC61CE3EDCF1AC94A84E031F2E89F8054C94A015,
	ValueEnumerator__ctor_m122732DF448B45E8E82956E07AC8314C60E28C29,
	ValueEnumerator__ctor_m7BA4BAD5FEBAC4054F71575B728DC27EC4080F0A,
	ValueEnumerator_get_Current_mAA24A52FDEB7160BD268193175388EACB41B7CE2,
	ValueEnumerator_MoveNext_m5B596A2EF2FF395EDA8F5CAB97C0789498D250C9,
	ValueEnumerator_GetEnumerator_m765261287A2C0AEF757B94994826F43951387E4C,
	KeyEnumerator__ctor_m6EA81E2BED4CA5194A7306D8B324F7356E37F80A,
	KeyEnumerator__ctor_mA6338E82A9F8AA19A1744352B4FE54103AD70405,
	KeyEnumerator__ctor_m526EA1364C367B83C931F4208CDD816BD02810EA,
	KeyEnumerator_get_Current_mB4E0F33D7E23A7F365D12B3530DE7FB6B7A1F7E3,
	KeyEnumerator_MoveNext_m42FE2CEE808A7E065895BA333B7FBD2F3AEE032F,
	KeyEnumerator_GetEnumerator_mD4687B4D6D10E4D6870CBBECC680689A62A95C0B,
	LinqEnumerator__ctor_m9FD8AB1580F3D94C5C36D070DBE85E023ED36E30,
	LinqEnumerator_get_Current_m28F0BE4D9B5736F5BD79197C1895EAC1592EBAAF,
	LinqEnumerator_System_Collections_IEnumerator_get_Current_m6B6C12C7E8CD21DF513FCDCB4E88E454790B6FF0,
	LinqEnumerator_MoveNext_mCA8604B6E8D857CF16003E674048C05E29447819,
	LinqEnumerator_Dispose_m5D6A54C4B712D138739726323D5BEA50A4E12E32,
	LinqEnumerator_GetEnumerator_m4A9F0720F0C0964F91032AB8B8776F09DC70A90B,
	LinqEnumerator_Reset_m56B65E398518EF57070307FDC48069DFE37BC57B,
	LinqEnumerator_System_Collections_IEnumerable_GetEnumerator_mB63F02D713868ABF87DAB18ABFD5D832F4D805A4,
	U3Cget_ChildrenU3Ed__43__ctor_mA2E1AC1211A03DAFF45B69AF872ED71E58F4D458,
	U3Cget_ChildrenU3Ed__43_System_IDisposable_Dispose_m0C7490DE49A53AB049729E66293845681AB08395,
	U3Cget_ChildrenU3Ed__43_MoveNext_m33A56DB8F47EADE4EB91E3FBFF4D01F1CF255839,
	U3Cget_ChildrenU3Ed__43_System_Collections_Generic_IEnumeratorU3CSimpleJSON_JSONNodeU3E_get_Current_m85EB3E729C5EE85E2103FED7453D79C1D132C2EB,
	U3Cget_ChildrenU3Ed__43_System_Collections_IEnumerator_Reset_m755BAC68C65681AA8266C6AC37D2308771D54067,
	U3Cget_ChildrenU3Ed__43_System_Collections_IEnumerator_get_Current_m04BDDA2EB2EC20489BB50BDDB46313F624F90CF9,
	U3Cget_ChildrenU3Ed__43_System_Collections_Generic_IEnumerableU3CSimpleJSON_JSONNodeU3E_GetEnumerator_m96326AFEFC6998DB0E90D15633CFE23661C21916,
	U3Cget_ChildrenU3Ed__43_System_Collections_IEnumerable_GetEnumerator_m39BF4FF795523B96CA4FA6383244D82117D96C46,
	U3Cget_DeepChildrenU3Ed__45__ctor_m89830CB6F115E0AD956EF880354CAFBAD7AF9E5A,
	U3Cget_DeepChildrenU3Ed__45_System_IDisposable_Dispose_mCE52C471742B7A6DA19AF43E9096545012D560DD,
	U3Cget_DeepChildrenU3Ed__45_MoveNext_m644F556E82CCF23C7B91E0B0266F4716E18C2F5E,
	U3Cget_DeepChildrenU3Ed__45_U3CU3Em__Finally1_mBA31C43EB8ACB72C8A163B470D786ACB361CF740,
	U3Cget_DeepChildrenU3Ed__45_U3CU3Em__Finally2_mC829190BED7A6B48F2F4C64848495925A3C58EEE,
	U3Cget_DeepChildrenU3Ed__45_System_Collections_Generic_IEnumeratorU3CSimpleJSON_JSONNodeU3E_get_Current_m6E1A05C1C6A7BF9748F1768E2B2AB1B140F49983,
	U3Cget_DeepChildrenU3Ed__45_System_Collections_IEnumerator_Reset_mB10807E87C7440A590E9580E6A5B329ACCAD49E4,
	U3Cget_DeepChildrenU3Ed__45_System_Collections_IEnumerator_get_Current_m2A8CD7D70A8ACF8A362378B75EAF7B41BC9FCEF6,
	U3Cget_DeepChildrenU3Ed__45_System_Collections_Generic_IEnumerableU3CSimpleJSON_JSONNodeU3E_GetEnumerator_mAD2929E624663DCA925B762F05FCF8CDDE1FC6C8,
	U3Cget_DeepChildrenU3Ed__45_System_Collections_IEnumerable_GetEnumerator_m8BB12003DCC4402BDA35F5B5AE1B82EF7C1A4856,
	JSONArray_get_Inline_mBA0C9AEBB7420DBDFD977C0F54CC237E8F2BE3E5,
	JSONArray_set_Inline_m731089F5D0FA649ED210518DC299635A8D86A1DC,
	JSONArray_get_Tag_m360EB078D7897D6D52783B8CDA6B736D014E97BC,
	JSONArray_get_IsArray_mA7B4EF5B0128FB64ACEB7EAC66FA3522991980AF,
	JSONArray_GetEnumerator_m6AF64AE0DD2A5AAB8C0E271BF0CAB8AA1FD32E17,
	JSONArray_get_Item_m8BE9047FC512840E6A4594560EDF86BB4E0FF657,
	JSONArray_set_Item_mBCD05590C34BC589B786E753B9FE796EBA3F6725,
	JSONArray_get_Item_mE18312128B02B505BA656D7F444B05A6769710AE,
	JSONArray_set_Item_mE4E0DE5133E60AF49E46FEDAD00D2A04349C0855,
	JSONArray_get_Count_mB71218A2D8288D0665C467844F7351D301FDAFDD,
	JSONArray_Add_mD1FBE0F0FC20E7415014B7FF21939592EBB0C9A1,
	JSONArray_Remove_m79500DBD9751A04C02756470A4D22DDCF9C97FEC,
	JSONArray_Remove_m64C3EBFE3DB5BE130232769DC43000E84589E674,
	JSONArray_Clear_m86E2E8BE6493C5C555525B9935AFF9E53BB72C2B,
	JSONArray_Clone_mA05BA59E71672A88208218DF12C4E5F7A8773502,
	JSONArray_get_Children_m733AE4C5816E51E6F86441110606489A0406AA91,
	JSONArray_WriteToStringBuilder_m9F23115433028794DCAC019F82EEFD946990D994,
	JSONArray__ctor_m92FFF2DC8E1425398814F50D4B253EB459B8477F,
	U3Cget_ChildrenU3Ed__24__ctor_m4FA6CFA96B1189496D9E219499A0C05F713A6D28,
	U3Cget_ChildrenU3Ed__24_System_IDisposable_Dispose_m91E6F93E3940835795BCA9BFD783592E29BDEE5A,
	U3Cget_ChildrenU3Ed__24_MoveNext_m9C8F57C9E0722A9D843A2BA0259E7EE30778CF6B,
	U3Cget_ChildrenU3Ed__24_U3CU3Em__Finally1_m8E8730694C83B14CFFB30D810166D12563C1DFF2,
	U3Cget_ChildrenU3Ed__24_System_Collections_Generic_IEnumeratorU3CSimpleJSON_JSONNodeU3E_get_Current_m6958E538A455210191F2E06BA531D4AE5F0E97F0,
	U3Cget_ChildrenU3Ed__24_System_Collections_IEnumerator_Reset_mE122AA2BA93A72C8C8733C4F7EC6A7B8CFB42FCD,
	U3Cget_ChildrenU3Ed__24_System_Collections_IEnumerator_get_Current_m508CF18DF3857321EA1CFDC62E0406DBEF6FDF7F,
	U3Cget_ChildrenU3Ed__24_System_Collections_Generic_IEnumerableU3CSimpleJSON_JSONNodeU3E_GetEnumerator_m7679E5F774E9512FC2DA58B2D0236A66983BC632,
	U3Cget_ChildrenU3Ed__24_System_Collections_IEnumerable_GetEnumerator_m7593480F6CC6218E2EA7CD84ED3A56FF6274AB32,
	JSONObject_get_Inline_mCDF2154366BEFF9E547918F999E7F3C7C4865F84,
	JSONObject_set_Inline_m7F048A7565E5A53FDB610D44B7CA75A314CB7A7A,
	JSONObject_get_Tag_mD57D6BCAD1C677B88693FD508129CFAD661F4FBD,
	JSONObject_get_IsObject_m9F72861BE5A0DB2888AA3CBEC82718E08DD71E93,
	JSONObject_GetEnumerator_m8912E3D1EA302655BB5701B53EB19437238BABDA,
	JSONObject_get_Item_m219B9BA37D800A5DFEAA14E4EECA375B3565BF96,
	JSONObject_set_Item_m1AC7334DBA67D0CB6C9549B83B3FFA75CF226AEF,
	JSONObject_get_Item_m5C2EDBE7B154A3FC1CC43616C4C40255B4D95652,
	JSONObject_set_Item_mFB6E61E3FA394B7D2CA01CC957A6A253642D109B,
	JSONObject_get_Count_m9109E9A81559A9006EE160CA6A0F3291C71F2D08,
	JSONObject_Add_m25BD208A0AC0F0223FD93FBCB42785B12A6E1A18,
	JSONObject_Remove_m34280FDB4512E61F42781475E492BE98514830C9,
	JSONObject_Remove_mD1B01E22A9C1FEE83A00ECDFD8E0D8A422F8E4C2,
	JSONObject_Remove_m51B998A7997D184A1A20359D512C6B5A1B825404,
	JSONObject_Clear_m74686B9AF4B75949F959B81AAF8DE5076C60B3FE,
	JSONObject_Clone_mF3146F5687820508FD22051B23EFA20430B811C1,
	JSONObject_HasKey_m79E034D14422C265C62C6C50C8E6F8337749457E,
	JSONObject_GetValueOrDefault_m969ABBC8049DB2DF4EC53968CDF7DF45666873BC,
	JSONObject_get_Children_m03D7227DE57F0BE2977FC0436C0DE48858650B7C,
	JSONObject_WriteToStringBuilder_m931DC8805C6B8F09617958EFDAEA957751EB2EAE,
	JSONObject__ctor_m8007967452F5257DC9F5DF2B78B411BFD4B6D6AB,
	U3CU3Ec__DisplayClass21_0__ctor_m6976B4CF7F93E28364B390F81E55DAD60BB141C1,
	U3CU3Ec__DisplayClass21_0_U3CRemoveU3Eb__0_m8B35D441B276B749481FF797FC51A256A7A56105,
	U3Cget_ChildrenU3Ed__27__ctor_mC18696B4562A62E4AA0969D6399C8C0631E35DC8,
	U3Cget_ChildrenU3Ed__27_System_IDisposable_Dispose_mC5CC72D1E22DD570C8E2EB525332F70406CDB9AA,
	U3Cget_ChildrenU3Ed__27_MoveNext_mF000F683CB97030C47BF22BD34472814A0C7630C,
	U3Cget_ChildrenU3Ed__27_U3CU3Em__Finally1_mF5ECB5874D716A4939B7F1DB00D93DC58CEA824D,
	U3Cget_ChildrenU3Ed__27_System_Collections_Generic_IEnumeratorU3CSimpleJSON_JSONNodeU3E_get_Current_mD5BCAEE8B6A2ADEAF8EC61432A9619287942CD66,
	U3Cget_ChildrenU3Ed__27_System_Collections_IEnumerator_Reset_m7F54C4A2495814DE04F74FB9E9296EA2B68BFF6D,
	U3Cget_ChildrenU3Ed__27_System_Collections_IEnumerator_get_Current_mF24C3141BA1436A87068A46004816112F281FF9E,
	U3Cget_ChildrenU3Ed__27_System_Collections_Generic_IEnumerableU3CSimpleJSON_JSONNodeU3E_GetEnumerator_mB7F1824F0A6AD34C4EFEB913F04662B64CEF262C,
	U3Cget_ChildrenU3Ed__27_System_Collections_IEnumerable_GetEnumerator_m02800F9D77652D9E15E570729565FE79BCC2B3F8,
	JSONString_get_Tag_m68B0FF9ADDC3E203E5D60BB10639AEABACA34D44,
	JSONString_get_IsString_m933985E37AE8A887A2039A9BAC7698F083BCD6E3,
	JSONString_GetEnumerator_m1CB9E437FC8622F3FE05D0AC12024D144747E0B8,
	JSONString_get_Value_mEAD2BD372A2C517E83233BA5F6E309745AA5E9B4,
	JSONString_set_Value_mB974D9B82AB8F9FAB84DCA99B8BD4B7C1C08ED00,
	JSONString__ctor_m1DD5FB9A4147F72A0ED5F773FF82FA269241AD19,
	JSONString_Clone_m59FCBC159496A334397171CF5127205C82C30A73,
	JSONString_WriteToStringBuilder_mDF24D860FBF8E71F6F04799DD70F7700CE41D818,
	JSONString_Equals_m1C60B537E558E6DF85ACF3EF9FF43BF9A3CF5435,
	JSONString_GetHashCode_m979A74F84B4C0F45BF63D75DE1146490F743EE00,
	JSONString_Clear_m3E9CBF4AB37C6FD0011E19CA99E074FEA129FED7,
	JSONNumber_get_Tag_m7C6E217E85B6161812496B63E5D371B910AAC856,
	JSONNumber_get_IsNumber_mFABFD0C9C4905CFB34A62700A1BD335F53E4214E,
	JSONNumber_GetEnumerator_m4D13E84756AEED9FCD7EFEEE4D01187DD049C596,
	JSONNumber_get_Value_mBC5AB046D134B1E54C228C9C1C2231F8448CD56D,
	JSONNumber_set_Value_m2264762BBD76F39DDC5DF3160910A44FBEFDE54C,
	JSONNumber_get_AsDouble_m8C004121700A7E7EB2B77ED223187227E33DE60B,
	JSONNumber_set_AsDouble_m8E17AF8C0E9AE0EF6E25D86CB1B119904ADC0558,
	JSONNumber_get_AsLong_mF96069F806F51121CBFE8847D9E0D312F05986BB,
	JSONNumber_set_AsLong_m541EF4E20CD8683CA860E0B969CECF7B71E2A357,
	JSONNumber_get_AsULong_mD1EB0D23B9143C4CC1AA4BF75F17E326C08785CA,
	JSONNumber_set_AsULong_m320EA0ACC4B63183B5223CFCF0B25B8DA383C0DA,
	JSONNumber__ctor_m1CE3527102D15EBC3A183E3519895E291CAC1D90,
	JSONNumber__ctor_m39FDDE1A9EFEE9C4F2498E531D12B97AA49A1BA5,
	JSONNumber_Clone_m1C9DD94EB3011E55E840B55B4D4F3EAB63AF8A52,
	JSONNumber_WriteToStringBuilder_mD311BC3C1EE3E159C43801EB214F084E567367F2,
	JSONNumber_IsNumeric_m9039F8DA776517548A2A6BEA7377B419C0525887,
	JSONNumber_Equals_mC04BB811CCAF20E70AE696AE74ECFDF5DA888688,
	JSONNumber_GetHashCode_m976ADFE41037830524798C7E6AFE08006B5F77AD,
	JSONNumber_Clear_mEB7835A2B2D433CE017CFD91CAE974ADB27CE72C,
	JSONBool_get_Tag_m82CE84C4C89E157D4DB036B9F0745343C005C338,
	JSONBool_get_IsBoolean_m2671AE98710859611DF47E6BC58E6582C3A5B445,
	JSONBool_GetEnumerator_mA07A10A6111713F7AD09FF03D09A6028556094D9,
	JSONBool_get_Value_mBEA89869448B0B597758D5BF2A3B576CA0BB64E3,
	JSONBool_set_Value_mC960EE4083CA91D0059BE24661AFC06E131E2CFC,
	JSONBool_get_AsBool_mE04224144EAD0A9AD2F3B14BC0C68557A3BF22AC,
	JSONBool_set_AsBool_m88EDF61A5ABBFF3ECF723312852E14F3C60AE365,
	JSONBool__ctor_mBB02E388CFB96B99E84561FCFF68147F00391C58,
	JSONBool__ctor_m8CFB6AA78095EA003AB9B5EDD8932E8E0B01A1B9,
	JSONBool_Clone_m0B98A17130A9A6FCEC5A92408F551E344CB80274,
	JSONBool_WriteToStringBuilder_m82C70C80863730E8A22EE7A5B099C765F2E1D91E,
	JSONBool_Equals_m2671F40DA8F1128BA1451FE7066515C6E0C50D45,
	JSONBool_GetHashCode_mC5B59375A9EE9978A5ADD1A24ECEE3FC920836DB,
	JSONBool_Clear_m7841012AB307EA72DCFA23305AF45E45ACF7B7DE,
	JSONNull_CreateOrGet_mDC16038413CE71B027A7F9AB1546AF8666D3D3BD,
	JSONNull__ctor_m909243259F39D10FA6FEB176474DEF9C9972D76B,
	JSONNull_get_Tag_m89A7F368EA6269874235F85E43AE82254AAFD41E,
	JSONNull_get_IsNull_m1174212D6379871AC361EF06FA05DD510FC55595,
	JSONNull_GetEnumerator_m16D254C74386D1A0AB2EFD1DE0EAF409C73B7686,
	JSONNull_get_Value_mB15431220D7D0B45CE002A204DF9E070CF78DBE0,
	JSONNull_set_Value_mAF0CD2E912EF772E0892EB4ABB77294F689CF20A,
	JSONNull_get_AsBool_m6F3817CD49ED7CC10C180D31D84ED4B0151C78CE,
	JSONNull_set_AsBool_m5717BC3921B7DE0683E9160B3816628B5CBC663D,
	JSONNull_Clone_m103493F0850508FB95CCA260491BAA283658289F,
	JSONNull_Equals_m8A39CAD3A41E9584C434B90A1360C62B3E158DE6,
	JSONNull_GetHashCode_m74BE6286F06C6E7D5E35381E8BD27215117D9061,
	JSONNull_WriteToStringBuilder_mB5B78BFA6A4943319926C1B2AE93F68C7B9B5FFD,
	JSONNull__cctor_m00A365175E9F31A2842DA242EE490783F0EAC483,
	JSONLazyCreator_get_Tag_m1CB86FEA25328F1BE9CC01F6D020C9450E9F466E,
	JSONLazyCreator_GetEnumerator_m720BF0642A079A8BD44F6D650CF4D833DEF67757,
	JSONLazyCreator__ctor_m0B3625D19DDD8DBDBB45822FAABCE266FA4EE694,
	JSONLazyCreator__ctor_m02E2D630C60045F25A3AC001B7A17DF2D5D197B4,
	NULL,
	JSONLazyCreator_get_Item_m562D16AE7F1F0CACA5ED050B390B63F98EBC77B1,
	JSONLazyCreator_set_Item_m42894F9D00193BC7138C5D451E1B0BBD1BFE1084,
	JSONLazyCreator_get_Item_mF7AE3ADFBE062BF3B83FECCE0EF10F10996DE0CD,
	JSONLazyCreator_set_Item_m0107997E3B3CB75FACD86FB487C5D9416171CBEC,
	JSONLazyCreator_Add_mA8451EE34FEA0205B6BD6527AB46E5926451F49F,
	JSONLazyCreator_Add_mDC69A4E203B73054072D1575EC4CF20D95064F61,
	JSONLazyCreator_op_Equality_m46508F81FB60FE9DCA683335676093A23D59D799,
	JSONLazyCreator_op_Inequality_m06C76EEC055AE314ED6E4FE7A49719AC7ACA397D,
	JSONLazyCreator_Equals_m753939907CFDB1548B0DAAB38E4737EF17B50066,
	JSONLazyCreator_GetHashCode_m878E7AFF42AE5C43F4F643B6AEB25662491316F9,
	JSONLazyCreator_get_AsInt_mE1404FBC99CE4E8EF4ABBE0BDF661206BAC2C44D,
	JSONLazyCreator_set_AsInt_m13146E53FD6A2F7573B752BFF079E0AF6A5FAE74,
	JSONLazyCreator_get_AsFloat_m2600D4B0E1179583EFE268070C66EAC11D380E04,
	JSONLazyCreator_set_AsFloat_m9DCF79C70D4ED3728C12B709A6D95A0F0A057DE0,
	JSONLazyCreator_get_AsDouble_m41D6DF89CD7CEC00F36962068EE072D391EC0B38,
	JSONLazyCreator_set_AsDouble_mB7ABE38136DBEDA7CC9AC12A381322D6C49ADED9,
	JSONLazyCreator_get_AsLong_mFBA0000985629FA20509FA45A6A8B751C9CAC2B8,
	JSONLazyCreator_set_AsLong_mBD4640D2F347DEF793A631A44026A03D3D5D73A4,
	JSONLazyCreator_get_AsULong_m09F6B8D28F383D9A0F857339A6663B24D6AB97A2,
	JSONLazyCreator_set_AsULong_m5514AFD97B29BBA5D1A4EC80F7086929DE977A7D,
	JSONLazyCreator_get_AsBool_m7D8AF5879C2C8036916AA6B15E22CB4B80412CF4,
	JSONLazyCreator_set_AsBool_m4DB409DB959182CAA610147A51A2ECDBAFEA6092,
	JSONLazyCreator_get_AsArray_m493C069A3624597885A7B6E00C82E829A84B47C4,
	JSONLazyCreator_get_AsObject_mE01B43B261A6A56F4FCE40AB11F3AAF90B7C292D,
	JSONLazyCreator_WriteToStringBuilder_mC9975859B1C42C9F5E507E604121D10B2FB2D93D,
	JSON_Parse_mEE6C962A58074E33C05C49D74221F1852E7963CE,
	U3CPrivateImplementationDetailsU3E_ComputeStringHash_m6EA1F233618497AEFF8902A5EDFA24C74E2F2876,
};
extern void AssemblyStep_AddMountPoint_m194EA8814100C8F34D6BB078603A47010B7B707D_AdjustorThunk (void);
extern void AssemblyStep_ToString_m320E7A2E2902A63202043DA88272743C043F82EB_AdjustorThunk (void);
extern void AssemblyStep_IsValid_mF9FCF7AB253203EA0B1C0D39E09B71CE913F6A41_AdjustorThunk (void);
extern void AssemblyStep_RequiresFastener_mFD703603DDAD3E14B20E48D70E9EE9A5E3988974_AdjustorThunk (void);
extern void AssemblyStep_HasAdditionalMountPoints_m5341C821D4FE304D28E15E715126EF409BEA18E3_AdjustorThunk (void);
extern void AssemblyStepData_ToString_mE8C413BE6001E1478285B8AA28545AB3EBC65360_AdjustorThunk (void);
extern void AssemblyStep__ctor_m40EF831184A0632EF7E7D625F994BB6378CEE04E_AdjustorThunk (void);
extern void AssemblyStep__ctor_m9E10466D3C2D06685921F7B5B8A0C782FF812EFE_AdjustorThunk (void);
extern void AssemblyStep_ToString_mF97FA0F6A8BF46F54FA596AB0A2C5C7F43719B1B_AdjustorThunk (void);
extern void Enumerator_get_IsValid_mBC273331DC1699FF46BD3621AE5059A54AD98BA6_AdjustorThunk (void);
extern void Enumerator__ctor_mF21239C69620D815F8CD34F022BE18E9DAF9CB10_AdjustorThunk (void);
extern void Enumerator__ctor_mAC4ED0FA4B083E2652E865A41EA5C74A49478EFE_AdjustorThunk (void);
extern void Enumerator_get_Current_mDE6750203413E1069D0520793D6AA0B2527CB20E_AdjustorThunk (void);
extern void Enumerator_MoveNext_m238CF072385A1106BEDEFCE33BA2B0DBE999758A_AdjustorThunk (void);
extern void ValueEnumerator__ctor_mCC61CE3EDCF1AC94A84E031F2E89F8054C94A015_AdjustorThunk (void);
extern void ValueEnumerator__ctor_m122732DF448B45E8E82956E07AC8314C60E28C29_AdjustorThunk (void);
extern void ValueEnumerator__ctor_m7BA4BAD5FEBAC4054F71575B728DC27EC4080F0A_AdjustorThunk (void);
extern void ValueEnumerator_get_Current_mAA24A52FDEB7160BD268193175388EACB41B7CE2_AdjustorThunk (void);
extern void ValueEnumerator_MoveNext_m5B596A2EF2FF395EDA8F5CAB97C0789498D250C9_AdjustorThunk (void);
extern void ValueEnumerator_GetEnumerator_m765261287A2C0AEF757B94994826F43951387E4C_AdjustorThunk (void);
extern void KeyEnumerator__ctor_m6EA81E2BED4CA5194A7306D8B324F7356E37F80A_AdjustorThunk (void);
extern void KeyEnumerator__ctor_mA6338E82A9F8AA19A1744352B4FE54103AD70405_AdjustorThunk (void);
extern void KeyEnumerator__ctor_m526EA1364C367B83C931F4208CDD816BD02810EA_AdjustorThunk (void);
extern void KeyEnumerator_get_Current_mB4E0F33D7E23A7F365D12B3530DE7FB6B7A1F7E3_AdjustorThunk (void);
extern void KeyEnumerator_MoveNext_m42FE2CEE808A7E065895BA333B7FBD2F3AEE032F_AdjustorThunk (void);
extern void KeyEnumerator_GetEnumerator_mD4687B4D6D10E4D6870CBBECC680689A62A95C0B_AdjustorThunk (void);
static Il2CppTokenAdjustorThunkPair s_adjustorThunks[26] = 
{
	{ 0x060000EE, AssemblyStep_AddMountPoint_m194EA8814100C8F34D6BB078603A47010B7B707D_AdjustorThunk },
	{ 0x060000EF, AssemblyStep_ToString_m320E7A2E2902A63202043DA88272743C043F82EB_AdjustorThunk },
	{ 0x060000F0, AssemblyStep_IsValid_mF9FCF7AB253203EA0B1C0D39E09B71CE913F6A41_AdjustorThunk },
	{ 0x060000F1, AssemblyStep_RequiresFastener_mFD703603DDAD3E14B20E48D70E9EE9A5E3988974_AdjustorThunk },
	{ 0x060000F2, AssemblyStep_HasAdditionalMountPoints_m5341C821D4FE304D28E15E715126EF409BEA18E3_AdjustorThunk },
	{ 0x060000FC, AssemblyStepData_ToString_mE8C413BE6001E1478285B8AA28545AB3EBC65360_AdjustorThunk },
	{ 0x060001AB, AssemblyStep__ctor_m40EF831184A0632EF7E7D625F994BB6378CEE04E_AdjustorThunk },
	{ 0x060001AC, AssemblyStep__ctor_m9E10466D3C2D06685921F7B5B8A0C782FF812EFE_AdjustorThunk },
	{ 0x060001AD, AssemblyStep_ToString_mF97FA0F6A8BF46F54FA596AB0A2C5C7F43719B1B_AdjustorThunk },
	{ 0x06000586, Enumerator_get_IsValid_mBC273331DC1699FF46BD3621AE5059A54AD98BA6_AdjustorThunk },
	{ 0x06000587, Enumerator__ctor_mF21239C69620D815F8CD34F022BE18E9DAF9CB10_AdjustorThunk },
	{ 0x06000588, Enumerator__ctor_mAC4ED0FA4B083E2652E865A41EA5C74A49478EFE_AdjustorThunk },
	{ 0x06000589, Enumerator_get_Current_mDE6750203413E1069D0520793D6AA0B2527CB20E_AdjustorThunk },
	{ 0x0600058A, Enumerator_MoveNext_m238CF072385A1106BEDEFCE33BA2B0DBE999758A_AdjustorThunk },
	{ 0x0600058B, ValueEnumerator__ctor_mCC61CE3EDCF1AC94A84E031F2E89F8054C94A015_AdjustorThunk },
	{ 0x0600058C, ValueEnumerator__ctor_m122732DF448B45E8E82956E07AC8314C60E28C29_AdjustorThunk },
	{ 0x0600058D, ValueEnumerator__ctor_m7BA4BAD5FEBAC4054F71575B728DC27EC4080F0A_AdjustorThunk },
	{ 0x0600058E, ValueEnumerator_get_Current_mAA24A52FDEB7160BD268193175388EACB41B7CE2_AdjustorThunk },
	{ 0x0600058F, ValueEnumerator_MoveNext_m5B596A2EF2FF395EDA8F5CAB97C0789498D250C9_AdjustorThunk },
	{ 0x06000590, ValueEnumerator_GetEnumerator_m765261287A2C0AEF757B94994826F43951387E4C_AdjustorThunk },
	{ 0x06000591, KeyEnumerator__ctor_m6EA81E2BED4CA5194A7306D8B324F7356E37F80A_AdjustorThunk },
	{ 0x06000592, KeyEnumerator__ctor_mA6338E82A9F8AA19A1744352B4FE54103AD70405_AdjustorThunk },
	{ 0x06000593, KeyEnumerator__ctor_m526EA1364C367B83C931F4208CDD816BD02810EA_AdjustorThunk },
	{ 0x06000594, KeyEnumerator_get_Current_mB4E0F33D7E23A7F365D12B3530DE7FB6B7A1F7E3_AdjustorThunk },
	{ 0x06000595, KeyEnumerator_MoveNext_m42FE2CEE808A7E065895BA333B7FBD2F3AEE032F_AdjustorThunk },
	{ 0x06000596, KeyEnumerator_GetEnumerator_mD4687B4D6D10E4D6870CBBECC680689A62A95C0B_AdjustorThunk },
};
static const int32_t s_InvokerIndices[1605] = 
{
	6262,
	6262,
	6262,
	4921,
	6262,
	1860,
	6262,
	6262,
	6262,
	6262,
	6262,
	6135,
	6163,
	4545,
	386,
	2672,
	1182,
	797,
	388,
	6262,
	6262,
	6262,
	4362,
	790,
	4362,
	383,
	796,
	2687,
	6262,
	4894,
	6262,
	6041,
	6135,
	6262,
	6135,
	4894,
	6262,
	6041,
	6135,
	6262,
	6135,
	4894,
	6262,
	6041,
	6135,
	6262,
	6135,
	4894,
	6262,
	6041,
	6135,
	6262,
	6135,
	4894,
	6262,
	6041,
	6135,
	6262,
	6135,
	4894,
	6262,
	6041,
	6135,
	6262,
	6135,
	4894,
	6262,
	6041,
	6135,
	6262,
	6135,
	4894,
	6262,
	6041,
	6135,
	6262,
	6135,
	4894,
	6262,
	6041,
	6135,
	6262,
	6135,
	4894,
	6262,
	6041,
	6135,
	6262,
	6135,
	6194,
	4976,
	1595,
	1184,
	385,
	1179,
	387,
	799,
	1182,
	2687,
	379,
	1195,
	4545,
	796,
	796,
	946,
	379,
	28,
	6262,
	4894,
	6262,
	6041,
	6135,
	6262,
	6135,
	6262,
	4976,
	6262,
	4894,
	6262,
	6041,
	6135,
	6262,
	6135,
	6262,
	4976,
	6262,
	4894,
	6262,
	6041,
	6135,
	6262,
	6135,
	6262,
	4976,
	6262,
	4894,
	6262,
	6041,
	6135,
	6262,
	6135,
	6262,
	4976,
	6262,
	4894,
	6262,
	6041,
	6135,
	6262,
	6135,
	6262,
	4976,
	6262,
	4894,
	6262,
	6041,
	6135,
	6262,
	6135,
	4894,
	6262,
	6041,
	6135,
	6262,
	6135,
	4894,
	6262,
	6041,
	6135,
	6262,
	6135,
	4894,
	6262,
	6041,
	6135,
	6262,
	6135,
	4894,
	6262,
	6041,
	6135,
	6262,
	6135,
	4894,
	6262,
	6041,
	6135,
	6262,
	6135,
	6135,
	6262,
	6262,
	440,
	795,
	795,
	4545,
	795,
	795,
	6262,
	4894,
	6262,
	6041,
	6135,
	6262,
	6135,
	4894,
	6262,
	6041,
	6135,
	6262,
	6135,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	6135,
	6135,
	6106,
	6135,
	6041,
	6106,
	6135,
	4344,
	4350,
	4344,
	6262,
	6262,
	6262,
	1339,
	6262,
	7070,
	6572,
	6573,
	953,
	6135,
	6041,
	6041,
	6041,
	10981,
	9480,
	9859,
	9859,
	7548,
	0,
	0,
	0,
	0,
	6135,
	6135,
	6041,
	4823,
	6262,
	2065,
	4350,
	2065,
	4350,
	4350,
	2672,
	6262,
	6262,
	4823,
	6262,
	2174,
	4894,
	6262,
	6041,
	6135,
	6262,
	6135,
	4894,
	6262,
	6041,
	6262,
	6135,
	6262,
	6135,
	4894,
	6262,
	6041,
	6262,
	6135,
	6262,
	6135,
	6262,
	6135,
	6262,
	6262,
	6262,
	6262,
	6262,
	6262,
	4921,
	6262,
	4921,
	6262,
	6262,
	6262,
	6262,
	6262,
	6262,
	4894,
	6262,
	6041,
	6135,
	6262,
	6135,
	6262,
	6262,
	6262,
	6262,
	6262,
	6262,
	4921,
	4921,
	4921,
	4350,
	6262,
	6262,
	6262,
	6262,
	4921,
	959,
	6262,
	6262,
	6262,
	4976,
	4921,
	4921,
	4921,
	6262,
	6262,
	6262,
	6262,
	6262,
	6135,
	6262,
	6262,
	6262,
	6262,
	6262,
	6262,
	4921,
	3451,
	6262,
	6135,
	6262,
	6135,
	4921,
	6135,
	4921,
	6262,
	6262,
	6262,
	4894,
	6262,
	6041,
	6135,
	6262,
	6135,
	4894,
	6262,
	6041,
	6135,
	6262,
	6135,
	4894,
	6262,
	6041,
	6135,
	6262,
	6135,
	4921,
	4921,
	4921,
	4921,
	4921,
	4921,
	4921,
	4921,
	6194,
	6041,
	6194,
	4976,
	6262,
	6262,
	6262,
	6262,
	4921,
	4350,
	2672,
	4350,
	4921,
	6262,
	6262,
	6262,
	1175,
	6262,
	6262,
	6262,
	6262,
	6262,
	6262,
	5165,
	6262,
	6262,
	6262,
	4350,
	4921,
	6262,
	6262,
	6135,
	4365,
	6262,
	808,
	4350,
	4350,
	2063,
	2063,
	6262,
	6262,
	1860,
	6262,
	953,
	215,
	6135,
	6262,
	4921,
	4894,
	6262,
	6041,
	6135,
	6262,
	6135,
	4894,
	6262,
	6041,
	6135,
	6262,
	6135,
	4894,
	6262,
	6041,
	6135,
	6262,
	6135,
	4894,
	6262,
	6041,
	6262,
	6135,
	6262,
	6135,
	4894,
	6262,
	6041,
	6262,
	6135,
	6262,
	6135,
	4894,
	6262,
	6041,
	6135,
	6262,
	6135,
	6262,
	6262,
	4921,
	4921,
	2422,
	6262,
	6262,
	4823,
	6262,
	6262,
	6135,
	4823,
	4823,
	6262,
	4976,
	4976,
	4921,
	4894,
	1304,
	6262,
	6262,
	6041,
	6262,
	4921,
	4921,
	6262,
	6262,
	4894,
	6262,
	6041,
	6135,
	6262,
	6135,
	2065,
	6135,
	6135,
	6135,
	4350,
	2065,
	6262,
	4894,
	6262,
	6041,
	6262,
	6135,
	6262,
	6135,
	6262,
	4921,
	4894,
	6262,
	6041,
	6135,
	6262,
	6135,
	6262,
	4921,
	4894,
	6262,
	6041,
	6135,
	6262,
	6135,
	6262,
	6262,
	6262,
	6262,
	6262,
	2663,
	6262,
	2672,
	3413,
	4921,
	4921,
	4921,
	4921,
	6262,
	6135,
	6262,
	6262,
	4894,
	6262,
	6041,
	6135,
	6262,
	6135,
	6262,
	6262,
	1352,
	4921,
	6262,
	6262,
	6262,
	6262,
	6262,
	6262,
	6262,
	6262,
	6262,
	6262,
	6262,
	6262,
	1371,
	6262,
	6262,
	6262,
	6262,
	6041,
	6135,
	6262,
	6262,
	6262,
	6262,
	6262,
	2672,
	6262,
	6041,
	4921,
	4921,
	4921,
	4921,
	6262,
	6262,
	6262,
	6262,
	6262,
	6262,
	6262,
	6262,
	6262,
	6262,
	6262,
	6262,
	6262,
	6262,
	6262,
	4921,
	4921,
	4921,
	6262,
	6135,
	6262,
	6262,
	4894,
	6262,
	6041,
	6135,
	6262,
	6135,
	6262,
	6262,
	6262,
	6262,
	1371,
	5125,
	5125,
	5125,
	5125,
	6262,
	6041,
	6262,
	6262,
	6262,
	6262,
	6262,
	6262,
	6135,
	6262,
	4921,
	6262,
	4894,
	6262,
	6041,
	6135,
	6262,
	6135,
	6262,
	6262,
	6262,
	6262,
	2672,
	6041,
	4921,
	4921,
	4921,
	4921,
	6262,
	6262,
	6262,
	6262,
	958,
	6262,
	4921,
	4921,
	6262,
	6262,
	6262,
	6135,
	6262,
	6262,
	6262,
	6262,
	6262,
	6262,
	6262,
	6262,
	6262,
	6262,
	4894,
	6262,
	6041,
	6135,
	6262,
	6135,
	6262,
	4921,
	4921,
	6262,
	6262,
	6262,
	6262,
	6262,
	2672,
	3413,
	4921,
	4921,
	4921,
	4921,
	6262,
	6262,
	6262,
	6262,
	6135,
	4823,
	6262,
	6262,
	4894,
	6262,
	6041,
	6135,
	6262,
	6135,
	6262,
	6262,
	6262,
	6262,
	6262,
	2672,
	3413,
	1253,
	4921,
	4921,
	4921,
	4921,
	6262,
	6262,
	6135,
	6262,
	6262,
	6262,
	6262,
	6262,
	6262,
	6262,
	4894,
	6262,
	6041,
	6135,
	6262,
	6135,
	6262,
	953,
	214,
	4815,
	6262,
	6262,
	6135,
	4921,
	4921,
	4921,
	6262,
	6262,
	6262,
	6262,
	6262,
	6262,
	6262,
	2672,
	446,
	214,
	6262,
	6262,
	6262,
	6262,
	6262,
	6041,
	6135,
	6262,
	6262,
	6262,
	6262,
	6262,
	2672,
	6262,
	6262,
	6262,
	6262,
	6262,
	6262,
	6262,
	6262,
	6262,
	6262,
	6262,
	6262,
	6262,
	6262,
	6262,
	6262,
	6262,
	6262,
	6262,
	6262,
	6262,
	6262,
	6262,
	4339,
	4921,
	6262,
	6041,
	6262,
	6262,
	4894,
	6262,
	6041,
	6135,
	6262,
	6135,
	6262,
	6262,
	6262,
	6262,
	6262,
	6262,
	6262,
	6262,
	6135,
	6262,
	6262,
	6262,
	6135,
	6135,
	6262,
	6262,
	6262,
	6262,
	6262,
	2065,
	5028,
	6135,
	6262,
	4894,
	6262,
	6041,
	6135,
	6262,
	6135,
	4894,
	6262,
	6041,
	6135,
	6262,
	6135,
	6262,
	6262,
	6262,
	6262,
	4823,
	4894,
	6041,
	6106,
	6262,
	6262,
	6262,
	6262,
	6262,
	6262,
	6262,
	6262,
	6135,
	1371,
	1175,
	6041,
	6135,
	6262,
	1595,
	2136,
	4350,
	4823,
	4823,
	4823,
	4823,
	6262,
	6041,
	6262,
	6262,
	6262,
	4894,
	6262,
	6041,
	6135,
	6262,
	6135,
	4894,
	6262,
	6041,
	6135,
	6262,
	6135,
	4894,
	6262,
	6041,
	6135,
	6262,
	6135,
	4894,
	6262,
	6041,
	6135,
	6262,
	6135,
	2091,
	2091,
	6262,
	951,
	6262,
	6262,
	6262,
	6135,
	4350,
	2065,
	6254,
	461,
	4409,
	2091,
	781,
	6135,
	1167,
	4350,
	6135,
	1194,
	6135,
	2065,
	2065,
	2069,
	6135,
	2079,
	6262,
	2672,
	6262,
	6262,
	6262,
	4894,
	6262,
	6041,
	6135,
	6262,
	6135,
	4894,
	6262,
	6041,
	6135,
	6262,
	6135,
	4894,
	6262,
	6041,
	6135,
	6262,
	6135,
	4894,
	6262,
	6041,
	6135,
	6262,
	6135,
	4894,
	6262,
	6041,
	6135,
	6262,
	6135,
	4894,
	6262,
	6041,
	6135,
	6262,
	6135,
	4894,
	6262,
	6041,
	6135,
	6262,
	6135,
	4894,
	6262,
	6041,
	6135,
	6262,
	6135,
	4894,
	6262,
	6041,
	6135,
	6262,
	6135,
	6262,
	6262,
	6135,
	6262,
	2065,
	2065,
	4921,
	4921,
	6254,
	6262,
	6135,
	6262,
	4921,
	4921,
	4921,
	6262,
	4823,
	6262,
	1371,
	6262,
	6262,
	6262,
	4894,
	6262,
	6041,
	6135,
	6262,
	6135,
	6262,
	2063,
	1167,
	4823,
	4823,
	4976,
	6026,
	6262,
	6262,
	6262,
	4894,
	6262,
	6041,
	6135,
	6262,
	6135,
	4894,
	6262,
	6041,
	6135,
	6262,
	6135,
	6262,
	6262,
	6262,
	6262,
	6041,
	6262,
	6262,
	6262,
	6262,
	6135,
	6262,
	4921,
	6262,
	6262,
	6262,
	6262,
	6262,
	4823,
	4976,
	4976,
	4823,
	4823,
	6041,
	6041,
	6262,
	4894,
	6262,
	6041,
	6135,
	6262,
	6135,
	6262,
	6262,
	6262,
	6262,
	4921,
	6135,
	6262,
	6262,
	6135,
	4350,
	6135,
	6262,
	6262,
	6262,
	6262,
	6262,
	6135,
	6262,
	6135,
	6262,
	6135,
	6262,
	4921,
	6262,
	6041,
	6106,
	6262,
	6262,
	6262,
	6262,
	5028,
	4949,
	4911,
	4894,
	6262,
	6041,
	6135,
	6262,
	6135,
	6262,
	6262,
	6262,
	384,
	798,
	1188,
	6262,
	6262,
	4976,
	6262,
	6262,
	6262,
	6262,
	6262,
	4921,
	4921,
	4921,
	4921,
	4921,
	4921,
	4921,
	4921,
	6262,
	6262,
	6262,
	6262,
	6262,
	2672,
	4921,
	2672,
	6262,
	4921,
	6135,
	6135,
	4903,
	4823,
	6262,
	6262,
	6262,
	6262,
	6262,
	10935,
	6262,
	6262,
	6262,
	6262,
	4921,
	4350,
	3451,
	6135,
	6135,
	4921,
	2672,
	0,
	4350,
	6135,
	6262,
	6262,
	6041,
	6262,
	6262,
	4894,
	6262,
	6041,
	6135,
	6262,
	6135,
	4894,
	6262,
	6041,
	6135,
	6262,
	6135,
	4894,
	6262,
	6041,
	6135,
	6262,
	6135,
	6262,
	3451,
	6262,
	6262,
	6262,
	6262,
	2658,
	6262,
	6262,
	6262,
	6262,
	6262,
	6135,
	4339,
	6262,
	6262,
	4976,
	6262,
	6262,
	6135,
	6262,
	6262,
	6262,
	6262,
	6262,
	6262,
	6262,
	6262,
	6262,
	6262,
	6262,
	6262,
	6262,
	964,
	6262,
	6262,
	6262,
	6262,
	6262,
	6262,
	6262,
	6262,
	6262,
	2707,
	4921,
	4921,
	6262,
	2672,
	6262,
	4921,
	4350,
	4921,
	2718,
	6262,
	6262,
	4921,
	6262,
	6262,
	4921,
	4921,
	4921,
	4921,
	4921,
	4921,
	4921,
	4921,
	4921,
	4921,
	4921,
	4921,
	3750,
	3750,
	6262,
	4921,
	4921,
	4921,
	4921,
	4921,
	4921,
	6262,
	2672,
	6262,
	6262,
	6262,
	6262,
	6041,
	6262,
	6262,
	6262,
	6262,
	6262,
	6262,
	6262,
	6262,
	4823,
	4976,
	4976,
	6262,
	6262,
	6262,
	6135,
	6262,
	4921,
	4921,
	1178,
	381,
	382,
	789,
	794,
	4921,
	6262,
	6262,
	6262,
	6262,
	6135,
	6135,
	6135,
	2078,
	4362,
	4362,
	6135,
	2080,
	2678,
	2066,
	4921,
	6262,
	4921,
	6262,
	2759,
	4921,
	6262,
	6262,
	4894,
	6262,
	6041,
	6135,
	6262,
	6135,
	4894,
	6262,
	6041,
	6135,
	6262,
	6135,
	4894,
	6262,
	6041,
	6135,
	6262,
	6135,
	4894,
	6262,
	6041,
	6135,
	6262,
	6135,
	0,
	4344,
	2443,
	4350,
	2672,
	6135,
	4921,
	6106,
	6041,
	6041,
	6041,
	6041,
	6041,
	6041,
	6041,
	4823,
	2672,
	4921,
	4350,
	4344,
	4350,
	6262,
	6135,
	6135,
	6135,
	3451,
	2065,
	6135,
	4344,
	0,
	0,
	6135,
	6349,
	6350,
	6058,
	4844,
	6106,
	4894,
	6194,
	4976,
	6041,
	4823,
	6107,
	4895,
	6250,
	5024,
	6135,
	6135,
	9859,
	9859,
	9850,
	9685,
	9869,
	9952,
	9855,
	9747,
	9856,
	9778,
	9873,
	10098,
	9846,
	9624,
	9809,
	8330,
	8330,
	3451,
	6106,
	10935,
	9859,
	8568,
	9859,
	6262,
	10977,
	6041,
	4625,
	4627,
	5897,
	6041,
	4625,
	4627,
	5157,
	6135,
	6041,
	6350,
	4625,
	4627,
	5157,
	6135,
	6041,
	6349,
	4921,
	5897,
	6135,
	6041,
	6262,
	6135,
	6262,
	6135,
	4894,
	6262,
	6041,
	6135,
	6262,
	6135,
	6135,
	6135,
	4894,
	6262,
	6041,
	6262,
	6262,
	6135,
	6262,
	6135,
	6135,
	6135,
	6041,
	4823,
	6106,
	6041,
	6348,
	4344,
	2443,
	4350,
	2672,
	6106,
	2672,
	4344,
	4350,
	6262,
	6135,
	6135,
	927,
	6262,
	4894,
	6262,
	6041,
	6262,
	6135,
	6262,
	6135,
	6135,
	6135,
	6041,
	4823,
	6106,
	6041,
	6348,
	4350,
	2672,
	4344,
	2443,
	6106,
	2672,
	4350,
	4344,
	4350,
	6262,
	6135,
	3451,
	2065,
	6135,
	927,
	6262,
	6262,
	3228,
	4894,
	6262,
	6041,
	6262,
	6135,
	6262,
	6135,
	6135,
	6135,
	6106,
	6041,
	6348,
	6135,
	4921,
	4921,
	6135,
	927,
	3451,
	6106,
	6262,
	6106,
	6041,
	6348,
	6135,
	4921,
	6058,
	4844,
	6107,
	4895,
	6250,
	5024,
	4844,
	4921,
	6135,
	927,
	9624,
	3451,
	6106,
	6262,
	6106,
	6041,
	6348,
	6135,
	4921,
	6041,
	4823,
	4823,
	4921,
	6135,
	927,
	3451,
	6106,
	6262,
	10935,
	6262,
	6106,
	6041,
	6348,
	6135,
	4921,
	6041,
	4823,
	6135,
	3451,
	6106,
	927,
	10977,
	6106,
	6348,
	4921,
	2672,
	0,
	4344,
	2443,
	4350,
	2672,
	4921,
	2672,
	8330,
	8330,
	3451,
	6106,
	6106,
	4894,
	6194,
	4976,
	6058,
	4844,
	6107,
	4895,
	6250,
	5024,
	6041,
	4823,
	6135,
	6135,
	927,
	9859,
	10017,
};
static const Il2CppTokenRangePair s_rgctxIndices[2] = 
{
	{ 0x06000487, { 0, 3 } },
	{ 0x0600062A, { 3, 1 } },
};
extern const uint32_t g_rgctx_T_t3E810D52BBC643E6F86E3E5BCBFE0864957D541D;
extern const uint32_t g_rgctx_T_t3E810D52BBC643E6F86E3E5BCBFE0864957D541D;
extern const Il2CppRGCTXConstrainedData g_rgctx_T_t3E810D52BBC643E6F86E3E5BCBFE0864957D541D_Object_ToString_mF8AC1EB9D85AB52EC8FD8B8BDD131E855E69673F;
extern const uint32_t g_rgctx_T_t13CA6E5FFB26617441B8E8F9AEE48D6EB182AE56;
static const Il2CppRGCTXDefinition s_rgctxValues[4] = 
{
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t3E810D52BBC643E6F86E3E5BCBFE0864957D541D },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_T_t3E810D52BBC643E6F86E3E5BCBFE0864957D541D },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_T_t3E810D52BBC643E6F86E3E5BCBFE0864957D541D_Object_ToString_mF8AC1EB9D85AB52EC8FD8B8BDD131E855E69673F },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t13CA6E5FFB26617441B8E8F9AEE48D6EB182AE56 },
};
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_AssemblyU2DCSharp_CodeGenModule;
const Il2CppCodeGenModule g_AssemblyU2DCSharp_CodeGenModule = 
{
	"Assembly-CSharp.dll",
	1605,
	s_methodPointers,
	26,
	s_adjustorThunks,
	s_InvokerIndices,
	0,
	NULL,
	2,
	s_rgctxIndices,
	4,
	s_rgctxValues,
	NULL,
	NULL, // module initializer,
	NULL,
	NULL,
	NULL,
};
