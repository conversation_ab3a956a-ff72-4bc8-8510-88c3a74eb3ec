using UnityEngine;
using System.Collections;

/// <summary>
/// VR装配调试器
/// 用于测试和调试VR装配系统的各个功能
/// </summary>
public class VRAssemblyDebugger : MonoBehaviour
{
    [Header("组件引用")]
    [SerializeField] private VRAssemblyPositioner positioner;
    [SerializeField] private Neo4jAssemblyController assemblyController;
    [SerializeField] private VRSimulator vrSimulator;

    [Header("测试设置")]
    [SerializeField] private bool enableDebugMode = true;
    [SerializeField] private bool showDetailedLogs = true;
    [SerializeField] private bool autoFindComponents = true;

    [Header("测试零件")]
    [SerializeField] private AssemblyPart testMovingPart;
    [SerializeField] private AssemblyPart testTargetPart;

    [Header("第二部分功能测试")]
    [SerializeField] private int targetReferencePointIndex = 0; // 目标参考点索引
    [SerializeField] private bool showDetailedAssemblyPointInfo = true; // 显示详细装配点信息
    [SerializeField] private bool useNegativeZAxis = true; // 是否让-Z轴(装配面)朝向摄像机

    [Header("快捷键设置")]
    [SerializeField] private KeyCode testPositioningKey = KeyCode.F6; // 改为F6避免与VR控制器冲突
    [SerializeField] private KeyCode testOrientationKey = KeyCode.F7; // 改为F7避免与VR控制器冲突
    [SerializeField] private KeyCode resetPositionKey = KeyCode.F8;
    [SerializeField] private KeyCode toggleDebugKey = KeyCode.F1;
    [SerializeField] private KeyCode testCameraBasedPositioningKey = KeyCode.F9; // 改为F9避免与VR控制器冲突

    void Start()
    {
        if (autoFindComponents)
        {
            FindComponents();
        }
        
        ValidateSetup();
        
        if (enableDebugMode)
        {
            Debug.Log("=== VR装配调试器启动 ===");
            LogSystemStatus();
        }
    }

    void Update()
    {
        HandleDebugInput();
    }

    /// <summary>
    /// 自动查找组件
    /// </summary>
    private void FindComponents()
    {
        if (positioner == null)
        {
            positioner = FindObjectOfType<VRAssemblyPositioner>();
            if (positioner != null)
            {
                Debug.Log("[VRAssemblyDebugger] 找到VRAssemblyPositioner组件");
            }
        }

        if (assemblyController == null)
        {
            assemblyController = FindObjectOfType<Neo4jAssemblyController>();
            if (assemblyController != null)
            {
                Debug.Log("[VRAssemblyDebugger] 找到Neo4jAssemblyController组件");
            }
        }

        if (vrSimulator == null)
        {
            vrSimulator = FindObjectOfType<VRSimulator>();
            if (vrSimulator != null)
            {
                Debug.Log("[VRAssemblyDebugger] 找到VRSimulator组件");
            }
        }

        // 查找测试零件
        if (testMovingPart == null || testTargetPart == null)
        {
            AssemblyPart[] parts = FindObjectsOfType<AssemblyPart>();
            if (parts.Length >= 2)
            {
                testMovingPart = parts[0];
                testTargetPart = parts[1];
                Debug.Log($"[VRAssemblyDebugger] 自动设置测试零件: {testMovingPart.PartName} -> {testTargetPart.PartName}");
            }
        }
    }

    /// <summary>
    /// 验证设置
    /// </summary>
    private void ValidateSetup()
    {
        bool hasErrors = false;

        if (positioner == null)
        {
            Debug.LogError("[VRAssemblyDebugger] ❌ 未找到VRAssemblyPositioner组件！");
            hasErrors = true;
        }

        if (assemblyController == null)
        {
            Debug.LogWarning("[VRAssemblyDebugger] ⚠️ 未找到Neo4jAssemblyController组件");
        }

        if (vrSimulator == null)
        {
            Debug.LogWarning("[VRAssemblyDebugger] ⚠️ 未找到VRSimulator组件");
        }

        if (testMovingPart == null || testTargetPart == null)
        {
            Debug.LogWarning("[VRAssemblyDebugger] ⚠️ 测试零件未设置，某些测试功能将不可用");
        }

        if (!hasErrors)
        {
            Debug.Log("[VRAssemblyDebugger] ✅ 组件验证通过");
        }
    }

    /// <summary>
    /// 记录系统状态
    /// </summary>
    private void LogSystemStatus()
    {
        Debug.Log("=== 系统状态检查 ===");
        
        if (positioner != null)
        {
            Debug.Log($"VRAssemblyPositioner: ✅ 已找到");
            
            // 检查positioner的关键设置
            var vrCamera = GetPrivateField(positioner, "vrCamera");
            var assemblyRoot = GetPrivateField(positioner, "assemblyRoot");
            
            Debug.Log($"  VR摄像机: {(vrCamera != null ? "✅ 已设置" : "❌ 未设置")}");
            Debug.Log($"  装配根节点: {(assemblyRoot != null ? "✅ 已设置" : "❌ 未设置")}");
        }

        if (vrSimulator != null)
        {
            Debug.Log($"VRSimulator: ✅ 已找到");
            Debug.Log($"  VR模拟: {(vrSimulator.enabled ? "✅ 启用" : "❌ 禁用")}");
        }

        Debug.Log("==================");
    }

    /// <summary>
    /// 处理调试输入
    /// </summary>
    private void HandleDebugInput()
    {
        if (Input.GetKeyDown(testPositioningKey))
        {
            TestPositioning();
        }

        if (Input.GetKeyDown(testCameraBasedPositioningKey))
        {
            TestCameraBasedPositioning();
        }

        if (Input.GetKeyDown(testOrientationKey))
        {
            TestOrientation();
        }

        if (Input.GetKeyDown(resetPositionKey))
        {
            ResetPosition();
        }

        if (Input.GetKeyDown(toggleDebugKey))
        {
            ToggleDebugMode();
        }
    }

    /// <summary>
    /// 测试定位功能（原有方法）
    /// </summary>
    [ContextMenu("测试定位功能")]
    public void TestPositioning()
    {
        if (positioner == null)
        {
            Debug.LogError("[VRAssemblyDebugger] VRAssemblyPositioner未找到！");
            return;
        }

        Debug.Log("=== 开始测试定位功能 ===");

        if (testMovingPart != null && testTargetPart != null)
        {
            Debug.Log($"测试零件: {testMovingPart.PartName} -> {testTargetPart.PartName}");

            // 调用定位方法
            StartCoroutine(positioner.AdjustOrientationForAssemblyStep(testMovingPart, testTargetPart));
        }
        else
        {
            Debug.Log("使用通用定位测试");
            StartCoroutine(positioner.MoveAssemblyToOptimalPosition());
        }
    }

    /// <summary>
    /// 测试基于摄像机的定位功能（第一部分功能）
    /// </summary>
    [ContextMenu("测试基于摄像机的定位")]
    public void TestCameraBasedPositioning()
    {
        if (positioner == null)
        {
            Debug.LogError("[VRAssemblyDebugger] VRAssemblyPositioner未找到！");
            return;
        }

        Debug.Log("=== 开始测试基于摄像机的定位功能 ===");
        Debug.Log("这将把装配区域移动到摄像机视野的固定位置");

        // 显示当前摄像机信息
        if (vrSimulator != null && vrSimulator.enabled)
        {
            var vrCamera = GetVRSimulatorCamera();
            if (vrCamera != null)
            {
                Debug.Log($"📷 当前VR摄像机位置: {vrCamera.transform.position}");
                Debug.Log($"📷 当前VR摄像机朝向: {vrCamera.transform.forward}");
                Debug.Log($"📷 当前VR摄像机旋转: {vrCamera.transform.rotation.eulerAngles}");
            }
        }
        else
        {
            var mainCamera = Camera.main;
            if (mainCamera != null)
            {
                Debug.Log($"📷 当前主摄像机位置: {mainCamera.transform.position}");
                Debug.Log($"📷 当前主摄像机朝向: {mainCamera.transform.forward}");
                Debug.Log($"📷 当前主摄像机旋转: {mainCamera.transform.rotation.eulerAngles}");
            }
        }

        // 调用基于摄像机的定位方法
        StartCoroutine(positioner.MoveAssemblyToOptimalPosition());

        Debug.Log("💡 提示：转动摄像机视角后再次按U键，观察装配区域是否移动到新的固定位置");
    }

    /// <summary>
    /// 获取VR模拟器的摄像机
    /// </summary>
    private Camera GetVRSimulatorCamera()
    {
        if (vrSimulator == null) return null;

        var method = vrSimulator.GetType().GetMethod("GetVRCamera");
        if (method != null)
        {
            return method.Invoke(vrSimulator, null) as Camera;
        }
        return null;
    }

    /// <summary>
    /// 测试朝向功能（第二部分功能）
    /// </summary>
    [ContextMenu("测试装配步骤朝向")]
    public void TestOrientation()
    {
        if (positioner == null)
        {
            Debug.LogError("[VRAssemblyDebugger] VRAssemblyPositioner未找到！");
            return;
        }

        Debug.Log("=== 开始测试第二部分功能：装配步骤朝向 ===");

        if (testMovingPart != null && testTargetPart != null)
        {
            Debug.Log($"测试装配步骤: {testMovingPart.PartName} -> {testTargetPart.PartName}");
            Debug.Log($"目标参考点索引: {targetReferencePointIndex}");
            Debug.Log("这将让目标零件的指定装配点Z轴朝向摄像机");

            // 验证参考点索引
            if (targetReferencePointIndex >= testTargetPart.ReferencePoints.Length)
            {
                Debug.LogError($"参考点索引 {targetReferencePointIndex} 超出范围！目标零件只有 {testTargetPart.ReferencePoints.Length} 个参考点");
                return;
            }

            // 显示当前摄像机信息
            ShowCurrentCameraInfo();

            // 显示目标零件的装配点信息
            if (showDetailedAssemblyPointInfo)
            {
                ShowTargetAssemblyPointInfo();
            }

            // 执行第二部分功能：只旋转装配区域使安装点朝向摄像机
            StartCoroutine(TestOnlyOrientationStep());
        }
        else
        {
            Debug.LogWarning("需要设置测试零件才能测试朝向功能");
            Debug.LogWarning("请在VRAssemblyDebugger中设置Test Moving Part和Test Target Part");
        }
    }

    /// <summary>
    /// 显示当前摄像机信息
    /// </summary>
    private void ShowCurrentCameraInfo()
    {
        Camera currentCamera = null;

        if (vrSimulator != null && vrSimulator.enabled)
        {
            currentCamera = GetVRSimulatorCamera();
        }
        else
        {
            currentCamera = Camera.main;
        }

        if (currentCamera != null)
        {
            Debug.Log($"📷 当前摄像机信息:");
            Debug.Log($"   位置: {currentCamera.transform.position}");
            Debug.Log($"   朝向: {currentCamera.transform.forward}");
            Debug.Log($"   旋转: {currentCamera.transform.rotation.eulerAngles}");
        }
    }

    /// <summary>
    /// 显示目标装配点信息
    /// </summary>
    private void ShowTargetAssemblyPointInfo()
    {
        if (testTargetPart == null) return;

        Debug.Log($"🎯 目标零件信息:");
        Debug.Log($"   零件名称: {testTargetPart.PartName}");

        // 显示所有参考点信息
        for (int i = 0; i < testTargetPart.ReferencePoints.Length; i++)
        {
            Transform refPoint = testTargetPart.GetReferencePoint(i);
            if (refPoint != null)
            {
                Debug.Log($"   参考点{i} ({refPoint.name}):");
                Debug.Log($"     位置: {refPoint.position}");
                Debug.Log($"     当前Z轴方向: {refPoint.forward}");
                Debug.Log($"     当前-Z轴方向: {-refPoint.forward}");

                // 计算与摄像机的关系
                Camera cam = Camera.main;
                if (cam != null)
                {
                    Vector3 toCamera = (cam.transform.position - refPoint.position).normalized;
                    float zAxisAlignment = Vector3.Dot(refPoint.forward, toCamera);
                    float negZAxisAlignment = Vector3.Dot(-refPoint.forward, toCamera);

                    Debug.Log($"     Z轴与摄像机方向对齐度: {zAxisAlignment:F3}");
                    Debug.Log($"     -Z轴与摄像机方向对齐度: {negZAxisAlignment:F3}");
                }
            }
        }
    }

    /// <summary>
    /// 只执行第二步：通过旋转装配区域使安装点朝向摄像机
    /// </summary>
    private IEnumerator TestOnlyOrientationStep()
    {
        Debug.Log("=== 只执行第二步：通过旋转装配区域使安装点朝向摄像机 ===");
        Debug.Log($"配置: 使用{(useNegativeZAxis ? "-Z轴(装配面)" : "Z轴(装配点背面)")}朝向摄像机");

        // 获取目标参考点
        Transform targetRefPoint = testTargetPart.GetReferencePoint(targetReferencePointIndex);
        if (targetRefPoint == null)
        {
            Debug.LogError($"无法获取参考点 {targetReferencePointIndex}");
            yield break;
        }

        // 获取摄像机
        Camera cam = Camera.main;
        if (vrSimulator != null && vrSimulator.enabled)
        {
            cam = GetVRSimulatorCamera();
        }

        if (cam == null)
        {
            Debug.LogError("无法找到摄像机");
            yield break;
        }

        Debug.Log($"🎯 目标零件: {testTargetPart.PartName}");
        Debug.Log($"📍 目标参考点: {targetRefPoint.name} (索引: {targetReferencePointIndex})");
        Debug.Log($"📷 摄像机: {cam.name}");

        // 直接执行第二步：通过旋转装配区域使安装点朝向摄像机
        yield return positioner.AlignMountPointToCameraByRotatingAssemblyRoot(
            testTargetPart, targetReferencePointIndex, cam.transform, useNegativeZAxis);

        Debug.Log("=== 第二步完成：装配区域旋转调整完成 ===");

        // 验证结果
        VerifyAssemblyPointOrientation();
    }

    /// <summary>
    /// 测试指定装配点的朝向调整（完整的两步流程）
    /// </summary>
    private IEnumerator TestSpecificAssemblyPointOrientation()
    {
        Debug.Log("=== 开始测试指定装配点朝向调整 ===");
        Debug.Log($"配置: 使用{(useNegativeZAxis ? "-Z轴(装配面)" : "Z轴(装配点背面)")}朝向摄像机");

        // 获取目标位置（使用第一部分的定位系统）
        Vector3 targetPosition = positioner.CalculateOptimalAssemblyPosition();

        // 获取目标参考点
        Transform targetRefPoint = testTargetPart.GetReferencePoint(targetReferencePointIndex);
        if (targetRefPoint == null)
        {
            Debug.LogError($"无法获取参考点 {targetReferencePointIndex}");
            yield break;
        }

        // 获取摄像机
        Camera cam = Camera.main;
        if (vrSimulator != null && vrSimulator.enabled)
        {
            cam = GetVRSimulatorCamera();
        }

        if (cam == null)
        {
            Debug.LogError("无法找到摄像机");
            yield break;
        }

        Debug.Log($"第一步：移动装配区域到最佳位置");
        Debug.Log($"  目标位置: {targetPosition}");

        // 第一步：移动装配区域到最佳位置（不改变旋转）
        // 获取当前装配根节点的旋转
        var assemblyRootField = positioner.GetType().GetField("assemblyRoot",
            System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
        Transform assemblyRoot = assemblyRootField?.GetValue(positioner) as Transform;
        Quaternion currentRotation = assemblyRoot != null ? assemblyRoot.rotation : Quaternion.identity;

        yield return positioner.MoveAssemblyToPosition(targetPosition, currentRotation);

        Debug.Log($"第二步：通过旋转装配区域使安装点朝向摄像机");

        // 第二步：使用正确的Test.cs方法 - 旋转装配区域而不是零件
        yield return positioner.AlignMountPointToCameraByRotatingAssemblyRoot(testTargetPart, targetReferencePointIndex, cam.transform, useNegativeZAxis);

        Debug.Log("=== 指定装配点朝向调整完成 ===");

        // 验证结果
        VerifyAssemblyPointOrientation();
    }

    /// <summary>
    /// 验证装配点朝向结果
    /// </summary>
    private void VerifyAssemblyPointOrientation()
    {
        if (testTargetPart == null) return;

        Transform targetRefPoint = testTargetPart.GetReferencePoint(targetReferencePointIndex);
        if (targetRefPoint == null) return;

        Camera cam = Camera.main;
        if (vrSimulator != null && vrSimulator.enabled)
        {
            cam = GetVRSimulatorCamera();
        }

        if (cam == null) return;

        Debug.Log("=== 验证装配点朝向结果 ===");

        // 详细的位置和方向信息
        Vector3 refPointPos = targetRefPoint.position;
        Vector3 cameraPos = cam.transform.position;
        Vector3 toCamera = (cameraPos - refPointPos).normalized;

        Debug.Log($"📍 详细位置信息:");
        Debug.Log($"   参考点位置: {refPointPos}");
        Debug.Log($"   摄像机位置: {cameraPos}");
        Debug.Log($"   距离: {Vector3.Distance(refPointPos, cameraPos):F2}m");
        Debug.Log($"   到摄像机方向: {toCamera}");

        Debug.Log($"🧭 参考点当前朝向:");
        Debug.Log($"   参考点旋转: {targetRefPoint.rotation.eulerAngles}");
        Debug.Log($"   Z轴方向(forward): {targetRefPoint.forward}");
        Debug.Log($"   -Z轴方向(back): {-targetRefPoint.forward}");
        Debug.Log($"   Y轴方向(up): {targetRefPoint.up}");
        Debug.Log($"   X轴方向(right): {targetRefPoint.right}");

        float zAxisAlignment = Vector3.Dot(targetRefPoint.forward, toCamera);
        float negZAxisAlignment = Vector3.Dot(-targetRefPoint.forward, toCamera);

        Debug.Log($"📊 朝向验证结果:");
        Debug.Log($"   参考点: {targetRefPoint.name}");
        Debug.Log($"   Z轴与摄像机方向对齐度: {zAxisAlignment:F3}");
        Debug.Log($"   -Z轴与摄像机方向对齐度: {negZAxisAlignment:F3}");

        // 根据配置判断成功标准
        Debug.Log($"📈 配置分析:");
        Debug.Log($"   期望朝向: {(useNegativeZAxis ? "-Z轴(装配面)朝向摄像机" : "Z轴(装配点背面)朝向摄像机")}");

        float targetAlignment = useNegativeZAxis ? negZAxisAlignment : zAxisAlignment;
        string targetAxis = useNegativeZAxis ? "-Z轴" : "Z轴";

        Debug.Log($"   {targetAxis}对齐度: {targetAlignment:F3}");

        if (targetAlignment > 0.95f)
        {
            Debug.Log($"   ✅ {targetAxis}朝向摄像机成功！对齐度优秀");
        }
        else if (targetAlignment > 0.8f)
        {
            Debug.Log($"   ⚠️ {targetAxis}朝向摄像机基本成功，但有轻微偏差");
        }
        else if (targetAlignment > 0.5f)
        {
            Debug.Log($"   ⚠️ {targetAxis}朝向摄像机部分成功，偏差较大");
        }
        else
        {
            Debug.Log($"   ❌ {targetAxis}朝向摄像机失败，对齐度不足");
        }

        // 额外信息：显示另一个轴的情况
        float otherAlignment = useNegativeZAxis ? zAxisAlignment : negZAxisAlignment;
        string otherAxis = useNegativeZAxis ? "Z轴" : "-Z轴";
        Debug.Log($"   {otherAxis}对齐度: {otherAlignment:F3} (参考信息)");

        // 总体评估
        if (targetAlignment > 0.95f)
        {
            Debug.Log("   🎯 总体评估: 优秀！装配点朝向非常准确");
        }
        else if (targetAlignment > 0.8f)
        {
            Debug.Log("   🎯 总体评估: 良好！装配点朝向基本准确");
        }
        else if (targetAlignment > 0.5f)
        {
            Debug.Log("   🎯 总体评估: 一般！装配点朝向有明显偏差");
        }
        else
        {
            Debug.Log("   🎯 总体评估: 需要改进！装配点朝向偏差很大");
        }

        Debug.Log("=== 验证完成 ===");
    }

    /// <summary>
    /// 诊断朝向问题
    /// </summary>
    [ContextMenu("诊断朝向问题")]
    public void DiagnoseOrientationIssue()
    {
        if (testTargetPart == null)
        {
            Debug.LogError("请先设置Test Target Part");
            return;
        }

        Transform targetRefPoint = testTargetPart.GetReferencePoint(targetReferencePointIndex);
        if (targetRefPoint == null)
        {
            Debug.LogError($"无法获取参考点 {targetReferencePointIndex}");
            return;
        }

        Debug.Log("=== 朝向问题诊断开始 ===");

        // 记录旋转前的状态
        Vector3 beforeRotation = targetRefPoint.rotation.eulerAngles;
        Vector3 beforeForward = targetRefPoint.forward;

        Debug.Log($"🔍 旋转前状态:");
        Debug.Log($"   参考点旋转: {beforeRotation}");
        Debug.Log($"   Z轴方向: {beforeForward}");

        // 获取摄像机
        Camera cam = Camera.main;
        if (vrSimulator != null && vrSimulator.enabled)
        {
            cam = GetVRSimulatorCamera();
        }

        if (cam == null)
        {
            Debug.LogError("无法找到摄像机");
            return;
        }

        // 手动计算应该的旋转
        Vector3 toCamera = (cam.transform.position - targetRefPoint.position).normalized;
        Debug.Log($"📷 到摄像机方向: {toCamera}");

        // 计算期望的旋转
        Quaternion expectedRotation;
        if (useNegativeZAxis)
        {
            expectedRotation = Quaternion.LookRotation(-toCamera, Vector3.up);
            Debug.Log($"🎯 期望-Z轴朝向摄像机");
        }
        else
        {
            expectedRotation = Quaternion.LookRotation(toCamera, Vector3.up);
            Debug.Log($"🎯 期望Z轴朝向摄像机");
        }

        Debug.Log($"🔄 期望旋转: {expectedRotation.eulerAngles}");

        // 直接应用旋转到参考点（测试用）
        Debug.Log($"🧪 直接应用旋转到参考点进行测试...");
        targetRefPoint.rotation = expectedRotation;

        // 验证直接旋转的效果
        Vector3 afterForward = targetRefPoint.forward;
        float directZAlignment = Vector3.Dot(afterForward, toCamera);
        float directNegZAlignment = Vector3.Dot(-afterForward, toCamera);

        Debug.Log($"📊 直接旋转后的对齐度:");
        Debug.Log($"   Z轴对齐度: {directZAlignment:F3}");
        Debug.Log($"   -Z轴对齐度: {directNegZAlignment:F3}");

        if (useNegativeZAxis)
        {
            if (directNegZAlignment > 0.95f)
            {
                Debug.Log($"   ✅ 直接旋转成功！-Z轴正确朝向摄像机");
                Debug.Log($"   💡 问题可能在于装配根节点的旋转没有正确传递到参考点");
            }
            else
            {
                Debug.Log($"   ❌ 直接旋转也失败，可能是计算逻辑问题");
            }
        }
        else
        {
            if (directZAlignment > 0.95f)
            {
                Debug.Log($"   ✅ 直接旋转成功！Z轴正确朝向摄像机");
                Debug.Log($"   💡 问题可能在于装配根节点的旋转没有正确传递到参考点");
            }
            else
            {
                Debug.Log($"   ❌ 直接旋转也失败，可能是计算逻辑问题");
            }
        }

        Debug.Log("=== 诊断完成 ===");
        Debug.Log("💡 建议：如果直接旋转成功，说明问题在于装配根节点的旋转传递；如果直接旋转也失败，说明计算逻辑需要调整");
    }

    /// <summary>
    /// 重置位置
    /// </summary>
    [ContextMenu("重置位置")]
    public void ResetPosition()
    {
        if (positioner == null)
        {
            Debug.LogError("[VRAssemblyDebugger] VRAssemblyPositioner未找到！");
            return;
        }

        Debug.Log("=== 重置装配区域位置 ===");
        StartCoroutine(positioner.RestoreOriginalPosition());
    }

    /// <summary>
    /// 切换调试模式
    /// </summary>
    [ContextMenu("切换调试模式")]
    public void ToggleDebugMode()
    {
        enableDebugMode = !enableDebugMode;
        Debug.Log($"[VRAssemblyDebugger] 调试模式: {(enableDebugMode ? "启用" : "禁用")}");
        
        if (enableDebugMode)
        {
            LogSystemStatus();
        }
    }

    /// <summary>
    /// 强制初始化VRAssemblyPositioner
    /// </summary>
    [ContextMenu("强制初始化定位器")]
    public void ForceInitializePositioner()
    {
        if (positioner == null)
        {
            Debug.LogError("[VRAssemblyDebugger] VRAssemblyPositioner未找到！");
            return;
        }

        Debug.Log("=== 强制初始化VRAssemblyPositioner ===");
        
        // 调用私有的初始化方法
        var initMethod = positioner.GetType().GetMethod("InitializePositioner", 
            System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
        
        if (initMethod != null)
        {
            initMethod.Invoke(positioner, null);
            Debug.Log("✅ 初始化完成");
        }
        else
        {
            Debug.LogError("❌ 无法找到初始化方法");
        }
        
        LogSystemStatus();
    }

    /// <summary>
    /// 获取私有字段值
    /// </summary>
    private object GetPrivateField(object obj, string fieldName)
    {
        var field = obj.GetType().GetField(fieldName,
            System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
        return field?.GetValue(obj);
    }

    /// <summary>
    /// 设置装配区域相对位置
    /// </summary>
    public void SetRelativePosition(Vector3 newPosition)
    {
        if (positioner == null)
        {
            Debug.LogError("[VRAssemblyDebugger] VRAssemblyPositioner未找到！");
            return;
        }

        // 使用反射设置私有字段
        var field = positioner.GetType().GetField("relativePosition",
            System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);

        if (field != null)
        {
            field.SetValue(positioner, newPosition);
            Debug.Log($"[VRAssemblyDebugger] 设置相对位置为: {newPosition}");

            // 立即测试新位置
            TestCameraBasedPositioning();
        }
        else
        {
            Debug.LogError("[VRAssemblyDebugger] 无法找到relativePosition字段");
        }
    }


    private GUIStyle EditorGUIStyle()
    {
        GUIStyle style = new GUIStyle(GUI.skin.label);
        style.normal.textColor = Color.white;
        style.fontSize = 12;
        return style;
    }
}
