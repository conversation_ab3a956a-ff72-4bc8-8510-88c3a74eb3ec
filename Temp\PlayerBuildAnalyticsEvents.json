{"player_build_data": [{"type": "il2cppData", "msg": {"build_event_id": "e87fcaab6979e12449ec2bce2ff9f520", "node_executed": false, "attribute_total_count_eager_static_constructor": 79, "attribute_total_count_set_option": 0, "attribute_total_count_generate_into_own_cpp_file": 0, "extra_types_total_count": 0, "option_extra_types_file_count": 0, "option_jobs": 28, "option_debug_assembly_name_count": 0, "option_additional_cpp_count": 0, "option_emit_null_checks": true, "option_enable_stacktrace": false, "option_enable_deep_profiler": false, "option_enable_stats": false, "option_enable_array_bounds_check": true, "option_enable_divide_by_zero_check": false, "option_disable_generic_sharing": false, "option_maximum_recursive_generic_depth": 7, "option_generic_virtual_method_iterations": 1, "option_code_generation_option": ["EnableInlining"], "option_file_generation_option": [], "option_generics_option": [], "option_feature": [], "option_diagnostic_option": [], "option_convert_to_cpp": true, "option_compile_cpp": false, "option_development_mode": false, "option_enable_debugger": false, "option_generate_usym_file": false}, "version": 1}, {"type": "playerBuildTimingData", "msg": {"build_event_id": "e87fcaab6979e12449ec2bce2ff9f520", "build_player": 20877, "preprocess_player": 73, "produce_player_script_assemblies": 1993, "build_scripts_dlls": 573, "writing_asset_files": 467, "postprocess_built_player": 11089, "node_summary_table": []}, "version": 1}, {"type": "unityLinkerData", "msg": {"build_event_id": "e87fcaab6979e12449ec2bce2ff9f520", "node_executed": false, "attribute_marked_count_always_link_assembly": 0, "attribute_swept_count_always_link_assembly": 1, "attribute_total_count_always_link_assembly": 1, "attribute_marked_count_preserve": 163, "attribute_total_count_preserve": 101, "attribute_total_count_preserve_body": 0, "attribute_marked_count_required_member": 0, "attribute_swept_count_required_member": 8, "attribute_total_count_required_member": 8, "attribute_marked_count_require_derived": 0, "attribute_swept_count_require_derived": 0, "attribute_total_count_require_derived": 0, "attribute_marked_count_require_implementors": 0, "attribute_swept_count_require_implementors": 0, "attribute_total_count_require_implementors": 0, "attribute_total_count_required_interface": 0, "attribute_marked_count_require_attribute_usages": 0, "attribute_total_count_require_attribute_usages": 0, "attribute_total_count_dynamic_dependency": 0, "attribute_marked_count_dynamic_dependency": 11, "attribute_swept_count_dynamic_dependency": -11, "assembly_counts_total_in": 100, "assembly_counts_link": 34, "assembly_counts_copy": 14, "assembly_counts_delete": 52, "assembly_counts_total_out": 48, "unresolved_stubbing_total_count": 0, "unresolved_stubbing_missing_interface_method_count": 0, "unresolved_stubbing_missing_abstract_class_method_count": 0, "unresolved_stubbing_missing_type_count": 0, "unresolved_stubbing_missing_method_count": 0, "unrecognized_reflection_access_total_count": 265, "unrecognized_reflection_access_core_count": 161, "unrecognized_reflection_access_unity_count": 100, "unrecognized_reflection_access_user_count": 4, "recognized_reflection_access_total_count": 41, "recognized_reflection_access_core_count": 29, "recognized_reflection_access_unity_count": 2, "recognized_reflection_access_user_count": 10, "link_xml_total_count": 11, "link_xml_embedded_count": 1, "link_xml_embedded_unity_count": 0, "link_xml_embedded_user_count": 0, "link_xml_file_count": 10, "link_xml_assembly_preserve_all_total_count": 0, "link_xml_assembly_preserve_all_unity_count": 0, "link_xml_assembly_preserve_all_core_count": 0, "link_xml_assembly_preserve_all_user_count": 0, "engine_module_total_in": 65, "engine_module_deleted": 36, "engine_module_total_out": 29, "option_rule_set": "Minimal", "option_enable_report": false, "option_enable_snapshot": false, "option_enable_engine_module_stripping": true, "option_unity_root_strategy": "AllNonEngineAndNonClassLibraries", "option_enable_ildump": false}, "version": 1}]}