using UnityEngine;
using UnityEngine.UI;
using TMPro;

/// <summary>
/// VR开始菜单调试器
/// 
/// 用于调试VR开始菜单的各种问题
/// 包括输入框显示、用户名验证、按钮响应等
/// </summary>
public class VRStartMenuDebugger : MonoBehaviour
{
    [Header("调试设置")]
    [SerializeField] private bool enableDebugLogs = true;
    [SerializeField] private bool autoFix = true;
    
    [Header("组件引用")]
    [SerializeField] private VRStartMenuManager menuManager;
    [SerializeField] private TMP_InputField usernameInput;
    [SerializeField] private Button startButton;
    
    void Start()
    {
        if (enableDebugLogs)
        {
            StartCoroutine(InitializeDebugger());
        }
    }
    
    /// <summary>
    /// 初始化调试器
    /// </summary>
    private System.Collections.IEnumerator InitializeDebugger()
    {
        // 等待一帧确保所有组件都已加载
        yield return null;
        
        Debug.Log("[VRStartMenuDebugger] 开始调试VR开始菜单");
        
        // 查找组件
        FindComponents();
        
        // 检查组件状态
        CheckComponentStatus();
        
        // 自动修复问题
        if (autoFix)
        {
            AutoFixIssues();
        }
        
        // 设置调试监听
        SetupDebugListeners();
        
        Debug.Log("[VRStartMenuDebugger] 调试器初始化完成");
        Debug.Log("调试快捷键：");
        Debug.Log("F5 - 检查所有组件状态");
        Debug.Log("F6 - 修复输入框颜色");
        Debug.Log("F7 - 测试用户名验证");
        Debug.Log("F8 - 模拟按钮点击");
    }
    
    /// <summary>
    /// 查找组件
    /// </summary>
    private void FindComponents()
    {
        // 查找菜单管理器
        if (menuManager == null)
        {
            menuManager = FindObjectOfType<VRStartMenuManager>();
        }
        
        // 查找输入框
        if (usernameInput == null)
        {
            usernameInput = FindObjectOfType<TMP_InputField>();
        }
        
        // 查找开始按钮
        if (startButton == null)
        {
            startButton = FindObjectOfType<Button>();
        }
        
        Debug.Log($"[VRStartMenuDebugger] 组件查找结果:");
        Debug.Log($"  菜单管理器: {(menuManager != null ? "找到" : "未找到")}");
        Debug.Log($"  输入框: {(usernameInput != null ? "找到" : "未找到")}");
        Debug.Log($"  开始按钮: {(startButton != null ? "找到" : "未找到")}");
    }
    
    /// <summary>
    /// 检查组件状态
    /// </summary>
    private void CheckComponentStatus()
    {
        Debug.Log("=== 组件状态检查 ===");
        
        // 检查输入框
        if (usernameInput != null)
        {
            Debug.Log($"输入框状态:");
            Debug.Log($"  名称: {usernameInput.name}");
            Debug.Log($"  激活: {usernameInput.gameObject.activeInHierarchy}");
            Debug.Log($"  可交互: {usernameInput.interactable}");
            Debug.Log($"  当前文本: '{usernameInput.text}'");
            
            if (usernameInput.textComponent != null)
            {
                Debug.Log($"  文本组件颜色: {usernameInput.textComponent.color}");
                Debug.Log($"  文本组件字体大小: {usernameInput.textComponent.fontSize}");
            }
            else
            {
                Debug.LogError("  文本组件缺失！");
            }
            
            if (usernameInput.placeholder != null)
            {
                var placeholder = usernameInput.placeholder.GetComponent<TextMeshProUGUI>();
                if (placeholder != null)
                {
                    Debug.Log($"  占位符文本: '{placeholder.text}'");
                    Debug.Log($"  占位符颜色: {placeholder.color}");
                }
            }
            else
            {
                Debug.LogWarning("  占位符组件缺失！");
            }
        }
        else
        {
            Debug.LogError("输入框未找到！");
        }
        
        // 检查开始按钮
        if (startButton != null)
        {
            Debug.Log($"开始按钮状态:");
            Debug.Log($"  名称: {startButton.name}");
            Debug.Log($"  激活: {startButton.gameObject.activeInHierarchy}");
            Debug.Log($"  可交互: {startButton.interactable}");
            Debug.Log($"  监听器数量: {startButton.onClick.GetPersistentEventCount()}");
        }
        else
        {
            Debug.LogError("开始按钮未找到！");
        }
        
        // 检查菜单管理器
        if (menuManager != null)
        {
            Debug.Log($"菜单管理器状态:");
            Debug.Log($"  名称: {menuManager.name}");
            Debug.Log($"  激活: {menuManager.gameObject.activeInHierarchy}");
            Debug.Log($"  启用: {menuManager.enabled}");
        }
        else
        {
            Debug.LogError("菜单管理器未找到！");
        }
        
        Debug.Log("=== 状态检查完成 ===");
    }
    
    /// <summary>
    /// 自动修复问题
    /// </summary>
    private void AutoFixIssues()
    {
        Debug.Log("[VRStartMenuDebugger] 开始自动修复问题");
        
        // 修复输入框颜色
        FixInputFieldColors();
        
        // 确保事件监听
        EnsureEventListeners();
        
        Debug.Log("[VRStartMenuDebugger] 自动修复完成");
    }
    
    /// <summary>
    /// 修复输入框颜色
    /// </summary>
    private void FixInputFieldColors()
    {
        if (usernameInput == null) return;
        
        // 设置文本颜色为白色（在深色背景下可见）
        if (usernameInput.textComponent != null)
        {
            usernameInput.textComponent.color = Color.white;
            Debug.Log("[VRStartMenuDebugger] 设置输入框文本颜色为白色");
        }
        
        // 设置占位符颜色为浅灰色
        if (usernameInput.placeholder != null)
        {
            var placeholder = usernameInput.placeholder.GetComponent<TextMeshProUGUI>();
            if (placeholder != null)
            {
                placeholder.color = new Color(0.7f, 0.7f, 0.7f, 1f);
                Debug.Log("[VRStartMenuDebugger] 设置占位符颜色为浅灰色");
            }
        }
        
        // 确保输入框背景有足够的对比度
        var backgroundImage = usernameInput.GetComponent<Image>();
        if (backgroundImage != null)
        {
            // 如果背景太浅，设置为深色
            if (backgroundImage.color.r + backgroundImage.color.g + backgroundImage.color.b > 1.5f)
            {
                backgroundImage.color = new Color(0.2f, 0.2f, 0.2f, 0.8f);
                Debug.Log("[VRStartMenuDebugger] 设置输入框背景为深色");
            }
        }
    }
    
    /// <summary>
    /// 确保事件监听
    /// </summary>
    private void EnsureEventListeners()
    {
        if (usernameInput != null && menuManager != null)
        {
            // 移除现有监听器
            usernameInput.onValueChanged.RemoveAllListeners();
            
            // 添加调试监听器
            usernameInput.onValueChanged.AddListener(OnDebugUsernameChanged);
            
            Debug.Log("[VRStartMenuDebugger] 添加调试用户名监听器");
        }
        
        if (startButton != null)
        {
            // 添加调试监听器（不移除原有的）
            startButton.onClick.AddListener(OnDebugStartButtonClicked);
            
            Debug.Log("[VRStartMenuDebugger] 添加调试按钮监听器");
        }
    }
    
    /// <summary>
    /// 设置调试监听
    /// </summary>
    private void SetupDebugListeners()
    {
        if (menuManager != null)
        {
            menuManager.OnUsernameChanged += OnUsernameChangedDebug;
            menuManager.OnStartButtonClicked += OnStartButtonClickedDebug;
        }
    }
    
    /// <summary>
    /// 调试用户名改变事件
    /// </summary>
    private void OnDebugUsernameChanged(string username)
    {
        Debug.Log($"[VRStartMenuDebugger] 用户名输入改变: '{username}'");
        Debug.Log($"  长度: {username.Length}");
        Debug.Log($"  是否为空: {string.IsNullOrWhiteSpace(username)}");
    }
    
    /// <summary>
    /// 调试按钮点击事件
    /// </summary>
    private void OnDebugStartButtonClicked()
    {
        Debug.Log("[VRStartMenuDebugger] 开始按钮被点击（调试监听器）");
        
        if (usernameInput != null)
        {
            string currentText = usernameInput.text;
            Debug.Log($"  当前输入框文本: '{currentText}'");
            Debug.Log($"  文本是否为空: {string.IsNullOrWhiteSpace(currentText)}");
        }
    }
    
    /// <summary>
    /// 菜单管理器用户名改变事件
    /// </summary>
    private void OnUsernameChangedDebug(string username)
    {
        Debug.Log($"[VRStartMenuDebugger] 菜单管理器用户名改变: '{username}'");
    }
    
    /// <summary>
    /// 菜单管理器开始按钮点击事件
    /// </summary>
    private void OnStartButtonClickedDebug()
    {
        Debug.Log("[VRStartMenuDebugger] 菜单管理器开始按钮点击事件");
    }
    
    void Update()
    {
        if (!enableDebugLogs) return;
        
        // 调试快捷键
        if (Input.GetKeyDown(KeyCode.F5))
        {
            CheckComponentStatus();
        }
        else if (Input.GetKeyDown(KeyCode.F6))
        {
            FixInputFieldColors();
        }
        else if (Input.GetKeyDown(KeyCode.F7))
        {
            TestUsernameValidation();
        }
        else if (Input.GetKeyDown(KeyCode.F8))
        {
            SimulateButtonClick();
        }
    }
    
    /// <summary>
    /// 测试用户名验证
    /// </summary>
    [ContextMenu("测试用户名验证")]
    public void TestUsernameValidation()
    {
        if (usernameInput == null) return;
        
        string[] testCases = { "", "   ", "张三", "test", "用户123" };
        
        Debug.Log("=== 用户名验证测试 ===");
        
        foreach (string testCase in testCases)
        {
            usernameInput.text = testCase;
            usernameInput.onValueChanged.Invoke(testCase);
            
            bool isEmpty = string.IsNullOrWhiteSpace(testCase);
            Debug.Log($"测试用例: '{testCase}' -> 是否为空: {isEmpty}");
        }
        
        Debug.Log("=== 测试完成 ===");
    }
    
    /// <summary>
    /// 模拟按钮点击
    /// </summary>
    [ContextMenu("模拟按钮点击")]
    public void SimulateButtonClick()
    {
        if (startButton != null)
        {
            Debug.Log("[VRStartMenuDebugger] 模拟按钮点击");
            startButton.onClick.Invoke();
        }
        else
        {
            Debug.LogWarning("[VRStartMenuDebugger] 开始按钮未找到，无法模拟点击");
        }
    }
    
    void OnDestroy()
    {
        // 清理事件监听
        if (menuManager != null)
        {
            menuManager.OnUsernameChanged -= OnUsernameChangedDebug;
            menuManager.OnStartButtonClicked -= OnStartButtonClickedDebug;
        }
    }
}
