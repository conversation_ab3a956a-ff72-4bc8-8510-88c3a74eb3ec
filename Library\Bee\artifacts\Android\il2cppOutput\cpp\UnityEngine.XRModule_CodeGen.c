﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





// 0x00000001 System.Void Microsoft.CodeAnalysis.EmbeddedAttribute::.ctor()
extern void EmbeddedAttribute__ctor_m3FF623BFEE0DB7F6FD99E1EAFE20C5581224D314 (void);
// 0x00000002 System.Void System.Runtime.CompilerServices.IsReadOnlyAttribute::.ctor()
extern void IsReadOnlyAttribute__ctor_m7C0ECD764E09B041BA7629AC5C020D9972AC8697 (void);
// 0x00000003 System.Void UnityEngine.XR.XRNodeState::set_uniqueID(System.UInt64)
extern void XRNodeState_set_uniqueID_m1C42BE763CEB5BE66EECE54288DE28D30CCB085F (void);
// 0x00000004 UnityEngine.XR.XRNode UnityEngine.XR.XRNodeState::get_nodeType()
extern void XRNodeState_get_nodeType_mFC49286B25FF7732CE6CAF231A1DE5C083C13932 (void);
// 0x00000005 System.Void UnityEngine.XR.XRNodeState::set_nodeType(UnityEngine.XR.XRNode)
extern void XRNodeState_set_nodeType_m17D747D9C558277596BD29F59FD8FDEE1A892FF4 (void);
// 0x00000006 System.Void UnityEngine.XR.XRNodeState::set_tracked(System.Boolean)
extern void XRNodeState_set_tracked_m5B448272E0E14001A16A150EE7B602ADE1D85A45 (void);
// 0x00000007 System.Boolean UnityEngine.XR.XRNodeState::TryGetPosition(UnityEngine.Vector3&)
extern void XRNodeState_TryGetPosition_mC17C5DFEEA4062A6D91D7E252DD108829AC4E0A6 (void);
// 0x00000008 System.Boolean UnityEngine.XR.XRNodeState::TryGetRotation(UnityEngine.Quaternion&)
extern void XRNodeState_TryGetRotation_mA7F9597BE3AFE747BF2F95BF491779520FEF7301 (void);
// 0x00000009 System.Boolean UnityEngine.XR.XRNodeState::TryGet(UnityEngine.Vector3,UnityEngine.XR.AvailableTrackingData,UnityEngine.Vector3&)
extern void XRNodeState_TryGet_m6C1D3FC5E773BC5E134F37815FB08839489F5630 (void);
// 0x0000000A System.Boolean UnityEngine.XR.XRNodeState::TryGet(UnityEngine.Quaternion,UnityEngine.XR.AvailableTrackingData,UnityEngine.Quaternion&)
extern void XRNodeState_TryGet_m3EC1CBB3B6B823611B82073139949C041CE7C616 (void);
// 0x0000000B System.Void UnityEngine.XR.InputTracking::add_trackingAcquired(System.Action`1<UnityEngine.XR.XRNodeState>)
extern void InputTracking_add_trackingAcquired_mB4BFD78603798E38C0915B6E9E49DBF2E3EEE3E4 (void);
// 0x0000000C System.Void UnityEngine.XR.InputTracking::remove_trackingAcquired(System.Action`1<UnityEngine.XR.XRNodeState>)
extern void InputTracking_remove_trackingAcquired_m610A04BE4915CA2FA429810C607C42CF24792816 (void);
// 0x0000000D System.Void UnityEngine.XR.InputTracking::InvokeTrackingEvent(UnityEngine.XR.InputTracking/TrackingStateEventType,UnityEngine.XR.XRNode,System.Int64,System.Boolean)
extern void InputTracking_InvokeTrackingEvent_mA218CBE5D81A639B9C9A084A5360FEAD4625C42C (void);
// 0x0000000E System.Void UnityEngine.XR.InputTracking::GetNodeStates(System.Collections.Generic.List`1<UnityEngine.XR.XRNodeState>)
extern void InputTracking_GetNodeStates_mA2E8D154A47C817ED74AD42F6B38A9C906A57A67 (void);
// 0x0000000F System.Void UnityEngine.XR.InputTracking::GetNodeStates_Internal(System.Collections.Generic.List`1<UnityEngine.XR.XRNodeState>)
extern void InputTracking_GetNodeStates_Internal_m6E67CE5EC9C950ED633AEC42F8BD5B15EFE3B916 (void);
// 0x00000010 System.UInt64 UnityEngine.XR.InputTracking::GetDeviceIdAtXRNode(UnityEngine.XR.XRNode)
extern void InputTracking_GetDeviceIdAtXRNode_mF6AA8F79B81E685890712AE72FADC4D1113CDECC (void);
// 0x00000011 System.UInt32 UnityEngine.XR.HapticCapabilities::get_numChannels()
extern void HapticCapabilities_get_numChannels_m23871C9860B4A1F6E22DE4579A7550A7A9382BA9 (void);
// 0x00000012 System.Boolean UnityEngine.XR.HapticCapabilities::get_supportsImpulse()
extern void HapticCapabilities_get_supportsImpulse_m855193672304BD935913E215B690B14952E0C59C (void);
// 0x00000013 System.Boolean UnityEngine.XR.HapticCapabilities::get_supportsBuffer()
extern void HapticCapabilities_get_supportsBuffer_mE87BCD83AA4FEE3C89EE906EF2F1B6DFA9B7BA59 (void);
// 0x00000014 System.UInt32 UnityEngine.XR.HapticCapabilities::get_bufferFrequencyHz()
extern void HapticCapabilities_get_bufferFrequencyHz_m31270DBF2491B9FDC969D0082ABB5CF0F0DFDBAA (void);
// 0x00000015 System.UInt32 UnityEngine.XR.HapticCapabilities::get_bufferMaxSize()
extern void HapticCapabilities_get_bufferMaxSize_mAC7A86B40B716A2B9B17D4B3B26EE6B7D0346C7C (void);
// 0x00000016 System.UInt32 UnityEngine.XR.HapticCapabilities::get_bufferOptimalSize()
extern void HapticCapabilities_get_bufferOptimalSize_m166A6DD9882CD48F702FE7447E87C60AAF479407 (void);
// 0x00000017 System.Boolean UnityEngine.XR.HapticCapabilities::Equals(System.Object)
extern void HapticCapabilities_Equals_m87B9C608F4E14AE0052DA784111BA3FE74EF5B57 (void);
// 0x00000018 System.Boolean UnityEngine.XR.HapticCapabilities::Equals(UnityEngine.XR.HapticCapabilities)
extern void HapticCapabilities_Equals_m4CD220F74171CB85C19D2CA75D3BC9933B699F0F (void);
// 0x00000019 System.Int32 UnityEngine.XR.HapticCapabilities::GetHashCode()
extern void HapticCapabilities_GetHashCode_m7C7504CA0AADBD291E33540E08B00EFF47A2A2DB (void);
// 0x0000001A System.String UnityEngine.XR.InputFeatureUsage::get_name()
extern void InputFeatureUsage_get_name_mA51C7B42F66E87B3527CBD061D1E6944058FE085 (void);
// 0x0000001B UnityEngine.XR.InputFeatureType UnityEngine.XR.InputFeatureUsage::get_internalType()
extern void InputFeatureUsage_get_internalType_m39EE9801AA983980676998D910C3A906191F8810 (void);
// 0x0000001C System.Boolean UnityEngine.XR.InputFeatureUsage::Equals(System.Object)
extern void InputFeatureUsage_Equals_m490288B62FC8FB23746E66937C85D8600BC150C0 (void);
// 0x0000001D System.Boolean UnityEngine.XR.InputFeatureUsage::Equals(UnityEngine.XR.InputFeatureUsage)
extern void InputFeatureUsage_Equals_m77BD3037164E49DCCFE7C912FD67B14D9A0A621D (void);
// 0x0000001E System.Int32 UnityEngine.XR.InputFeatureUsage::GetHashCode()
extern void InputFeatureUsage_GetHashCode_m823C86783F2394534F08BF9F56F4A23656D8E34D (void);
// 0x0000001F System.String UnityEngine.XR.InputFeatureUsage`1::get_name()
// 0x00000020 System.Void UnityEngine.XR.InputFeatureUsage`1::set_name(System.String)
// 0x00000021 System.Void UnityEngine.XR.InputFeatureUsage`1::.ctor(System.String)
// 0x00000022 System.Boolean UnityEngine.XR.InputFeatureUsage`1::Equals(System.Object)
// 0x00000023 System.Boolean UnityEngine.XR.InputFeatureUsage`1::Equals(UnityEngine.XR.InputFeatureUsage`1<T>)
// 0x00000024 System.Int32 UnityEngine.XR.InputFeatureUsage`1::GetHashCode()
// 0x00000025 System.Void UnityEngine.XR.CommonUsages::.cctor()
extern void CommonUsages__cctor_mC385C864BC1092A2B00B21E9AA6A7F079B195B9C (void);
// 0x00000026 System.Void UnityEngine.XR.InputDevice::.ctor(System.UInt64)
extern void InputDevice__ctor_mC7506750471891883425541E9DE3EB23D8FE4E34 (void);
// 0x00000027 System.UInt64 UnityEngine.XR.InputDevice::get_deviceId()
extern void InputDevice_get_deviceId_mF3FFF56432109EF58AC2EE178FA343261E950A19 (void);
// 0x00000028 System.Boolean UnityEngine.XR.InputDevice::get_isValid()
extern void InputDevice_get_isValid_mA908CF8195CECA44FF457430AFF9198C3FEC0948 (void);
// 0x00000029 System.String UnityEngine.XR.InputDevice::get_name()
extern void InputDevice_get_name_mAEA08A3755E715697022C54227556428CEF4E1CE (void);
// 0x0000002A UnityEngine.XR.InputDeviceCharacteristics UnityEngine.XR.InputDevice::get_characteristics()
extern void InputDevice_get_characteristics_mEEDC9AB2254C155C47B48848FECF347A87925ADB (void);
// 0x0000002B System.Boolean UnityEngine.XR.InputDevice::IsValidId()
extern void InputDevice_IsValidId_m13527A66E7D03B0C9AA64C2FFCC4A2F3889756BB (void);
// 0x0000002C System.Boolean UnityEngine.XR.InputDevice::SendHapticImpulse(System.UInt32,System.Single,System.Single)
extern void InputDevice_SendHapticImpulse_m7166A784508F8E0F3AE5BD88863171C7A905BC1B (void);
// 0x0000002D System.Boolean UnityEngine.XR.InputDevice::TryGetHapticCapabilities(UnityEngine.XR.HapticCapabilities&)
extern void InputDevice_TryGetHapticCapabilities_mC97EE9A231941988E8B6FA8AF0CB310EFE1B77A3 (void);
// 0x0000002E System.Boolean UnityEngine.XR.InputDevice::TryGetFeatureValue(UnityEngine.XR.InputFeatureUsage`1<System.Boolean>,System.Boolean&)
extern void InputDevice_TryGetFeatureValue_m24EC3B6C41AE4098269427232AD5F52E786BF884 (void);
// 0x0000002F System.Boolean UnityEngine.XR.InputDevice::TryGetFeatureValue(UnityEngine.XR.InputFeatureUsage`1<System.UInt32>,System.UInt32&)
extern void InputDevice_TryGetFeatureValue_m9FC969BEFF0E5BAB78DD9F2130F437788D20068C (void);
// 0x00000030 System.Boolean UnityEngine.XR.InputDevice::TryGetFeatureValue(UnityEngine.XR.InputFeatureUsage`1<System.Single>,System.Single&)
extern void InputDevice_TryGetFeatureValue_m675D52240379FEF80D6499B5031941812FDFD081 (void);
// 0x00000031 System.Boolean UnityEngine.XR.InputDevice::TryGetFeatureValue(UnityEngine.XR.InputFeatureUsage`1<UnityEngine.Vector2>,UnityEngine.Vector2&)
extern void InputDevice_TryGetFeatureValue_mB2C15D1FC747DA9FB5958FA17E77049886FB3BBA (void);
// 0x00000032 System.Boolean UnityEngine.XR.InputDevice::TryGetFeatureValue(UnityEngine.XR.InputFeatureUsage`1<UnityEngine.Vector3>,UnityEngine.Vector3&)
extern void InputDevice_TryGetFeatureValue_m472B5ECE996FB7440CACCF1E85722DA4963E3167 (void);
// 0x00000033 System.Boolean UnityEngine.XR.InputDevice::TryGetFeatureValue(UnityEngine.XR.InputFeatureUsage`1<UnityEngine.Quaternion>,UnityEngine.Quaternion&)
extern void InputDevice_TryGetFeatureValue_m0C1A9761DD0D1C6D1EF4BAB2FAF1BC1A9541BB9F (void);
// 0x00000034 System.Boolean UnityEngine.XR.InputDevice::TryGetFeatureValue(UnityEngine.XR.InputFeatureUsage`1<UnityEngine.XR.InputTrackingState>,UnityEngine.XR.InputTrackingState&)
extern void InputDevice_TryGetFeatureValue_m8A01F07356DC85042F6BB7C6258A75C3EC3C4E11 (void);
// 0x00000035 System.Boolean UnityEngine.XR.InputDevice::CheckValidAndSetDefault(T&)
// 0x00000036 System.Boolean UnityEngine.XR.InputDevice::Equals(System.Object)
extern void InputDevice_Equals_m206193EDB0C7A69C0BDFC821CE87B887C7BE0CAC (void);
// 0x00000037 System.Boolean UnityEngine.XR.InputDevice::Equals(UnityEngine.XR.InputDevice)
extern void InputDevice_Equals_mD6717F0B7B8605BB9F616947C1A3289C52ED41FC (void);
// 0x00000038 System.Int32 UnityEngine.XR.InputDevice::GetHashCode()
extern void InputDevice_GetHashCode_m4F8831F85A8646E52B12B5431BEC004AD1BAAD9D (void);
// 0x00000039 System.UInt64 UnityEngine.XR.Hand::get_deviceId()
extern void Hand_get_deviceId_m3FD7DC0BD8EBE55A433CBDB6F3D9486D6491714A (void);
// 0x0000003A System.UInt32 UnityEngine.XR.Hand::get_featureIndex()
extern void Hand_get_featureIndex_mD4AFCDFAEE8471B59400FF25F84328FA17229775 (void);
// 0x0000003B System.Boolean UnityEngine.XR.Hand::Equals(System.Object)
extern void Hand_Equals_m2BEF7EF009B595519DA0ADBE1912C197CEAD2E06 (void);
// 0x0000003C System.Boolean UnityEngine.XR.Hand::Equals(UnityEngine.XR.Hand)
extern void Hand_Equals_mA5FFEB3581A5E053717C0718AD68E596E801C864 (void);
// 0x0000003D System.Int32 UnityEngine.XR.Hand::GetHashCode()
extern void Hand_GetHashCode_mC1EA0B4473AFA966324B32BAC552ADB9BA9D24EE (void);
// 0x0000003E System.UInt64 UnityEngine.XR.Eyes::get_deviceId()
extern void Eyes_get_deviceId_m673FE292832DCAB360282083381923052944EBFD (void);
// 0x0000003F System.UInt32 UnityEngine.XR.Eyes::get_featureIndex()
extern void Eyes_get_featureIndex_mC4D34E37C0C008A69E75136C501F8907BC4F2EE8 (void);
// 0x00000040 System.Boolean UnityEngine.XR.Eyes::Equals(System.Object)
extern void Eyes_Equals_m4DC0E274FA64B59ED26E913BE0C95554476F228D (void);
// 0x00000041 System.Boolean UnityEngine.XR.Eyes::Equals(UnityEngine.XR.Eyes)
extern void Eyes_Equals_mE463ABC100B9BDB2790A18804BAD09FA1F289969 (void);
// 0x00000042 System.Int32 UnityEngine.XR.Eyes::GetHashCode()
extern void Eyes_GetHashCode_m754EAF148F82A79A7DD39D5F0D294EC4ABC763C8 (void);
// 0x00000043 System.UInt64 UnityEngine.XR.Bone::get_deviceId()
extern void Bone_get_deviceId_mA75E5FA9A3288C5F5E4172D7632FE228487BE0F1 (void);
// 0x00000044 System.UInt32 UnityEngine.XR.Bone::get_featureIndex()
extern void Bone_get_featureIndex_mF108AEAE50FFD1B908846717BBD95C59665925BE (void);
// 0x00000045 System.Boolean UnityEngine.XR.Bone::Equals(System.Object)
extern void Bone_Equals_mDC77AF51237B5937D83C048DF2734EB7162620F7 (void);
// 0x00000046 System.Boolean UnityEngine.XR.Bone::Equals(UnityEngine.XR.Bone)
extern void Bone_Equals_mB8585697810AA19D6DD257F9479F01993739020F (void);
// 0x00000047 System.Int32 UnityEngine.XR.Bone::GetHashCode()
extern void Bone_GetHashCode_mF14BB3AB7B7F52290CDF5D6A7C63AE11B3294F82 (void);
// 0x00000048 UnityEngine.XR.InputDevice UnityEngine.XR.InputDevices::GetDeviceAtXRNode(UnityEngine.XR.XRNode)
extern void InputDevices_GetDeviceAtXRNode_m3D322E7D1FFDA9C4D53E6B944E636C39B7A9592B (void);
// 0x00000049 System.Void UnityEngine.XR.InputDevices::GetDevices(System.Collections.Generic.List`1<UnityEngine.XR.InputDevice>)
extern void InputDevices_GetDevices_mDB6E1E057DC81A1833AEB55B62FA22228D6EFA26 (void);
// 0x0000004A System.Void UnityEngine.XR.InputDevices::GetDevicesWithCharacteristics(UnityEngine.XR.InputDeviceCharacteristics,System.Collections.Generic.List`1<UnityEngine.XR.InputDevice>)
extern void InputDevices_GetDevicesWithCharacteristics_m82F54DE2802FCE4EB730FCFBF8731CA91A27DEB0 (void);
// 0x0000004B System.Void UnityEngine.XR.InputDevices::add_deviceConnected(System.Action`1<UnityEngine.XR.InputDevice>)
extern void InputDevices_add_deviceConnected_m0329DBAE47CA4A20778F7DCBA470FCF9A672E9C9 (void);
// 0x0000004C System.Void UnityEngine.XR.InputDevices::remove_deviceConnected(System.Action`1<UnityEngine.XR.InputDevice>)
extern void InputDevices_remove_deviceConnected_m52D0C5E73A9BBEEF775E21DF93DDF0DD4F7D1BB5 (void);
// 0x0000004D System.Void UnityEngine.XR.InputDevices::add_deviceDisconnected(System.Action`1<UnityEngine.XR.InputDevice>)
extern void InputDevices_add_deviceDisconnected_mADAB4CDAFF3655811C41D7715B29DCC0A2082F1B (void);
// 0x0000004E System.Void UnityEngine.XR.InputDevices::remove_deviceDisconnected(System.Action`1<UnityEngine.XR.InputDevice>)
extern void InputDevices_remove_deviceDisconnected_m1F052759E0E0911D2AB2B1275D49381BBFDCED1A (void);
// 0x0000004F System.Void UnityEngine.XR.InputDevices::add_deviceConfigChanged(System.Action`1<UnityEngine.XR.InputDevice>)
extern void InputDevices_add_deviceConfigChanged_mFCA359D6E3569BC8CD39458476194AF0DAEF5946 (void);
// 0x00000050 System.Void UnityEngine.XR.InputDevices::remove_deviceConfigChanged(System.Action`1<UnityEngine.XR.InputDevice>)
extern void InputDevices_remove_deviceConfigChanged_m459857D040C452378C00AD947681EB2B40E06E8E (void);
// 0x00000051 System.Void UnityEngine.XR.InputDevices::InvokeConnectionEvent(System.UInt64,UnityEngine.XR.ConnectionChangeType)
extern void InputDevices_InvokeConnectionEvent_m10F62F8E2E197247E88668345C22114268233B1A (void);
// 0x00000052 System.Void UnityEngine.XR.InputDevices::GetDevices_Internal(System.Collections.Generic.List`1<UnityEngine.XR.InputDevice>)
extern void InputDevices_GetDevices_Internal_mD8C7DD36BABD46C9AD2C048602566B2043BF438D (void);
// 0x00000053 System.Boolean UnityEngine.XR.InputDevices::SendHapticImpulse(System.UInt64,System.UInt32,System.Single,System.Single)
extern void InputDevices_SendHapticImpulse_mC965C7C6A20D28CD51EDFC5BE61E85D6505A74F7 (void);
// 0x00000054 System.Boolean UnityEngine.XR.InputDevices::TryGetHapticCapabilities(System.UInt64,UnityEngine.XR.HapticCapabilities&)
extern void InputDevices_TryGetHapticCapabilities_mBCEC7D1AE8C869608B3FF5D05EF2629729B91CA7 (void);
// 0x00000055 System.Boolean UnityEngine.XR.InputDevices::TryGetFeatureValue_bool(System.UInt64,System.String,System.Boolean&)
extern void InputDevices_TryGetFeatureValue_bool_mD652BFAC63C19FFE34BA0E552A764C655402F0F2 (void);
// 0x00000056 System.Boolean UnityEngine.XR.InputDevices::TryGetFeatureValue_UInt32(System.UInt64,System.String,System.UInt32&)
extern void InputDevices_TryGetFeatureValue_UInt32_m50BDA6D2D451E2032210DFF4CDA8FE7EDE3EE4D2 (void);
// 0x00000057 System.Boolean UnityEngine.XR.InputDevices::TryGetFeatureValue_float(System.UInt64,System.String,System.Single&)
extern void InputDevices_TryGetFeatureValue_float_m2F667629D89251CD719FF2752E644467DF5B7F7D (void);
// 0x00000058 System.Boolean UnityEngine.XR.InputDevices::TryGetFeatureValue_Vector2f(System.UInt64,System.String,UnityEngine.Vector2&)
extern void InputDevices_TryGetFeatureValue_Vector2f_mF98BD89249A038E4D9EDA8D4E64CCEFAFB40E646 (void);
// 0x00000059 System.Boolean UnityEngine.XR.InputDevices::TryGetFeatureValue_Vector3f(System.UInt64,System.String,UnityEngine.Vector3&)
extern void InputDevices_TryGetFeatureValue_Vector3f_m765F82CDF7B96A1F616753B9C14AF5144F71F9BB (void);
// 0x0000005A System.Boolean UnityEngine.XR.InputDevices::TryGetFeatureValue_Quaternionf(System.UInt64,System.String,UnityEngine.Quaternion&)
extern void InputDevices_TryGetFeatureValue_Quaternionf_m7E4BA3BCD07D23060BA8273A1018CD40EB41B1B4 (void);
// 0x0000005B System.Boolean UnityEngine.XR.InputDevices::IsDeviceValid(System.UInt64)
extern void InputDevices_IsDeviceValid_m3A26291DCB7733D1538DF700E4638BE9DF9A994A (void);
// 0x0000005C System.String UnityEngine.XR.InputDevices::GetDeviceName(System.UInt64)
extern void InputDevices_GetDeviceName_m0AC6649B2A6AF0394487068FC82DFFA6D33A3D92 (void);
// 0x0000005D UnityEngine.XR.InputDeviceCharacteristics UnityEngine.XR.InputDevices::GetDeviceCharacteristics(System.UInt64)
extern void InputDevices_GetDeviceCharacteristics_m484271629C390D593B073791099D05EC8F45CDF9 (void);
// 0x0000005E System.Void UnityEngine.XR.XRMeshSubsystemDescriptor::.ctor()
extern void XRMeshSubsystemDescriptor__ctor_mFD056F69A8BECE56819411D4CD84653B3B735A1B (void);
// 0x0000005F System.String UnityEngine.XR.MeshId::ToString()
extern void MeshId_ToString_m8D2D9206A924A86FF28CF6499B7C66941FD0E7D4 (void);
// 0x00000060 System.Int32 UnityEngine.XR.MeshId::GetHashCode()
extern void MeshId_GetHashCode_mC6B6428EB5856505023C0D48879B92C2E3F062B0 (void);
// 0x00000061 System.Boolean UnityEngine.XR.MeshId::Equals(System.Object)
extern void MeshId_Equals_m1106B9A800E87442FE36125FD3EC3CB12FD778E9 (void);
// 0x00000062 System.Boolean UnityEngine.XR.MeshId::Equals(UnityEngine.XR.MeshId)
extern void MeshId_Equals_m3D0AC754FDC327819342797C38CF20C5F3D25818 (void);
// 0x00000063 System.Void UnityEngine.XR.MeshId::.cctor()
extern void MeshId__cctor_mE4556EF31E7F96397E4C9E7C3DF80A3C129D431D (void);
// 0x00000064 System.Int32 UnityEngine.XR.HashCodeHelper::Combine(System.Int32,System.Int32)
extern void HashCodeHelper_Combine_m2A63E4964D06338CC6F9DE17F5EFCCC348A6A1D7 (void);
// 0x00000065 System.Int32 UnityEngine.XR.HashCodeHelper::Combine(System.Int32,System.Int32,System.Int32)
extern void HashCodeHelper_Combine_mEDE733EC1ABDEA82040BBC18551FB975F3FAC322 (void);
// 0x00000066 System.Int32 UnityEngine.XR.HashCodeHelper::Combine(System.Int32,System.Int32,System.Int32,System.Int32)
extern void HashCodeHelper_Combine_mF867D2FAB545AD0A4832679FC73F7A2B289A37F7 (void);
// 0x00000067 System.Int32 UnityEngine.XR.HashCodeHelper::Combine(System.Int32,System.Int32,System.Int32,System.Int32,System.Int32)
extern void HashCodeHelper_Combine_mBE52BFDECDDA219A4A334421E8706078A4BE6680 (void);
// 0x00000068 System.Int32 UnityEngine.XR.HashCodeHelper::Combine(System.Int32,System.Int32,System.Int32,System.Int32,System.Int32,System.Int32)
extern void HashCodeHelper_Combine_m429AA1346C5A7ACFA6EC9A5D26250CEA4A1BAF89 (void);
// 0x00000069 System.Int32 UnityEngine.XR.HashCodeHelper::Combine(System.Int32,System.Int32,System.Int32,System.Int32,System.Int32,System.Int32,System.Int32)
extern void HashCodeHelper_Combine_m110C54CCBA83DD580295D7BA248976C50F6BF606 (void);
// 0x0000006A System.Int32 UnityEngine.XR.HashCodeHelper::Combine(System.Int32,System.Int32,System.Int32,System.Int32,System.Int32,System.Int32,System.Int32,System.Int32)
extern void HashCodeHelper_Combine_mBF383AC565B49ACFCB9A0046504C40A8997BAEFD (void);
// 0x0000006B UnityEngine.XR.MeshId UnityEngine.XR.MeshGenerationResult::get_MeshId()
extern void MeshGenerationResult_get_MeshId_m2260BF3F7EB6ED4F2CF0A8F6BEFDD2CD82B6751D (void);
// 0x0000006C UnityEngine.Mesh UnityEngine.XR.MeshGenerationResult::get_Mesh()
extern void MeshGenerationResult_get_Mesh_m95A022CCC5DDD8D0E8B9CEB3A2D3E06A0E7210F9 (void);
// 0x0000006D UnityEngine.MeshCollider UnityEngine.XR.MeshGenerationResult::get_MeshCollider()
extern void MeshGenerationResult_get_MeshCollider_m07210E3B1723B5BA535250E2012755AAC46FB180 (void);
// 0x0000006E UnityEngine.XR.MeshGenerationStatus UnityEngine.XR.MeshGenerationResult::get_Status()
extern void MeshGenerationResult_get_Status_m392754927B1E28DB2E6078A212F8FCC013AC10C0 (void);
// 0x0000006F UnityEngine.XR.MeshVertexAttributes UnityEngine.XR.MeshGenerationResult::get_Attributes()
extern void MeshGenerationResult_get_Attributes_m62E3E9EC74240C37D818481B1243B3EB40424C6B (void);
// 0x00000070 UnityEngine.Vector3 UnityEngine.XR.MeshGenerationResult::get_Position()
extern void MeshGenerationResult_get_Position_m72AE25CA3412AE3C653251095067B496CEC1617F (void);
// 0x00000071 UnityEngine.Quaternion UnityEngine.XR.MeshGenerationResult::get_Rotation()
extern void MeshGenerationResult_get_Rotation_mC27F07EC357E8B3EED94DBEE5365B33154941EE3 (void);
// 0x00000072 UnityEngine.Vector3 UnityEngine.XR.MeshGenerationResult::get_Scale()
extern void MeshGenerationResult_get_Scale_mA3CEB3EA848509F5B1A5525F40A45AA9CB8BD5D3 (void);
// 0x00000073 System.Boolean UnityEngine.XR.MeshGenerationResult::Equals(System.Object)
extern void MeshGenerationResult_Equals_m7A6C6A94553E313F74FB2B44326CF32FC94E122F (void);
// 0x00000074 System.Boolean UnityEngine.XR.MeshGenerationResult::Equals(UnityEngine.XR.MeshGenerationResult)
extern void MeshGenerationResult_Equals_mE32D058EBA520E8CC77A08119D82AE735CE0ACAC (void);
// 0x00000075 System.Int32 UnityEngine.XR.MeshGenerationResult::GetHashCode()
extern void MeshGenerationResult_GetHashCode_mA542C935FE2ABB8EC0A3CE001267176095F7DCBC (void);
// 0x00000076 System.Void UnityEngine.XR.XRMeshSubsystem::InvokeMeshReadyDelegate(UnityEngine.XR.MeshGenerationResult,System.Action`1<UnityEngine.XR.MeshGenerationResult>)
extern void XRMeshSubsystem_InvokeMeshReadyDelegate_m495A42DE7C44B760CB7D41244A9314F860EA6C53 (void);
// 0x00000077 System.Void UnityEngine.XR.XRMeshSubsystem::.ctor()
extern void XRMeshSubsystem__ctor_mA9C27A31A690B9023F70B628A9D9F5E3F5ED2AEA (void);
// 0x00000078 System.Void UnityEngine.XR.XRMeshSubsystem/MeshTransformList::Dispose()
extern void MeshTransformList_Dispose_m0F0B77B84E50F02EB1A48751831808F9C69E0636 (void);
// 0x00000079 System.Void UnityEngine.XR.XRMeshSubsystem/MeshTransformList::Dispose(System.IntPtr)
extern void MeshTransformList_Dispose_m7655ACDE6BC605B30EF2BC387A7B9D0F4D9EED19 (void);
// 0x0000007A System.Boolean UnityEngine.XR.XRInputSubsystem::TryRecenter()
extern void XRInputSubsystem_TryRecenter_m4F8888E40ED79139DCB81D56A67C03B4D931A6BB (void);
// 0x0000007B System.Boolean UnityEngine.XR.XRInputSubsystem::TrySetTrackingOriginMode(UnityEngine.XR.TrackingOriginModeFlags)
extern void XRInputSubsystem_TrySetTrackingOriginMode_m132C190CEAE4403A381BF1C1C4B5FF349F2A3FA7 (void);
// 0x0000007C UnityEngine.XR.TrackingOriginModeFlags UnityEngine.XR.XRInputSubsystem::GetTrackingOriginMode()
extern void XRInputSubsystem_GetTrackingOriginMode_mBAFED615F74039A681825BB956AD3C8FA7DE45F2 (void);
// 0x0000007D UnityEngine.XR.TrackingOriginModeFlags UnityEngine.XR.XRInputSubsystem::GetSupportedTrackingOriginModes()
extern void XRInputSubsystem_GetSupportedTrackingOriginModes_mBA7190E84E6BB4F251C232B97565E228AECB3018 (void);
// 0x0000007E System.Void UnityEngine.XR.XRInputSubsystem::add_trackingOriginUpdated(System.Action`1<UnityEngine.XR.XRInputSubsystem>)
extern void XRInputSubsystem_add_trackingOriginUpdated_mA5E69767B6E8D505BE73804A4B4EA738A27F675E (void);
// 0x0000007F System.Void UnityEngine.XR.XRInputSubsystem::remove_trackingOriginUpdated(System.Action`1<UnityEngine.XR.XRInputSubsystem>)
extern void XRInputSubsystem_remove_trackingOriginUpdated_m6A04D2813F1D4A37C013BA00EBC862D1EEA7473E (void);
// 0x00000080 System.Void UnityEngine.XR.XRInputSubsystem::InvokeTrackingOriginUpdatedEvent(System.IntPtr)
extern void XRInputSubsystem_InvokeTrackingOriginUpdatedEvent_mD9F93C1B2F0BCDE37190DC500F9D93B273362EEB (void);
// 0x00000081 System.Void UnityEngine.XR.XRInputSubsystem::InvokeBoundaryChangedEvent(System.IntPtr)
extern void XRInputSubsystem_InvokeBoundaryChangedEvent_m5BAE59235BADE518D0E32B8D420A0572B63C68C2 (void);
// 0x00000082 System.Void UnityEngine.XR.XRInputSubsystem::.ctor()
extern void XRInputSubsystem__ctor_mD0260427CD99745155B171BB6D03862B3CE303E4 (void);
// 0x00000083 System.Void UnityEngine.XR.XRInputSubsystemDescriptor::.ctor()
extern void XRInputSubsystemDescriptor__ctor_m7DFAE8F8670A5721F02B0AE27BB47389BA0F8DFB (void);
// 0x00000084 System.Void UnityEngine.XR.XRDisplaySubsystemDescriptor::.ctor()
extern void XRDisplaySubsystemDescriptor__ctor_mB9B2993D74FFC580731C03B390C764260458FAA6 (void);
// 0x00000085 System.Void UnityEngine.XR.XRDisplaySubsystem::InvokeDisplayFocusChanged(System.Boolean)
extern void XRDisplaySubsystem_InvokeDisplayFocusChanged_m57036DB43BB9F6BF12AADC268AC47D190378BE56 (void);
// 0x00000086 System.Void UnityEngine.XR.XRDisplaySubsystem::MarkTransformLateLatched(UnityEngine.Transform,UnityEngine.XR.XRDisplaySubsystem/LateLatchNode)
extern void XRDisplaySubsystem_MarkTransformLateLatched_m413E10547A6E6607F4B41F0ED6CFA2EC6986E944 (void);
// 0x00000087 System.Void UnityEngine.XR.XRDisplaySubsystem::SetMSAALevel(System.Int32)
extern void XRDisplaySubsystem_SetMSAALevel_m5059067DF2E69C356138B8C2DC99131C22F3488C (void);
// 0x00000088 System.Void UnityEngine.XR.XRDisplaySubsystem::EndRecordingIfLateLatched(UnityEngine.Camera)
extern void XRDisplaySubsystem_EndRecordingIfLateLatched_mDDADB0A2E961464C86429243D7CCDBE1A75D6A20 (void);
// 0x00000089 System.Boolean UnityEngine.XR.XRDisplaySubsystem::Internal_TryEndRecordingIfLateLatched(UnityEngine.Camera)
extern void XRDisplaySubsystem_Internal_TryEndRecordingIfLateLatched_mB550855846A6B88D9FE8B1A5B829F1D754579B40 (void);
// 0x0000008A System.Void UnityEngine.XR.XRDisplaySubsystem::BeginRecordingIfLateLatched(UnityEngine.Camera)
extern void XRDisplaySubsystem_BeginRecordingIfLateLatched_mDD37688ACD9999F051DB8802819701BA58DFE58B (void);
// 0x0000008B System.Boolean UnityEngine.XR.XRDisplaySubsystem::Internal_TryBeginRecordingIfLateLatched(UnityEngine.Camera)
extern void XRDisplaySubsystem_Internal_TryBeginRecordingIfLateLatched_mEF81E07646D84AAF371EAAE29EAF5B16CB23856C (void);
// 0x0000008C System.Void UnityEngine.XR.XRDisplaySubsystem::.ctor()
extern void XRDisplaySubsystem__ctor_m5DA92849F107C6A802BF584D5E328FF2DB971B01 (void);
static Il2CppMethodPointer s_methodPointers[140] = 
{
	EmbeddedAttribute__ctor_m3FF623BFEE0DB7F6FD99E1EAFE20C5581224D314,
	IsReadOnlyAttribute__ctor_m7C0ECD764E09B041BA7629AC5C020D9972AC8697,
	XRNodeState_set_uniqueID_m1C42BE763CEB5BE66EECE54288DE28D30CCB085F,
	XRNodeState_get_nodeType_mFC49286B25FF7732CE6CAF231A1DE5C083C13932,
	XRNodeState_set_nodeType_m17D747D9C558277596BD29F59FD8FDEE1A892FF4,
	XRNodeState_set_tracked_m5B448272E0E14001A16A150EE7B602ADE1D85A45,
	XRNodeState_TryGetPosition_mC17C5DFEEA4062A6D91D7E252DD108829AC4E0A6,
	XRNodeState_TryGetRotation_mA7F9597BE3AFE747BF2F95BF491779520FEF7301,
	XRNodeState_TryGet_m6C1D3FC5E773BC5E134F37815FB08839489F5630,
	XRNodeState_TryGet_m3EC1CBB3B6B823611B82073139949C041CE7C616,
	InputTracking_add_trackingAcquired_mB4BFD78603798E38C0915B6E9E49DBF2E3EEE3E4,
	InputTracking_remove_trackingAcquired_m610A04BE4915CA2FA429810C607C42CF24792816,
	InputTracking_InvokeTrackingEvent_mA218CBE5D81A639B9C9A084A5360FEAD4625C42C,
	InputTracking_GetNodeStates_mA2E8D154A47C817ED74AD42F6B38A9C906A57A67,
	InputTracking_GetNodeStates_Internal_m6E67CE5EC9C950ED633AEC42F8BD5B15EFE3B916,
	InputTracking_GetDeviceIdAtXRNode_mF6AA8F79B81E685890712AE72FADC4D1113CDECC,
	HapticCapabilities_get_numChannels_m23871C9860B4A1F6E22DE4579A7550A7A9382BA9,
	HapticCapabilities_get_supportsImpulse_m855193672304BD935913E215B690B14952E0C59C,
	HapticCapabilities_get_supportsBuffer_mE87BCD83AA4FEE3C89EE906EF2F1B6DFA9B7BA59,
	HapticCapabilities_get_bufferFrequencyHz_m31270DBF2491B9FDC969D0082ABB5CF0F0DFDBAA,
	HapticCapabilities_get_bufferMaxSize_mAC7A86B40B716A2B9B17D4B3B26EE6B7D0346C7C,
	HapticCapabilities_get_bufferOptimalSize_m166A6DD9882CD48F702FE7447E87C60AAF479407,
	HapticCapabilities_Equals_m87B9C608F4E14AE0052DA784111BA3FE74EF5B57,
	HapticCapabilities_Equals_m4CD220F74171CB85C19D2CA75D3BC9933B699F0F,
	HapticCapabilities_GetHashCode_m7C7504CA0AADBD291E33540E08B00EFF47A2A2DB,
	InputFeatureUsage_get_name_mA51C7B42F66E87B3527CBD061D1E6944058FE085,
	InputFeatureUsage_get_internalType_m39EE9801AA983980676998D910C3A906191F8810,
	InputFeatureUsage_Equals_m490288B62FC8FB23746E66937C85D8600BC150C0,
	InputFeatureUsage_Equals_m77BD3037164E49DCCFE7C912FD67B14D9A0A621D,
	InputFeatureUsage_GetHashCode_m823C86783F2394534F08BF9F56F4A23656D8E34D,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	CommonUsages__cctor_mC385C864BC1092A2B00B21E9AA6A7F079B195B9C,
	InputDevice__ctor_mC7506750471891883425541E9DE3EB23D8FE4E34,
	InputDevice_get_deviceId_mF3FFF56432109EF58AC2EE178FA343261E950A19,
	InputDevice_get_isValid_mA908CF8195CECA44FF457430AFF9198C3FEC0948,
	InputDevice_get_name_mAEA08A3755E715697022C54227556428CEF4E1CE,
	InputDevice_get_characteristics_mEEDC9AB2254C155C47B48848FECF347A87925ADB,
	InputDevice_IsValidId_m13527A66E7D03B0C9AA64C2FFCC4A2F3889756BB,
	InputDevice_SendHapticImpulse_m7166A784508F8E0F3AE5BD88863171C7A905BC1B,
	InputDevice_TryGetHapticCapabilities_mC97EE9A231941988E8B6FA8AF0CB310EFE1B77A3,
	InputDevice_TryGetFeatureValue_m24EC3B6C41AE4098269427232AD5F52E786BF884,
	InputDevice_TryGetFeatureValue_m9FC969BEFF0E5BAB78DD9F2130F437788D20068C,
	InputDevice_TryGetFeatureValue_m675D52240379FEF80D6499B5031941812FDFD081,
	InputDevice_TryGetFeatureValue_mB2C15D1FC747DA9FB5958FA17E77049886FB3BBA,
	InputDevice_TryGetFeatureValue_m472B5ECE996FB7440CACCF1E85722DA4963E3167,
	InputDevice_TryGetFeatureValue_m0C1A9761DD0D1C6D1EF4BAB2FAF1BC1A9541BB9F,
	InputDevice_TryGetFeatureValue_m8A01F07356DC85042F6BB7C6258A75C3EC3C4E11,
	NULL,
	InputDevice_Equals_m206193EDB0C7A69C0BDFC821CE87B887C7BE0CAC,
	InputDevice_Equals_mD6717F0B7B8605BB9F616947C1A3289C52ED41FC,
	InputDevice_GetHashCode_m4F8831F85A8646E52B12B5431BEC004AD1BAAD9D,
	Hand_get_deviceId_m3FD7DC0BD8EBE55A433CBDB6F3D9486D6491714A,
	Hand_get_featureIndex_mD4AFCDFAEE8471B59400FF25F84328FA17229775,
	Hand_Equals_m2BEF7EF009B595519DA0ADBE1912C197CEAD2E06,
	Hand_Equals_mA5FFEB3581A5E053717C0718AD68E596E801C864,
	Hand_GetHashCode_mC1EA0B4473AFA966324B32BAC552ADB9BA9D24EE,
	Eyes_get_deviceId_m673FE292832DCAB360282083381923052944EBFD,
	Eyes_get_featureIndex_mC4D34E37C0C008A69E75136C501F8907BC4F2EE8,
	Eyes_Equals_m4DC0E274FA64B59ED26E913BE0C95554476F228D,
	Eyes_Equals_mE463ABC100B9BDB2790A18804BAD09FA1F289969,
	Eyes_GetHashCode_m754EAF148F82A79A7DD39D5F0D294EC4ABC763C8,
	Bone_get_deviceId_mA75E5FA9A3288C5F5E4172D7632FE228487BE0F1,
	Bone_get_featureIndex_mF108AEAE50FFD1B908846717BBD95C59665925BE,
	Bone_Equals_mDC77AF51237B5937D83C048DF2734EB7162620F7,
	Bone_Equals_mB8585697810AA19D6DD257F9479F01993739020F,
	Bone_GetHashCode_mF14BB3AB7B7F52290CDF5D6A7C63AE11B3294F82,
	InputDevices_GetDeviceAtXRNode_m3D322E7D1FFDA9C4D53E6B944E636C39B7A9592B,
	InputDevices_GetDevices_mDB6E1E057DC81A1833AEB55B62FA22228D6EFA26,
	InputDevices_GetDevicesWithCharacteristics_m82F54DE2802FCE4EB730FCFBF8731CA91A27DEB0,
	InputDevices_add_deviceConnected_m0329DBAE47CA4A20778F7DCBA470FCF9A672E9C9,
	InputDevices_remove_deviceConnected_m52D0C5E73A9BBEEF775E21DF93DDF0DD4F7D1BB5,
	InputDevices_add_deviceDisconnected_mADAB4CDAFF3655811C41D7715B29DCC0A2082F1B,
	InputDevices_remove_deviceDisconnected_m1F052759E0E0911D2AB2B1275D49381BBFDCED1A,
	InputDevices_add_deviceConfigChanged_mFCA359D6E3569BC8CD39458476194AF0DAEF5946,
	InputDevices_remove_deviceConfigChanged_m459857D040C452378C00AD947681EB2B40E06E8E,
	InputDevices_InvokeConnectionEvent_m10F62F8E2E197247E88668345C22114268233B1A,
	InputDevices_GetDevices_Internal_mD8C7DD36BABD46C9AD2C048602566B2043BF438D,
	InputDevices_SendHapticImpulse_mC965C7C6A20D28CD51EDFC5BE61E85D6505A74F7,
	InputDevices_TryGetHapticCapabilities_mBCEC7D1AE8C869608B3FF5D05EF2629729B91CA7,
	InputDevices_TryGetFeatureValue_bool_mD652BFAC63C19FFE34BA0E552A764C655402F0F2,
	InputDevices_TryGetFeatureValue_UInt32_m50BDA6D2D451E2032210DFF4CDA8FE7EDE3EE4D2,
	InputDevices_TryGetFeatureValue_float_m2F667629D89251CD719FF2752E644467DF5B7F7D,
	InputDevices_TryGetFeatureValue_Vector2f_mF98BD89249A038E4D9EDA8D4E64CCEFAFB40E646,
	InputDevices_TryGetFeatureValue_Vector3f_m765F82CDF7B96A1F616753B9C14AF5144F71F9BB,
	InputDevices_TryGetFeatureValue_Quaternionf_m7E4BA3BCD07D23060BA8273A1018CD40EB41B1B4,
	InputDevices_IsDeviceValid_m3A26291DCB7733D1538DF700E4638BE9DF9A994A,
	InputDevices_GetDeviceName_m0AC6649B2A6AF0394487068FC82DFFA6D33A3D92,
	InputDevices_GetDeviceCharacteristics_m484271629C390D593B073791099D05EC8F45CDF9,
	XRMeshSubsystemDescriptor__ctor_mFD056F69A8BECE56819411D4CD84653B3B735A1B,
	MeshId_ToString_m8D2D9206A924A86FF28CF6499B7C66941FD0E7D4,
	MeshId_GetHashCode_mC6B6428EB5856505023C0D48879B92C2E3F062B0,
	MeshId_Equals_m1106B9A800E87442FE36125FD3EC3CB12FD778E9,
	MeshId_Equals_m3D0AC754FDC327819342797C38CF20C5F3D25818,
	MeshId__cctor_mE4556EF31E7F96397E4C9E7C3DF80A3C129D431D,
	HashCodeHelper_Combine_m2A63E4964D06338CC6F9DE17F5EFCCC348A6A1D7,
	HashCodeHelper_Combine_mEDE733EC1ABDEA82040BBC18551FB975F3FAC322,
	HashCodeHelper_Combine_mF867D2FAB545AD0A4832679FC73F7A2B289A37F7,
	HashCodeHelper_Combine_mBE52BFDECDDA219A4A334421E8706078A4BE6680,
	HashCodeHelper_Combine_m429AA1346C5A7ACFA6EC9A5D26250CEA4A1BAF89,
	HashCodeHelper_Combine_m110C54CCBA83DD580295D7BA248976C50F6BF606,
	HashCodeHelper_Combine_mBF383AC565B49ACFCB9A0046504C40A8997BAEFD,
	MeshGenerationResult_get_MeshId_m2260BF3F7EB6ED4F2CF0A8F6BEFDD2CD82B6751D,
	MeshGenerationResult_get_Mesh_m95A022CCC5DDD8D0E8B9CEB3A2D3E06A0E7210F9,
	MeshGenerationResult_get_MeshCollider_m07210E3B1723B5BA535250E2012755AAC46FB180,
	MeshGenerationResult_get_Status_m392754927B1E28DB2E6078A212F8FCC013AC10C0,
	MeshGenerationResult_get_Attributes_m62E3E9EC74240C37D818481B1243B3EB40424C6B,
	MeshGenerationResult_get_Position_m72AE25CA3412AE3C653251095067B496CEC1617F,
	MeshGenerationResult_get_Rotation_mC27F07EC357E8B3EED94DBEE5365B33154941EE3,
	MeshGenerationResult_get_Scale_mA3CEB3EA848509F5B1A5525F40A45AA9CB8BD5D3,
	MeshGenerationResult_Equals_m7A6C6A94553E313F74FB2B44326CF32FC94E122F,
	MeshGenerationResult_Equals_mE32D058EBA520E8CC77A08119D82AE735CE0ACAC,
	MeshGenerationResult_GetHashCode_mA542C935FE2ABB8EC0A3CE001267176095F7DCBC,
	XRMeshSubsystem_InvokeMeshReadyDelegate_m495A42DE7C44B760CB7D41244A9314F860EA6C53,
	XRMeshSubsystem__ctor_mA9C27A31A690B9023F70B628A9D9F5E3F5ED2AEA,
	MeshTransformList_Dispose_m0F0B77B84E50F02EB1A48751831808F9C69E0636,
	MeshTransformList_Dispose_m7655ACDE6BC605B30EF2BC387A7B9D0F4D9EED19,
	XRInputSubsystem_TryRecenter_m4F8888E40ED79139DCB81D56A67C03B4D931A6BB,
	XRInputSubsystem_TrySetTrackingOriginMode_m132C190CEAE4403A381BF1C1C4B5FF349F2A3FA7,
	XRInputSubsystem_GetTrackingOriginMode_mBAFED615F74039A681825BB956AD3C8FA7DE45F2,
	XRInputSubsystem_GetSupportedTrackingOriginModes_mBA7190E84E6BB4F251C232B97565E228AECB3018,
	XRInputSubsystem_add_trackingOriginUpdated_mA5E69767B6E8D505BE73804A4B4EA738A27F675E,
	XRInputSubsystem_remove_trackingOriginUpdated_m6A04D2813F1D4A37C013BA00EBC862D1EEA7473E,
	XRInputSubsystem_InvokeTrackingOriginUpdatedEvent_mD9F93C1B2F0BCDE37190DC500F9D93B273362EEB,
	XRInputSubsystem_InvokeBoundaryChangedEvent_m5BAE59235BADE518D0E32B8D420A0572B63C68C2,
	XRInputSubsystem__ctor_mD0260427CD99745155B171BB6D03862B3CE303E4,
	XRInputSubsystemDescriptor__ctor_m7DFAE8F8670A5721F02B0AE27BB47389BA0F8DFB,
	XRDisplaySubsystemDescriptor__ctor_mB9B2993D74FFC580731C03B390C764260458FAA6,
	XRDisplaySubsystem_InvokeDisplayFocusChanged_m57036DB43BB9F6BF12AADC268AC47D190378BE56,
	XRDisplaySubsystem_MarkTransformLateLatched_m413E10547A6E6607F4B41F0ED6CFA2EC6986E944,
	XRDisplaySubsystem_SetMSAALevel_m5059067DF2E69C356138B8C2DC99131C22F3488C,
	XRDisplaySubsystem_EndRecordingIfLateLatched_mDDADB0A2E961464C86429243D7CCDBE1A75D6A20,
	XRDisplaySubsystem_Internal_TryEndRecordingIfLateLatched_mB550855846A6B88D9FE8B1A5B829F1D754579B40,
	XRDisplaySubsystem_BeginRecordingIfLateLatched_mDD37688ACD9999F051DB8802819701BA58DFE58B,
	XRDisplaySubsystem_Internal_TryBeginRecordingIfLateLatched_mEF81E07646D84AAF371EAAE29EAF5B16CB23856C,
	XRDisplaySubsystem__ctor_m5DA92849F107C6A802BF584D5E328FF2DB971B01,
};
extern void XRNodeState_set_uniqueID_m1C42BE763CEB5BE66EECE54288DE28D30CCB085F_AdjustorThunk (void);
extern void XRNodeState_get_nodeType_mFC49286B25FF7732CE6CAF231A1DE5C083C13932_AdjustorThunk (void);
extern void XRNodeState_set_nodeType_m17D747D9C558277596BD29F59FD8FDEE1A892FF4_AdjustorThunk (void);
extern void XRNodeState_set_tracked_m5B448272E0E14001A16A150EE7B602ADE1D85A45_AdjustorThunk (void);
extern void XRNodeState_TryGetPosition_mC17C5DFEEA4062A6D91D7E252DD108829AC4E0A6_AdjustorThunk (void);
extern void XRNodeState_TryGetRotation_mA7F9597BE3AFE747BF2F95BF491779520FEF7301_AdjustorThunk (void);
extern void XRNodeState_TryGet_m6C1D3FC5E773BC5E134F37815FB08839489F5630_AdjustorThunk (void);
extern void XRNodeState_TryGet_m3EC1CBB3B6B823611B82073139949C041CE7C616_AdjustorThunk (void);
extern void HapticCapabilities_get_numChannels_m23871C9860B4A1F6E22DE4579A7550A7A9382BA9_AdjustorThunk (void);
extern void HapticCapabilities_get_supportsImpulse_m855193672304BD935913E215B690B14952E0C59C_AdjustorThunk (void);
extern void HapticCapabilities_get_supportsBuffer_mE87BCD83AA4FEE3C89EE906EF2F1B6DFA9B7BA59_AdjustorThunk (void);
extern void HapticCapabilities_get_bufferFrequencyHz_m31270DBF2491B9FDC969D0082ABB5CF0F0DFDBAA_AdjustorThunk (void);
extern void HapticCapabilities_get_bufferMaxSize_mAC7A86B40B716A2B9B17D4B3B26EE6B7D0346C7C_AdjustorThunk (void);
extern void HapticCapabilities_get_bufferOptimalSize_m166A6DD9882CD48F702FE7447E87C60AAF479407_AdjustorThunk (void);
extern void HapticCapabilities_Equals_m87B9C608F4E14AE0052DA784111BA3FE74EF5B57_AdjustorThunk (void);
extern void HapticCapabilities_Equals_m4CD220F74171CB85C19D2CA75D3BC9933B699F0F_AdjustorThunk (void);
extern void HapticCapabilities_GetHashCode_m7C7504CA0AADBD291E33540E08B00EFF47A2A2DB_AdjustorThunk (void);
extern void InputFeatureUsage_get_name_mA51C7B42F66E87B3527CBD061D1E6944058FE085_AdjustorThunk (void);
extern void InputFeatureUsage_get_internalType_m39EE9801AA983980676998D910C3A906191F8810_AdjustorThunk (void);
extern void InputFeatureUsage_Equals_m490288B62FC8FB23746E66937C85D8600BC150C0_AdjustorThunk (void);
extern void InputFeatureUsage_Equals_m77BD3037164E49DCCFE7C912FD67B14D9A0A621D_AdjustorThunk (void);
extern void InputFeatureUsage_GetHashCode_m823C86783F2394534F08BF9F56F4A23656D8E34D_AdjustorThunk (void);
extern void InputDevice__ctor_mC7506750471891883425541E9DE3EB23D8FE4E34_AdjustorThunk (void);
extern void InputDevice_get_deviceId_mF3FFF56432109EF58AC2EE178FA343261E950A19_AdjustorThunk (void);
extern void InputDevice_get_isValid_mA908CF8195CECA44FF457430AFF9198C3FEC0948_AdjustorThunk (void);
extern void InputDevice_get_name_mAEA08A3755E715697022C54227556428CEF4E1CE_AdjustorThunk (void);
extern void InputDevice_get_characteristics_mEEDC9AB2254C155C47B48848FECF347A87925ADB_AdjustorThunk (void);
extern void InputDevice_IsValidId_m13527A66E7D03B0C9AA64C2FFCC4A2F3889756BB_AdjustorThunk (void);
extern void InputDevice_SendHapticImpulse_m7166A784508F8E0F3AE5BD88863171C7A905BC1B_AdjustorThunk (void);
extern void InputDevice_TryGetHapticCapabilities_mC97EE9A231941988E8B6FA8AF0CB310EFE1B77A3_AdjustorThunk (void);
extern void InputDevice_TryGetFeatureValue_m24EC3B6C41AE4098269427232AD5F52E786BF884_AdjustorThunk (void);
extern void InputDevice_TryGetFeatureValue_m9FC969BEFF0E5BAB78DD9F2130F437788D20068C_AdjustorThunk (void);
extern void InputDevice_TryGetFeatureValue_m675D52240379FEF80D6499B5031941812FDFD081_AdjustorThunk (void);
extern void InputDevice_TryGetFeatureValue_mB2C15D1FC747DA9FB5958FA17E77049886FB3BBA_AdjustorThunk (void);
extern void InputDevice_TryGetFeatureValue_m472B5ECE996FB7440CACCF1E85722DA4963E3167_AdjustorThunk (void);
extern void InputDevice_TryGetFeatureValue_m0C1A9761DD0D1C6D1EF4BAB2FAF1BC1A9541BB9F_AdjustorThunk (void);
extern void InputDevice_TryGetFeatureValue_m8A01F07356DC85042F6BB7C6258A75C3EC3C4E11_AdjustorThunk (void);
extern void InputDevice_Equals_m206193EDB0C7A69C0BDFC821CE87B887C7BE0CAC_AdjustorThunk (void);
extern void InputDevice_Equals_mD6717F0B7B8605BB9F616947C1A3289C52ED41FC_AdjustorThunk (void);
extern void InputDevice_GetHashCode_m4F8831F85A8646E52B12B5431BEC004AD1BAAD9D_AdjustorThunk (void);
extern void Hand_get_deviceId_m3FD7DC0BD8EBE55A433CBDB6F3D9486D6491714A_AdjustorThunk (void);
extern void Hand_get_featureIndex_mD4AFCDFAEE8471B59400FF25F84328FA17229775_AdjustorThunk (void);
extern void Hand_Equals_m2BEF7EF009B595519DA0ADBE1912C197CEAD2E06_AdjustorThunk (void);
extern void Hand_Equals_mA5FFEB3581A5E053717C0718AD68E596E801C864_AdjustorThunk (void);
extern void Hand_GetHashCode_mC1EA0B4473AFA966324B32BAC552ADB9BA9D24EE_AdjustorThunk (void);
extern void Eyes_get_deviceId_m673FE292832DCAB360282083381923052944EBFD_AdjustorThunk (void);
extern void Eyes_get_featureIndex_mC4D34E37C0C008A69E75136C501F8907BC4F2EE8_AdjustorThunk (void);
extern void Eyes_Equals_m4DC0E274FA64B59ED26E913BE0C95554476F228D_AdjustorThunk (void);
extern void Eyes_Equals_mE463ABC100B9BDB2790A18804BAD09FA1F289969_AdjustorThunk (void);
extern void Eyes_GetHashCode_m754EAF148F82A79A7DD39D5F0D294EC4ABC763C8_AdjustorThunk (void);
extern void Bone_get_deviceId_mA75E5FA9A3288C5F5E4172D7632FE228487BE0F1_AdjustorThunk (void);
extern void Bone_get_featureIndex_mF108AEAE50FFD1B908846717BBD95C59665925BE_AdjustorThunk (void);
extern void Bone_Equals_mDC77AF51237B5937D83C048DF2734EB7162620F7_AdjustorThunk (void);
extern void Bone_Equals_mB8585697810AA19D6DD257F9479F01993739020F_AdjustorThunk (void);
extern void Bone_GetHashCode_mF14BB3AB7B7F52290CDF5D6A7C63AE11B3294F82_AdjustorThunk (void);
extern void MeshId_ToString_m8D2D9206A924A86FF28CF6499B7C66941FD0E7D4_AdjustorThunk (void);
extern void MeshId_GetHashCode_mC6B6428EB5856505023C0D48879B92C2E3F062B0_AdjustorThunk (void);
extern void MeshId_Equals_m1106B9A800E87442FE36125FD3EC3CB12FD778E9_AdjustorThunk (void);
extern void MeshId_Equals_m3D0AC754FDC327819342797C38CF20C5F3D25818_AdjustorThunk (void);
extern void MeshGenerationResult_get_MeshId_m2260BF3F7EB6ED4F2CF0A8F6BEFDD2CD82B6751D_AdjustorThunk (void);
extern void MeshGenerationResult_get_Mesh_m95A022CCC5DDD8D0E8B9CEB3A2D3E06A0E7210F9_AdjustorThunk (void);
extern void MeshGenerationResult_get_MeshCollider_m07210E3B1723B5BA535250E2012755AAC46FB180_AdjustorThunk (void);
extern void MeshGenerationResult_get_Status_m392754927B1E28DB2E6078A212F8FCC013AC10C0_AdjustorThunk (void);
extern void MeshGenerationResult_get_Attributes_m62E3E9EC74240C37D818481B1243B3EB40424C6B_AdjustorThunk (void);
extern void MeshGenerationResult_get_Position_m72AE25CA3412AE3C653251095067B496CEC1617F_AdjustorThunk (void);
extern void MeshGenerationResult_get_Rotation_mC27F07EC357E8B3EED94DBEE5365B33154941EE3_AdjustorThunk (void);
extern void MeshGenerationResult_get_Scale_mA3CEB3EA848509F5B1A5525F40A45AA9CB8BD5D3_AdjustorThunk (void);
extern void MeshGenerationResult_Equals_m7A6C6A94553E313F74FB2B44326CF32FC94E122F_AdjustorThunk (void);
extern void MeshGenerationResult_Equals_mE32D058EBA520E8CC77A08119D82AE735CE0ACAC_AdjustorThunk (void);
extern void MeshGenerationResult_GetHashCode_mA542C935FE2ABB8EC0A3CE001267176095F7DCBC_AdjustorThunk (void);
extern void MeshTransformList_Dispose_m0F0B77B84E50F02EB1A48751831808F9C69E0636_AdjustorThunk (void);
static Il2CppTokenAdjustorThunkPair s_adjustorThunks[71] = 
{
	{ 0x06000003, XRNodeState_set_uniqueID_m1C42BE763CEB5BE66EECE54288DE28D30CCB085F_AdjustorThunk },
	{ 0x06000004, XRNodeState_get_nodeType_mFC49286B25FF7732CE6CAF231A1DE5C083C13932_AdjustorThunk },
	{ 0x06000005, XRNodeState_set_nodeType_m17D747D9C558277596BD29F59FD8FDEE1A892FF4_AdjustorThunk },
	{ 0x06000006, XRNodeState_set_tracked_m5B448272E0E14001A16A150EE7B602ADE1D85A45_AdjustorThunk },
	{ 0x06000007, XRNodeState_TryGetPosition_mC17C5DFEEA4062A6D91D7E252DD108829AC4E0A6_AdjustorThunk },
	{ 0x06000008, XRNodeState_TryGetRotation_mA7F9597BE3AFE747BF2F95BF491779520FEF7301_AdjustorThunk },
	{ 0x06000009, XRNodeState_TryGet_m6C1D3FC5E773BC5E134F37815FB08839489F5630_AdjustorThunk },
	{ 0x0600000A, XRNodeState_TryGet_m3EC1CBB3B6B823611B82073139949C041CE7C616_AdjustorThunk },
	{ 0x06000011, HapticCapabilities_get_numChannels_m23871C9860B4A1F6E22DE4579A7550A7A9382BA9_AdjustorThunk },
	{ 0x06000012, HapticCapabilities_get_supportsImpulse_m855193672304BD935913E215B690B14952E0C59C_AdjustorThunk },
	{ 0x06000013, HapticCapabilities_get_supportsBuffer_mE87BCD83AA4FEE3C89EE906EF2F1B6DFA9B7BA59_AdjustorThunk },
	{ 0x06000014, HapticCapabilities_get_bufferFrequencyHz_m31270DBF2491B9FDC969D0082ABB5CF0F0DFDBAA_AdjustorThunk },
	{ 0x06000015, HapticCapabilities_get_bufferMaxSize_mAC7A86B40B716A2B9B17D4B3B26EE6B7D0346C7C_AdjustorThunk },
	{ 0x06000016, HapticCapabilities_get_bufferOptimalSize_m166A6DD9882CD48F702FE7447E87C60AAF479407_AdjustorThunk },
	{ 0x06000017, HapticCapabilities_Equals_m87B9C608F4E14AE0052DA784111BA3FE74EF5B57_AdjustorThunk },
	{ 0x06000018, HapticCapabilities_Equals_m4CD220F74171CB85C19D2CA75D3BC9933B699F0F_AdjustorThunk },
	{ 0x06000019, HapticCapabilities_GetHashCode_m7C7504CA0AADBD291E33540E08B00EFF47A2A2DB_AdjustorThunk },
	{ 0x0600001A, InputFeatureUsage_get_name_mA51C7B42F66E87B3527CBD061D1E6944058FE085_AdjustorThunk },
	{ 0x0600001B, InputFeatureUsage_get_internalType_m39EE9801AA983980676998D910C3A906191F8810_AdjustorThunk },
	{ 0x0600001C, InputFeatureUsage_Equals_m490288B62FC8FB23746E66937C85D8600BC150C0_AdjustorThunk },
	{ 0x0600001D, InputFeatureUsage_Equals_m77BD3037164E49DCCFE7C912FD67B14D9A0A621D_AdjustorThunk },
	{ 0x0600001E, InputFeatureUsage_GetHashCode_m823C86783F2394534F08BF9F56F4A23656D8E34D_AdjustorThunk },
	{ 0x06000026, InputDevice__ctor_mC7506750471891883425541E9DE3EB23D8FE4E34_AdjustorThunk },
	{ 0x06000027, InputDevice_get_deviceId_mF3FFF56432109EF58AC2EE178FA343261E950A19_AdjustorThunk },
	{ 0x06000028, InputDevice_get_isValid_mA908CF8195CECA44FF457430AFF9198C3FEC0948_AdjustorThunk },
	{ 0x06000029, InputDevice_get_name_mAEA08A3755E715697022C54227556428CEF4E1CE_AdjustorThunk },
	{ 0x0600002A, InputDevice_get_characteristics_mEEDC9AB2254C155C47B48848FECF347A87925ADB_AdjustorThunk },
	{ 0x0600002B, InputDevice_IsValidId_m13527A66E7D03B0C9AA64C2FFCC4A2F3889756BB_AdjustorThunk },
	{ 0x0600002C, InputDevice_SendHapticImpulse_m7166A784508F8E0F3AE5BD88863171C7A905BC1B_AdjustorThunk },
	{ 0x0600002D, InputDevice_TryGetHapticCapabilities_mC97EE9A231941988E8B6FA8AF0CB310EFE1B77A3_AdjustorThunk },
	{ 0x0600002E, InputDevice_TryGetFeatureValue_m24EC3B6C41AE4098269427232AD5F52E786BF884_AdjustorThunk },
	{ 0x0600002F, InputDevice_TryGetFeatureValue_m9FC969BEFF0E5BAB78DD9F2130F437788D20068C_AdjustorThunk },
	{ 0x06000030, InputDevice_TryGetFeatureValue_m675D52240379FEF80D6499B5031941812FDFD081_AdjustorThunk },
	{ 0x06000031, InputDevice_TryGetFeatureValue_mB2C15D1FC747DA9FB5958FA17E77049886FB3BBA_AdjustorThunk },
	{ 0x06000032, InputDevice_TryGetFeatureValue_m472B5ECE996FB7440CACCF1E85722DA4963E3167_AdjustorThunk },
	{ 0x06000033, InputDevice_TryGetFeatureValue_m0C1A9761DD0D1C6D1EF4BAB2FAF1BC1A9541BB9F_AdjustorThunk },
	{ 0x06000034, InputDevice_TryGetFeatureValue_m8A01F07356DC85042F6BB7C6258A75C3EC3C4E11_AdjustorThunk },
	{ 0x06000036, InputDevice_Equals_m206193EDB0C7A69C0BDFC821CE87B887C7BE0CAC_AdjustorThunk },
	{ 0x06000037, InputDevice_Equals_mD6717F0B7B8605BB9F616947C1A3289C52ED41FC_AdjustorThunk },
	{ 0x06000038, InputDevice_GetHashCode_m4F8831F85A8646E52B12B5431BEC004AD1BAAD9D_AdjustorThunk },
	{ 0x06000039, Hand_get_deviceId_m3FD7DC0BD8EBE55A433CBDB6F3D9486D6491714A_AdjustorThunk },
	{ 0x0600003A, Hand_get_featureIndex_mD4AFCDFAEE8471B59400FF25F84328FA17229775_AdjustorThunk },
	{ 0x0600003B, Hand_Equals_m2BEF7EF009B595519DA0ADBE1912C197CEAD2E06_AdjustorThunk },
	{ 0x0600003C, Hand_Equals_mA5FFEB3581A5E053717C0718AD68E596E801C864_AdjustorThunk },
	{ 0x0600003D, Hand_GetHashCode_mC1EA0B4473AFA966324B32BAC552ADB9BA9D24EE_AdjustorThunk },
	{ 0x0600003E, Eyes_get_deviceId_m673FE292832DCAB360282083381923052944EBFD_AdjustorThunk },
	{ 0x0600003F, Eyes_get_featureIndex_mC4D34E37C0C008A69E75136C501F8907BC4F2EE8_AdjustorThunk },
	{ 0x06000040, Eyes_Equals_m4DC0E274FA64B59ED26E913BE0C95554476F228D_AdjustorThunk },
	{ 0x06000041, Eyes_Equals_mE463ABC100B9BDB2790A18804BAD09FA1F289969_AdjustorThunk },
	{ 0x06000042, Eyes_GetHashCode_m754EAF148F82A79A7DD39D5F0D294EC4ABC763C8_AdjustorThunk },
	{ 0x06000043, Bone_get_deviceId_mA75E5FA9A3288C5F5E4172D7632FE228487BE0F1_AdjustorThunk },
	{ 0x06000044, Bone_get_featureIndex_mF108AEAE50FFD1B908846717BBD95C59665925BE_AdjustorThunk },
	{ 0x06000045, Bone_Equals_mDC77AF51237B5937D83C048DF2734EB7162620F7_AdjustorThunk },
	{ 0x06000046, Bone_Equals_mB8585697810AA19D6DD257F9479F01993739020F_AdjustorThunk },
	{ 0x06000047, Bone_GetHashCode_mF14BB3AB7B7F52290CDF5D6A7C63AE11B3294F82_AdjustorThunk },
	{ 0x0600005F, MeshId_ToString_m8D2D9206A924A86FF28CF6499B7C66941FD0E7D4_AdjustorThunk },
	{ 0x06000060, MeshId_GetHashCode_mC6B6428EB5856505023C0D48879B92C2E3F062B0_AdjustorThunk },
	{ 0x06000061, MeshId_Equals_m1106B9A800E87442FE36125FD3EC3CB12FD778E9_AdjustorThunk },
	{ 0x06000062, MeshId_Equals_m3D0AC754FDC327819342797C38CF20C5F3D25818_AdjustorThunk },
	{ 0x0600006B, MeshGenerationResult_get_MeshId_m2260BF3F7EB6ED4F2CF0A8F6BEFDD2CD82B6751D_AdjustorThunk },
	{ 0x0600006C, MeshGenerationResult_get_Mesh_m95A022CCC5DDD8D0E8B9CEB3A2D3E06A0E7210F9_AdjustorThunk },
	{ 0x0600006D, MeshGenerationResult_get_MeshCollider_m07210E3B1723B5BA535250E2012755AAC46FB180_AdjustorThunk },
	{ 0x0600006E, MeshGenerationResult_get_Status_m392754927B1E28DB2E6078A212F8FCC013AC10C0_AdjustorThunk },
	{ 0x0600006F, MeshGenerationResult_get_Attributes_m62E3E9EC74240C37D818481B1243B3EB40424C6B_AdjustorThunk },
	{ 0x06000070, MeshGenerationResult_get_Position_m72AE25CA3412AE3C653251095067B496CEC1617F_AdjustorThunk },
	{ 0x06000071, MeshGenerationResult_get_Rotation_mC27F07EC357E8B3EED94DBEE5365B33154941EE3_AdjustorThunk },
	{ 0x06000072, MeshGenerationResult_get_Scale_mA3CEB3EA848509F5B1A5525F40A45AA9CB8BD5D3_AdjustorThunk },
	{ 0x06000073, MeshGenerationResult_Equals_m7A6C6A94553E313F74FB2B44326CF32FC94E122F_AdjustorThunk },
	{ 0x06000074, MeshGenerationResult_Equals_mE32D058EBA520E8CC77A08119D82AE735CE0ACAC_AdjustorThunk },
	{ 0x06000075, MeshGenerationResult_GetHashCode_mA542C935FE2ABB8EC0A3CE001267176095F7DCBC_AdjustorThunk },
	{ 0x06000078, MeshTransformList_Dispose_m0F0B77B84E50F02EB1A48751831808F9C69E0636_AdjustorThunk },
};
static const int32_t s_InvokerIndices[140] = 
{
	6260,
	6260,
	5022,
	6104,
	4892,
	4821,
	3336,
	3336,
	1111,
	1096,
	10143,
	10143,
	7435,
	10143,
	10143,
	10093,
	6247,
	6039,
	6039,
	6247,
	6247,
	6247,
	3449,
	3408,
	6104,
	6133,
	6247,
	3449,
	3418,
	6104,
	0,
	0,
	0,
	0,
	0,
	0,
	10975,
	5022,
	6248,
	6039,
	6133,
	6247,
	6039,
	1105,
	3336,
	1487,
	1491,
	1490,
	1492,
	1493,
	1489,
	1488,
	0,
	3449,
	3414,
	6104,
	6248,
	6247,
	3449,
	3405,
	6104,
	6248,
	6247,
	3449,
	3389,
	6104,
	6248,
	6247,
	3449,
	3358,
	6104,
	9708,
	10143,
	8877,
	10143,
	10143,
	10143,
	10143,
	10143,
	10143,
	8881,
	10143,
	7119,
	8378,
	7641,
	7641,
	7641,
	7641,
	7641,
	7641,
	9635,
	9871,
	10021,
	6260,
	6133,
	6104,
	3449,
	3442,
	10975,
	8472,
	7708,
	7166,
	6743,
	6596,
	6535,
	6487,
	6125,
	6133,
	6133,
	6104,
	6104,
	6252,
	6161,
	6252,
	3449,
	3441,
	6104,
	2649,
	6260,
	6260,
	10141,
	6039,
	3422,
	6104,
	6104,
	4919,
	4919,
	10141,
	10141,
	6260,
	6260,
	6260,
	4821,
	2665,
	4892,
	4919,
	3449,
	4919,
	3449,
	6260,
};
static const Il2CppTokenRangePair s_rgctxIndices[1] = 
{
	{ 0x0200000F, { 0, 5 } },
};
extern const uint32_t g_rgctx_InputFeatureUsage_1_set_name_m9C2C093BBED5BE9C72D51D95FF8D7BD9E1B58FDC;
extern const uint32_t g_rgctx_InputFeatureUsage_1_tC3E25C35CC3C47EA8C96D9AD547165F3A28C8211;
extern const uint32_t g_rgctx_InputFeatureUsage_1_tC3E25C35CC3C47EA8C96D9AD547165F3A28C8211;
extern const uint32_t g_rgctx_InputFeatureUsage_1_Equals_m32D9AF9C7F5E3ED62A18CC5A632C325B33CA06EB;
extern const uint32_t g_rgctx_InputFeatureUsage_1_get_name_m3E355A98DFFCFF3CACAA50623EEA65FAFCCF1F34;
static const Il2CppRGCTXDefinition s_rgctxValues[5] = 
{
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_InputFeatureUsage_1_set_name_m9C2C093BBED5BE9C72D51D95FF8D7BD9E1B58FDC },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_InputFeatureUsage_1_tC3E25C35CC3C47EA8C96D9AD547165F3A28C8211 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_InputFeatureUsage_1_tC3E25C35CC3C47EA8C96D9AD547165F3A28C8211 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_InputFeatureUsage_1_Equals_m32D9AF9C7F5E3ED62A18CC5A632C325B33CA06EB },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_InputFeatureUsage_1_get_name_m3E355A98DFFCFF3CACAA50623EEA65FAFCCF1F34 },
};
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_UnityEngine_XRModule_CodeGenModule;
const Il2CppCodeGenModule g_UnityEngine_XRModule_CodeGenModule = 
{
	"UnityEngine.XRModule.dll",
	140,
	s_methodPointers,
	71,
	s_adjustorThunks,
	s_InvokerIndices,
	0,
	NULL,
	1,
	s_rgctxIndices,
	5,
	s_rgctxValues,
	NULL,
	NULL, // module initializer,
	NULL,
	NULL,
	NULL,
};
