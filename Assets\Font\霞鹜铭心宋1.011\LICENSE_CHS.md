# IPA 开放字体授权协议 1.0 版（简体中文译本）

译者按：译文仅供参考，如有错误或出入，请参见原日语/英文版。此外，本译文并没有法律效力，请参见以下声明。

本简体中文译文原译为[夜煞之乐](https://github.com/NightFurySL2001)，[一点字坊](https://github.com/ichitenfont)校验内文，[落霞孤鹜](https://github.com/lxgw)改进部分翻译。本译文只供参考使用。如有任何差异，请参阅日语/英语原文条款。

* [For English, please click here.](LICENSE.md/#ipa-font-license-agreement-v10)
* [日本語バージョンは、こちらをご覧ください。](LICENSE.md)

## IPA 开放字体授权协议 1.0 版

「授权方」 (“Licensor”) 根据本授权协议（「协议」, “Agreement”）提供「授权程序」（如以下第 1 章所定义）。任何「接收者」使用、复制、或分发本「授权程序」，或行使任何在本「协议」下的权利（如以下第 1 章所定义），则视为「接收者」同意本「授权」。

### 第 1 章　定义

1. 「数字字体程序」 (“Digital Font Program”) 应当指包含或用于呈现或显示字体的电脑程序。
2. 「授权程序」 (“Licensed Program”) 应当指由「授权方」在本「协议」下授权的「数字字体程序」。
3. 「衍生程序」 (“Derived Program”) 应当指对「授权程序」或其中的一部分进行修改、增加、删减、替代等任何改编行为，也包括通过「授权程序」或「数字文档文件」里面的「嵌入字体」获取部分或全部字体信息创造出新的「数字字体程序」的情况，无论是否修改字体信息。
4. 「数字内容」 (“Digital Content”) 应当指以数字数据形式提供给最终用户的产品，例如视频内容、动态和／或静态图片、电视节目或其他广播内容，以及由文字符号、图片、摄影图像、图形符号和／或类似内容所组成的产品。
5. 「数字文档文件」 (“Digital Document File”) 应当指由各种软件程序创建的 PDF 文件或其他内容，其中部分或全部「授权程序」将会嵌入或包含在文件中，并且仅用于显示字体（或称「嵌入式字体」, embedded fonts）。「嵌入式字体」仅用于显示嵌入 (embedded) 于特定「数字文档文件」中的字符，而「嵌入式字体」与「数字字体程序」中嵌入的字体具有差异，后者可以用于显示该特定「数字文档文件」之外的字符。
6. 「电脑」 (“Computer”) 在本「授权」里应当包括服务器。
7. 「复制和其他开发利用」 (“Reproduction and Other Exploitation”) 应当包括复制、转让、分发、租赁、公共传播、展示、展览、改编和任何其他利用方式。
8. 「接收者」 (“Recipient”) 应当指任何在本「授权」下接收「授权程序」的人，包括从其他「接收者」接收「授权程序」的人。

### 第 2 章　许可

「授权方」根据本「协议」中的每个规定，授予「接收者」在任何与全部国家或地区使用「授权程序」的许可。但是，「授权程序」里包含的任何与全部权利皆由「授权方」持有。本「协议」绝对无意将「授权方」持有的「授权程序」的任何权利（除了本「协议」特别规定者）或任何关于商标、商品名称或服务商标的权利，转移给「接收者」。

1. 「接收者」可以在任意数量的「电脑」上安装「授权程序」，并且根据本「协议」中的每个规定使用该程序。
2. 「接收者」可以在印刷材料或「数字内容」里使用「授权程序」，无论是否进行修改，作为字符文本等的表达。
3. 「接收者」可以在商业或非商业目的的情况下，在任何形式的媒体（包括但不限于广播，通讯和各种记录媒体），根据上一章的定义对印刷材料或「数字内容」进行「复制和其他开发利用」。
4. 如果任何「接收者」从「数字文档文件」中提取「嵌入式字体」以创建「衍生程序」，则该「衍生程序」应遵守本协议的条款。
5. 如果任何「接收者」对「数字文档文件」进行「复制和其他开发利用」，而该「数字文档文件」所包含「授权程序」的「嵌入式字体」仅用于在该「数字文档文件」中呈现数字内容，则该「接收者」无需因此行为承担本「协议」的其他义务。
6. 「接收者」可以在本「授权」第 3 章第 2 条下，复制未经修改的「授权程序」，出于商业或非商业目的，将其副本公开传输或以其他方式重新分发给第三方（「重新分发」, “Redistribute”）。
7. 「接收者」可以根据上述条款创建、使用、复制和／或「重新分发」「衍生程序」：前提是，「接收者」应当在「重新分发」「衍生程序」时遵循第 3 章第 1 条的规定。

### 第 3 章　限制

本「授权」上一章所授予的许可应当受到下列限制：

1. 如果某「衍生程序」根据上一章第 4 至 7 条「重新分发」，则必须满足以下条件：
   - (1) 以下文件必须跟随「衍生程序」一并「重新分发」，或者通过网络或邮寄提供，所收取的费用不得超过邮费、存储媒介和手续费用的总和：
     - (a) 「衍生程序」的副本；以及
     - (b) 任何字体开发程序制作「衍生程序」时产生、可以对「衍生程序」进行进一步修改的额外文件（如果有这类文件）。
   - (2) 「接收者」应当有通过「重新分发」的方法，让「接收者」可以将「衍生程序」替换成初次在本「授权」下分发的「授权程序」（即「原始程序」）。「重新分发」的方法可以包括提供与「原始程序」的差异文件，或者是提供让「接收者」将「衍生程序」替换成「原始程序」的步骤。
   - (3) 「接收者」必须根据本「协议」规定的条款授权「衍生程序」。
   - (4) 任何人不得在「衍生程序」的程序名称、字体名称或文件名称中使用或包括「授权程序」的名称。
   - (5) 为满足本条的要求，而通过网络或邮寄方式提供的任何材料，可以由愿意提供的任何一方或任何人提供。
2. 如果「接收者」根据上一章第 6 条「重新分发」「授权程序」，该「接收者」必须满足以下条件：
   - (1) 「接收者」不可修改「授权程序」的名称。
   - (2) 「接收者」不可对「授权程序」进行加工或其他修改。
   - (3) 「接收者」必须随「授权程序」附上本「协议」的副本。
3. 此「授权程序」由「授权方」❝按原样❞提供，「授权方」不对「授权程序」或任何「衍生程序」提供任何明示或暗示的担保，包括但不限于对所有权、非侵权、可贸易性、或对特定用途的适用性提供免责声明。在任何情况下，「授权方」均不对任何直接、间接、偶发、特殊、扩展、示范性、或后果性损害（包括但不限于采购替代产品或服务；由于系统故障引起的损害；损失或系统损坏；损失利益）承担责任，无论是基于合同、严格责任、还是侵权（包括过失或其他情况）而进行的安装、使用、「复制和其他开发利用」「授权程序」或任何「衍生程序」，或行使此处授予的任何权利，即使已事先告知可能发生此类损害。
4. 「授权方」并无义务回应任何技术问题或咨询，或者提供其他关于安装、使用或对「授权程序」或「衍生程序」进行「复制和其他开发利用」的用户支持。

### 第 4 章　终止

1. 本「授权」的期限应在「接收者」接收「授权程序」时开始，并应在「接收者」以任何方式保留任何此类「授权程序」时持续。
2. 尽管有上一条的规定，如果「接收者」违反了本「协议」中的任何规定，则本「协议」将自动终止，恕不另行通知。本「协议」终止后，「接收者」不得使用「授权程序」或「衍生程序」，或对其进行「复制和其他开发利用」。但是，本「协议」的终止不应影响任何其他「接收者」从违反本「协议」的「接收者」那里接收「授权程序」或「衍生程序」的权利。

### 第 5 章　适用法律

1. IPA 可以发布本「授权」的修订版和／或新版本。在这种情况下，「接收者」可以选择使用本「协议」或者任何后续版本，以使用或对「授权程序」或「衍生程序」进行「复制和其他开发利用」。上面未指定的其他事项应受日本国版权法和日本国其他相关法律法规的约束。
2. 本「授权」本协议应当根据日本国法律解释。
