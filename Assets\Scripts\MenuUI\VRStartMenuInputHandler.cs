using UnityEngine;
using UnityEngine.UI;
using TMPro;
using System.Collections;

#if UNITY_XR_INTERACTION_TOOLKIT
using UnityEngine.XR.Interaction.Toolkit;
using UnityEngine.XR;
#endif

/// <summary>
/// VR开始菜单输入处理器
/// 
/// 专门处理VR环境中开始菜单的输入交互
/// 包括虚拟键盘、控制器输入和触觉反馈
/// </summary>
public class VRStartMenuInputHandler : MonoBehaviour
{
    [Header("输入组件")]
    [SerializeField] private TMP_InputField targetInputField;
    [SerializeField] private Button startButton;
    [SerializeField] private Canvas virtualKeyboard;
    
    [Header("VR控制器设置")]
    [SerializeField] private bool enableControllerInput = true;
    [SerializeField] private bool enableVirtualKeyboard = true;
    [SerializeField] private bool enableHapticFeedback = true;
    
    [Header("虚拟键盘设置")]
    [SerializeField] private float keyboardDistance = 1.5f;
    [SerializeField] private Vector3 keyboardOffset = new Vector3(0, -0.5f, 0);
    [SerializeField] private float keyboardScale = 0.008f;
    
    [Header("输入验证")]
    [SerializeField] private int maxInputLength = 20;
    [SerializeField] private bool allowSpecialCharacters = false;
    [SerializeField] private string allowedCharacters = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
    
    [Header("音效")]
    [SerializeField] private AudioSource audioSource;
    [SerializeField] private AudioClip keyPressSound;
    [SerializeField] private AudioClip deleteSound;
    [SerializeField] private AudioClip confirmSound;
    
    // 私有变量
    private Camera vrCamera;
    private bool isKeyboardVisible = false;
    private string currentInput = "";
    private Coroutine inputCoroutine;
    
#if UNITY_XR_INTERACTION_TOOLKIT
    private XRRayInteractor[] rayInteractors;
    private XRController[] controllers;
#endif
    
    // 事件
    public System.Action<string> OnInputChanged;
    public System.Action<string> OnInputConfirmed;
    public System.Action OnInputCancelled;
    
    void Start()
    {
        InitializeInputHandler();
    }
    
    void Update()
    {
        HandleVRInput();
    }
    
    /// <summary>
    /// 初始化输入处理器
    /// </summary>
    private void InitializeInputHandler()
    {
        // 查找VR摄像机
        vrCamera = Camera.main;
        if (vrCamera == null)
        {
            vrCamera = FindObjectOfType<Camera>();
        }
        
        // 查找音频源
        if (audioSource == null)
        {
            audioSource = GetComponent<AudioSource>();
            if (audioSource == null)
            {
                audioSource = gameObject.AddComponent<AudioSource>();
            }
        }
        
        // 初始化VR控制器
        InitializeVRControllers();
        
        // 设置输入框事件
        SetupInputFieldEvents();
        
        // 创建虚拟键盘
        if (enableVirtualKeyboard && virtualKeyboard == null)
        {
            CreateVirtualKeyboard();
        }
        
        Debug.Log("[VRStartMenuInputHandler] 输入处理器初始化完成");
    }
    
    /// <summary>
    /// 初始化VR控制器
    /// </summary>
    private void InitializeVRControllers()
    {
#if UNITY_XR_INTERACTION_TOOLKIT
        rayInteractors = FindObjectsOfType<XRRayInteractor>();
        controllers = FindObjectsOfType<XRController>();
        
        Debug.Log($"[VRStartMenuInputHandler] 找到 {rayInteractors.Length} 个射线交互器和 {controllers.Length} 个控制器");
#endif
    }
    
    /// <summary>
    /// 设置输入框事件
    /// </summary>
    private void SetupInputFieldEvents()
    {
        if (targetInputField != null)
        {
            targetInputField.onSelect.AddListener(OnInputFieldSelected);
            targetInputField.onDeselect.AddListener(OnInputFieldDeselected);
            targetInputField.onValueChanged.AddListener(OnInputValueChanged);
        }
    }
    
    /// <summary>
    /// 处理VR输入
    /// </summary>
    private void HandleVRInput()
    {
        if (!enableControllerInput) return;
        
#if UNITY_XR_INTERACTION_TOOLKIT
        foreach (var controller in controllers)
        {
            if (controller == null) continue;
            
            // 检查菜单按钮（显示/隐藏虚拟键盘）
            bool menuPressed = false;
            controller.inputDevice.TryGetFeatureValue(CommonUsages.menuButton, out menuPressed);
            
            if (menuPressed && !isKeyboardVisible)
            {
                ShowVirtualKeyboard();
            }
            else if (menuPressed && isKeyboardVisible)
            {
                HideVirtualKeyboard();
            }
            
            // 检查主按钮（确认输入）
            bool primaryPressed = false;
            controller.inputDevice.TryGetFeatureValue(CommonUsages.primaryButton, out primaryPressed);
            
            if (primaryPressed && targetInputField != null && targetInputField.isFocused)
            {
                ConfirmInput();
            }
            
            // 检查副按钮（删除字符）
            bool secondaryPressed = false;
            controller.inputDevice.TryGetFeatureValue(CommonUsages.secondaryButton, out secondaryPressed);
            
            if (secondaryPressed && targetInputField != null && targetInputField.isFocused)
            {
                DeleteLastCharacter();
            }
        }
#endif
    }
    
    /// <summary>
    /// 输入框被选中
    /// </summary>
    private void OnInputFieldSelected(string text)
    {
        Debug.Log("[VRStartMenuInputHandler] 输入框被选中");
        
        if (enableVirtualKeyboard)
        {
            ShowVirtualKeyboard();
        }
        
        // 触觉反馈
        TriggerHapticFeedback(0.1f);
    }
    
    /// <summary>
    /// 输入框失去焦点
    /// </summary>
    private void OnInputFieldDeselected(string text)
    {
        Debug.Log("[VRStartMenuInputHandler] 输入框失去焦点");
        
        if (enableVirtualKeyboard)
        {
            HideVirtualKeyboard();
        }
    }
    
    /// <summary>
    /// 输入值改变
    /// </summary>
    private void OnInputValueChanged(string text)
    {
        currentInput = ValidateInput(text);
        
        // 如果输入被修改，更新输入框
        if (currentInput != text && targetInputField != null)
        {
            targetInputField.text = currentInput;
        }
        
        OnInputChanged?.Invoke(currentInput);
        
        // 播放按键音效
        PlaySound(keyPressSound);
    }
    
    /// <summary>
    /// 验证输入
    /// </summary>
    private string ValidateInput(string input)
    {
        if (string.IsNullOrEmpty(input)) return input;
        
        // 限制长度
        if (input.Length > maxInputLength)
        {
            input = input.Substring(0, maxInputLength);
        }
        
        // 过滤字符
        if (!allowSpecialCharacters)
        {
            string filteredInput = "";
            foreach (char c in input)
            {
                if (allowedCharacters.Contains(c.ToString()))
                {
                    filteredInput += c;
                }
            }
            input = filteredInput;
        }
        
        return input;
    }
    
    /// <summary>
    /// 显示虚拟键盘
    /// </summary>
    private void ShowVirtualKeyboard()
    {
        if (virtualKeyboard == null || isKeyboardVisible) return;
        
        virtualKeyboard.gameObject.SetActive(true);
        PositionVirtualKeyboard();
        isKeyboardVisible = true;
        
        Debug.Log("[VRStartMenuInputHandler] 显示虚拟键盘");
    }
    
    /// <summary>
    /// 隐藏虚拟键盘
    /// </summary>
    private void HideVirtualKeyboard()
    {
        if (virtualKeyboard == null || !isKeyboardVisible) return;
        
        virtualKeyboard.gameObject.SetActive(false);
        isKeyboardVisible = false;
        
        Debug.Log("[VRStartMenuInputHandler] 隐藏虚拟键盘");
    }
    
    /// <summary>
    /// 定位虚拟键盘
    /// </summary>
    private void PositionVirtualKeyboard()
    {
        if (virtualKeyboard == null || vrCamera == null) return;
        
        Vector3 cameraPosition = vrCamera.transform.position;
        Vector3 cameraForward = vrCamera.transform.forward;
        
        Vector3 keyboardPosition = cameraPosition + cameraForward * keyboardDistance + keyboardOffset;
        virtualKeyboard.transform.position = keyboardPosition;
        virtualKeyboard.transform.localScale = Vector3.one * keyboardScale;
        
        virtualKeyboard.transform.LookAt(vrCamera.transform);
        virtualKeyboard.transform.Rotate(0, 180, 0);
    }
    
    /// <summary>
    /// 创建虚拟键盘
    /// </summary>
    private void CreateVirtualKeyboard()
    {
        // 这里可以创建一个简单的虚拟键盘
        // 或者加载预制体
        GameObject keyboardGO = new GameObject("Virtual Keyboard");
        virtualKeyboard = keyboardGO.AddComponent<Canvas>();
        virtualKeyboard.renderMode = RenderMode.WorldSpace;
        virtualKeyboard.worldCamera = vrCamera;
        
        // 添加简单的键盘UI
        CreateSimpleKeyboard(keyboardGO.transform);
        
        keyboardGO.SetActive(false);
    }
    
    /// <summary>
    /// 创建简单键盘
    /// </summary>
    private void CreateSimpleKeyboard(Transform parent)
    {
        // 创建背景面板
        GameObject panel = new GameObject("Keyboard Panel");
        panel.transform.SetParent(parent, false);
        
        Image panelImage = panel.AddComponent<Image>();
        panelImage.color = new Color(0.1f, 0.1f, 0.1f, 0.9f);
        
        RectTransform panelRect = panel.GetComponent<RectTransform>();
        panelRect.sizeDelta = new Vector2(800, 300);
        
        // 添加一些基本按键（简化版）
        CreateKeyboardButton(panel.transform, "确认", new Vector2(300, -100), ConfirmInput);
        CreateKeyboardButton(panel.transform, "删除", new Vector2(150, -100), DeleteLastCharacter);
        CreateKeyboardButton(panel.transform, "取消", new Vector2(0, -100), CancelInput);
    }
    
    /// <summary>
    /// 创建键盘按钮
    /// </summary>
    private void CreateKeyboardButton(Transform parent, string text, Vector2 position, System.Action action)
    {
        GameObject buttonGO = new GameObject($"Key_{text}");
        buttonGO.transform.SetParent(parent, false);
        
        Image buttonImage = buttonGO.AddComponent<Image>();
        buttonImage.color = new Color(0.3f, 0.3f, 0.3f, 1f);
        
        Button button = buttonGO.AddComponent<Button>();
        button.onClick.AddListener(() => action?.Invoke());
        
        RectTransform buttonRect = buttonGO.GetComponent<RectTransform>();
        buttonRect.sizeDelta = new Vector2(120, 60);
        buttonRect.anchoredPosition = position;
        
        // 添加文本
        GameObject textGO = new GameObject("Text");
        textGO.transform.SetParent(buttonGO.transform, false);
        
        TextMeshProUGUI buttonText = textGO.AddComponent<TextMeshProUGUI>();
        buttonText.text = text;
        buttonText.fontSize = 18;
        buttonText.color = Color.white;
        buttonText.alignment = TextAlignmentOptions.Center;
        
        RectTransform textRect = textGO.GetComponent<RectTransform>();
        textRect.anchorMin = Vector2.zero;
        textRect.anchorMax = Vector2.one;
        textRect.offsetMin = Vector2.zero;
        textRect.offsetMax = Vector2.zero;
    }
    
    /// <summary>
    /// 确认输入
    /// </summary>
    private void ConfirmInput()
    {
        OnInputConfirmed?.Invoke(currentInput);
        PlaySound(confirmSound);
        TriggerHapticFeedback(0.2f);
        
        if (startButton != null)
        {
            startButton.onClick.Invoke();
        }
        
        Debug.Log($"[VRStartMenuInputHandler] 确认输入: {currentInput}");
    }
    
    /// <summary>
    /// 删除最后一个字符
    /// </summary>
    private void DeleteLastCharacter()
    {
        if (targetInputField != null && !string.IsNullOrEmpty(targetInputField.text))
        {
            targetInputField.text = targetInputField.text.Substring(0, targetInputField.text.Length - 1);
            PlaySound(deleteSound);
            TriggerHapticFeedback(0.1f);
        }
    }
    
    /// <summary>
    /// 取消输入
    /// </summary>
    private void CancelInput()
    {
        if (targetInputField != null)
        {
            targetInputField.text = "";
        }
        
        OnInputCancelled?.Invoke();
        HideVirtualKeyboard();
        
        Debug.Log("[VRStartMenuInputHandler] 取消输入");
    }
    
    /// <summary>
    /// 触发触觉反馈
    /// </summary>
    private void TriggerHapticFeedback(float intensity)
    {
        if (!enableHapticFeedback) return;
        
#if UNITY_XR_INTERACTION_TOOLKIT
        foreach (var controller in controllers)
        {
            if (controller != null)
            {
                controller.inputDevice.TryGetHapticCapabilities(out HapticCapabilities capabilities);
                if (capabilities.supportsImpulse)
                {
                    controller.inputDevice.SendHapticImpulse(0, intensity, 0.1f);
                }
            }
        }
#endif
    }
    
    /// <summary>
    /// 播放音效
    /// </summary>
    private void PlaySound(AudioClip clip)
    {
        if (audioSource != null && clip != null)
        {
            audioSource.PlayOneShot(clip);
        }
    }
    
    /// <summary>
    /// 设置目标输入框
    /// </summary>
    public void SetTargetInputField(TMP_InputField inputField)
    {
        targetInputField = inputField;
        SetupInputFieldEvents();
    }
    
    /// <summary>
    /// 设置开始按钮
    /// </summary>
    public void SetStartButton(Button button)
    {
        startButton = button;
    }
    
    void OnDestroy()
    {
        // 清理事件
        if (targetInputField != null)
        {
            targetInputField.onSelect.RemoveListener(OnInputFieldSelected);
            targetInputField.onDeselect.RemoveListener(OnInputFieldDeselected);
            targetInputField.onValueChanged.RemoveListener(OnInputValueChanged);
        }
    }
}
